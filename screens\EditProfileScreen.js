import React, { useContext, useEffect, useRef, useState } from 'react'
import { Alert, Image, ImageBackground, Keyboard, KeyboardAvoidingView, Modal, PermissionsAndroid, Platform, Pressable, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native'
import EntutoEditText from '../components/common/EntutoEditText';
import EntutoSwitch from '../components/common/EntutoSwitch';
import EntutoTextView from '../components/common/EntutoTextView';
import ProgressiveImage from '../components/common/ProgressiveImage';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import { decodeHtmlEntitessData, encodeHtmlEntitessData, hasImageUrlExist, _inputFormatTextForTag, checkValueLength } from '../utils/Utils';
import { MultiSelect } from 'react-native-element-dropdown';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import CustomStatusBar from '../components/common/CustomStatusBar';
import ServerConnector from '../utils/ServerConnector';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import { TAGGED_SYMBOL, _RedirectionErrorList } from '../utils/Appconfig';
import { AppStateContext } from '..';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import CustomSnackbar from '../components/common/CustomSnackbar';
import MimeTypeList from '../utils/MimeTypeList';
import ActionSheet from 'react-native-actions-sheet';
import MediaMenuActionView from '../components/profile/MediaMenuActionView';
import { launchImageLibrary } from 'react-native-image-picker';
import SelectBoxComponent from '../components/common/SelectBoxComponent';
import appData from '../data/Data';
import ImagePicker from 'react-native-image-crop-picker';
import ErrorMessages from '../constants/ErrorMessages';
import InputTagPeopleSearchComponent from '../components/tag/InputTagPeopleSearchComponent';
import { requestStoragePermission } from '../utils/PermissionManager';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import EntutoDropdown from '../components/common/EntutoDropdown';


const EditProfileScreen = ({ route, navigation }) => {
    const PlaceholderCoverImage = require('../assets/Images/PlaceholderCoverImage.png');
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const { fullUserDetails, changeUserDetails, captureProfileImg,
        captureCoverImg, changeCaptureProfileImg, changeCaptureCoverImg, changeUserProfileImage } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const __is_profile_paid = fullUserDetails.hasOwnProperty("_user_account_type") ? fullUserDetails._user_account_type == "PAID" ? true : false : false;

    const [profileCoverImg, setprofileCoverImg] = useState(null);
    const [profileImg, setprofileImg] = useState(null);

    const [displayCat, setdisplayCat] = useState(false);

    const [catValue, setcatValue] = useState([]);
    const [catValueErr, setcatValueErr] = useState("");
    const [catFocused, setcatFocused] = useState(false);
    const [catValueList, setcatValueList] = useState([]);
    const [dbCategoryList, setdbCategoryList] = useState([]);

    const [userName, setuserName] = useState("");
    const [userNameErr, setuserNameErr] = useState("");

    const [displayName, setdisplayName] = useState("");
    const [displayNameErr, setdisplayNameErr] = useState("");

    const [bio, setbio] = useState("");
    const [bioErr, setbioErr] = useState("");


    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(true);

    const [verificationClickHappen, setverificationClickHappen] = useState(false);
    const [profileVerifiedStatusTxt, setprofileVerifiedStatusTxt] = useState("");

    const mediaActionRef = useRef(null);

    const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);
    const [mediaActionType, setmediaActionType] = useState("");

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [snackBarType, setsnackBarType] = useState("FAILED");
    const [isProfileVerified, setisProfileVerified] = useState(false);

    const [showTagPopup, setshowTagPopup] = useState(false);
    const [tagName, settagName] = useState("");
    const descInputRef = useRef(null);

    const [genderList, setGenderList] = useState([
        { value: "MALE", label: "Male" },
        { value: "FEMALE", label: "Female" },
        // { value: "OTHERS", label: "Others" },
        // { value: "DO_NOT_SPECIFY", label: "Do not specify" },

    ]);
    const [selectedGender, setSelectedGender] = useState("");
    const [selectedGenderErr, setSelectedGenderErr] = useState("");


    // useEffect(() => {
    //     const unsubscribe = navigation.addListener('focus', () => {
    //         setShowLoading(true)
    //         getUserProfileService();

    //     });
    //     return unsubscribe;
    // }, []);
    useEffect(() => {
        setShowLoading(true)
        getUserProfileService();
    }, []);

    useEffect(() => {
        if (captureProfileImg != null) {
            setdisableUpdateBtn(false);
            setprofileImg(captureProfileImg.uri)
        }
    }, [captureProfileImg]);
    useEffect(() => {
        if (captureCoverImg != null) {
            setdisableUpdateBtn(false);
            setprofileCoverImg(captureCoverImg.uri)
        }

    }, [captureCoverImg]);


    function getUserProfileService(redirecthappen = false) {
        let hashMap = {
            _action_code: "11:GET_USER_PROFILE",
            req_profile_seq: __ProfileSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let displayCat = false;
            if (data.data[0].display_category === "YES") {
                displayCat = true;
            }
            setdisplayCat(displayCat);
            let category = "";
            if (data.data[0].category !== null) {
                category = data.data[0].category;
            }
            setdbCategoryList(category);

            let display_name = "";
            if (data.data[0].display_name !== null) {
                display_name = data.data[0].display_name;
            }
            setdisplayName(display_name);
            let profile_bio = ""
            if (data.data[0].profile_bio !== null) {
                let decodeBio = data.data[0].profile_bio.replace(/<br\s*[\/]?>/gi, '\n');
                profile_bio = decodeHtmlEntitessData(decodeBio);;
            }
            setbio(profile_bio);


            let fb_link = ""
            if (data.data[0].fb_link !== null) {
                fb_link = data.data[0].fb_link;
            }
            let twiter_link = ""
            if (data.data[0].twiter_link !== null) {
                twiter_link = data.data[0].twiter_link;
            }
            let insta_link = ""
            if (data.data[0].insta_link !== null) {
                insta_link = data.data[0].insta_link;
            }
            let verificationClickHappenD = false;
            let profileVerifiedStatusTxtD = "";

            if (data.data[0].verification == "PENDING") {
                profileVerifiedStatusTxtD = "Pending Verification";
            }
            else if (data.data[0].verification == "VERIFIED") {
                profileVerifiedStatusTxtD = "Verified";
                setisProfileVerified(true);
            }
            else if (data.data[0].verification == "REJECTED") {
                profileVerifiedStatusTxtD = "Re-submit Verification"
                verificationClickHappenD = true;
            }
            else {
                profileVerifiedStatusTxtD = "Submit Verification";
                verificationClickHappenD = true;
            }
            let genderVal = "";
            if (checkValueLength(data.data[0].gender)) {
                genderVal = data.data[0].gender
            }
            setSelectedGender(genderVal)
            setuserName(data.data[0].user_handle);
            setverificationClickHappen(verificationClickHappenD);
            setprofileVerifiedStatusTxt(profileVerifiedStatusTxtD);
            setShowLoading(false);
            // getAllCategoryService(category);
            setprofileImg(data.data[0].profile_picture);
            setprofileCoverImg(data.data[0].cover_image);
            let userDeatails = {
                _user_handle: data.data[0].user_handle,
                _user_account_type: data.data[0].type,
                _user_display_name: display_name,
                _is_profile_verified: data.data[0].is_verified,
                _profile_picture: data.data[0].profile_picture,
                _cover_image: data.data[0].cover_image,
            };
            changeUserProfileImage(data.data[0].profile_picture)
            changeUserDetails(userDeatails);
            seterrorMsg("");
            if (redirecthappen) {
                setdisableUpdateBtn(true)
                setShowLoading(false);
                appData._editProfileChangesRefresh = true;
                setTimeout(() => {
                    navigation.goBack(null);
                }, 300);

            }
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                seterrorMsg(errorMessage);
                setShowLoading(false);
            }
        });
    }

    function getAllCategoryService(selectCatList) {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: "CATEGORY",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let catList = [];
            data.data.map((obj) => {
                if (selectCatList.includes(obj.config_key)) {
                    catList.push(obj.config_key);
                }
            });
            setcatValueList(data.data);
            setcatValue(catList);
            setShowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setcatValueList([]);
                setcatValue([]);
                setShowLoading(false);
            }
        });
    }
    const updateBtnPress = () => {
        setSnackbarMsg("");
        setdisplaySnackbar(false);
        setrefreshSnackBar(Math.random());
        Keyboard.dismiss();
        let isFormValid = true;
        if (__is_profile_paid) {
            if (bio.length == 0) {
                setbioErr("[ required ]");
                isFormValid = false;
            }
        }
        if (isFormValid) {
            setShowLoading(true);
            submitProfileDataService();
        }

    }
    function submitProfileDataService() {
        let displayCatV = "NO";
        if (displayCat) {
            displayCatV = "YES"
        }
        let catSeq = "no";
        // catValue.map((obj) => {
        //     if (catSeq.length !== 0) {
        //         catSeq += ",";
        //     }
        //     catSeq += obj;
        // });
        // if (catSeq.length == 0) {
        //     setSnackbarMsg(ErrorMessages.categoryRequiredErr);
        //     setdisplaySnackbar(true);
        //     setsnackBarType("FAILED");
        //     setrefreshSnackBar(Math.random());
        //     setShowLoading(false);
        //     return;
        // }

        let encodeStD = encodeHtmlEntitessData(bio);
        let hashMap = {
            _action_code: "11:SAVE_USER_PROFILE",
            user_name: encodeURIComponent(userName),
            display_name: encodeURIComponent(displayName),
            user_bio: encodeURIComponent(encodeStD),
            category: catSeq,
            disp_category: displayCatV,
        }
        if (checkValueLength(selectedGender)) {
            hashMap.gender = selectedGender;
        }
        let imageHashMap = [];

        if (captureProfileImg != null) {
            let parts = captureProfileImg.uri.split('.');
            let fileFormat = parts[parts.length - 1];
            let captureFile = {
                uri: Platform.OS === 'android' ? captureProfileImg.uri : captureProfileImg.uri.replace('file://', ''),
                name: "profile_file_" + new Date() + "." + fileFormat,
                type: MimeTypeList[fileFormat]
            };
            imageHashMap.push({ inputName: "profile_file", imageData: captureFile });
        }
        if (captureCoverImg != null) {
            let parts = captureCoverImg.uri.split('.');
            let fileFormat = parts[parts.length - 1];
            let captureFile = {
                uri: Platform.OS === 'android' ? captureCoverImg.uri : captureCoverImg.uri.replace('file://', ''),
                name: "cover_file_" + new Date() + "." + fileFormat,
                type: MimeTypeList[fileFormat]
            };
            imageHashMap.push({ inputName: "cover_file", imageData: captureFile });
        }

        if (!hasImageUrlExist(profileImg)) {
            hashMap.reset_profile = "YES";
        }
        if (!hasImageUrlExist(profileCoverImg)) {
            hashMap.reset_cover = "YES";
        }

        let connector = new ServerConnector();
        connector.postDataMultiPart(hashMap, imageHashMap, (data) => { // success method
            getUserProfileService(true);
            // setSnackbarMsg(data.msg);
            // setsnackBarType("SUCCESS");
            // setdisplaySnackbar(true);
            // setrefreshSnackBar(Math.random());


        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.user_name) {
                            setuserNameErr(data.data.user_name);
                            fieldErrorShown = true;
                        }
                        if (data.data.display_name) {
                            setdisplayNameErr(data.data.display_name);
                            fieldErrorShown = true;
                        }
                        if (data.data.user_bio) {
                            setbioErr(data.data.user_bio);
                            fieldErrorShown = true;
                        }
                        if (data.data.category) {
                            setcatValueErr(data.data.category);
                            fieldErrorShown = true;
                        }
                        // if (data.data.fb_link) {
                        //     this.setState({
                        //         fbLinkErr: data.data.fb_link,
                        //     });
                        //     fieldErrorShown = true;
                        // }
                        // if (data.data.twiter_link) {
                        //     this.setState({
                        //         twitterLinkErr: data.data.twiter_link,
                        //     });
                        //     fieldErrorShown = true;
                        // }
                        // if (data.data.country) {
                        //     this.setState({
                        //         countryNameErr: data.data.country,
                        //     });
                        //     fieldErrorShown = true;
                        // }
                        // if (data.data.state) {
                        //     this.setState({
                        //         stateNameErr: data.data.state,
                        //     });
                        //     fieldErrorShown = true;
                        // }
                        // if (data.data.insta_link) {
                        //     this.setState({
                        //         instaLinkErr: data.data.insta_link,
                        //     });
                        //     fieldErrorShown = true;
                        // }
                        if (data.data.cover_file) {
                            setSnackbarMsg(data.data.cover_file);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            return;
                        }
                        if (data.data.profile_file) {
                            setSnackbarMsg(data.data.profile_file);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());

                            return;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    setSnackbarMsg(errorMessage);
                    setdisplaySnackbar(true);
                    setsnackBarType("FAILED");
                    setrefreshSnackBar(Math.random());
                }
            }
        });
    }
    const onDisplayCatChange = () => {
        setdisableUpdateBtn(false);
        setdisplayCat(!displayCat);
    }
    // const renderDropItem = (item,) => {
    //     return (
    //         <View style={defaultStyle.dropdownItem}>
    //             <EntutoTextView style={defaultStyle.dropdownTextNotSelectedStyle}>{item.display_value}</EntutoTextView>
    //             {catValue.includes(item.config_key) && (
    //                 <MaterialIcons
    //                     style={defaultStyle.dropdownIcon}
    //                     color={theme.colors.primaryColor}
    //                     name="check-circle"
    //                     size={24}
    //                 />
    //             )}
    //         </View>
    //     );
    // }
    const verifiedIconBtnPress = () => {
        navigation.navigate('VerificationsScreen');
    }
    const userNameChangeHandler = (text) => {
        setuserName(text);
        setuserNameErr("");
        setdisableUpdateBtn(false);
    }
    const displayNameChangeHandler = (text) => {
        setdisplayName(text);
        setdisplayNameErr("");
        setdisableUpdateBtn(false);
    }
    const bioChangeHandler = (text) => {
        // console.log("text", text)
        var count = (text.match(/\n/g) || []).length;
        // console.log("count", count);
        // if (count < 10) { // Old Code Modified on 03 08 22 09 09

        let txtNewLineArr = text.split("\n");
        let lastNewLineWord = txtNewLineArr[txtNewLineArr.length - 1];
        let textStArr = lastNewLineWord.split(" ");
        let lastIndex = textStArr.length - 1;
        let lastWord = textStArr[lastIndex]
        if (lastWord == TAGGED_SYMBOL) {//.charAt(0)
            setshowTagPopup(true);
            settagName(lastWord);
        }
        else {
            settagName("");
            setshowTagPopup(false);
        }

        setbio(text);
        setbioErr("");
        setdisableUpdateBtn(false);
        // }


    }
    const coverChangeImgPress = () => {
        setmediaActionType("COVER");
        mediaActionRef.current?.show();
    }
    const profileChangeImgPress = () => {
        // openImageFolder();
        setmediaActionType("PROFILE");
        mediaActionRef.current?.show();

    }
    const mediaMenuActionViewPress = (clickID, obj) => {
        mediaActionRef.current?.hide();
        if (mediaActionType == "COVER") {
            if (clickID == "CAPTURE") {
                setdisableUpdateBtn(false);
                navigation.navigate("CameraScreen", {
                    cameraType: "CAMERA", cameFrom: "CAP_COVER"
                });
            }
            if (clickID == "GALLERY") {
                setdisableUpdateBtn(false);
                mediaActionRef.current?.hide();
                setTimeout(() => {
                    launchImageLibraryData();
                }, 500);

            }
            if (clickID == "REMOVE") {
                setdisableUpdateBtn(false);
                changeCaptureCoverImg(null);
                setprofileCoverImg(null);
                mediaActionRef.current?.hide();
            }
        }

        if (mediaActionType == "PROFILE") {

            if (clickID == "CAPTURE") {
                setdisableUpdateBtn(false);
                navigation.navigate("CameraScreen", {
                    cameraType: "CAMERA", cameFrom: "CAP_PROFILE"
                });
            }
            if (clickID == "GALLERY") {
                setdisableUpdateBtn(false);
                mediaActionRef.current?.hide();
                setTimeout(() => {
                    launchImageLibraryData();
                }, 500);


            }
            if (clickID == "REMOVE") {
                setdisableUpdateBtn(false);
                changeCaptureProfileImg(null);
                setprofileImg(null);
                mediaActionRef.current?.hide();
            }
        }
    }

    React.useEffect(
        () =>
            navigation.addListener('beforeRemove', (e) => {
                if (disableUpdateBtn) {
                    // If we don't have unsaved changes, then we don't need to do anything
                    return;
                }

                // Prevent default behavior of leaving the screen
                e.preventDefault();

                // Prompt the user before leaving the screen
                Alert.alert(
                    ErrorMessages.discardChangesTitle,
                    ErrorMessages.discardChangesMsg,
                    [
                        { text: "Don't leave", style: 'cancel', onPress: () => { } },
                        {
                            text: 'Discard',
                            style: 'destructive',
                            // If the user confirmed, then we dispatch the action we blocked earlier
                            // This will continue the action that had triggered the removal of the screen
                            onPress: () => navigation.dispatch(e.data.action),
                        },
                    ]
                );
            }),
        [navigation, disableUpdateBtn]
    );

    const launchImageLibraryData = async () => {
        const checkPermission = requestStoragePermission();
        checkPermission.then(res => {
            if (res) {
                openImageFolder();
            }
        })
    }
    const openImageFolder = () => {
        var options = {
            mediaType: 'photo', //to allow only photo to select ...no video
            // saveToPhotos: true,  //to store captured photo via camera to photos or else it will be stored in temp folders and will get deleted on temp clear
            includeBase64: false,
            selectionLimit: 1,
            // videoQuality: 'low',
            // durationLimit: appData._videoDurationLimit,
        };
        launchImageLibrary(options, (response) => {
            if (response.didCancel) {
                // console.log('User cancelled image picker');
            } else if (response.error) {
                // console.log('ImagePicker Error: ', response.error);
            } else if (response.customButton) {
                // console.log('User tapped custom button: ', response.customButton);
            } else {
                if (response.hasOwnProperty("assets")) {
                    if (response.assets[0].fileSize <= 15360000) { //15MB
                        let captureType = "IMAGE";
                        let responseUri = response.assets[0].uri;
                        setTimeout(() => {
                            corpGalleryImage(responseUri, mediaActionType);
                        }, 500);

                        // if (mediaActionType == "PROFILE") {
                        //     changeCaptureProfileImg({ uri: responseUri, captureType: captureType, imageData: response.assets[0] });

                        // }
                        // if (mediaActionType == "COVER") {
                        //     changeCaptureCoverImg({ uri: responseUri, captureType: captureType, imageData: response.assets[0] });

                        // }
                    } else {
                        setSnackbarMsg(ErrorMessages.editProfileMediaSizeErr);
                        setsnackBarType("FAILED");
                        setdisplaySnackbar(true);
                        setrefreshSnackBar(Math.random());
                    }
                }
                else {
                    setSnackbarMsg("Somethings went wrong!");
                    setsnackBarType("FAILED");
                    setdisplaySnackbar(true);
                    setrefreshSnackBar(Math.random());
                }
            }
        });
    }
    const categoryValueChange = (item) => {
        if (item.length < 5) {
            setcatValue(item);
        }
    }
    const [modalVisible, setModalVisible] = useState(false);
    const selectCatgoryBoxClick = (clickID, obj) => {
        setModalVisible(false);
        if (clickID == "DONE") {
            setdisableUpdateBtn(false)
            setcatValue([...[], ...obj.selectedItem])
        }
    }
    const corpGalleryImage = (imgUrl, type) => {
        let circularOverlay = false;
        let corpW = 1280;
        let corpH = 800;
        if (type == "PROFILE") {
            circularOverlay = true;
            corpW = 800;
            corpH = 800;
        }
        ImagePicker.openCropper({
            path: imgUrl,
            width: corpW,
            height: corpH,
            cropperCircleOverlay: circularOverlay,
        }).then(image => {
            setCaptureImageData(image.path);
        }).catch(err => {

        });
    }
    const setCaptureImageData = (imgUrl) => {
        let captureType = "IMAGE";
        if (mediaActionType == "PROFILE") {
            changeCaptureProfileImg({ uri: imgUrl, captureType: captureType, });

        }
        if (mediaActionType == "COVER") {
            changeCaptureCoverImg({ uri: imgUrl, captureType: captureType, });

        }
    }
    const tagPeopleSearchPress = (clickID, obj) => {
        setshowTagPopup(false);
        if (clickID == "SELECTED") {
            let formatTxt = _inputFormatTextForTag(bio, TAGGED_SYMBOL + obj.user_handle);
            setshowTagPopup(false);
            setbio(formatTxt + " ");
            descInputRef.current.focus()
        }
    }
    const onGenderChange = (item) => {
        setdisableUpdateBtn(false);
        setSelectedGender(item.value);
        setSelectedGenderErr("");
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar title="Edit Profile" showBackBtn={true} navigation={navigation} showTopButton={true}
                showBorderBottom={false}
                buttonComponent={<TouchableOpacity
                    onPress={() => updateBtnPress()}
                    disabled={disableUpdateBtn}
                ><EntutoTextView style={{ ...defaultStyle.postBtn, opacity: disableUpdateBtn ? 0.4 : 1 }}>UPDATE</EntutoTextView></TouchableOpacity>} />
            <Modal
                animationType="fade"
                visible={showTagPopup}
                onRequestClose={() => setshowTagPopup(false)}
                style={{ margin: 0, flex: 1 }}>
                <InputTagPeopleSearchComponent
                    tagPeopleSearchPress={tagPeopleSearchPress}
                    navigation={navigation}
                    preSearchStr={tagName}
                    captionTxt={bio} />
            </Modal>
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                enabled={Platform.OS == 'ios' ? true : true}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
                <ScrollView
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    {/* <KeyboardAvoidingView
                        enabled={Platform.OS == 'ios' ? true : false}
                        style={{ flex: 1 }}

                        behavior={Platform.OS === 'ios' ? 'position' : 'height'}> */}
                    <View>
                        {
                            errorMsg.length != 0 ?
                                // <View style={defaultStyle.errorBoxOutside}>
                                <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                                // </View>
                                : null
                        }

                        <View style={style.profileCoverBox}>
                            {/* <ImageBackground
                                style={style.coverImage}
                                source={hasImageUrlExist(profileCoverImg) ? { uri: profileCoverImg } : PlaceholderCoverImage}> */}
                            {/* <View style={style.coverImgChangeBox}>
                                    <TouchableOpacity onPress={() => coverChangeImgPress()}>
                                        <Image
                                            style={style.coverImgChangeImage}
                                            source={require('../assets/Images/icon/add_img_btn.png')}
                                            resizeMode={'cover'}
                                        />
                                    </TouchableOpacity>
                                </View> */}
                            <View style={style.profileImageBox}>
                                <View style={{ position: 'relative' }}>
                                    <ProgressiveImage
                                        resizeMode='cover'
                                        style={style.profileImage}
                                        source={hasImageUrlExist(profileImg) ? { uri: profileImg } : null}
                                        defaultImageSource={require("../assets/Images/full_user_image_place_holder.png")} />

                                    <View style={style.profileAddImgBox}>
                                        <TouchableOpacity onPress={() => profileChangeImgPress()}>
                                            <Image
                                                style={style.profileAddImg}
                                                source={require('../assets/Images/icon/add_img_btn.png')}
                                                resizeMode={'cover'}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </View>
                            {/* </ImageBackground> */}
                        </View>
                        <TouchableOpacity onPress={() => verifiedIconBtnPress()}
                            disabled={!verificationClickHappen}
                        >
                            <View style={style.verifiedBox}>
                                <Image
                                    style={{
                                        ...style.verifiedIcon,
                                        opacity: !verificationClickHappen ? isProfileVerified ? 1 : 0.4 : 0.4
                                    }}
                                    source={require('../assets/Images/icon/verifiedicon.png')}
                                    resizeMode={'cover'}

                                />
                                <EntutoTextView style={{
                                    ...style.verifiedTxt,
                                    borderBottomWidth: !verificationClickHappen ? isProfileVerified ? 0 : 0.4 : 0.5
                                }}>
                                    {profileVerifiedStatusTxt}
                                </EntutoTextView>
                            </View>
                        </TouchableOpacity>
                        <View style={defaultStyle.container}>
                            <View style={style.inputgap}>
                                <EntutoEditText
                                    labelTxt="User Name"
                                    placeholderTxt="User Name"
                                    value={userName}
                                    onChangeText={text => userNameChangeHandler(text)}
                                    showErrorField={userNameErr.length}
                                    errorMsg={userNameErr}
                                    showRightIcon={false}
                                    paddingEndValue={60}
                                    endComponent={
                                        <View style={{ marginEnd: 8 }}>
                                            <ImageBackground
                                                style={{ height: 20, width: 20, alignItems: 'center', justifyContent: 'center' }}
                                                source={require('../assets/Images/icon/round_circle.png')}>
                                                <Image
                                                    style={{ height: 5, width: 8.5 }}
                                                    source={require('../assets/Images/icon/tick_white.png')}
                                                    resizeMode={'cover'}
                                                />
                                            </ImageBackground>
                                        </View>
                                    }
                                />
                            </View>
                            <View style={style.inputgap}>
                                <EntutoEditText
                                    labelTxt="Display Name"
                                    placeholderTxt="Display Name"
                                    value={displayName}
                                    onChangeText={text => displayNameChangeHandler(text)}
                                    showErrorField={displayNameErr.length}
                                    errorMsg={displayNameErr}
                                />
                            </View>
                            <View style={style.inputgap}>
                                <EntutoEditText
                                    refValue={descInputRef}
                                    labelTxt="Bio"
                                    placeholderTxt="Bio"
                                    multiline
                                    numberOfLines={2}
                                    value={bio}
                                    maxHeight={150}
                                    onChangeText={text => bioChangeHandler(text)}
                                    showErrorField={bioErr.length}
                                    errorMsg={bioErr}
                                    maxLength={250}

                                />
                                <View style={defaultStyle.inputUnderCountBox}>
                                    <EntutoTextView style={defaultStyle.inputUnderCountBoxTxt}>{bio.length}/250</EntutoTextView>
                                </View>
                            </View>
                            <View style={style.inputgap}>
                                <EntutoDropdown label='Gender' placeholder='Select Gender'
                                    value={selectedGender}
                                    options={genderList}
                                    onOptionChange={onGenderChange} />
                            </View>

                            {/* <TouchableOpacity onPress={() => navigation.navigate("SelectDataFieldScreen", {
                            list: catValueList, selectedValue: catValue, title: "Select Category",
                            maxSelectedValue: 4, multiSelect: true,
                        })}>
                            <EntutoTextView>Go To category</EntutoTextView>
                        </TouchableOpacity> */}
                            <Modal
                                animationType="fade"
                                visible={modalVisible}
                                style={{ margin: 0, flex: 1 }}>
                                <SelectBoxComponent
                                    selectBoxClick={selectCatgoryBoxClick}
                                    list={catValueList}
                                    selectedValue={catValue}
                                    title="Select Category"
                                    maxSelectedValue={4}
                                    multiSelect={true}
                                />
                            </Modal>
                            {/* <Pressable onPress={() => setModalVisible(true)} style={{ marginTop: 15 }}>
                                <View style={{ ...defaultStyle.customDropdown }}>
                                    <View style={style.customMultiSelectBox}>
                                        {
                                            catValueList.map((obj, i) => {
                                                if (catValue.includes(obj.config_key)) {
                                                    return <View key={i} style={defaultStyle.dropdownSelectedStyle}>
                                                        <EntutoTextView style={defaultStyle.dropdownTextSelectedStyle}>{obj.display_value}</EntutoTextView>
                                                    </View>
                                                }
                                            })
                                        }
                                        {
                                            catValue.length == 0 ?
                                                <EntutoTextView style={defaultStyle.customMultiSelectSinglePH}>Select Categories</EntutoTextView>
                                                : null
                                        }
                                    </View>
                                    <TouchableOpacity >
                                        <MaterialIcons style={defaultStyle.dropdownIcon}
                                            color={theme.colors.dropdownInActiveColor}
                                            name="keyboard-arrow-right" size={theme.dimensions.dropdownRightIcon} />
                                    </TouchableOpacity>
                                </View>
                            </Pressable>
                            {
                                catValueErr.length != 0 ?
                                    <View>
                                        <EntutoTextView style={{ ...style.errorTxt, color: theme.colors.errorColor }} >
                                            {catValueErr}
                                        </EntutoTextView>
                                    </View>
                                    : null
                            } */}

                            {/* <View style={defaultStyle.dropdownContainer}>
                            <MultiSelect
                                style={[defaultStyle.dropdownMain, catFocused && {
                                    borderBottomColor: theme.colors.dropdownActiveColor,
                                    borderBottomWidth: theme.dimensions.dropdownActiveBorder
                                }]}
                                placeholderStyle={defaultStyle.dropdownPlaceholderStyle}
                                selectedTextStyle={defaultStyle.dropdownSelectedTextStyle}
                                inputSearchStyle={defaultStyle.dropdownInputSearchStyle}
                                data={catValueList}
                                search
                                maxHeight={300}
                                labelField="display_value"
                                valueField="config_key"
                                placeholder={catValue.length != 0 ?
                                    catValue.length + ` ${catValue.length == 1 ? "Category" : "Categories"} Selected`
                                    : 'Select Category'}
                                searchPlaceholder="Search..."
                                value={catValue}
                                onFocus={() => setcatFocused(true)}
                                onBlur={() => setcatFocused(false)}
                                onChange={item => {
                                    categoryValueChange(item)
                                    setcatFocused(false);
                                    setdisableUpdateBtn(false);
                                }}
                                renderItem={renderDropItem}
                                renderRightIcon={() => (
                                    <MaterialIcons style={defaultStyle.dropdownIcon}
                                        color={catFocused ? theme.colors.dropdownActiveColor : theme.colors.dropdownInActiveColor}
                                        name="keyboard-arrow-down" size={theme.dimensions.dropdownRightIcon} />
                                )}
                                selectedStyle={defaultStyle.dropdownSelectedStyle}
                                renderSelectedItem={(item, unSelect) => (
                                    <TouchableOpacity >
                                        <View style={defaultStyle.dropdownSelectedStyle}>
                                            <EntutoTextView style={defaultStyle.dropdownTextSelectedStyle}>{item.display_value}</EntutoTextView>
                                        </View>
                                    </TouchableOpacity>
                                )}

                            />
                        </View> */}

                            {/* <View style={style.boxWithSwitchBox}>
                                <EntutoTextView style={style.boxWithSwitchTxt}>Display Category</EntutoTextView>
                                <EntutoSwitch value={displayCat} onValueChange={() => onDisplayCatChange()} />
                            </View>
                            <View style={{ marginBottom: 4 }} />
                            <View style={style.boxWithSwitchBoxDivider} /> */}
                        </View>
                    </View>
                    {/* </KeyboardAvoidingView> */}
                </ScrollView>
            </KeyboardAvoidingView>
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />
            <ActionSheet ref={mediaActionRef}
                statusBarTranslucent

                bounciness={4}
                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <MediaMenuActionView mediaMenuActionViewPress={mediaMenuActionViewPress} 
                showChooseMedia={true} showCaptureImage={false}/>
            </ActionSheet >
        </>
    )
}

export default EditProfileScreen;

const styles = theme => StyleSheet.create({
    coverImage: {
        height: 172,
        position: 'relative',
    },
    profileCoverBox: {
        position: 'relative',
        height: 114,//changes from 172 to 114
    },
    profileImage: {
        width: 112,
        height: 112,
        borderWidth: 2,
        borderColor: '#ffffff',
        borderRadius: 112,

    },
    profileAddImgBox: {
        position: 'absolute',
        top: 40,
        right: -15,
        height: 35,
        width: 35,
        borderRadius: 35,
        backgroundColor: '#FFFFFF',
        alignItems: 'center',
        justifyContent: 'center',
    },
    profileAddImg: {
        width: 20,
        height: 20,
    },
    profileImageBox: {
        position: 'absolute',
        top: 12,//Change from bottom:-22
        left: 0,
        right: 0,
        alignItems: 'center',
    },
    coverImgChangeBox: {
        position: 'absolute',
        top: 15,
        right: 15,
        height: 35,
        width: 35,
        borderRadius: 35,
        backgroundColor: '#FFFFFF',
        alignItems: 'center',
        justifyContent: 'center',
    },
    coverImgChangeImage: {
        width: 20,
        height: 20,
    },
    verifiedBox: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 30,
        marginBottom: 20
    },
    verifiedIcon: {
        width: 20,
        height: 19
    },
    verifiedTxt: {
        color: theme.colors.mainHeadingColor,
        marginLeft: 8,
    },
    boxWithSwitchBox: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 15,
        marginLeft: 8,
        marginTop: 20

    },
    boxWithSwitchTxt: {
        flex: 1,
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(17),
        fontWeight: '600'
    },
    boxWithSwitchBoxDivider: {
        flex: 1,
        borderWidth: 0.5,
        borderColor: theme.colors.primaryTextColor,
        opacity: 0.2
    },
    customDropdown: {
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomColor: '#00000050',
        borderBottomWidth: 0.5,
        paddingBottom: 8,
        paddingTop: 15,
    },
    customMultiSelectBox: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        flex: 4,
    },
    errorTxt: {
        fontSize: theme.calculateFontSize(12),
        marginTop: 6,
    },
    inputgap: {
        marginBottom: 20,
    },
})
