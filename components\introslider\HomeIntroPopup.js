import { Image, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import Colors from '../../constants/Colors';
import IntroImage from '../../assets/Images/intro_data.gif'
import useSThemedStyles from '../../theme/useSThemedStyles';

const HomeIntroPopup = ({ navigation, homeIntroPopup }) => {
  const style = useSThemedStyles(styles);
  const popupBtnPress = () => {
    navigation.navigate('AppIntroSliderScreen')
  }
  const closebtnPress = () => {
    homeIntroPopup("CLOSE", {})
  }
  return (
    <>
      <View style={style.homeIntroBox}>

        <View style={style.popupBox}>
          <TouchableOpacity
            style={{}}
            onPress={() => popupBtnPress()}>
            <Image style={style.popupImage}
              resizeMode="cover"
              source={IntroImage} />
          </TouchableOpacity>
          <View style={style.crossIconBox}>
            <TouchableOpacity style={style.crossIconTouch} onPress={() => closebtnPress()}>
              <Image style={style.crossIcon}
                resizeMode="contain"
                source={require('../../assets/Images/icon/close_icon.png')} />
            </TouchableOpacity>
          </View>

        </View>


      </View>
    </>
  )
}

export default HomeIntroPopup

const styles = theme => StyleSheet.create({
  homeIntroBox: {
    position: 'absolute',
    bottom: 72,
    left: 15,
    zIndex: 100090,

  },
  popupBox: {
    height: 220,
    width: 160,
    borderRadius: 15,
    backgroundColor: theme.colors.backgroundColor,
    position: 'relative',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.50,
    shadowRadius: 1.41,
    elevation: 2,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',

  },
  popupImage: {
    height: 220,
    width: 160,
    overflow: 'hidden',
    borderRadius: 15,
    padding: 1
  },
  crossIconBox: {
    position: 'absolute',
    right: 0,
    top: 0,
    // backgroundColor: 'red',
    zIndex: 999,
  },
  crossIconTouch: {
    padding: 10,
  },
  crossIcon: {
    width: 16,
    height: 16,
    tintColor: theme.colors.introPopupIconTint,
  },

})