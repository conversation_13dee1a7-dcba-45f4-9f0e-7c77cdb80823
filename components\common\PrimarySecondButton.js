import React from 'react'
import { Platform, StyleSheet, TouchableOpacity } from 'react-native'
import { Button } from 'react-native-paper'
import Dimensions from '../../constants/Dimensions'
import useSThemedStyles from '../../theme/useSThemedStyles'
import useSTheme from '../../theme/useSTheme'
import EntutoTextView from './EntutoTextView'

const PrimarySecondButton = ({ buttonStyle = {}, label = "",
    labelStyle = {}, onPress, ...props }) => {
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    return <TouchableOpacity style={{ ...style.button, ...buttonStyle }} onPress={onPress}>
        <EntutoTextView style={{ ...style.labelStyle, ...labelStyle }}>{label}</EntutoTextView>
    </TouchableOpacity>
}

export default PrimarySecondButton
const styles = theme => StyleSheet.create({
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderColor: theme.colors.buttonBorderColor,
        borderWidth: 1,
        backgroundColor: theme.colors.primaryColor,
    },
    labelStyle: {
        fontSize: theme.calculateFontSize(theme.dimensions.primaryBtnText),
        padding: 6,
        color: "#FFFFFF"
    },

})
