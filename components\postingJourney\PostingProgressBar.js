import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import GALLERY_ICON from '../../assets/Images/icon/gallery_icon.png'
import CAPTION_ICON from '../../assets/Images/icon/caption_icon.png'
import MONETIZATION_ICON from '../../assets/Images/icon/monetization_icon.png'
import TAGS_ICON from '../../assets/Images/icon/tags_icon.png'
import TickIcon from '../../assets/Images/icon/tick_white.png'
import LinearGradient from 'react-native-linear-gradient';

const PostingProgressBar = ({ progressStep = 0, barBtnPress = null, clickable = true }) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const [progressLineWidth, setProgressLineWidth] = useState(0);
  useEffect(() => {
    let widthV = (progressStep / 3) * 100;
    if (progressStep > 3) {
      widthV = 100;
    }
    setProgressLineWidth(widthV);
  }, [progressStep])

  return (
    <View style={style.container}>
      <View style={style.linearGradientBox}>
        <LinearGradient
          colors={['#FC6767', '#ED058A', '#EC008C']}
          locations={[0, 0.8, 0.9]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{ ...style.linearGradient, width: `${progressLineWidth}%` }} />
      </View>

      <ProgressBarItem image={GALLERY_ICON} clickable={clickable} isActive={progressStep > 0} value={"IMAGE"} barBtnPress={barBtnPress} />
      <ProgressBarItem image={CAPTION_ICON} clickable={clickable} isActive={progressStep > 1} value={"CAPTION"} barBtnPress={barBtnPress} />
      <ProgressBarItem image={MONETIZATION_ICON} clickable={clickable} isActive={progressStep > 2} value={"PRICE"} barBtnPress={barBtnPress} />
      <ProgressBarItem image={TAGS_ICON} clickable={clickable} isActive={progressStep > 3} value={"TAGS"} barBtnPress={barBtnPress} />

    </View>
  )
}

export default PostingProgressBar
const ProgressBarItem = ({ image = null, isActive = false, barBtnPress, value, clickable = true }) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  return (

    <View style={{
      ...style.progressBarItem,
      borderColor: isActive ? theme.colors.primaryColor : theme.colors.postProgressBarBackground,
      backgroundColor: theme.colors.postProgressBarBackground,
      zIndex: 2
    }}>
      <TouchableOpacity disabled={!clickable} onPress={() => barBtnPress(value)} style={{ zIndex: 2, }}>
        <Image source={image} style={style.progressBarItemIcon} />
      </TouchableOpacity>
      {
        isActive ?
          <View style={style.tickIconBox}>
            <Image source={TickIcon} style={style.tickIcon} />
          </View>
          : null
      }


    </View>
  );
}

const styles = theme => StyleSheet.create({
  container: {
    height: 45,
    width: '100%',
    position: 'relative',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  progressBarItem: {
    width: 43,
    height: 43,
    borderRadius: 21.5,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressBarItemIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
    tintColor: theme.colors.progressBarItemIcon
  },
  linearGradientBox: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    zIndex: 1,
  },
  linearGradient: {

    height: 4,
    borderRadius: 2,
    width: '100%'
  },
  tickIconBox: {
    position: 'absolute',
    top: 0,
    right: -1,
    height: 11,
    width: 11,
    borderRadius: 11,
    borderColor: theme.colors.primaryColor,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primaryColor,

  },
  tickIcon: {
    width: 7,
    height: 7,
    resizeMode: 'contain',
    tintColor: '#FFF'
  }

})