import React from 'react';
import {StyleSheet} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import useSTheme from '../../theme/useSTheme';

const LoginSignUpLinearGrad = () => {
  const theme = useSTheme();
  return (
    <LinearGradient
      colors={[
        theme.colors.loginSignupGradient1,
        theme.colors.loginSignupGradient2,
        theme.colors.loginSignupGradient3,
      ]}
      locations={[0.05, 0.3, 1]}
      start={{x: 0.5, y: 0.1}}
      end={{x: 0.5, y: 1.0}}
      style={styles.linearGrad}
    />
  );
};

export default LoginSignUpLinearGrad;
const styles = StyleSheet.create({
  linearGrad: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
});
