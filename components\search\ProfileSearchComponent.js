import { FlatList, Keyboard, Platform, RefreshControl, StyleSheet, Text, View } from 'react-native'
import React, { useCallback, useEffect, useState } from 'react'
import useSTheme from '../../theme/useSTheme';
import { checkValueLength } from '../../utils/Utils';
import SearchProfileRow from './SearchProfileRow';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { ActivityIndicator } from 'react-native-paper';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';

const ProfileSearchComponent = ({
    searchText = "",
    navigation, itemCallback = null
}) => {
    const theme = useSTheme();
    const [showLoading, setShowLoading] = useState(false);
    const [bottomLoading, setBottomLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState("");
    const [errorMsgKey, setErrorMsgKey] = useState(Math.random());
    const [isNoDataFound, setIsNoDataFound] = useState(false);
    const RowsPerPage = 10;//Don't change this value this is fixed
    const [startRecord, setStartRecord] = useState(0);
    const [selectedQuery, setSelectedQuery] = useState("");
    const [progressLoading, setProgressLoading] = useState(false)
    useEffect(() => {
        const timeOutVal = setTimeout(() => {
            if (!showLoading && !bottomLoading) {
                if (searchText.length > 1) {
                    setShowLoading(true);
                    setErrorMsg("");
                    setProfileData([])
                    getProfileSearchService(searchText, 0)
                }
                else {
                    if (searchText.length == 0) {
                        setShowLoading(true);
                        setErrorMsg("");
                        setProfileData([])
                        getProfileSearchService("", 0)
                    }
                }
            }
        }, 500);
        return () => {
            clearTimeout(timeOutVal)
        }
    }, [searchText]);
    const [profileData, setProfileData] = useState([]);

    const ItemSeparatorComponent = () => {
        return <View style={{ height: 1 }} />
    }
    const renderItem = useCallback(
        ({ item }) => {
            return <SearchProfileRow data={item} navigation={navigation} />
        },
        [profileData]
    );
    function getProfileSearchService(query, stRecord) {
        setStartRecord(stRecord);
        setSelectedQuery(query)
        let hashMap = {
            _action_code: "11:DO_SEARCH",
            _start_row: stRecord,
            _rows_page: RowsPerPage,
        }
        if (checkValueLength(query)) {
            hashMap.search_str = query;
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setProgressLoading(false);
            setShowLoading(false);
            setBottomLoading(false);
            let list = [...profileData];
            if (parseInt(stRecord) == 0) {
                list = data.data;
            }
            else {
                list = list.concat(data.data);
            }
            if (parseInt(stRecord) == 0 && checkValueLength(query)) {
                if (itemCallback) {
                    itemCallback("STORE_QUERY", { query: query })
                }
            }
            if (itemCallback) {
                itemCallback("STORE_BACKUP_PROFILE", { query: query, data: list });
            }
            setProfileData(list);
            setErrorMsg("");
            setErrorMsgKey(Math.random())
            if (data.data.length < 9) {
                setIsNoDataFound(true);
            }
            else {
                setIsNoDataFound(false);
            }

        }, (errorCode, errorMessage, data) => { // failure method
            setProgressLoading(false);
            if (_RedirectionErrorList.includes(errorCode)) {
                setShowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                setBottomLoading(false);
                setIsNoDataFound(true);
                if (parseInt(stRecord) == 0) {

                    setProfileData([]);

                    setErrorMsg(errorMessage);
                    setErrorMsgKey(Math.random());
                }
            }
        });
    }
    const listFooterComponent = useCallback(
        () => {
            return (
                <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 15 }}>
                    <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={'large'} />
                </View>
            );
        },
        [],
    );
    const onEndProfileReached = () => {
        if (!bottomLoading && !isNoDataFound) {
            let startRec = startRecord + RowsPerPage;
            setBottomLoading(true);
            getProfileSearchService(selectedQuery, startRec)
        }
    }
    const handleRefresh = () => {
        setProgressLoading(true);
        getProfileSearchService(selectedQuery, 0);
    }
    const [keyboardHeight, setKeyboardHeight] = useState(80)
    const handleShowKeyboard = (e) => {
        const height = e.endCoordinates.height;
        setKeyboardHeight(height);
    };

    const handleHideKeyboard = () => {
        setKeyboardHeight(80);
    };

    React.useEffect(() => {
        if (Platform.OS == 'android') {
            return;
        }
        const keyboardDidShowSubscribtion = Keyboard.addListener(
            'keyboardDidShow',
            handleShowKeyboard,
        );
        const keyboardWillHideSubscribtion = Keyboard.addListener(
            'keyboardWillHide',
            handleHideKeyboard,
        );
        return () => {
            Keyboard.removeSubscription?.(keyboardDidShowSubscribtion);
            Keyboard.removeSubscription?.(keyboardWillHideSubscribtion);
        };
    }, []);
    return (
        <View style={{ paddingHorizontal: 0, marginTop: 10 }}>
            {
                showLoading ?
                    <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                        <ActivityIndicator animating={true} />
                    </View>
                    : null
            }
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsgKey} />
                    : null
            }

            <FlatList
                onScrollBeginDrag={() => Keyboard.dismiss()}
                keyboardShouldPersistTaps={'handled'}
                contentContainerStyle={{ paddingBottom: keyboardHeight, backgroundColor: theme.colors.backgroundColor }}
                removeClippedSubviews={true}
                data={profileData}
                renderItem={renderItem}
                keyExtractor={(item, index) => `${item.profile_seq}_${index}`}
                // keyExtractor={keyExtractor}
                ItemSeparatorComponent={ItemSeparatorComponent}
                onEndReached={onEndProfileReached}
                initialNumToRender={10}
                disableVirtualization
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                ListFooterComponent={bottomLoading && listFooterComponent}
                refreshControl={
                    <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                }
                getItemLayout={(data, index) => {
                    return { index, length: 150, offset: 150 * index }
                }}
            />
        </View>
    )
}

export default ProfileSearchComponent

const styles = StyleSheet.create({})