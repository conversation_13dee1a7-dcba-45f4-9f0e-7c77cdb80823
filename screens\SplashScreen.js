import React, {useContext, useEffect, useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  Button,
  Image,
  Alert,
  Platform,
} from 'react-native';
import Dimensions from '../constants/Dimensions';
// import AppLogo from '../assets/Images/Spalash_logo.gif'
import AppLogo from '../assets/Images/Logo.png';
import {
  _getAppThemeColor,
  _getAppThemeType,
  _getUserCredential,
} from '../utils/AuthLogin';
import appData from '../data/Data';
import {AppStateContext} from '..';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  NOTI_NEW_POST_SUB,
  NOTI_NEW_PROFILE_FOLLOW,
  NOTI_NEW_PROFILE_SUB,
  NOTI_POST_CAPTION,
  NOTI_POST_COMMENT,
  NOTI_POST_LIKE,
  NOTI_POST_TAG,
  NOTI_PROFILE_BIO,
  NOTI_STORY_CAPTION,
} from '../constants/Constants';
import ServerConnector from '../utils/ServerConnector';
import dynamicLinks from '@react-native-firebase/dynamic-links';
import {ActivityIndicator} from 'react-native-paper';
// import ScreenGuardModule from 'react-native-screenguard';
import {APP_IN_TEST_MODE} from '../utils/Appconfig';
import {checkValueLength} from '../utils/Utils';
import useSTheme from '../theme/useSTheme';

const SplashScreen = ({navigation}) => {
  const [isDataAvl, setisDataAvl] = useState(false);
  const {changeUserDetails, fullUserDetails, changeUserProfileImage} =
    useContext(AppStateContext);
  const [isAlreadyRedirected, setisAlreadyRedirected] = useState(false);
  const [isLoadedGif, setisLoadedGif] = useState(false);
  const [isValidAppVersion, setisValidAppVersion] = useState(true);
  const appTheme = useSTheme();
  React.useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      setisAlreadyRedirected(false);
    });
    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    _getUserCredential(data => {
      if (data != null) {
        setisDataAvl(true);
        changeUserDetails(data);
        appData._userDetails = data;
      }
    });
    _getAppThemeColor(data => {
      if (checkValueLength(data)) {
        appTheme.changeThemeColor(data);
      } else {
        appTheme.changeThemeColor(appData._defaultThemeColor);
      }
    });
    _getAppThemeType(data => {
      if (checkValueLength(data)) {
        appTheme.changeAppTheme(data);
      } else {
        appTheme.changeAppTheme(appData._defaultAppTheme);
      }
    });
  }, []);
  useEffect(() => {
    requestUserPermission();
  }, []);

  useEffect(() => {
    messaging().onNotificationOpenedApp(remoteMessage => {
      if (restrictionApp()) {
        redirectTospecificScreen(remoteMessage.data);
      }
      // console.log(
      //     'Notification caused app to open from background state 1:',
      //     remoteMessage,
      // );
    });

    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          if (restrictionApp()) {
            redirectTospecificScreen(remoteMessage.data);
          }
          // console.log(
          //     'Notification caused app to open from quit state2:',
          //     remoteMessage.notification,
          // ); // e.g. "Settings"
        }
      });
  }, []);

  //FCM Integration
  async function requestUserPermission() {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      // console.log('Authorization status:', authStatus);
      getFcmTocken();
    }
  }
  const getFcmTocken = async () => {
    let fcmToken = await AsyncStorage.getItem('_FCM_TOKEN_A');
    // console.log(fcmToken, "the old token");
    if (!fcmToken) {
      try {
        const fcmToken = await messaging().getToken();
        // console.log(fcmToken, "the New token");
        AsyncStorage.setItem('_FCM_TOKEN_A', fcmToken);
      } catch (error) {
        // console.log(error, "error raised in fcm token")
      }
    }
  };
  const redirectTospecificScreen = data => {
    if (data.hasOwnProperty('type')) {
      setisAlreadyRedirected(true);
      if (data.type == NOTI_NEW_PROFILE_SUB) {
        let profile_seq = data.profile_seq;
        navigation.navigate('OthersProfileScreen', {
          profileSeq: profile_seq,
        });
      } else if (data.type == NOTI_NEW_POST_SUB) {
        let post_seq = data.post_seq;
        navigation.navigate('SinglePostScreen', {
          postSeq: post_seq,
        });
      } else if (data.type == NOTI_NEW_PROFILE_FOLLOW) {
        let profile_seq = data.profile_seq;
        navigation.navigate('OthersProfileScreen', {
          profileSeq: profile_seq,
        });
      } else if (data.type == NOTI_POST_LIKE) {
        let post_seq = data.post_seq;
        navigation.navigate('SinglePostScreen', {
          postSeq: post_seq,
        });
      } else if (data.type == NOTI_POST_COMMENT) {
        let post_seq = data.post_seq;
        navigation.navigate('SinglePostScreen', {
          postSeq: post_seq,
        });
      } else if (data.type == NOTI_POST_TAG) {
        let post_seq = data.post_seq;
        navigation.navigate('SinglePostScreen', {
          postSeq: post_seq,
        });
      } else if (data.type == NOTI_POST_CAPTION) {
        let post_seq = data.post_seq;
        navigation.navigate('SinglePostScreen', {
          postSeq: post_seq,
        });
      } else if (data.type == NOTI_PROFILE_BIO) {
        let profile_seq = data.post_seq;
        navigation.navigate('OthersProfileScreen', {
          profileSeq: profile_seq,
        });
      } else if (data.type == NOTI_STORY_CAPTION) {
        let post_seq = data.post_seq;
        navigation.navigate('SingleStoryStatusScreen', {
          storySeq: post_seq,
        });
      } else {
        goToHomeScreen();
      }
    }
  };

  const splashToRedirect = () => {
    console.log(isDataAvl, restrictionApp());
    if (isDataAvl) {
      if (restrictionApp()) {
        getUserProfileService();
      }
    } else {
      if (restrictionApp()) {
        navigation.replace('LoginScreen', {
          ErrorMsg: '',
        }); //LoginScreen
      }
    }
  };
  const onGifLoaded = () => {
    setisLoadedGif(true);
    setTimeout(() => {
      if (!isAlreadyRedirected) {
        splashToRedirect();
      }
    }, 500);
  };
  function getUserProfileService() {
    let profileSeq = -1;
    if (fullUserDetails.hasOwnProperty('_profile_seq')) {
      profileSeq = fullUserDetails._profile_seq;
    }
    let hashMap = {
      _action_code: '11:GET_USER_PROFILE',
      req_profile_seq: profileSeq,
    };

    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method

        let display_name = '-';
        if (data.data[0].display_name !== null) {
          display_name = data.data[0].display_name;
        }
        let hasBankDetails = 'NO';
        if (data.data[0].bank_account !== null) {
          if (data.data[0].bank_account.length !== 0) {
            hasBankDetails = 'YES';
          }
        }
        let hasStateCity = 'NO';
        if (data.data[0].state !== null) {
          if (data.data[0].state.length !== 0) {
            hasStateCity = 'YES';
          }
        }
        let userDeatails = {
          _user_handle: data.data[0].user_handle,
          _user_account_type: data.data[0].type,
          _user_display_name: display_name,
          _has_bank_details: hasBankDetails,
          _is_profile_verified: data.data[0].is_verified,
          _has_state_city: hasStateCity,
          _profile_picture: data.data[0].profile_picture,
          _cover_image: data.data[0].cover_image,
        };
        let uiColor = 'COLOR_1';
        if (data.data[0].hasOwnProperty('ui_colour')) {
          if (checkValueLength(data.data[0].ui_colour)) {
            uiColor = data.data[0].ui_colour;
          }
        }
        appTheme.changeThemeColor(uiColor);
        // setShowLoading(false);
        changeUserProfileImage(data.data[0].profile_picture);
        changeUserDetails(userDeatails);
        const routes = navigation.getState()?.routes;
        if (routes != undefined) {
          if (routes.length == 1) {
            gotoPerticulerScreenD();
          } else {
            goToHomeScreen();
          }
        } else {
          goToHomeScreen();
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
        navigation.replace('LoginScreen', {
          ErrorMsg: '',
        }); //LoginScreen
      },
    );
  }
  const gotoPerticulerScreenD = () => {
    dynamicLinks()
      .getInitialLink()
      .then(link => {
        setTimeout(() => {
          if (restrictionApp()) {
            dynamicLinkRedirection(link);
          }
        }, 100);
      });
  };
  const dynamicLinkRedirection = dynamicUrl => {
    if (dynamicUrl == null) {
      goToHomeScreen();
    } else {
      if (dynamicUrl.hasOwnProperty('url')) {
        let rUrl = dynamicUrl.url;
        let params = rUrl.substring(rUrl.indexOf('?') + 1);
        if (rUrl.startsWith('https://sotrue.co.in/post')) {
          navigation.replace('SinglePostScreen', {
            postSeq: params,
          });
        } else if (rUrl.startsWith('https://sotrue.co.in/profile')) {
          navigation.replace('OthersProfileScreen', {
            profileSeq: params,
          });
        } else {
          goToHomeScreen();
        }
      } else {
        goToHomeScreen();
      }
    }
  };
  function goToHomeScreen() {
    //Comment out on 14/11/2024
    // navigation.replace('HomeScreen');
    navigation.replace('VideoContentScreen', {
      postSeq: -1,
      postProfileSeq: -1,
      cameFrom: 'SPLASH_SCREEN',
    });
  }
  function restrictionApp() {
    let validApp = true;
    if (Platform.OS == 'ios') {
      const majorVersionIOS = parseInt(Platform.Version, 10);
      if (majorVersionIOS < 13) {
        if (!APP_IN_TEST_MODE) {
          validApp = false;
        }
      }
    }
    setisValidAppVersion(validApp);
    return validApp;
  }

  useEffect(() => {
    // console.log("Executed Screen gaurd")
    // ScreenGuardModule.registerWithBlurView(
    //     35,
    //     (_) => {
    // });
    // setTimeout(() => {
    //     ScreenGuardModule.register(
    //         //insert any hex color you want here, default black if null or empty
    //        '#000000',
    //    _ => {
    //  // .....do anything you want after the screenshot
    //       },)
    // }, 1000);
  }, []);
  return (
    <View style={styles.centerLayout}>
      <Image
        style={{...styles.logo, height: Platform.OS == 'ios' ? 1 : 200}}
        source={AppLogo}
        onLoadEnd={() => {
          onGifLoaded();
        }}
        resizeMode="contain"
      />
      {Platform.OS == 'ios' ? (
        <View>
          {!isValidAppVersion ? (
            <View style={{paddingHorizontal: 40}}>
              {/* <Text style={{textAlign:'center',fontSize:22,marginBottom:10}}>Sorry!</Text> */}
              <Text
                allowFontScaling={false}
                style={{textAlign: 'center', fontSize: 18}}>
                SoTrue is not compatible with your current OS. Please make sure
                that you use iOS version 13 or above
              </Text>
            </View>
          ) : (
            <ActivityIndicator />
          )}
        </View>
      ) : null}
    </View>
  );
};

export default SplashScreen;

const styles = StyleSheet.create({
  centerLayout: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: Dimensions.splashLogoWidthSize - 50,
    height: 200,
  },
});
