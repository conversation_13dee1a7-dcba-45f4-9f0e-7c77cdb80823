import React, {useContext, useEffect, useState} from 'react';
import {
  StatusBar,
  StyleSheet,
  Linking,
  Alert,
  Platform,
  AppState,
  Appearance,
} from 'react-native';
import {
  DefaultTheme,
  useNavigation,
  CommonActions,
} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import SplashScreen from '../screens/SplashScreen';
import LoginScreen from '../screens/LoginScreen';
import Colors from '../constants/Colors';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import SignupScreen from '../screens/SignupScreen';
import {AppStateContext, PageRefreshContext} from '..';
import HomeDrawerNavigator from './HomeDrawerNavigator';
import ForgotVerifyEmailScreen from '../screens/ForgotVerifyEmailScreen';
import ForgotResetPassScreen from '../screens/ForgotResetPassScreen';
import ForgotResetPassSuccessScreen from '../screens/ForgotResetPassSuccessScreen';
import SignupOtpScreen from '../screens/SignupOtpScreen';
import StoryStatusScreen from '../screens/StoryStatusScreen';
import VideoDisplayScreen from '../screens/VideoDisplayScreen';
import OthersProfileScreen from '../screens/OthersProfileScreen';
import AccountSettingsScreen from '../screens/AccountSettingsScreen';
import FollowingScreen from '../screens/FollowingScreen';
import CommentScreen from '../screens/CommentScreen';
import CameraScreen from '../screens/CameraScreen';
import CustomStatusBar from '../components/common/CustomStatusBar';
import {BottomNavigator} from './BottomNavigator';
import SearchResultScreen from '../screens/SearchResultScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
import AccountInfoScreen from '../screens/AccountInfoScreen';
import BlockedAccountScreen from '../screens/BlockedAccountScreen';
import PrivacySafetyScreen from '../screens/PrivacySafetyScreen';
import CaptureMediaDisplayScreen from '../screens/CaptureMediaDisplayScreen';
import AddStoryScreen from '../screens/AddStoryScreen';
import SubscriptionScreen from '../screens/SubscriptionScreen';
import MySubscribersScreen from '../screens/MySubscribersScreen';
import VerificationsScreen from '../screens/VerificationsScreen';
import SuccessfullVerificationScreen from '../screens/SuccessfullVerificationScreen';
import ReferAndEarnScreen from '../screens/ReferAndEarnScreen';
import ReferralHistoryScreen from '../screens/ReferralHistoryScreen';
import MySubscriptionsScreen from '../screens/MySubscriptionsScreen';
import AddPostScreen from '../screens/AddPostScreen';
import RestrictedAccountScreen from '../screens/RestrictedAccountScreen';
import FollowersScreen from '../screens/FollowersScreen';
import BookmarkListScreen from '../screens/BookmarkListScreen';
import NotificationSettingsScreen from '../screens/NotificationSettingsScreen';
import SinglePostScreen from '../screens/SinglePostScreen';
import SelectDataFieldScreen from '../screens/SelectDataFieldScreen';
import ImageDisplayScreen from '../screens/ImageDisplayScreen';
import AboutSettingScreen from '../screens/AboutSettingScreen';
import TermsAndUseScreen from '../screens/TermsAndUseScreen';
import FaqDisScreen from '../screens/FaqDisScreen';
import PrivacyPolicyDisScreen from '../screens/PrivacyPolicyDisScreen';
import EditPostScreen from '../screens/EditPostScreen';
import CategoryWiseUserScreen from '../screens/CategoryWiseUserScreen';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SettingScreen from '../screens/SettingScreen';
import {Notifier, Easing} from 'react-native-notifier';
import SignupGmailScreen from '../screens/SignupGmailScreen';
import {
  NOTI_NEW_POST_SUB,
  NOTI_NEW_PROFILE_FOLLOW,
  NOTI_NEW_PROFILE_SUB,
  NOTI_POST_CAPTION,
  NOTI_POST_COMMENT,
  NOTI_POST_LIKE,
  NOTI_POST_TAG,
  NOTI_PROFILE_BIO,
  NOTI_STORY_CAPTION,
} from '../constants/Constants';
import TagPeopleScreen from '../screens/TagPeopleScreen';
import TagPeopleListScreen from '../screens/TagPeopleListScreen';
import SingleStoryStatusScreen from '../screens/SingleStoryStatusScreen';
import ServerConnector from '../utils/ServerConnector';
import AppIntroSliderScreen from '../screens/AppIntroSliderScreen';
import dynamicLinks from '@react-native-firebase/dynamic-links';
import SignupAppleScreen from '../screens/SignupAppleScreen';
import DisableAccountScreen from '../screens/DisableAccountScreen';
import SignInNScreen from '../screens/SignInNScreen';
import SignupNScreen from '../screens/SignupNScreen';
import SinglePostNScreen from '../screens/SinglePostNScreen';
import TermsAndConditionScreen from '../screens/TermsAndConditionScreen';
import FlashMessage from 'react-native-flash-message';
import VideoContentScreen from '../screens/VideoContentScreen';
import UnlockSinglePostScreen from '../screens/UnlockSinglePostScreen';
import AddPostSuccessScreen from '../screens/AddPostSuccessScreen';
import NotificationScreen from '../screens/NotificationScreen';
import QuickSignInScreen from '../screens/2.0/QuickSignInScreen';
import QuickSignInOTPScreen from '../screens/2.0/QuickSignInOTPScreen';
import QuickSignUpScreen from '../screens/2.0/QuickSignUpScreen';
import QuickSignUpPersonalizeScreen from '../screens/2.0/QuickSignUpPersonalizeScreen';
import PlaylistScreen from '../screens/PlaylistScreen';
import PostingJourneyScreen from '../screens/2.0/PostingJourneyScreen';
import LoginOldFlowScreen from '../screens/LoginOldFlowScreen';
import ViewTransactionsScreen from '../screens/ViewTransactionsScreen';
import SearchResultMainScreen from '../screens/SearchResultMainScreen';
import UserPersonalizeScreen from '../screens/2.0/UserPersonalizeScreen';
import PlaylistEpisodeScreen from '../screens/PlaylistEpisodeScreen';
import ShareYourProfileScreen from '../screens/ShareYourProfileScreen';
import PlaylistShowScreen from '../screens/PlaylistShowScreen';
import PlaylistSeasonScreen from '../screens/PlaylistSeasonScreen';
import PlaylistSeasonEpisodeScreen from '../screens/PlaylistSeasonEpisodeScreen';
import TempHomeScreen from '../screens/2.0/TempHomeScreen';
import NotificationGuideSelectionScreen from '../screens/2.0/NotificationGuideSelectionScreen';
// import ScreenGuardModule from 'react-native-screenguard';

const Stack = createNativeStackNavigator();
const useMount = func => useEffect(() => func(), []);

const AppNavigator = () => {
  const {
    changeNewNotficationCame,
    changeNewNotficationTypeList,
    newNotificationTypeList,
  } = useContext(AppStateContext);
  const {changeNotificationRefresh} = useContext(PageRefreshContext);
  const MyTheme = {
    ...DefaultTheme,
    colors: {
      ...DefaultTheme.colors,
      background: Colors.backgroundColor,
    },
  };
  useMount(() => {
    const getUrlAsync = async () => {
      // Get the deep link used to open the app
      const initialUrl = await Linking.getInitialURL();

      // The setTimeout is just for testing purpose
      setTimeout(() => {
        // console.log("initialUrl2", initialUrl)
      }, 1000);
    };

    getUrlAsync();
  });
  const navigation = useNavigation();
  useEffect(() => {
    requestUserPermission();
    getNotificationService();
  }, []);

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      // Alert.alert('A new FCM message arrived!', JSON.stringify(remoteMessage));

      if (remoteMessage.hasOwnProperty('data')) {
        let resData = remoteMessage.data;
        if (resData.hasOwnProperty('type')) {
          changeNewNotficationCame(true);
          // console.log("remoteMessage List", newNotificationTypeList)
          let dataType = newNotificationTypeList;
          dataType.push(resData.type);
          changeNewNotficationTypeList(dataType);
        }
      }
      Notifier.showNotification({
        title: remoteMessage.notification.title,
        description: remoteMessage.notification.body,
        duration: 5000,
        showAnimationDuration: 800,
        componentProps: {
          titleStyle: {color: '#000'},
        },
        showEasing: Easing.bounce,
        // onHidden: () => console.log('Hidden'),
        onPress: () => redirectTospecificScreen(remoteMessage.data),
        hideOnPress: true,
        swipeEnabled: true,
      });
      // redirectTospecificScreen(remoteMessage.data)
    });

    return unsubscribe;
  }, [newNotificationTypeList]);

  //FCM Integration
  async function requestUserPermission() {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      // console.log('Authorization status:', authStatus);
      getFcmTocken();
    }
  }
  const getFcmTocken = async () => {
    let fcmToken = await AsyncStorage.getItem('_FCM_TOKEN_A');
    // console.log(fcmToken, "the old token");
    if (!fcmToken) {
      try {
        const fcmToken = await messaging().getToken();
        // console.log(fcmToken, "the New token");
        AsyncStorage.setItem('_FCM_TOKEN_A', fcmToken);
      } catch (error) {
        console.log(error, 'error raised in fcm token');
      }
    }
  };

  const redirectTospecificScreen = data => {
    if (data.hasOwnProperty('type')) {
      if (data.type == NOTI_NEW_PROFILE_SUB) {
        let profile_seq = data.profile_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'OthersProfileScreen',
            params: {
              profileSeq: profile_seq,
            },
          }),
        );
      } else if (data.type == NOTI_NEW_POST_SUB) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_NEW_PROFILE_FOLLOW) {
        let profile_seq = data.profile_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'OthersProfileScreen',
            params: {
              profileSeq: profile_seq,
            },
          }),
        );
      } else if (data.type == NOTI_POST_LIKE) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_POST_COMMENT) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_POST_TAG) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_POST_CAPTION) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SinglePostScreen',
            params: {
              postSeq: post_seq,
            },
          }),
        );
      } else if (data.type == NOTI_PROFILE_BIO) {
        let profile_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'OthersProfileScreen',
            params: {
              profileSeq: profile_seq,
            },
          }),
        );
      } else if (data.type == NOTI_STORY_CAPTION) {
        let post_seq = data.post_seq;
        navigation.dispatch(
          CommonActions.navigate({
            name: 'SingleStoryStatusScreen',
            params: {
              storySeq: post_seq,
            },
          }),
        );
      } else {
        navigation.dispatch(
          CommonActions.navigate({
            name: 'HomeScreen',
          }),
        );
      }
    }
  };
  const [aState, setAppState] = useState(AppState.currentState);
  useEffect(() => {
    const appStateListener = AppState.addEventListener(
      'change',
      nextAppState => {
        // console.log('Next AppState is: ', nextAppState);
        if (nextAppState == 'active') {
          getNotificationService();
          // console.log("Active State");
          // changeNewNotficationCame(true);
          // console.log("appStateListener List", newNotificationTypeList)
          // let dataType = newNotificationTypeList;
          // dataType.push("POST_TAG");
          // changeNewNotficationTypeList(dataType);
          // ScreenGuardModule.register(
          //             //insert any hex color you want here, default black if null or empty
          //            '#000000',
          //        _ => {
          //      // .....do anything you want after the screenshot
          //           },)
        } else {
          // ScreenGuardModule.unregister();
        }

        setAppState(nextAppState);
      },
    );
    return () => {
      appStateListener?.remove();
    };
  }, []);
  function getNotificationService() {
    let hashMap = {
      _action_code: '11:GET_NOTIFICATION_STATUS',
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        let notiCame = false;
        let notList = [];
        data.data.map(obj => {
          if (obj.status == 'YES') {
            notiCame = true;
            let notiType = givingNotiTypeByViewType(obj.view_tab);
            notList.push(notiType);
          }
        });
        if (notiCame) {
          changeNewNotficationCame(true);
          let dataType = [...newNotificationTypeList];
          dataType.concat(notList);
          changeNewNotficationTypeList(notList);
        } else {
          changeNewNotficationTypeList([]);
        }
      },
      (errorCode, errorMessage, data) => {
        // failure method
      },
    );
  }
  const givingNotiTypeByViewType = viewType => {
    let notiType = '';
    if (viewType == 'SUBSCRIBE') {
      notiType = NOTI_NEW_PROFILE_SUB;
    } else if (viewType == 'LIKE') {
      notiType = NOTI_POST_LIKE;
    } else if (viewType == 'COMMENTS') {
      notiType = NOTI_POST_COMMENT;
    } else if (viewType == 'FOLLOW') {
      notiType = NOTI_NEW_PROFILE_FOLLOW;
    } else if (viewType == 'TAG') {
      notiType = NOTI_POST_TAG;
    }
    return notiType;
  };

  const handleDynamicLink = link => {
    dynamicLinkRedirection(link);
  };
  useEffect(() => {
    // console.log("UnSubscribe Log")
    const unsubscribe = dynamicLinks().onLink(handleDynamicLink);
    // When the component is unmounted, remove the listener
    return () => unsubscribe();
  }, []);
  useEffect(() => {
    dynamicLinks()
      .getInitialLink()
      .then(link => {
        dynamicLinkRedirection(link);
      });
  }, []);

  const dynamicLinkRedirection = dynamicUrl => {
    if (dynamicUrl == null) return;
    // console.log("dynamicUrl", dynamicUrl)
    const routes = navigation.getState()?.routes;
    // console.log("routes", routes)
    if (routes != undefined) {
      if (dynamicUrl.hasOwnProperty('url')) {
        // console.log("dynamicUrl IN", dynamicUrl.url)
        let rUrl = dynamicUrl.url;
        let params = rUrl.substring(rUrl.indexOf('?') + 1);
        if (rUrl.startsWith('https://sotrue.co.in/post')) {
          navigation.dispatch(
            CommonActions.navigate({
              name: 'SinglePostScreen',
              params: {
                postSeq: params,
              },
            }),
          );
        } else if (rUrl.startsWith('https://sotrue.co.in/profile')) {
          navigation.dispatch(
            CommonActions.navigate({
              name: 'OthersProfileScreen',
              params: {
                profileSeq: params,
              },
            }),
          );
        } else {
          navigation.dispatch(
            CommonActions.navigate({
              name: 'HomeScreen',
            }),
          );
        }
        // console.log("params", params)
      }
    }
  };
  useEffect(() => {
    const listener = Appearance.addChangeListener(colorTheme => {
      // console.log("colorTheme", colorTheme)
    });
    return () => {
      listener;
    };
  }, []);

  return (
    <>
      {/* <SafeAreaProvider>
             <StatusBar
                animated={true}
                barStyle={'dark-content'}
                hidden={false}
                backgroundColor={Colors.statusBarColor}
            /> */}
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: {backgroundColor: '#111111'},
          animationEnabled: Platform.select({
            ios: true,
            android: false,
          }),
        }}>
        <Stack.Group>
          <Stack.Screen name="SplashScreen">
            {props => <SplashScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="LoginScreen">
            {props => <LoginScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ForgotPasswordScreen">
            {props => <ForgotPasswordScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ForgotVerifyEmailScreen">
            {props => <ForgotVerifyEmailScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ForgotResetPassScreen">
            {props => <ForgotResetPassScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ForgotResetPassSuccessScreen">
            {props => <ForgotResetPassSuccessScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SignupScreen">
            {props => <SignupScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="TempHomeScreen">
            {props => <TempHomeScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="HomeScreen">
            {props => <BottomNavigator {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SignupOtpScreen">
            {props => <SignupOtpScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SignupGmailScreen">
            {props => <SignupGmailScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SignupAppleScreen">
            {props => <SignupAppleScreen {...props} />}
          </Stack.Screen>
          {/* 2.0 */}
          <Stack.Screen name="QuickSignInScreen">
            {props => <QuickSignInScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="QuickSignInOTPScreen">
            {props => <QuickSignInOTPScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="QuickSignUpScreen">
            {props => <QuickSignUpScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="QuickSignUpPersonalizeScreen">
            {props => <QuickSignUpPersonalizeScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="LoginOldFlowScreen">
            {props => <LoginOldFlowScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="NotificationGuideSelectionScreen">
            {props => <NotificationGuideSelectionScreen {...props} />}
          </Stack.Screen>
        </Stack.Group>
        <Stack.Group>
          {/*screenOptions={{ presentation: 'modal' }} */}
          <Stack.Screen name="StoryStatusScreen">
            {props => <StoryStatusScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="VideoDisplayScreen">
            {props => <VideoDisplayScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="OthersProfileScreen">
            {props => <OthersProfileScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="AccountSettingsScreen">
            {props => <AccountSettingsScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="FollowingScreen">
            {props => <FollowingScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="FollowersScreen">
            {props => <FollowersScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="CommentScreen">
            {props => <CommentScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen
            name="SearchResultScreen"
            options={{animation: 'slide_from_right'}}>
            {props => <SearchResultScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="EditProfileScreen">
            {props => <EditProfileScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="AccountInfoScreen">
            {props => <AccountInfoScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="CameraScreen">
            {props => <CameraScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="CaptureMediaDisplayScreen">
            {props => <CaptureMediaDisplayScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="BlockedAccountScreen">
            {props => <BlockedAccountScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="PrivacySafetyScreen">
            {props => <PrivacySafetyScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="AddStoryScreen">
            {props => <AddStoryScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SubscriptionScreen">
            {props => <SubscriptionScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="MySubscribersScreen">
            {props => <MySubscribersScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="VerificationsScreen">
            {props => <VerificationsScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SuccessfullVerificationScreen">
            {props => <SuccessfullVerificationScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ReferAndEarnScreen">
            {props => <ReferAndEarnScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ReferralHistoryScreen">
            {props => <ReferralHistoryScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="MySubscriptionsScreen">
            {props => <MySubscriptionsScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="AddPostScreen">
            {props => <AddPostScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="RestrictedAccountScreen">
            {props => <RestrictedAccountScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="BookmarkListScreen">
            {props => <BookmarkListScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="NotificationSettingsScreen">
            {props => <NotificationSettingsScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SinglePostScreen">
            {props => <SinglePostScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SelectDataFieldScreen">
            {props => <SelectDataFieldScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ImageDisplayScreen">
            {props => <ImageDisplayScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="AboutSettingScreen">
            {props => <AboutSettingScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="TermsAndUseScreen">
            {props => <TermsAndUseScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="PrivacyPolicyDisScreen">
            {props => <PrivacyPolicyDisScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="FaqDisScreen">
            {props => <FaqDisScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="EditPostScreen">
            {props => <EditPostScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="CategoryWiseUserScreen">
            {props => <CategoryWiseUserScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SettingScreen">
            {props => <SettingScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="TagPeopleScreen">
            {props => <TagPeopleScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="TagPeopleListScreen">
            {props => <TagPeopleListScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SingleStoryStatusScreen">
            {props => <SingleStoryStatusScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="AppIntroSliderScreen">
            {props => <AppIntroSliderScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="DisableAccountScreen">
            {props => <DisableAccountScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SignInNScreen">
            {props => <SignInNScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SignupNScreen">
            {props => <SignupNScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SinglePostNScreen">
            {props => <SinglePostNScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="TermsAndConditionScreen">
            {props => <TermsAndConditionScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="VideoContentScreen">
            {props => <VideoContentScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="UnlockSinglePostScreen">
            {props => <UnlockSinglePostScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="AddPostSuccessScreen">
            {props => <AddPostSuccessScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="NotificationScreen">
            {props => <NotificationScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="PlaylistScreen">
            {props => <PlaylistScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="PostingJourneyScreen">
            {props => <PostingJourneyScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ViewTransactionsScreen">
            {props => <ViewTransactionsScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="SearchResultMainScreen">
            {props => <SearchResultMainScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="UserPersonalizeScreen">
            {props => <UserPersonalizeScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="PlaylistEpisodeScreen">
            {props => <PlaylistEpisodeScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="ShareYourProfileScreen">
            {props => <ShareYourProfileScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="PlaylistShowScreen">
            {props => <PlaylistShowScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="PlaylistSeasonScreen">
            {props => <PlaylistSeasonScreen {...props} />}
          </Stack.Screen>
          <Stack.Screen name="PlaylistSeasonEpisodeScreen">
            {props => <PlaylistSeasonEpisodeScreen {...props} />}
          </Stack.Screen>
        </Stack.Group>
      </Stack.Navigator>
      {/* </SafeAreaProvider> */}
      <FlashMessage position="top" />
    </>
  );
};

export default AppNavigator;

const styles = StyleSheet.create({});
