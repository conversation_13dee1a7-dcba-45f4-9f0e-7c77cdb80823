import React from 'react'
import { StyleSheet, View } from 'react-native'
import ContentLoader, { Rect, Circle } from 'react-content-loader/native';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';

const StoryPlaceholder = () => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    return (
        <View style={{ ...defaultStyle.container, flexDirection: 'row' }}>
            <ContentLoader
                width={476}
                height={70}
                speed={theme.dimensions.placeholderSpeed}
                backgroundColor={theme.colors.placeholderBackgroundColor}
                foregroundColor={theme.colors.placeholderForegroundColor}
                viewBox="0 0 476 70">
                <Rect x="7" y="49" rx="3" ry="3" width="52" height="6" />
                <Circle cx="32" cy="24" r="20" />
                <Rect x="68" y="49" rx="3" ry="3" width="52" height="6" />
                <Circle cx="93" cy="24" r="20" />
                <Rect x="192" y="48" rx="3" ry="3" width="52" height="6" />
                <Circle cx="275" cy="22" r="20" />
                <Rect x="250" y="48" rx="3" ry="3" width="52" height="6" />
                <Circle cx="216" cy="23" r="20" />
                <Rect x="128" y="48" rx="3" ry="3" width="52" height="6" />
                <Circle cx="155" cy="24" r="20" />

            </ContentLoader>
        </View>
    )
}

export default StoryPlaceholder;

const styles = StyleSheet.create({})
