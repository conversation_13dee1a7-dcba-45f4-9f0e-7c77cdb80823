import { Platform, StyleSheet, Text, View } from 'react-native'
import React, { useCallback, useEffect, useState } from 'react'
import Video from 'react-native-video'
import { hasImageUrlExist } from '../../utils/Utils'
import ReelProgressBar from '../videoContents/ReelProgressBar'

const FullScreenPlayer = ({
    videoPlayerRef = null,
    media_cover = null,
    media_file = null,
    playVideo = true,
    onLoadStart, onLoad, onBuffer,
    height = 20,
    style = {},
    insets = null,
    onSliderValueComplete = null
}) => {
    const [pausedVideo, setPausedVideo] = useState(playVideo);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);

    useEffect(() => {
        setPausedVideo(playVideo);
    }, [playVideo])

    const [videoCompletedPercentage, setVideoCompletedPercentage] = useState(0)
    const onVideoProgress = useCallback((e) => {
        setCurrentTime(e.currentTime)
        // const currentTime = parseFloat(e.currentTime);
        // const playableDuration = parseFloat(e.playableDuration)
        // const percentage = (currentTime / playableDuration) * 100;
        // setVideoCompletedPercentage(percentage);
    }, []);
    const onVideoLoad = useCallback((data) => {
        if (onLoad) {
            onLoad(data);
        }
        setDuration(data.duration)
    }, []);
    const onSliderStart = () => {
        setPausedVideo(true)
    }
    const onSliderValueCompleteChange = (value) => {
        if (videoPlayerRef) {
            videoPlayerRef.current.seek(value);
            setPausedVideo(false);
            if (onSliderValueComplete) {
                onSliderValueComplete()
            }
        }
    }

    return (
        <>

            <Video
                ref={videoPlayerRef}
                poster={hasImageUrlExist(media_cover) ? media_cover : null}
                posterResizeMode={'cover'}
                source={{ uri: media_file, cache: false }}
                resizeMode='cover'
                repeat={true}
                paused={pausedVideo}
                muted={false}
                onLoadStart={onLoadStart}
                onProgress={(e) => onVideoProgress(e)}
                onLoad={onVideoLoad}
                onBuffer={onBuffer}
                // onError={(e) => console.log(e)}
                // onEnd={() => onVideoEnd()}
                fullscreenAutorotate={true}
                autoPlay={true}
                playInBackground={false}
                ignoreSilentSwitch={'ignore'}
                style={{ ...style, }}
            />
            <View style={[styles.reelProgressBarContainer, Platform.OS == 'ios' ? { bottom: insets.bottom + 5, zIndex: 9999 } : { top: height - 15 }]}>
                <ReelProgressBar
                    value={videoCompletedPercentage}
                    currentTime={currentTime}
                    onSliderStart={onSliderStart}
                    onSliderValueCompleteChange={onSliderValueCompleteChange}
                    duration={duration} />
            </View>
        </>
    )
}

export default FullScreenPlayer

const styles = StyleSheet.create({
    reelProgressBarContainer: {
        position: Platform.OS == 'ios' ? 'relative' : 'absolute',
        // top:100,
        left: 0,
        right: 0,
        zIndex: 999,

    },
})