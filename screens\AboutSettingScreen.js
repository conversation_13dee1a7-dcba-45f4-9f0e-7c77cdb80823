import React, { } from 'react'
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native'
import HomeTopNavigationBar from '../components/HomeTopNavigationBar'
import ListItem from '../components/ListItem'
import CustomStatusBar from '../components/common/CustomStatusBar';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';


const AboutSettingScreen = ({ navigation }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const termsandUseBtnPress = () => {
        navigation.navigate('TermsAndUseScreen');
    }
    const privacyPolicyBtnPress = () => {
        navigation.navigate('PrivacyPolicyDisScreen');
    }
    const faqBtnPress = () => {
        navigation.navigate('FaqDisScreen');
    }
    const introVideoPress = () => {
        navigation.navigate('AppIntroSliderScreen');
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar showBackBtn={true} showBorderBottom={false} title="About" navigation={navigation} />
            <ScrollView
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                style={{ backgroundColor: theme.colors.backgroundColor }}
            >
                <View style={defaultStyle.container}>
                    <TouchableOpacity onPress={() => termsandUseBtnPress()}>
                        <ListItem label="Terms of Use" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => privacyPolicyBtnPress()}>
                        <ListItem label="Privacy Policy" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => faqBtnPress()}>
                        <ListItem label="FAQ's" />
                    </TouchableOpacity>
                    {/* <TouchableOpacity onPress={() => introVideoPress()}>
                        <ListItem label="The SoTrue Plan" />
                    </TouchableOpacity> */}

                </View>
            </ScrollView>
        </>
    )
}

export default AboutSettingScreen

const styles = StyleSheet.create({})
