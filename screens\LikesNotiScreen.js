import React, { useContext, useEffect, useState } from 'react'
import { StyleSheet, FlatList, View, RefreshControl } from 'react-native'
import CustomStatusBar from '../components/common/CustomStatusBar'
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox'
import NotificationRow from '../components/notifications/NotificationRow'
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder'
import { DEFAULT_NOTI_LIST_COUNT, MAX_NOTI_LIST_COUNT, _RedirectionErrorList } from '../utils/Appconfig'
import { RedirectionUrlFunction } from '../utils/RedirectionUrl'
import ServerConnector from '../utils/ServerConnector'
import NotificationHeader from '../components/common/NotificationHeader'
import { PageRefreshContext } from '..'
import Dimensions from '../constants/Dimensions'
import useDefaultStyle from '../theme/useDefaultStyle'
import useSTheme from '../theme/useSTheme'

const LikesNotiScreen = ({ notificationMenuPress, ...props }) => {
    const [subList, setsubList] = useState([]);
    const [listRefresh, setlistRefresh] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setshowLoading] = useState(false);
    const LikeMsg = " has liked your post!";
    const [displayCount, setDisplayCount] = useState(DEFAULT_NOTI_LIST_COUNT);
    const [listRefreshValue, setListRefreshValue] = useState(1);
    const [viewAllBtnValue, setViewAllBtnValue] = useState(true);

    const { notificationRefresh, changeNotificationRefresh } = useContext(PageRefreshContext);

    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    useEffect(() => {
        setshowLoading(true);
        setViewAllBtnValue(true);
        getNotiLikesService();
    }, [notificationRefresh]);
    function getNotiLikesService() {
        let hashMap = {
            _action_code: "11:GET_NOTIFICATIONS_LIKES",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false);
            setsubList([...[], ...data.data]);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                setsubList([]);
                seterrorMsg(errorMessage);
            }
        });
    }

    const renderItem = ({ item }) => {
        return (
            <NotificationRow rowType={"LIKES"} data={item} afterTxt={LikeMsg} profileSeq={item.liked_by_profile} timeTxt={item.like_time} />
        );
    };

    const handleRefresh = () => {
        setshowLoading(true);
        seterrorMsg("");
        notificationMenuPress("Press", { type: "LIKE" })
        getNotiLikesService();
    }
    const viewAllBtnPress = () => {

        if (!viewAllBtnValue) {

            setDisplayCount(DEFAULT_NOTI_LIST_COUNT);
        }
        else {
            notificationMenuPress("Press", { type: "LIKE" })
            setDisplayCount(MAX_NOTI_LIST_COUNT);
        }
        setViewAllBtnValue(!viewAllBtnValue);
        setListRefreshValue(Math.random())

    }
    const notificationRowClick = () => {
        notificationMenuPress("Press", { type: "LIKE" })
    }
    async function updateNotificationViewService() {
        let hashMap = {
            _action_code: "11:UPDATE_NOTIFICATION_VIEW",
            view_tab: "LIKE"
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

        }, (errorCode, errorMessage, data) => { // failure method

        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                <NotificationHeader headerTitle="Interactions" showViewAllBtn={subList.length > DEFAULT_NOTI_LIST_COUNT} headerValue="LIKES" viewAllBtnPress={viewAllBtnPress}
                    viewAllBtnLabel={viewAllBtnValue ? "View All" : "Close All"} />

                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside}>
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        :
                        <>
                            {
                                showLoading ?
                                    <NotiRowPlaceholder countVal={2} />
                                    :
                                    <View key={listRefreshValue} style={{ paddingBottom: Dimensions.notificationListGap }}>
                                        {
                                            subList.map((item, index) => {
                                                if ((index + 1) <= displayCount) {
                                                    return <NotificationRow key={index} rowType={"LIKES"} data={item} afterTxt={LikeMsg} profileSeq={item.liked_by_profile} timeTxt={item.like_time} rowClick={notificationRowClick} />
                                                }
                                            })
                                        }
                                    </View>
                                // <FlatList
                                //     contentContainerStyle={{ paddingBottom: 20 }}
                                //     data={subList}
                                //     renderItem={renderItem}
                                //     keyExtractor={(item, index) => `${index}`}
                                //     scrollEnabled={false}
                                //     refreshControl={
                                //         <RefreshControl refreshing={showLoading} onRefresh={() => handleRefresh()} />
                                //     } />

                            }
                        </>
                }
            </View>
        </>
    )
}

export default LikesNotiScreen

const styles = StyleSheet.create({

})
