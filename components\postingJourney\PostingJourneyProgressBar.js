import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import useSTheme from '../../theme/useSTheme';
import Animated from 'react-native-reanimated';

const PostingJourneyProgressBar = ({ progress = 40, style = {} }) => {
    const theme = useSTheme();
    return (
        <View style={{ ...styles.progressBarContainer, ...style }}>
            <Animated.View
                style={[
                    styles.progressBarContainer,
                    {
                        width: `${progress}%`,
                        backgroundColor: theme.colors.primaryColor,
                        position: 'absolute',
                        top: 0,
                        margin: 0,
                    },
                ]}
            />
        </View>
    )
}

export default PostingJourneyProgressBar

const styles = StyleSheet.create({
    progressBarContainer: {
        position: 'relative',
        minHeight: 5,
        borderRadius: 5 / 2,
        flex: 1,
        backgroundColor: '#EC008C',

    }
})