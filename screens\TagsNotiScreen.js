import React, { useContext, useEffect, useState } from 'react'
import { StyleSheet, FlatList, View, RefreshControl } from 'react-native'
import { ActivityIndicator } from 'react-native-paper'
import CustomStatusBar from '../components/common/CustomStatusBar'
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox'
import NotificationTagsRow from '../components/notifications/NotificationTagsRow'
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder'
import { DEFAULT_NOTI_LIST_COUNT, MAX_NOTI_LIST_COUNT, _RedirectionErrorList } from '../utils/Appconfig'
import { RedirectionUrlFunction } from '../utils/RedirectionUrl'
import ServerConnector from '../utils/ServerConnector'
import { getSecondsBetweenDates } from '../utils/Utils'
import NotificationHeader from '../components/common/NotificationHeader'
import { PageRefreshContext } from '..'
import useDefaultStyle from '../theme/useDefaultStyle'
import useSTheme from '../theme/useSTheme'

const TagsNotiScreen = ({ notificationMenuPress, ...props }) => {
    const [subList, setsubList] = useState([]);
    const [listRefresh, setlistRefresh] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setshowLoading] = useState(false);
    const LikeMsg = " has tag you!";

    const RowsPerPage = 10;
    const [startRecord, setstartRecord] = useState(0);
    const [bottomLoading, setbottomLoading] = useState(false);
    const [isNoDataFound, setisNoDataFound] = useState(false);
    const [bottomReachTime, setbottomReachTime] = useState(new Date());

    const [displayCount, setDisplayCount] = useState(DEFAULT_NOTI_LIST_COUNT);
    const [listRefreshValue, setListRefreshValue] = useState(1);
    const [viewAllBtnValue, setViewAllBtnValue] = useState(true);

    const { notificationRefresh, changeNotificationRefresh } = useContext(PageRefreshContext);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();

    useEffect(() => {
        setshowLoading(true);
        setViewAllBtnValue(true);
        seterrorMsg("");
        getNotiTagsService(0, RowsPerPage);
        // updateNotificationViewService();
    }, [notificationRefresh]);
    function getNotiTagsService(startRec, rowsPerPage) {
        setstartRecord(startRec)
        let hashMap = {
            _action_code: "11:GET_NOTIFICATIONS_TAG",
            _start_row: startRec,
            _rows_page: rowsPerPage,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false);
            setbottomLoading(false);
            if (parseInt(startRec) == 0) {
                setsubList([...[], ...data.data]);
            }
            else {
                setsubList([...subList, ...data.data]);
            }
            seterrorMsg("");
            setisNoDataFound(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                setisNoDataFound(true);
                setbottomLoading(false);
                if (parseInt(startRec) == 0) {
                    setsubList([]);
                    seterrorMsg(errorMessage);
                }
            }
        });
    }

    const renderItem = ({ item }) => {
        return (
            <NotificationTagsRow rowType={"TAGS"} data={item} afterTxt={LikeMsg} profileSeq={item.profile_seq} timeTxt={item.tagged_on} />
        );
    };

    const handleRefresh = () => {
        setshowLoading(true);
        seterrorMsg("");
        notificationMenuPress("Press", { type: "TAG" })
        getNotiTagsService(0, RowsPerPage);
    }
    const handleEndRefresh = () => {
        if (!isNoDataFound) {
            let currentTime = new Date();
            let diffTime = getSecondsBetweenDates(bottomReachTime, currentTime);
            if (diffTime > 4) {
                let startRec = startRecord + RowsPerPage;
                setbottomLoading(true);
                getNotiTagsService(startRec, RowsPerPage);
                setbottomReachTime(new Date());
            }

        }
    }
    const viewAllBtnPress = () => {

        if (!viewAllBtnValue) {
            setDisplayCount(DEFAULT_NOTI_LIST_COUNT);
        }
        else {
            notificationMenuPress("Press", { type: "TAG" })
            setDisplayCount(MAX_NOTI_LIST_COUNT);
        }
        setViewAllBtnValue(!viewAllBtnValue);
        setListRefreshValue(Math.random())

    }
    const notificationRowClick = () => {
        notificationMenuPress("Press", { type: "TAG" })
    }
    async function updateNotificationViewService() {
        let hashMap = {
            _action_code: "11:UPDATE_NOTIFICATION_VIEW",
            view_tab: "TAG"
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

        }, (errorCode, errorMessage, data) => { // failure method

        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={{ backgroundColor: theme.colors.backgroundColor }}>
                <NotificationHeader headerTitle="Tags" headerValue="TAGGED" showViewAllBtn={subList.length > DEFAULT_NOTI_LIST_COUNT} viewAllBtnPress={viewAllBtnPress}
                    viewAllBtnLabel={viewAllBtnValue ? "View All" : "Close All"} />

                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside}>
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        :
                        <>
                            {
                                showLoading ?
                                    <NotiRowPlaceholder countVal={2} />
                                    :
                                    <View key={listRefreshValue} style={{ paddingBottom: theme.dimensions.notificationListGap }}>
                                        {
                                            subList.map((item, index) => {
                                                return <NotificationTagsRow key={index} rowType={"TAGS"} data={item} afterTxt={LikeMsg} profileSeq={item.profile_seq} timeTxt={item.tagged_on} rowClick={notificationRowClick} />


                                            })
                                        }
                                    </View>

                                // <FlatList
                                //     contentContainerStyle={{ paddingBottom: 20 }}
                                //     data={subList}
                                //     renderItem={renderItem}
                                //     keyExtractor={(item, index) => `${index}`}
                                //     onEndReached={handleEndRefresh}
                                //     initialNumToRender={3}
                                //     scrollEnabled={false}
                                //     refreshControl={
                                //         <RefreshControl refreshing={showLoading} onRefresh={() => handleRefresh()} />
                                //     } />

                            }
                        </>
                }
                {
                    bottomLoading ?
                        <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 15 }}>
                            <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={'large'} />
                        </View>
                        : null
                }
            </View>
        </>
    )
}

export default TagsNotiScreen

const styles = StyleSheet.create({

})
