import React, { useContext, useEffect, useState } from 'react'
import { Image, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { ActivityIndicator } from 'react-native-paper';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import Stories from '../components/StoryComponent/Stories';
import appData from '../data/Data';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import MultipleStoryPlayComponent from '../components/StoryComponent/MultipleStoryPlayComponent';
import { AppStateContext } from '..';
import useDefaultStyle from '../theme/useDefaultStyle';

const StoryStatusScreen = ({ route, navigation }) => {
    const [storyList, setstoryList] = useState([]);
    const [hasStory, sethasStory] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setshowLoading] = useState(true);
    const [fullStoryList, setFullStoryList] = useState([])
    const { profileSeq, selectedProfileSeq } = route.params;
    const { fullUserDetails, userProfileImage } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : "";

    const [fetchedPSeqs, setFetchedPSeqs] = useState([]);
    const [storyProfileList, setStoryProfileList] = useState([]);
    const [noDataFoundError, setNoDataFoundError] = useState(false)
    const { defaultStyle } = useDefaultStyle();
    useEffect(() => {
        if (__ProfileSeq == profileSeq) {
            // console.log("My Profile")
            setStoryProfileList([profileSeq])
            getProfileStory([], profileSeq, -1);
        }
        else {
            // console.log("Other Profile", selectedProfileSeq);
            let filterArr = selectedProfileSeq.filter(pSeq => pSeq != profileSeq)
            let mainL = [profileSeq].concat(filterArr)
            // console.log("Sort Profile", mainL);
            setStoryProfileList(mainL)
            if (mainL.length > 1) {
                getProfileStory([], mainL[0], mainL[1])
            }
            else {
                getProfileStory([], mainL[0], -1)
            }
        }
        return () => null;
    }, []);
    function closeButton() {
        navigation.goBack(null);
    }
    function getProfileStory(prevData, pSeq, nextProfileSeq) {

        let hashMap = {
            _action_code: "11:GET_PROFILE_STORIES",
            profile_seq: pSeq,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            if (fetchedPSeqs.includes(pSeq)) {
                return;
            }
            let storyListObj = {};
            storyListObj.displayName = data.data[0].display_name;
            storyListObj.profilePic = data.data[0].profile_picture;
            storyListObj.userHandle = data.data[0].user_handle;
            storyListObj.isVerified = data.data[0].is_verified;
            storyListObj.profileSeq = data.data[0].profile_seq;
            storyListObj.caption = data.data[0].story_comments;
            storyListObj.stories = [];
            data.data.map((obj, i) => {
                let dataRow = {};
                dataRow.id = obj.story_seq;
                dataRow.storySeq = obj.story_seq;
                dataRow.url = obj.media_file;
                dataRow.type = obj.media_type;
                dataRow.caption = obj.story_comments;
                dataRow.story_comment_tags = obj.story_comment_tags;
                dataRow.media_cover = obj.media_cover;
                dataRow.status = obj.status;
                dataRow.block_reason = obj.block_reason;
                dataRow.duration = 3;
                storyListObj.stories[i] = dataRow;
            });
            sethasStory(true);
            setshowLoading(false)
            let mainList = [...prevData];
            mainList.push(storyListObj)
            setFullStoryList(mainList)
            setstoryList(storyListObj);
            if (!fetchedPSeqs.includes(pSeq)) {
                setFetchedPSeqs(prevState => [...prevState, pSeq]);
            }
            if (nextProfileSeq != undefined && nextProfileSeq != -1) {
                getProfileStory(mainList, nextProfileSeq, -1);
            }
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                appData.__StoryPageRefreshCheck = "YES";
                seterrorMsg(errorMessage);
                sethasStory(false);
                setstoryList({});
                setshowLoading(false)
                setFullStoryList([]);
            }

        });
    }
    const backBtnProfileClick = () => {
        // console.log("Back")
        navigation.goBack(null);
    }
    const getCurrentIndex = (cIndex) => {
        // console.log("cIndex", cIndex)
        // console.log("fetchedPSeqs", fetchedPSeqs.length)
        if (!showLoading) {
            let callOneTime = true;
            storyProfileList.map((item, index) => {
                if (cIndex >= fetchedPSeqs.length - 1) {
                    if (!fetchedPSeqs.includes(item)) {
                        let nextItem = -1;
                        if (storyProfileList[index + 1] !== undefined) {
                            nextItem = storyProfileList[index + 1]
                        }
                        if (callOneTime) {
                            callOneTime = false;
                            getProfileStory(fullStoryList, item, nextItem);
                        }
                    }
                }
            })
            if (storyProfileList.length == fetchedPSeqs.length && storyProfileList.length <= cIndex) {
                closeButton();
            }
        }

    }
    return (
        <View style={{ flex: 1, position: 'relative' }}>
            <StatusBar hidden={true} />
            {
                !hasStory ?
                    <View style={styles.profileBakBtnBox}>
                        <TouchableOpacity onPress={() => backBtnProfileClick()}
                            style={{ paddingHorizontal: 2, paddingVertical: 8, }}>
                            <View style={{ paddingHorizontal: 2, paddingVertical: 8, }}>
                                <Image
                                    style={styles.profileBackBtn}
                                    source={require('../assets/Images/icon/Arrow.png')}
                                />
                            </View>
                        </TouchableOpacity>
                    </View>
                    : null
            }
            {
                errorMsg.length != 0 ?
                    <View style={defaultStyle.errorBoxOutside} >
                        <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                    </View>
                    : null
            }
            {/* {
                hasStory ?
                    <Stories dataStories={storyList} onStoryClosePress={closeButton} onClose={closeButton} />
                    : null


            } */}
            {
                hasStory ?
                    <MultipleStoryPlayComponent
                        getCurrentIndex={getCurrentIndex}
                        storyList={fullStoryList}
                        navigation={navigation} />
                    : null
            }
            {
                showLoading ?
                    <View style={defaultStyle.errorBoxOutside} >
                        <ActivityIndicator size={'large'} />
                    </View>
                    : null
            }

        </View>
    )
}

export default StoryStatusScreen

const styles = StyleSheet.create({
    profileBakBtnBox: {
        position: 'absolute',
        left: 15,
        top: 20, zIndex: 1000,
    },
    profileBackBtn: {
        width: 24,
        height: 24,

    },
})
