import React, { useContext, useState } from 'react'
import { StyleSheet, View, ScrollView, TouchableOpacity } from 'react-native'
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import EntutoEditText from '../components/common/EntutoEditText';
import PrimaryButton from '../components/common/PrimaryButton';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import TopNavigationBar from '../components/TopNavigationBar';
import ServerConnector from '../utils/ServerConnector';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import { AppStateContext } from '..';
import ErrorMessages from '../constants/ErrorMessages';
import { CommonActions } from '@react-navigation/native';
import CustomStatusBar from '../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import EntutoTextView from '../components/common/EntutoTextView';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
const SignupOtpScreen = ({ route, navigation }) => {
    const [otp, setotp] = useState("");
    const [otpErr, setotpErr] = useState("");
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const [refreshKey, setrefreshKey] = useState(Math.random())
    const { _data, _emailId, _user_seq, _password } = route.params;
    const { changeUserDetails } = useContext(AppStateContext);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const loginTxtClick = () => {
        navigation.replace("LoginScreen", {
            ErrorMsg: "",
        });
    }
    const verifyButtonClick = () => {
        var isValid = true;
        setotpErr("");
        if (otp.length === 0) {
            setotpErr(ErrorMessages.noOtpErr);
            isValid = false;
        }
        if (isValid) {
            setShowLoading(true);
            appServiceCall();
        }
    }
    function appServiceCall() {
        let hashMap = {
            _action_code: "11:SUBMIT_OTP",
            otp: otp,
            user_seq: _user_seq,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            seterrorMsg("");
            appLoginServiceCall();
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_seq) {
                        seterrorMsg(data.data.user_seq);
                        setrefreshKey(Math.random());
                        fieldErrorShown = true;
                    }
                    if (data.data.otp) {
                        setotpErr(data.data.otp);
                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }
    function appLoginServiceCall() {
        setShowLoading(true);
        let hashMap = {
            _action_code: "11:APP_LOGIN",
            user_id: _emailId,
            password: _password
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let userDeatails = {
                _username: data.data.uid,
                _password: data.data.pwd,
                _profile_seq: data.data.profile_seq,
                _user_seq: data.data.user_seq,
                _user_handle: data.data.user_handle,
                _user_account_type: data.data.account_type,
                _user_display_name: "",
                _has_bank_details: "NO",
                _is_profile_verified: "NO",
                _is_gmail_login: "NO",
                _max_file_size: data.data.max_file_size,
            }
            changeUserDetails(userDeatails);
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'HomeScreen',
                        },
                    ],
                })
            );
            // navigation.replace('HomeScreen')
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_id) {
                        setuserNameErr(data.data.user_id);
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        setpasswordErr(data.data.password);
                        fieldErrorShown = true;
                    }
                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />

            <View style={{ flex: 1, position: 'relative' }}>
                <LoginSignUpLinearGrad />
                <CustomProgressDialog
                    showLoading={showLoading}
                />
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View>
                        <View style={defaultStyle.signupTextBox}>
                            <EntutoTextView style={defaultStyle.signupText}>Verify Your Email</EntutoTextView>
                            <EntutoTextView style={defaultStyle.signupSmallText}>{_data.msg}</EntutoTextView>
                        </View>
                        <View style={{ ...defaultStyle.signUpFormBox, marginTop: 80 }}>
                            <EntutoEditText
                                placeholderTxt="Otp"
                                value={otp}
                                keyboardType='numeric'
                                onChangeText={text => setotp(text)}
                                showErrorField={otpErr.length}
                                errorMsg={otpErr}
                            />
                        </View>
                        <View style={{ ...defaultStyle.signUpBtnBox, marginTop: 220 }}>
                            <TouchableOpacity style={{ ...defaultStyle.signUpBtn, backgroundColor: theme.colors.signUpBtnBackground }}
                                onPress={() => verifyButtonClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Verify</EntutoTextView>
                            </TouchableOpacity>
                            <TouchableOpacity style={defaultStyle.signUpBtn}
                                onPress={() => loginTxtClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Login</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>

            </View>
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                    : null
            }
        </>
    )
}

export default SignupOtpScreen;

const styles = StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: Colors.bodyTextColor
    },
    signupTxtVal: {
        color: Colors.primaryColor,
    },
    forgetPassLogo: {
        height: 200,
        width: "auto",
        resizeMode: "contain",
        marginTop: 10,
    },
    loginHeadTxt: {
        marginTop: 40,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },
    resendTxtVal: {
        color: Colors.primaryColor,
        marginBottom: 18,
    },

})
