import React from 'react'
import { StyleSheet, View } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { ProgressDialog } from 'react-native-simple-dialogs';
import EntutoTextView from './EntutoTextView';
import useSThemedStyles from '../../theme/useSThemedStyles';

const CustomProgressDialog = (props) => {
    const showLoading = props.showLoading;
    const loadingMsgTitle = props.hasOwnProperty('loadingMsgTitle') ? props.loadingMsgTitle : "Loading...";
    const loadingMsg = props.hasOwnProperty('loadingMsg') ? props.loadingMsg : "Please wait...";
    const indicatorSize = props.hasOwnProperty('indicatorSize') ? props.indicatorSize : "large";
    const style = useSThemedStyles(styles);
    return (
        // <ProgressDialog
        //     visible={showLoading}
        //     title={loadingMsgTitle}
        //     message={loadingMsg}
        //     activityIndicatorSize={indicatorSize}
        // />

        <>
            {
                showLoading ?
                    <>
                        <View style={style.spinnerOverlay}></View>
                        <View style={style.spinnerBox}>
                            <ActivityIndicator size={40} />
                            <EntutoTextView style={style.loadingMsg}>{loadingMsg}</EntutoTextView>
                        </View>
                    </>
                    : null
            }

        </>
    )
}

export default CustomProgressDialog;

const styles = theme => StyleSheet.create({
    spinnerOverlay: {
        flex: 1,
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: theme.colors.spinnerBackground,
        width: '100%',
        height: '100%',
        zIndex: 9999
    },
    spinnerBox: {
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: "center",
        position: 'absolute',
        left: 0,
        top: 0,
        backgroundColor: 'transparent',
        zIndex: 10000,
    },
    loadingMsg: {
        color: theme.colors.spinnerText,
        fontSize: 16,
        marginTop: 10,
    }
})
