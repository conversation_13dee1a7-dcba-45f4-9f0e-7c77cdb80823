import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';

export async function requestUserPermission() {
    const authStatus = await messaging().requestPermission();
    const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
        // console.log('Authorization status:', authStatus);
        getFcmTocken()
    }
}
const getFcmTocken = async () => {
    let fcmToken = await AsyncStorage.getItem('_FCM_TOKEN_A');
    // console.log(fcmToken, "the old token");
    if (!fcmToken) {
        try {
            const fcmToken = await messaging().getToken();
            if (fcmToken) {
                // console.log(fcmToken, "the New token");
                await AsyncStorage.setItem('_FCM_TOKEN_A', fcmToken);
            }

        } catch (error) {
            // console.log(error, "error raised in fcm token")
        }
    }

}
export const notificationListnner = async () => {
    messaging().onNotificationOpenedApp(remoteMessage => {
        // console.log("Notification caused app to", remoteMessage.notification);
    });
    messaging().onMessage(async remoteMessage => {
        // console.log("Foreground", remoteMessage);
    });
    messaging().getInitialNotification().then(remoteMessage => {
        if (remoteMessage) {
            // console.log("Notification caused app to open from quit state:", remoteMessage.notification);
        }
    })
}