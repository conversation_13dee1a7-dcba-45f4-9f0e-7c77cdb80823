import React, { useContext, useEffect, useState } from 'react'
import { Pressable, RefreshControl, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import EntutoTextView from '../components/common/EntutoTextView';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import Dimensions from '../constants/Dimensions';
import { TabView, SceneMap, TabBar } from 'react-native-tab-view';
import SubscriptionNotiScreen from './SubscriptionNotiScreen';
import CommentsNotiScreen from './CommentsNotiScreen';
import LikesNotiScreen from './LikesNotiScreen';
import CustomStatusBar from '../components/common/CustomStatusBar';
import Colors from '../constants/Colors';
import GestureRecognizer from 'react-native-swipe-gestures';
import { AppStateContext, NotiStateContext, PageRefreshContext } from '..';
import FollowNotiScreen from './FollowNotiScreen';
import { NOTI_NEW_POST_SUB, NOTI_NEW_PROFILE_FOLLOW, NOTI_NEW_PROFILE_SUB, NOTI_POST_CAPTION, NOTI_POST_COMMENT, NOTI_POST_LIKE, NOTI_POST_TAG, NOTI_PROFILE_BIO, NOTI_STORY_CAPTION } from '../constants/Constants';
import TagsNotiScreen from './TagsNotiScreen';
import ServerConnector from '../utils/ServerConnector';
import appData from '../data/Data';
import NotificationHeader from '../components/common/NotificationHeader';
import useSTheme from '../theme/useSTheme';


const NotificationScreen = ({ route, navigation }) => {
    const { newNotificationTypeList, changeNewNotficationTypeList, changeNewNotficationCame } = useContext(AppStateContext);
    const { notificationRefresh, changeNotificationRefresh } = useContext(PageRefreshContext);
    const { selectedNotificationMenu, changeSelectedNotificationMenu } = useContext(NotiStateContext);
    const initialLayout = { width: Dimensions.screenWidth, };
    const theme = useSTheme();
    const [selectedTab, setselectedTab] = useState(0);
    const [index, setIndex] = React.useState(0);
    const [routes] = React.useState([
        { key: 'subscriptions', title: 'Subscriptions' },
        { key: 'likes', title: 'Likes' },
        { key: 'comments', title: 'Comments' },
    ]);
    const [showSubBadge, setshowSubBadge] = useState(false);
    const [showLikesBadge, setshowLikesBadge] = useState(false);
    const [showCommentsBadge, setshowCommentsBadge] = useState(false);
    const [showFollowBadge, setshowFollowBadge] = useState(false);
    const [showTagsBadge, setshowTagsBadge] = useState(false);
    const [notiTypeList, setnotiTypeList] = useState([]);
    const [refreshing, setRefreshing] = useState(false);
    // useEffect(() => {
    //     let subBadge = false;
    //     let likeBadge = false;
    //     let cmntBadge = false;
    //     let followBadge = false;
    //     let tagsBadge = false;
    //     if (newNotificationTypeList.includes(NOTI_NEW_PROFILE_SUB)) {
    //         subBadge = true;
    //     }
    //     if (newNotificationTypeList.includes(NOTI_NEW_POST_SUB)) {
    //         subBadge = true;
    //     }
    //     if (newNotificationTypeList.includes(NOTI_POST_LIKE)) {
    //         likeBadge = true;
    //     }
    //     if (newNotificationTypeList.includes(NOTI_POST_COMMENT)) {
    //         cmntBadge = true;
    //     }
    //     if (newNotificationTypeList.includes(NOTI_NEW_PROFILE_FOLLOW)) {
    //         followBadge = true;
    //     }
    //     if (newNotificationTypeList.includes(NOTI_POST_TAG)) {
    //         tagsBadge = true;
    //     }
    //     if (newNotificationTypeList.includes(NOTI_POST_CAPTION)) {
    //         tagsBadge = true;
    //     }
    //     if (newNotificationTypeList.includes(NOTI_STORY_CAPTION)) {
    //         tagsBadge = true;
    //     }
    //     if (newNotificationTypeList.includes(NOTI_PROFILE_BIO)) {
    //         tagsBadge = true;
    //     }
    //     // setnotiTypeList(newNotificationTypeList);
    //     // setshowSubBadge(subBadge);
    //     // setshowLikesBadge(likeBadge);
    //     // setshowCommentsBadge(cmntBadge);
    //     // setshowFollowBadge(followBadge);
    //     // setshowTagsBadge(tagsBadge);
    //     // if (newNotificationTypeList.length == 0) {
    //     //     changeNewNotficationCame(false)
    //     // }
    //     // console.log("newNotificationTypeList", newNotificationTypeList)
    // }, [newNotificationTypeList]);
    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            changeNewNotficationCame(false)
            updateNotificationInServer(newNotificationTypeList);
            // notiBadgeChange(selectedNotificationMenu, newNotificationTypeList);
        });
        return unsubscribe;
    }, [navigation, newNotificationTypeList]);

    const onTabChange = (item) => {
        changeSelectedNotificationMenu(item);
        setselectedTab(item);
        notiBadgeChange(item, notiTypeList);
    }
    const config = {
        velocityThreshold: 0.3,
        directionalOffsetThreshold: 80,
    };
    const swipeLeft = () => {
        // let currVal = selectedTab;
        // currVal - 1;
        // if (currVal >= 0) {
        //     onTabChange(currVal)
        // }
        // console.log("Left", currVal)

    }
    const swipeRight = () => {
        // let currVal = selectedTab;
        // currVal + 1;
        // if (currVal <= 3) {
        //     onTabChange(currVal)
        // }
        // console.log("Right", currVal)

    }

    const notiBadgeChange = (currentTab, mainList) => {
        return;
        let list = mainList;
        if (list.length != 0) {
            if (currentTab == 0) {
                // if (list.includes(NOTI_NEW_PROFILE_SUB) || list.includes(NOTI_NEW_POST_SUB)) {
                //     updatenotificationViewService("SUBSCRIBE");
                // }
                const newList = list.filter((item) => item != NOTI_NEW_PROFILE_SUB);
                const newList2 = newList.filter((item) => item != NOTI_NEW_POST_SUB);
                // console.log("Sub list", newList2);
                changeNewNotficationTypeList(newList2);
            }
            else if (currentTab == 1) {
                // if (list.includes(NOTI_POST_LIKE)) {
                //     updatenotificationViewService("LIKE");
                // }
                const newList = list.filter((item) => item != NOTI_POST_LIKE);
                // console.log("Like list", newList)
                changeNewNotficationTypeList(newList);
            }
            else if (currentTab == 2) {
                // if (list.includes(NOTI_POST_COMMENT)) {
                //     updatenotificationViewService("COMMENTS");
                // }
                const newList = list.filter((item) => item != NOTI_POST_COMMENT);
                // console.log("Comment list", newList);
                changeNewNotficationTypeList(newList);
            }
            else if (currentTab == 3) {
                if (list.includes(NOTI_NEW_PROFILE_FOLLOW)) {
                    updatenotificationViewService("FOLLOW")
                }
                const newList = list.filter((item) => item != NOTI_NEW_PROFILE_FOLLOW);
                // console.log("Follow list", newList)
                changeNewNotficationTypeList(newList);
            }
            else if (currentTab == 4) {
                if (list.includes(NOTI_POST_CAPTION) || list.includes(NOTI_STORY_CAPTION)
                    || list.includes(NOTI_POST_TAG) || list.includes(NOTI_PROFILE_BIO)) {
                    updatenotificationViewService("TAG")
                }
                const newList = list.filter((item) => item != NOTI_POST_CAPTION);
                const newList2 = newList.filter((item) => item != NOTI_STORY_CAPTION);
                const newList3 = newList2.filter((item) => item != NOTI_POST_TAG);
                const newList4 = newList3.filter((item) => item != NOTI_PROFILE_BIO);
                // console.log("Tagged list", newList4)

                changeNewNotficationTypeList(newList4);
            }
        }
    }

    async function updatenotificationViewService(selectedTabVal) {

        let hashMap = {
            _action_code: "11:UPDATE_NOTIFICATION_VIEW",
            view_tab: selectedTabVal
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

        }, (errorCode, errorMessage, data) => { // failure method

        });
    }
    const notificationMenuPress = (clickID, obj) => {
        if (clickID == "Press") {
            if (obj.type == "SUBS") {
                notiBadgeChange(0, notiTypeList);
            }
            else if (obj.type == "LIKE") {
                notiBadgeChange(1, notiTypeList);
            }
            else if (obj.type == "COMMENT") {
                notiBadgeChange(2, notiTypeList);
            }
            else if (obj.type == "FOLLOW") {
                notiBadgeChange(3, notiTypeList);
            }
            else if (obj.type == "TAG") {
                notiBadgeChange(4, notiTypeList);
            }
        }
    }
    const viewAllBtnPress = (value) => {

    }
    useEffect(() => {
        setRefreshing(false);
        // console.log("notificationRefresh", notificationRefresh)
    }, [notificationRefresh])
    const onRefresh = () => {
        setRefreshing(true);
        changeNotificationRefresh(Math.random());

        // setTimeout(() => {
        //     setRefreshing(false);
        // }, 1000);
    }
    function updateNotificationInServer(listData) {
        let notList = [...listData];
        if (notList.includes(NOTI_NEW_PROFILE_SUB) || notList.includes(NOTI_NEW_POST_SUB)) {
            updatenotificationViewService("SUBSCRIBE");
        }
        if (notList.includes(NOTI_POST_LIKE)) {
            updatenotificationViewService("LIKE");
        }
        if (notList.includes(NOTI_POST_COMMENT)) {
            updatenotificationViewService("COMMENTS");
        }
        if (notList.includes(NOTI_NEW_PROFILE_FOLLOW)) {
            updatenotificationViewService("FOLLOW");
        }
        if (notList.includes(NOTI_POST_CAPTION) || notList.includes(NOTI_STORY_CAPTION)
            || notList.includes(NOTI_POST_TAG) || notList.includes(NOTI_PROFILE_BIO)) {
            updatenotificationViewService("TAG");
        }
        changeNewNotficationTypeList([]);
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Notifications" navigation={navigation} />
            <ScrollView contentContainerStyle={{ paddingBottom: 60 }} refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
                style={{ backgroundColor: theme.colors.backgroundColor }}>
                <View style={{ marginTop: 20 }}>
                    <View style={styles.notiRowBox}>
                        <SubscriptionNotiScreen notificationMenuPress={notificationMenuPress} />
                    </View>
                    <View style={styles.notiRowBox}>
                        <LikesNotiScreen notificationMenuPress={notificationMenuPress} />
                    </View>
                    <View style={styles.notiRowBox}>
                        <CommentsNotiScreen notificationMenuPress={notificationMenuPress} />
                    </View>
                    <View style={styles.notiRowBox}>
                        <FollowNotiScreen notificationMenuPress={notificationMenuPress} />
                    </View>
                    <View style={styles.notiRowBox}>
                        <TagsNotiScreen notificationMenuPress={notificationMenuPress} />
                    </View>


                </View>
            </ScrollView>
        </>
    )
}


export default NotificationScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    tabItemExtra: {
        marginEnd: 18,
    },
    tabBarStyle: {
        overflow: 'hidden',
        backgroundColor: '#E5E5E5',
        // shadowColor: 'black',
        // shadowOpacity: 0,
        // shadowRadius: 0,
        // shadowOffset: {
        //     height: 0,
        //     width: 0,
        // },
        height: 56,
    },
    tabBarIndicatorStyle: {
        backgroundColor: '#FE7C85',
        height: 3,
    },
    tabBarTabStyle: {
        width: 'auto',
        marginHorizontal: 10,
        alignItems: 'center',
        overflow: 'hidden',

    },
    dotBadge: {
        position: 'absolute',
        right: -10, top: 15,
        backgroundColor: 'red',
        borderRadius: 5,
        width: 5,
        height: 5,
        justifyContent: 'center',
        alignItems: 'center'
    },
    notiRowBox: {
        marginHorizontal: 8
    }

})
