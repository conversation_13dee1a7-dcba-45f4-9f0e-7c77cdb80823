import React from 'react'
import { StyleSheet, View } from 'react-native'
import ContentLoader, { Rect, Circle } from 'react-content-loader/native';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';

const PostRowPlaceholder = () => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    return (
        <View style={{ ...defaultStyle.container, }}>
            {Array.from(Array(6), (e, i) => {
                return <ContentLoader
                    key={i}
                    height={240}
                    width={340}
                    speed={theme.dimensions.placeholderSpeed}
                    backgroundColor={theme.colors.placeholderBackgroundColor}
                    foregroundColor={theme.colors.placeholderForegroundColor}
                    viewBox="0 0 340 220">
                    {/* Only SVG shapes */}
                    <Circle cx="31" cy="31" r="15" />
                    <Rect x="58" y="18" rx="2" ry="2" width="140" height="10" />
                    <Rect x="58" y="34" rx="2" ry="2" width="140" height="10" />
                    <Rect x="-4" y="54" rx="2" ry="2" width="400" height="170" />
                </ContentLoader>
            })}
        </View>
    )
}

export default PostRowPlaceholder;

const styles = StyleSheet.create({})
