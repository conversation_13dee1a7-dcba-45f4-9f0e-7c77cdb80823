import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import EntutoTextView from './EntutoTextView';
import useSThemedStyles from '../../theme/useSThemedStyles';

const LoginWithButton = ({ btnText, backColor, btnIcon, iconBackColor, btnTextColor = "", showMatIcon = false,
    matIcon = null, onPress = null, showIcon = true }) => {
    const style = useSThemedStyles(styles);
    return (
        <TouchableOpacity onPress={onPress} style={{ ...style.loginBtnBox, backgroundColor: backColor }}>

            {
                showIcon ?
                    <View style={{ ...style.loginBtnIconBox, backgroundColor: iconBackColor }}>
                        <Image source={btnIcon} style={style.loginBtnIcon}
                            resizeMode='contain' />
                    </View>
                    : null
            }{
                showMatIcon ?
                    <View style={{ ...style.loginBtnIconBox, backgroundColor: iconBackColor }}>
                        {matIcon}
                    </View>
                    : null

            }
            <EntutoTextView style={{ ...style.loginBtnBoxText, ...btnTextColor.length > 0 && { color: btnTextColor } }}>{btnText}</EntutoTextView>

        </TouchableOpacity>
    );
}

export default LoginWithButton

const styles = theme => StyleSheet.create({
    loginBtnBox: {
        minHeight: 60,
        flexDirection: 'row',
        flex: 1,
        borderRadius: 28,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 16,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.20,
        shadowRadius: 1.41,
        elevation: 2,
    },
    loginBtnBoxText: {
        fontSize: theme.calculateFontSize(18),
        color: '#FFFFFF',
        fontWeight: '600'
    },
    loginBtnIconBox: {
        width: 34,
        height: 34,
        borderRadius: 34,
        position: 'absolute',
        left: 12,
        backgroundColor: '#FFFFFF',
        justifyContent: 'center',
        alignItems: 'center',

    },
    loginBtnIcon: {
        width: 23,
        height: 23,
        borderRadius: 23,
    },
})