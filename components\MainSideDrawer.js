import React, { useContext, useEffect, useState } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import {
    DrawerItem,
    DrawerContentScrollView,
} from '@react-navigation/drawer';
import {
    Avatar,
    Drawer,
    Text,
    TouchableRipple,
    Switch,
} from 'react-native-paper';
import { AppStateContext } from '..';
import EntutoTextView from './common/EntutoTextView';
import { HasValueInObject } from '../utils/Utils';
import { UserHandlePrefix } from '../utils/Appconfig';
import Colors from '../constants/Colors';
import ConfirmationPopup from './common/ConfirmationPopup';
import { _clearAllData } from '../utils/AuthLogin';
import EntutoSwitch from './common/EntutoSwitch';

export function MainSideDrawer({ navigation, ...props }) {
    const { isDarkTheme, darkThemeToggleChange, fullUserDetails, changeUserDetails } = useContext(AppStateContext);
    const [isSwitchOn, setIsSwitchOn] = React.useState(false);
    const [showConfirmPopup, setshowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());

    useEffect(() => {
        setIsSwitchOn(isDarkTheme);
    }, [])
    const onToggleSwitch = () => {
        setIsSwitchOn(!isSwitchOn);
        darkThemeToggleChange(!isSwitchOn);
    }
    const CustomDrawerIcon = (props) => {
        return <View>
            <Image
                source={props.iconUrl}
                resizeMode="contain"
                style={{
                    width: props.size,
                    height: props.size,
                    tintColor: isDarkTheme && Colors.iconTintColorDark,
                }}
            />
        </View>
    }
    const logouBtnClick = () => {
        setshowConfirmPopup(true);
        setshowConfirmPopupKey(Math.random())
    }
    const logoutPopupClick = (clickID, data) => {
        if (clickID == "positive") {
            _clearAllData();
            navigation.replace("LoginScreen", {
                ErrorMsg: "",
            });
        }
    }
    const myPrfilePress = () => {
        navigation.navigate("MyProfileScreen");
    }

    return (
        <DrawerContentScrollView {...props}>
            <View
                style={styles.drawerContent}
            >
                <View style={styles.userInfoSection}>
                    <Avatar.Image
                        source={{
                            uri:
                                'https://pbs.twimg.com/profile_images/952545910990495744/b59hSXUd_400x400.jpg',
                        }}
                        size={50}
                    />
                    <EntutoTextView textType="Title" style={styles.title}>{HasValueInObject(fullUserDetails, "_user_display_name")}</EntutoTextView>
                    <EntutoTextView textType="Caption" style={styles.caption}>{UserHandlePrefix}{HasValueInObject(fullUserDetails, "_user_handle")}</EntutoTextView>
                    <View style={styles.row}>
                        <View style={styles.section}>
                            <EntutoTextView textType="Paragraph" style={{ ...styles.paragraph, ...styles.caption }}>
                                202
                            </EntutoTextView>
                            <EntutoTextView textType="Caption" style={styles.caption}>Following</EntutoTextView>
                        </View>
                        <View style={styles.section}>
                            <EntutoTextView textType="Paragraph" style={{ ...styles.paragraph, ...styles.caption }}>
                                159
                            </EntutoTextView>
                            <EntutoTextView textType="Caption" style={styles.caption}>Followers</EntutoTextView>
                        </View>
                    </View>
                </View>
                <Drawer.Section style={styles.drawerSection}>
                    <DrawerItem
                        // icon={({ color, size }) => (
                        //     <CustomDrawerIcon iconUrl={require('../assets/Images/oldicon/my_profile.png')} size={size} />
                        // )}
                        label="My Profile"
                        onPress={() => { myPrfilePress() }}
                    />
                    <DrawerItem
                        // icon={({ color, size }) => (
                        //     <CustomDrawerIcon iconUrl={require('../assets/Images/oldicon/Bookmark.png')} size={size} />
                        // )}
                        label="Bookmarks"
                        onPress={() => { }}
                    />
                    <DrawerItem
                        // icon={({ color, size }) => (
                        //     <CustomDrawerIcon iconUrl={require('../assets/Images/oldicon/Final_Icons2-36.png')} size={size} />
                        // )}
                        label="List"
                        onPress={() => { }}
                    />
                    <DrawerItem
                        // icon={({ color, size }) => (
                        //     <CustomDrawerIcon iconUrl={require('../assets/Images/oldicon/settings.png')} size={size} />
                        // )}
                        label="Settings"
                        onPress={() => { }}
                    />

                </Drawer.Section>
                <Drawer.Section >
                    <DrawerItem
                        // icon={({ color, size }) => (
                        //     <CustomDrawerIcon iconUrl={require('../assets/Images/oldicon/Help_Center_FAQ.png')} size={size} />
                        // )}
                        label="Help"
                        onPress={() => { }}
                    />
                    <DrawerItem
                        // icon={({ color, size }) => (
                        //     <CustomDrawerIcon iconUrl={require('../assets/Images/oldicon/Final_Icons2-36.png')} size={size} />
                        // )}
                        label="FAQ's"
                        onPress={() => { }}
                    />
                </Drawer.Section>
                <Drawer.Section title="Preferences">
                    <TouchableRipple onPress={() => { onToggleSwitch() }}>
                        <View style={styles.preference}>
                            <EntutoTextView>Dark Theme</EntutoTextView>
                            <View pointerEvents="none">
                                <EntutoSwitch value={isSwitchOn} />
                            </View>
                        </View>
                    </TouchableRipple>
                </Drawer.Section>
                <Drawer.Section >
                    <DrawerItem
                        // icon={({ color, size }) => (
                        //     <CustomDrawerIcon iconUrl={require('../assets/Images/oldicon/log_out.png')} size={size} />
                        // )}
                        style={{ backgroundColor: '#00000012' }}
                        labelStyle={{ color: isDarkTheme ? Colors.errorColorDark : Colors.errorColor }}
                        label="Logout"
                        onPress={() => { logouBtnClick(); }}
                    />

                </Drawer.Section>
                {
                    showConfirmPopup &&
                    <ConfirmationPopup
                        visiblePopupKey={showConfirmPopupKey}
                        visiblePopup={showConfirmPopup}
                        title="Confirmation"
                        messagebody="Are you sure you want to logout?"
                        positiveButton="Logout"
                        negativeButton="Cancel"
                        data={{}}
                        popupClick={(clickID, data) => { logoutPopupClick(clickID, data) }}
                    />
                }


            </View>
        </DrawerContentScrollView>
    );
}

const styles = StyleSheet.create({
    drawerContent: {
        flex: 1,
    },
    userInfoSection: {
        paddingLeft: 14,
        marginTop: 10,
    },
    title: {
        marginTop: 20,
        fontWeight: 'bold',
    },
    caption: {
        fontSize: 14,
        lineHeight: 14,
    },
    row: {
        marginTop: 20,
        flexDirection: 'row',
        alignItems: 'center',
    },
    section: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 15,
    },
    paragraph: {
        fontWeight: 'bold',
        marginRight: 3,
    },
    drawerSection: {
        marginTop: 15,
    },
    preference: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingVertical: 12,
        paddingHorizontal: 16,
    },
});