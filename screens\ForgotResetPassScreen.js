import React, { useState } from 'react'
import { StyleSheet, View, Image, ScrollView, TouchableOpacity } from 'react-native'
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import AppLogo from '../assets/Images/Logo.png'
import EntutoEditText from '../components/common/EntutoEditText';
import PrimaryButton from '../components/common/PrimaryButton';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import TopNavigationBar from '../components/TopNavigationBar';
import ServerConnector from '../utils/ServerConnector';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';

import ErrorMessages from '../constants/ErrorMessages';
import CustomStatusBar from '../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import EntutoTextView from '../components/common/EntutoTextView';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import OldLoginBackComponent from '../components/Login/OldLoginBackComponent';

const ForgotResetPassScreen = ({ route, navigation }) => {
    const [newPass, setnewPass] = useState("");
    const [newPassErr, setnewPassErr] = useState("");
    const [confirmPass, setconfirmPass] = useState("");
    const [confirmPassErr, setconfirmPassErr] = useState("");
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const [refreshKey, setrefreshKey] = useState(Math.random())
    const { _data, _emailId, _user_seq, _otp } = route.params;
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const loginTxtClick = () => {
        navigation.replace("LoginScreen", {
            ErrorMsg: "",
        });
    }
    const updatePassButtonClick = () => {
        var isValid = true;
        setnewPassErr("");
        setconfirmPassErr("");
        if (newPass.length === 0) {
            setnewPassErr(ErrorMessages.newPasswordErr);
            isValid = false;
        }
        // if (confirmPass.length === 0) {
        //     setconfirmPassErr("required");
        //     isValid = false;
        // }
        if (newPass != confirmPass) {
            setnewPassErr(ErrorMessages.confirmPasswordErr);
            isValid = false;
        }
        if (isValid) {
            setShowLoading(true);
            appServiceCall();
        }
    }
    function appServiceCall() {
        let hashMap = {
            _action_code: "11:SET_NEW_PASSWORD",
            password: newPass,
            otp: _otp,
            user_seq: _user_seq,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            seterrorMsg("");
            navigation.navigate("ForgotResetPassSuccessScreen", {
                _data: data,
                _emailId: _emailId,
                _user_seq: _user_seq,
                otp: _otp
            })
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_seq) {
                        seterrorMsg(data.data.user_seq);
                        setrefreshKey(Math.random())
                    }
                    if (data.data.password) {
                        setnewPassErr(data.data.password);
                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside} >
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                    // </View>

                    : null
            }
            <View style={{ flex: 1, position: 'relative', backgroundColor: theme.colors.oldLoginBackground }}>
                <CustomProgressDialog
                    showLoading={showLoading}
                />
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View>
                        <View style={defaultStyle.signupTextBox}>
                            <OldLoginBackComponent navigation={navigation} />
                            <EntutoTextView style={defaultStyle.signupText}>Reset Password</EntutoTextView>
                            <EntutoTextView style={defaultStyle.signupSmallText}>{_data.msg}</EntutoTextView>
                        </View>
                        <View style={{ ...defaultStyle.signUpFormBox, }}>
                            <EntutoEditText
                                mode={"flat"}
                                backgroundColor={{ backgroundColor: theme.colors.editTextBackgroundColor }}
                                activeUnderlineColor={true}
                                textColor={"#111111"}
                                placeholderTxt="New Password"
                                value={newPass}
                                onChangeText={text => { setnewPass(text); setnewPassErr("") }}
                                showErrorField={newPassErr.length}
                                errorMsg={newPassErr}
                                secureEntryTxt={true}
                                showPasswordIcon={true}
                            />
                            <EntutoEditText
                                mode={"flat"}
                                backgroundColor={{ backgroundColor: theme.colors.editTextBackgroundColor }}
                                activeUnderlineColor={true}
                                textColor={"#111111"}
                                placeholderTxt="Confirm Password"
                                value={confirmPass}
                                onChangeText={text => { setconfirmPass(text); setconfirmPassErr("") }}
                                showErrorField={confirmPassErr.length}
                                errorMsg={confirmPassErr}
                                secureEntryTxt={true}
                                showPasswordIcon={true}
                            />
                        </View>
                        <View style={{ ...defaultStyle.signUpBtnBox, marginTop: 120 }}>
                            <TouchableOpacity style={{ ...defaultStyle.signUpBtn, backgroundColor: '#111111' }}
                                onPress={() => updatePassButtonClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Update Password</EntutoTextView>
                            </TouchableOpacity>
                            <View style={{ flex: 1 }} />

                        </View>
                    </View>

                </ScrollView>
            </View>

        </>
    )
}

export default ForgotResetPassScreen;

const styles = StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: Colors.bodyTextColor
    },
    signupTxtVal: {
        color: Colors.primaryColor,
    },
    logoBox: {
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 50,
        marginTop: 50,
    },
    forgetPassLogo: {
        height: 82,
        width: 196,
        resizeMode: "contain",
        marginTop: 10,
    },
    loginHeadTxt: {
        marginTop: 40,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },
    resendTxtVal: {
        color: Colors.primaryColor,
        marginBottom: 18,
    },

})
