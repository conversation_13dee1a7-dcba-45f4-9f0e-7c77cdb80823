import { Image, Pressable, StyleSheet, View } from 'react-native'
import React from 'react'
import ProgressiveImage from '../common/ProgressiveImage'
import { hasImageUrlExist } from '../../utils/Utils'
import SubheadingBodyTxt from '../common/SubheadingBodyTxt'
import SubheadingTxt from '../common/SubheadingTxt'
import { UserHandlePrefix } from '../../utils/Appconfig'
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSTheme from '../../theme/useSTheme'
import useSThemedStyles from '../../theme/useSThemedStyles'

const InputTagPeopleProfile = ({ data, peopleRowPress, isChecked, navigation }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    return (
        <>
            <Pressable
                onPress={() => peopleRowPress(data)}
                android_ripple={{ color: theme.colors.pressableRippleColor, borderless: false }}>
                <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>

                    <View style={style.profileImageBox}>
                        <ProgressiveImage
                            style={style.profileImage}
                            source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                            defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                            resizeMode={'cover'}
                        />
                    </View>
                    <View style={style.profileNameBox}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <SubheadingTxt>{data.display_name}</SubheadingTxt>
                            {
                                data.is_verified == "YES" ?
                                    <Image
                                        style={style.verifiedIcon}
                                        source={require('../../assets/Images/icon/verifiedicon.png')}
                                        resizeMode={'contain'}
                                    />
                                    : null
                            }
                        </View>

                        <SubheadingBodyTxt>{UserHandlePrefix}{data.user_handle}</SubheadingBodyTxt>

                    </View>
                </View>

            </Pressable>
        </>
    )
}

export default InputTagPeopleProfile

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: theme.dimensions.sugProfileImgH,
        width: theme.dimensions.sugProfileImgW,
        borderRadius: theme.dimensions.sugProfileImgR,
    },
    profileNameBox: {
        flexDirection: 'column',
        marginLeft: theme.dimensions.sugProfileImgGapTxt,
    },
    verifiedIcon: {
        width: theme.dimensions.sugVerifiedIconW,
        height: theme.dimensions.sugVerifiedIconH,
        marginLeft: theme.dimensions.veritextLeftmargin,
    },
    tickIcon: {
        height: 24,
        width: 24
    }
})