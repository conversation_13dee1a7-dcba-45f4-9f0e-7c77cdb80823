import { Share, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import EntutoTextView from '../common/EntutoTextView';
import PrimaryButton from '../common/PrimaryButton';
import Clipboard from '@react-native-clipboard/clipboard';
import BottomSheetLoader from '../common/BottomSheetLoader';
import dynamicLinks from '@react-native-firebase/dynamic-links';
import { creationOfCopyLink, encryptOnlyNumber } from '../../utils/Utils';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';

const CopyLinkActionView = ({ copyLinkID, copyLinkType, }) => {
    const theme = useSTheme();
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    const [mainLink, setmainLink] = useState("");
    const [isCopied, setisCopied] = useState(false);
    const [showLoading, setshowLoading] = useState(true);
    useEffect(() => {
        CopyLinkCreate()
        // buildCopyLink(copyLinkType, copyLinkID)
        // setTimeout(() => {

        //     let txt = CopyLinkCreate()
        //     setmainLink(txt);
        //     setshowLoading(false);
        // }, 1000);

    }, []);

    const shareInviiteCodePress = () => {
        onShare()
    }
    const onShare = async () => {
        let referralCodeV = mainLink;

        const shareOptions = {
            message: referralCodeV
        }
        try {
            const shareResponse = await Share.open(shareOptions);
        } catch (error) {
            // console.log(error.message);
        }
    };
    const copyBtnPress = async () => {
        let copyLinkTxt = mainLink;

        setisCopied(true);
        Clipboard.setString(copyLinkTxt);
        setTimeout(() => {
            setisCopied(false);
        }, 3000);
    }

    async function CopyLinkCreate() {
        let link = creationOfCopyLink(copyLinkType, copyLinkID);
        setmainLink(link);
        setshowLoading(false);
        return;
        // let link = "";
        if (copyLinkType === "POST") {
            let encryptPostSeq = encryptOnlyNumber(copyLinkID);

            // link = window.location.origin.toString() + _CopyLinkPostURL + encryptPostSeq; // Old Data
            // link = "http://localhost/sotrue-redirection/post/?v=" + encryptPostSeq;// Local
            link = "https://sotrue.co.in/post/?id=" + encryptPostSeq; // Server

        }
        else if (copyLinkType === "PROFILE") {
            let encryptProfileSeq = encryptOnlyNumber(copyLinkID);
            // link = window.location.origin.toString() + _CopyLinkProfileURL + this.state.copyLinkID;//Old Data
            // link = "http://localhost/sotrue-redirection/profile/?v=" + this.state.copyLinkID;// Local
            link = "https://sotrue.co.in/profile/?id=" + encryptProfileSeq;// Server

        }
        else {
            link = "No Link Found!"
        }
        setmainLink(link);
        setshowLoading(false);
        // return link;
    }
    const buildCopyLink = async (type, value) => {
        let redirectType = "post";
        if (type == "PROFILE") {
            redirectType = "profile";
        }
        let seqVal = value;
        let redirectionUrl = 'https://sotrue.co.in/' + redirectType + "?" + seqVal;
        let fallbackUrlAndroid = "https://play.google.com/store/apps/details?id=com.sotruemobileproject";
        let fallbackUrlOthers = "https://sotrue.co.in/";
        const link = await dynamicLinks().buildLink({
            link: redirectionUrl,
            // domainUriPrefix is created in your Firebase console
            domainUriPrefix: 'https://sotrue.co.in',
            // optional setup which updates Firebase analytics campaign
            // "banner". This also needs setting up before hand           
            otherPlatform: {
                fallbackUrl: fallbackUrlOthers,
            },
            android: {
                packageName: "com.sotruemobileproject",
                fallbackUrl: fallbackUrlAndroid,
            },
            ios: {
                appStoreId: '1669489640',
                bundleId: 'club.sotrue.sotrueapp'

            },
            analytics: {
                campaign: 'banner',
                content: 'Click Me',
            },
            // social: {
            //     title: 'Sotrue Application',
            //     descriptionText: 'A Social Application',
            //     imageUrl: "https://www.sotrue.co.in/assets/images/logo.png",
            // },
        });
        // console.log(link)
        setmainLink(link);
        setshowLoading(false);

        // return link;
    }
    return (
        <View style={{ paddingBottom: 20, flex: 1, backgroundColor: theme.colors.backgroundColor }}>
            <View style={{ ...defaultStyle.popupBox, }}>
                <EntutoTextView style={defaultStyle.popupHeadTxt}>Copy Link</EntutoTextView>
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                <View style={style.referCodeBox}>
                    <EntutoTextView style={style.referTxt}>{mainLink}</EntutoTextView>

                    <View style={{ ...style.copyBtn, marginLeft: 'auto', marginRight: 8, }}>
                        <TouchableOpacity onPress={() => copyBtnPress()}>
                            <EntutoTextView style={style.copyBtnTxt}>
                                {isCopied ? "Copied" : "Copy"}
                            </EntutoTextView>
                        </TouchableOpacity>
                    </View>
                </View>
                {/* <View style={{ ...style.orTxtBox, marginTop: 16, }}>
                    <View style={style.orTxtLine} />
                    <View>
                        <EntutoTextView style={{ width: 50, textAlign: 'center' }}>OR</EntutoTextView>
                    </View>
                    <View style={style.orTxtLine} />
                </View>
                <PrimaryButton
                    label="Share Invite Code"
                    style={{ marginVertical: 20, }}
                    uppercase={false}
                    onPress={() => shareInviiteCodePress()} /> */}
            </View>
        </View>
    );
};

export default CopyLinkActionView;

const styles = theme => StyleSheet.create({
    referCodeBox: {
        backgroundColor: theme.colors.backgroundColor,
        borderColor: theme.colors.primaryColor,
        borderWidth: 1,
        borderStyle: 'dashed',
        borderRadius: 1,
        marginTop: 20,
        minHeight: 49,
        alignItems: 'center',
        flexDirection: 'row',
        paddingVertical: 8,

    },
    referTxt: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.copyLinkText),
        fontWeight: '600',
        paddingLeft: 20,
        paddingRight: 80,
    },
    copyBtn: {
        backgroundColor: theme.colors.primaryColor,
        borderRadius: 1,

    },
    copyBtnTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.copyLinkBtnText),
        color: theme.colors.primaryTextColor,
        fontFamily: theme.getFontFamily('bold'),
        paddingVertical: 6,
        paddingHorizontal: 16,

    },
    orTxtLine: {
        height: 0.5,
        flex: 1,
        backgroundColor: '#00000020',
    },
    orTxtBox: {
        flexDirection: 'row',
        alignItems: 'center',
    }
});
