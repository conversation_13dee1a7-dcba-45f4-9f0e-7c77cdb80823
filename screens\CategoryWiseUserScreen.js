import React, { useState, useEffect } from 'react';
import { Image, Keyboard, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { ActivityIndicator } from 'react-native-paper';
import CustomActivityIndicator from '../components/common/CustomActivityIndicator';
import CustomStatusBar from '../components/common/CustomStatusBar';
import HeadingTxt from '../components/common/HeadingTxt';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import SearchProfileRow from '../components/search/SearchProfileRow';
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import { getSecondsBetweenDates } from '../utils/Utils';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const CategoryWiseUserScreen = ({ navigation, route }) => {
    const { categoryValue } = route.params;
    const [serachList, setserachList] = useState([]);
    const [errorMsg, seterrorMsg] = useState("");
    const [errorMsgKey, seterrorMsgKey] = useState(Math.random());
    const [showLoading, setshowLoading] = useState(false);

    const RowsPerPage = 10;
    const [startRecord, setstartRecord] = useState(0);
    const [bottomLoading, setbottomLoading] = useState(false);
    const [isNoDataFound, setisNoDataFound] = useState(false);
    const [bottomReachTime, setbottomReachTime] = useState(new Date());
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    useEffect(() => {
        getSearchResult(0, RowsPerPage);
    }, [])


    const renderSearchRow = ({ item }) => {
        return (
            <SearchProfileRow data={item} navigation={navigation} />
        );
    };
    function getSearchResult(startRecord, rowsPerPage) {
        setstartRecord(startRecord)
        let hashMap = {
            _action_code: "11:DO_SEARCH",
            search_str: categoryValue,
            _start_row: startRecord,
            _rows_page: rowsPerPage,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false);
            setbottomLoading(false);
            if (parseInt(startRecord) == 0) {
                setserachList([...[], ...data.data]);
            }
            else {
                setserachList([...serachList, ...data.data]);
            }
            seterrorMsg("");
            seterrorMsgKey(Math.random())
            setisNoDataFound(false);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                setbottomLoading(false);
                setisNoDataFound(true);
                if (parseInt(startRecord) == 0) {
                    setserachList([]);
                    seterrorMsg(errorMessage);
                    seterrorMsgKey(Math.random());
                }
            }
        });
    }
    const handleEndRefresh = () => {
        if (!isNoDataFound) {
            let currentTime = new Date();
            let diffTime = getSecondsBetweenDates(bottomReachTime, currentTime);
            if (diffTime > 4) {
                let startRec = startRecord + RowsPerPage;
                setbottomLoading(true);
                getSearchResult(startRec, RowsPerPage);
                setbottomReachTime(new Date());
            }

        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title={categoryValue} navigation={navigation} showBorderBottom={false} />
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                <View style={defaultStyle.container}>

                    {
                        showLoading ?
                            <CustomActivityIndicator progress={showLoading} />
                            : null
                    }
                    {
                        errorMsg.length != 0 ?
                            <View style={{ ...defaultStyle.errorBoxOutside, marginTop: 50, minHeight: 100 }} >
                                <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsgKey} />
                            </View>
                            : null
                    }


                </View>
                <FlatList
                    contentContainerStyle={{ paddingBottom: 20 }}
                    data={serachList}
                    renderItem={renderSearchRow}
                    keyExtractor={(item, index) => `${index}`}
                    onEndReached={handleEndRefresh}
                    initialNumToRender={10}

                />
                {
                    bottomLoading ?
                        <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 15 }}>
                            <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={'large'} />
                        </View>
                        : null
                }
            </View>
        </>
    )
}

export default CategoryWiseUserScreen;

const styles = StyleSheet.create({
    searchBarBox: {
        alignItems: "center",
        flexDirection: "row",
        marginTop: 8,
    },
    searchBar: {
        flex: 1,
        flexDirection: "row",
        backgroundColor: '#F2EBE9',
        borderRadius: Dimensions.searchBarRadius,
        alignItems: "center",
    },
    searchIcon: {
        height: Dimensions.searchInputIconH,
        width: Dimensions.searchInputIconH,
        marginLeft: Dimensions.searchInputIconMH,
    },
    searchCrossIcon: {
        height: 15,
        width: 15,
        marginRight: 8,
    },
})
