import React, { useEffect, useState } from 'react'
import { StyleSheet, View, Image, ScrollView, TouchableOpacity } from 'react-native'
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import EntutoTextView from '../components/common/EntutoTextView';
import EntutoEditText from '../components/common/EntutoEditText';
import PrimaryButton from '../components/common/PrimaryButton';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import TopNavigationBar from '../components/TopNavigationBar';
import ServerConnector from '../utils/ServerConnector';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import AppLogo from '../assets/Images/Logo.png';
import CustomStatusBar from '../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import OldLoginBackComponent from '../components/Login/OldLoginBackComponent';

const ForgotPasswordScreen = ({ route, navigation }) => {
    const { navType } = route.params;
    const [emailId, setemailId] = useState("");
    const [emailIdErr, setemailIdErr] = useState("");
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const [refreshKey, setrefreshKey] = useState(Math.random());
    const [headerTitle, setHeaderTitle] = useState("");
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    useEffect(() => {
        let headerTxt = "Forgot your password?";
        if (navType == "RESET_PASS") {
            headerTxt = "Reset your password?";
        }
        setHeaderTitle(headerTxt);
    }, [navType])


    const loginTxtClick = () => {
        navigation.goBack(null);
    }
    const sendButtonClick = () => {
        var isValid = true;
        setemailIdErr("");
        if (emailId.length === 0) {
            setemailIdErr("Please specify email id");
            isValid = false;
        }
        if (isValid) {
            setShowLoading(true);
            appServiceCall();
        }
    }
    function appServiceCall() {
        let hashMap = {
            _action_code: "11:SEND_PASSWORD_OTP",
            email: emailId,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            seterrorMsg("");
            navigation.navigate("ForgotVerifyEmailScreen", {
                _data: data,
                _emailId: emailId,
                _access_key: data._access_key,
                _user_seq: data.data.user_seq
            })
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.email) {
                        setemailIdErr(data.data.email);
                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }
    return (
        <>
            <CustomStatusBar translucent={true} hidden={false} />

            <View style={{ flex: 1, position: 'relative', backgroundColor: theme.colors.oldLoginBackground }}>
                <CustomProgressDialog
                    showLoading={showLoading}
                />
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View>
                        <View style={defaultStyle.signupTextBox}>
                            <OldLoginBackComponent navigation={navigation} />
                            <EntutoTextView style={defaultStyle.signupText}>{headerTitle}</EntutoTextView>
                            <EntutoTextView style={defaultStyle.signupSmallText}>Don’t worry! Please enter the registered email
                                address associated with your account</EntutoTextView>
                        </View>
                        <View style={{ ...defaultStyle.signUpFormBox, marginTop: 80 }}>
                            <EntutoEditText
                                showLeftIcon={false}
                                // leftImage={MailIcon}
                                mode={"flat"}
                                backgroundColor={{ backgroundColor: theme.colors.editTextBackgroundColor }}
                                activeUnderlineColor={true}
                                textColor={"#111111"}
                                placeholderTxt="Email"
                                value={emailId}
                                onChangeText={text => setemailId(text)}
                                showErrorField={emailIdErr.length}
                                errorMsg={emailIdErr}
                            />
                        </View>
                        <View style={{ ...defaultStyle.signUpBtnBox, marginTop: 54 }}>
                            <TouchableOpacity style={{ ...defaultStyle.signUpBtn, backgroundColor: '#111111' }}
                                onPress={() => sendButtonClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Send</EntutoTextView>
                            </TouchableOpacity>
                            <View style={{ flex: 1 }} />
                            {/* <TouchableOpacity style={defaultStyle.signUpBtn}
                                onPress={() => loginTxtClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Login</EntutoTextView>
                            </TouchableOpacity> */}
                        </View>

                    </View>
                </ScrollView>
            </View>
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                    : null
            }
        </>
    )
}

export default ForgotPasswordScreen;

const styles = StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: Colors.bodyTextColor
    },
    signupTxtVal: {
        color: Colors.primaryColor,
    },
    logoBox: {
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 50,
        marginTop: 50,
    },
    forgetPassLogo: {
        height: 82,
        width: 196,
        resizeMode: "contain",
        marginTop: 10,
    },
    loginHeadTxt: {
        marginTop: 40,
        marginBottom: 1,
    },

})
