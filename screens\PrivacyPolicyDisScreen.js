import React, { useState } from 'react'
import { ActivityIndicator, StyleSheet, View } from 'react-native'
import HomeTopNavigationBar from '../components/HomeTopNavigationBar'
import CustomStatusBar from '../components/common/CustomStatusBar';
import { WebView } from 'react-native-webview';
import Dimensions from '../constants/Dimensions';

const PrivacyPolicyDisScreen = ({ navigation }) => {
    const [showLoading, setshowLoading] = useState(true)
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar showBackBtn={true} showBorderBottom={false} title="Privacy Policy" navigation={navigation} />

            <View style={{ minHeight: Dimensions.screenHeight - 70, flex: 1 }}>
                {
                    showLoading ?
                        <ActivityIndicator size={'large'} />
                        : null
                }
                <WebView source={{ uri: 'https://www.sotrue.co.in/privacy_policy_app.html' }}
                    automaticallyAdjustContentInsets={false}
                    androidLayerType="software"
                    onLoadEnd={() => {
                        setshowLoading(false);
                    }} />

            </View>
        </>
    )
}

export default PrivacyPolicyDisScreen

const styles = StyleSheet.create({})
