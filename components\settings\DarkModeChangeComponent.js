import { View } from 'react-native'
import React, { useEffect, useState } from 'react'
import useSTheme from '../../theme/useSTheme';
import { _setAppThemeType } from '../../utils/AuthLogin';
import FormButtonGroup from '../common/FormButtonGroup';

const DarkModeChangeComponent = () => {
    const [screenModeType, setScreenModeType] = useState("DARK");
    const theme = useSTheme();
    const [showLoading, setShowLoading] = useState(false);

    useEffect(() => {
        setScreenModeType(theme.appThemeType)
    }, []);
    const onDarkModeChange = (itemType) => {
        setShowLoading(true);
        setScreenModeType(itemType);
        theme.changeAppTheme(itemType);
        _setAppThemeType(itemType);
        setShowLoading(false);
    }
    return (
        <View>
            <FormButtonGroup
                disabled={showLoading}
                dataList={[
                    { label: "Light", value: "LIGHT" },
                    { label: "Dark", value: "DARK" },
                ]}
                selectedValue={screenModeType}
                onChange={onDarkModeChange}
            />
        </View>
    )
}

export default DarkModeChangeComponent
