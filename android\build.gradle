buildscript {
    ext {
        buildToolsVersion = "33.0.1"
        minSdkVersion = 21
        compileSdkVersion = 33  // Update this from 31 to 34
        targetSdkVersion = 33   
        ndkVersion = "21.4.7075529"
        googlePlayServicesAuthVersion = "20.2.0"
        RNNKotlinVersion = "1.9.0"  
         kotlinVersion = "1.9.0"
         exoPlayerVersion = "2.19.1"
    }
    repositories {
        gradlePluginPortal()
        maven {
            url 'https://maven.google.com'
        }
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.1.3")
        classpath('com.google.gms:google-services:4.3.14')
        classpath("com.facebook.react:react-native-gradle-plugin:0.73.3")
         classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven {
            url 'https://maven.google.com'
        }
        mavenCentral()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url "$rootDir/../node_modules/react-native/android"
            content {
                includeGroupByRegex("com\\.facebook.*")
                includeGroupByRegex("com\\.android.*")
                includeGroup("org.webkit")
            }
        }
        maven {
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        maven { 
            url 'https://www.jitpack.io' 
        }
    }
}