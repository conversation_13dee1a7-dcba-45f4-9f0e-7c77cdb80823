import React, { useEffect, useState } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import ServerConnector from '../../utils/ServerConnector';
import { decodeHtmlEntitessData, hasImageUrlExist } from '../../utils/Utils';
import EntutoTextView from '../common/EntutoTextView';
import LikeBtnComponent from '../common/LikeBtnComponent';
import ProgressiveImage from '../common/ProgressiveImage';
import SubheadingBodyTxt from '../common/SubheadingBodyTxt';
import SubheadingTxt from '../common/SubheadingTxt';
import useSThemedStyles from '../../theme/useSThemedStyles';
const SubCommentRow = ({ subdata, navigation, postSeq, SubCommentRowPress, selfComment = false }) => {

    const style = useSThemedStyles(styles);
    const [likeSubComment, setlikeSubComment] = useState(false);
    const [selfSubComment, setselfSubComment] = useState(false);
    const [subCmntTxt, setsubCmntTxt] = useState("")
    useEffect(() => {
        if (subdata.length != 0) {
            let decodeTxt = decodeHtmlEntitessData(subdata.sub_comment)
            setsubCmntTxt(decodeTxt)
        }
    }, [subdata])
    useEffect(() => {
        if (subdata.self_sub_like == "YES") {
            setlikeSubComment(true);
        }
        else {
            setlikeSubComment(false);
        }
        if (subdata.self_sub_comment == "YES") {
            setselfSubComment(true);
        }
        else {
            setselfSubComment(false);
        }
    }, [])

    const likeSubBtnPress = () => {
        if (likeSubComment) {
            removeCommentLike();
        }
        else {
            submitCommentLike();
        }
    }
    const deleteSubCommmentRow = () => {
        SubCommentRowPress("DELETE_U", { commentSeq: subdata.sub_comment_seq });
    }
    function submitCommentLike() {
        let hashMap = {
            _action_code: "11:SUBMIT_COMMENT_LIKE",
            comment_seq: subdata.sub_comment_seq,
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setlikeSubComment(true);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }
    function removeCommentLike() {
        let hashMap = {
            _action_code: "11:REMOVE_COMMENT_LIKE",
            comment_seq: subdata.sub_comment_seq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setlikeSubComment(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }
    if (selfComment) {

        return (
            <View style={{ ...style.cardView, marginEnd: 30, marginTop: 20 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginStart: 'auto' }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginEnd: 10 }}>
                        <EntutoTextView style={style.commentProfileName}>{subdata.sub_comment_full_name}</EntutoTextView>
                        {
                            subdata.sub_is_verified == "YES" ?
                                <Image
                                    style={style.verifiedIcon}
                                    source={require('../../assets/Images/icon/verifiedicon.png')}
                                    resizeMode={'contain'}
                                />
                                : null
                        }
                    </View>
                    <View style={style.profileImageBox}>
                        <ProgressiveImage
                            style={style.profileImage}
                            source={hasImageUrlExist(subdata.sub_comment_profile_pic) ? { uri: subdata.sub_comment_profile_pic } : null}
                            defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                            resizeMode={'cover'}
                        />

                    </View>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end', }}>
                    <EntutoTextView style={{ ...style.commentText, textAlign: 'right', }}>{subCmntTxt}</EntutoTextView>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginStart: 'auto', marginTop: 8 }}>
                    <View style={{ marginEnd: 15 }}>
                        <LikeBtnComponent likeButtonPress={likeSubBtnPress} isLike={likeSubComment} style={style.likeIcon} />
                    </View>
                    <View>
                        {
                            selfSubComment ?
                                <TouchableOpacity onPress={() => deleteSubCommmentRow()} >
                                    <EntutoTextView style={style.commentReBtn}>Delete</EntutoTextView>
                                </TouchableOpacity>
                                : null
                        }
                    </View>
                </View>
                {/* <View style={{ marginLeft: 'auto', }}>
                            <SubheadingBodyTxt>{subdata.sub_comment_time}</SubheadingBodyTxt>
                        </View> */}

            </View>
        )
    }
    else {
        return (
            <View style={{ ...style.cardView, marginTop: 20 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <View style={style.profileImageBox}>
                        <ProgressiveImage
                            style={style.profileImage}
                            source={hasImageUrlExist(subdata.sub_comment_profile_pic) ? { uri: subdata.sub_comment_profile_pic } : null}
                            defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                            resizeMode={'cover'}
                        />
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginStart: 10 }}>
                        <EntutoTextView style={style.commentProfileName}>{subdata.sub_comment_full_name}</EntutoTextView>
                        {
                            subdata.sub_is_verified == "YES" ?
                                <Image
                                    style={style.verifiedIcon}
                                    source={require('../../assets/Images/icon/verifiedicon.png')}
                                    resizeMode={'contain'}
                                />
                                : null
                        }
                    </View>
                </View>
                <View>
                    <EntutoTextView style={{ ...style.commentText, textAlign: 'left' }}>{subCmntTxt}</EntutoTextView>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 10 }}>

                    {
                        selfSubComment ?
                            <TouchableOpacity onPress={() => deleteSubCommmentRow()}
                                style={{ marginEnd: 10, }}>
                                <EntutoTextView style={style.commentReBtn}>Delete</EntutoTextView>
                            </TouchableOpacity>
                            : null
                    }
                    <View >
                        <LikeBtnComponent likeButtonPress={likeSubBtnPress} isLike={likeSubComment} style={style.likeIcon} />

                    </View>
                </View>
                {/* <View style={{ marginLeft: 'auto', }}>
                            <SubheadingBodyTxt>{subdata.sub_comment_time}</SubheadingBodyTxt>
                        </View> */}
            </View>
        )
    }
}

export default SubCommentRow;

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'column',
        marginVertical: 8,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: 21,
        width: 21,
        borderRadius: 21,
    },
    profileNameBox: {
        flex: 1,
        flexDirection: 'column',
        //marginHorizontal: 10,
        marginLeft: 10,
        marginRight: 8,
    },
    verifiedIcon: {
        width: 8,
        height: 8,
        marginLeft: theme.dimensions.veritextLeftmargin,
        // position: 'absolute',
        // right: -4,
        // top: 10,
    },
    likebtn: {
        paddingHorizontal: 3,
        paddingVertical: 2,
    },
    likeIcon: {
        width: 15,
        height: 15,
    },
    commentReBtn: {
        color: theme.colors.primaryColor,
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
        marginRight: 10,

    },
    commentText: {
        textAlign: 'right',
        fontSize: theme.calculateFontSize(theme.dimensions.commentText),
        marginTop: 10,
        marginEnd: 15,
    },
    commentProfileName: {
        color: theme.colors.primaryColor,
        textAlign: 'right',
        fontSize: theme.calculateFontSize(theme.dimensions.commentProfileText),
    }


})
