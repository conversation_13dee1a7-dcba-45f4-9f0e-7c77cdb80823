import React, { useRef, useState } from 'react'
import { <PERSON><PERSON>, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import ActionSheet from 'react-native-actions-sheet';
import ChangePassword from '../components/accountsetting/ChangePassword';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import CustomSnackbar from '../components/common/CustomSnackbar';
import CustomStatusBar from '../components/common/CustomStatusBar';
import EntutoSwitch from '../components/common/EntutoSwitch';
import EntutoTextView from '../components/common/EntutoTextView';
import HeadingTxt from '../components/common/HeadingTxt';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import ListItem from '../components/ListItem';
import ErrorMessages from '../constants/ErrorMessages';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';

const PrivacySafetyScreen = ({ route, navigation }) => {
    const [showFanCount, setshowFanCount] = useState(false);
    const [showMediaCount, setshowMediaCount] = useState(false);
    const [enableComment, setenableComment] = useState(false);
    const [enableWatermark, setenableWatermark] = useState(false);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [snackBarType, setsnackBarType] = useState("FAILED");

    const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);

    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(true);

    const changePassRef = useRef(null);

    React.useEffect(() => {
        getPrivacySettingsService();
    }, []);

    function getPrivacySettingsService() {
        let hashMap = {
            _action_code: "11:GET_PRIVACY_SETTINGS",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let show_fan_count = false;
            if (data.data[0].show_fan_count === "YES") {
                show_fan_count = true;
            }
            setshowFanCount(show_fan_count);
            let show_media_count = false;
            if (data.data[0].show_media_count === "YES") {
                show_media_count = true;
            }
            setshowMediaCount(show_media_count);
            let enable_comment = false;
            if (data.data[0].enable_comment === "YES") {
                enable_comment = true;
            }
            setenableComment(enable_comment);
            let enable_watermark = false;
            if (data.data[0].enable_watermark === "YES") {
                enable_watermark = true;
            }
            setenableWatermark(enable_watermark);

            setShowLoading(false)
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                seterrorMsg(errorMessage);
            }
        });
    }

    const onShowFanCountChange = () => {
        setshowFanCount(!showFanCount);
        setdisableUpdateBtn(false);
    }
    const onShowMediaCountChange = () => {
        setshowMediaCount(!showMediaCount);
        setdisableUpdateBtn(false);
    }
    const onEnableCommentChange = () => {
        setenableComment(!enableComment);
        setdisableUpdateBtn(false);
    }
    const onEnableWatermarkChange = () => {
        setenableWatermark(!enableWatermark);
        setdisableUpdateBtn(false);
    }
    React.useEffect(
        () =>
            navigation.addListener('beforeRemove', (e) => {
                if (disableUpdateBtn) {
                    // If we don't have unsaved changes, then we don't need to do anything
                    return;
                }

                // Prevent default behavior of leaving the screen
                e.preventDefault();

                // Prompt the user before leaving the screen
                Alert.alert(
                    ErrorMessages.discardChangesTitle,
                    ErrorMessages.discardChangesMsg,
                    [
                        { text: "Don't leave", style: 'cancel', onPress: () => { } },
                        {
                            text: 'Discard',
                            style: 'destructive',
                            // If the user confirmed, then we dispatch the action we blocked earlier
                            // This will continue the action that had triggered the removal of the screen
                            onPress: () => navigation.dispatch(e.data.action),
                        },
                    ]
                );
            }),
        [navigation, disableUpdateBtn]
    );
    const updateBtnPress = () => {
        setShowLoading(true);
        updateUserProfilePrivacy();
    }
    function updateUserProfilePrivacy() {

        let showFanCountV = "NO";
        if (showFanCount) {
            showFanCountV = "YES"
        }
        let showMediaCountV = "NO";
        if (showMediaCount) {
            showMediaCountV = "YES"
        }
        let enableCommentV = "NO";
        if (enableComment) {
            enableCommentV = "YES"
        }
        let enableWatermarkV = "NO";
        if (enableWatermark) {
            enableWatermarkV = "YES"
        }

        let hashMap = {
            _action_code: "11:UPDATE_PRIVACY_SETTINGS",
            show_fan_count: showFanCountV,
            show_media_count: showMediaCountV,
            enable_comment: enableCommentV,
            enable_watermark: enableWatermarkV,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            // getUserProfileService();
            setSnackbarMsg(data.msg);
            setdisplaySnackbar(true);
            setsnackBarType("SUCCESS");
            setrefreshSnackBar(Math.random());
            setdisableUpdateBtn(true)
            setShowLoading(false);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.show_fan_count) {
                            setSnackbarMsg(data.data.show_fan_count);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.show_media_count) {
                            setSnackbarMsg(data.data.show_media_count);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.enable_comment) {
                            setSnackbarMsg(data.data.enable_comment);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.enable_watermark) {
                            setSnackbarMsg(data.data.enable_watermark);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            return;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    setSnackbarMsg(errorMessage);
                    setdisplaySnackbar(true);
                    setsnackBarType("FAILED");
                    setrefreshSnackBar(Math.random());
                }
            }
        });
    }

    const blockedAccPress = () => {
        navigation.navigate("BlockedAccountScreen");
    }
    const restrictAccPress = () => {
        navigation.navigate("RestrictedAccountScreen");
    }
    const changePasswordPress = () => {
        changePassRef.current?.show();
    }
    const changePasswordActionClick = (clickId) => {
        if (clickId == "negetive") {
            changePassRef.current?.hide();
        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Privacy" showBackBtn={true} showBorderBottom={false} navigation={navigation}
                showTopButton={true}
                buttonComponent={<TouchableOpacity
                    onPress={() => updateBtnPress()}
                    disabled={disableUpdateBtn}
                ><EntutoTextView style={{ ...defaultStyle.postBtn, opacity: disableUpdateBtn ? 0.4 : 1 }}>UPDATE</EntutoTextView></TouchableOpacity>}
            />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside}>
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                    // </View>
                    : null
            }
            <ScrollView
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                style={{ backgroundColor: theme.colors.backgroundColor }}>
                <View style={defaultStyle.container}>

                    <View style={{ marginTop: 15, marginBottom: 5, }}>
                        <HeadingTxt>Profile Related</HeadingTxt>
                    </View>
                    <View style={style.boxWithSwitchBox}>
                        <View style={{ flexDirection: 'row' }}>
                            <EntutoTextView style={style.boxWithSwitchTxt}>Show Fan Count</EntutoTextView>
                            <EntutoSwitch value={showFanCount} onValueChange={() => onShowFanCountChange()} />
                        </View>
                        <View style={defaultStyle.listUnderLineView}>
                            <EntutoTextView style={defaultStyle.listUnderLineTxt}>
                                So can all see how many fans you have!
                            </EntutoTextView>
                        </View>
                    </View>

                    {/* <View style={style.boxWithSwitchBoxDivider} /> */}
                    <View style={style.boxWithSwitchBox}>
                        <View style={{ flexDirection: 'row' }}>
                            <EntutoTextView style={style.boxWithSwitchTxt}>Show Media Count</EntutoTextView>
                            <EntutoSwitch value={showMediaCount} onValueChange={() => onShowMediaCountChange()} />
                        </View>
                        <View style={defaultStyle.listUnderLineView}>
                            <EntutoTextView style={defaultStyle.listUnderLineTxt}>
                                So we can see how much content you have posted!
                            </EntutoTextView>
                        </View>
                    </View>
                    <View style={style.boxWithSwitchBoxDivider} />

                    <View style={{ marginTop: 30, marginBottom: 5, }}>
                        <HeadingTxt>Post Related</HeadingTxt>
                    </View>
                    <View style={style.boxWithSwitchBox}>
                        <View style={{ flexDirection: 'row' }}>
                            <EntutoTextView style={style.boxWithSwitchTxt}>Enable Comment</EntutoTextView>
                            <EntutoSwitch value={enableComment} onValueChange={() => onEnableCommentChange()} />
                        </View>
                        <View style={defaultStyle.listUnderLineView}>
                            <EntutoTextView style={defaultStyle.listUnderLineTxt}>
                                So your fans can tell you what they think!
                            </EntutoTextView>
                        </View>
                    </View>
                    <View style={style.boxWithSwitchBox}>
                        <View style={{ flexDirection: 'row' }}>
                            <EntutoTextView style={style.boxWithSwitchTxt}>Enable Watermark</EntutoTextView>
                            <EntutoSwitch value={enableWatermark} onValueChange={() => onEnableWatermarkChange()} />
                        </View>
                    </View>
                    <View style={style.boxWithSwitchBoxDivider} />
                    <View style={{ marginTop: 30, marginBottom: 5, }}>
                        <HeadingTxt>Safety Related</HeadingTxt>
                    </View>
                    <TouchableOpacity onPress={() => changePasswordPress()}>
                        <ListItem label="Change Password"
                            textstyle={style.boxWithSwitchTxt} />
                    </TouchableOpacity>
                    <View style={style.boxWithSwitchBoxDivider} />
                    <View style={{ marginTop: 30, marginBottom: 5, }}>
                        <HeadingTxt>Interaction Related</HeadingTxt>
                    </View>
                    {/* <TouchableOpacity onPress={() => restrictAccPress()}>
                        <ListItem label="Restricted Accounts"
                            textstyle={style.boxWithSwitchTxt}
                            bottomTxt="Protect yourself from unwanted interactions without losing followers or subscribers."
                            showBottomtxt={true} />
                    </TouchableOpacity> */}
                    <TouchableOpacity onPress={() => blockedAccPress()}>
                        <ListItem label="Blocked Accounts"
                            textstyle={style.boxWithSwitchTxt} />
                    </TouchableOpacity>
                </View>
            </ScrollView>
            <ActionSheet ref={changePassRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled
                    onMomentumScrollEnd={() => {
                        changePassRef.current?.handleChildScrollEnd();
                    }}
                    style={{ backgroundColor: theme.colors.backgroundColor }}
                >
                    <ChangePassword refVal={changePassRef} changePasswordActionClick={(clickId, obj) => changePasswordActionClick(clickId, obj)} />
                </ScrollView>

            </ActionSheet >
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />
        </>
    )
}

export default PrivacySafetyScreen;

const styles = theme => StyleSheet.create({
    boxWithSwitchBox: {
        flexDirection: 'column',
        marginVertical: 15,

    },
    boxWithSwitchTxt: {
        flex: 1,
        color: theme.colors.switchTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.boxWithSwitchTxt),
        fontWeight: '600'
    },
    boxWithSwitchBoxDivider: {
        flex: 1,
        borderWidth: 0.5,
        borderColor: theme.colors.switchTextColor,
        opacity: 0.2
    }
})
