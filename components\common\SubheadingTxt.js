import React, { useContext } from 'react'
import { StyleSheet, } from 'react-native'
import EntutoTextView from './EntutoTextView';
import useSTheme from '../../theme/useSTheme';

const SubheadingTxt = props => {
    const theme = useSTheme();
    return <EntutoTextView style={{
        ...styles.default,
        color: theme.colors.mainHeadingColor,
        fontSize: theme.calculateFontSize(theme.dimensions.SubheadingTxt),
        ...props.style
    }}>{props.children}</EntutoTextView>
}

export default SubheadingTxt;

const styles = StyleSheet.create({
    default: {
        fontWeight: "bold"
    }
})
