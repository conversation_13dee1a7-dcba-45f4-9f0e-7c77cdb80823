import { Image, ImageBackground, StatusBar, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import Dimensions from '../constants/Dimensions'
import { TouchableOpacity } from 'react-native'
import LinearGradient from 'react-native-linear-gradient';
import LikeIcon from '../assets/Images/icon/myprofile_setting.png'
import EntutoTextView from '../components/common/EntutoTextView';

const SinglePostNScreen = ({ route, navigation }) => {
    const backBtnPress = () => {
        navigation.goBack(null);
    }
    return (
        <>
            <StatusBar hidden={true} />
            <View style={styles.container}>
                <View style={styles.mediaBackBtnBox}>
                    <TouchableOpacity onPress={() => backBtnPress()}>
                        <Image
                            style={styles.mediaBackBtn}
                            source={require('../assets/Images/icon/Arrow.png')}
                            resizeMode='contain'
                        />
                    </TouchableOpacity>
                </View>

                <View>
                    <ImageBackground source={require('../assets/Images/default_image.jpg')}
                        style={styles.mediaImage} resizeMode="cover" >
                        <LinearGradient
                            colors={[
                                '#000000',
                                'transparent',
                                '#000000',
                                '#000000'
                            ]}
                            locations={[0, 0.15, 0.98, 1]}
                            style={styles.linearGradient}
                        />
                    </ImageBackground>
                </View>
                <View style={styles.bottomActionView}>
                    <View style={styles.likeCmntBox}>
                        <View style={styles.actionIconBox}>
                            <Image source={LikeIcon} style={styles.actionIcon} resizeMode='contain' />
                            <EntutoTextView style={styles.actionCountTxt}>369</EntutoTextView>
                        </View>
                        <View style={styles.actionIconBox}>
                            <Image source={LikeIcon} style={styles.actionIcon} resizeMode='contain' />
                            <EntutoTextView style={styles.actionCountTxt}>456</EntutoTextView>
                        </View>
                    </View>

                    <View style={styles.actionIconBox}>
                        <Image source={LikeIcon} style={styles.actionIcon} resizeMode='contain' />
                    </View>
                </View>

            </View>
        </>

    )
}

export default SinglePostNScreen

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#000000',
        height: Dimensions.screenHeight,
        width: Dimensions.screenWidth,
        position: 'relative',
        borderWidth: 1,
        borderColor: '#707070'
    },
    mediaBackBtnBox: {
        position: 'absolute',
        top: 52,
        left: 40,
        zIndex: 3
    },
    mediaBackBtn: {
        height: 20,
        width: 20,
        marginEnd: 5,
        opacity: 0.8,
        zIndex: 3
    },
    mediaImage: {
        width: Dimensions.screenWidth,
        height: Dimensions.screenHeight,
        backgroundColor: '#000',
        flex: 1,
        zIndex: 2
    },
    bottomActionView: {
        minHeight: 56,
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 3,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    linearGradient: {
        width: Dimensions.screenWidth,
        height: Dimensions.screenHeight,
        position: 'absolute'
    },
    actionIconBox: {
        flexDirection: 'row',
        paddingStart: 30,
        paddingEnd: 24,
        alignItems: 'center',
    },
    actionIcon: {
        width: 20,
        height: 20,
    },
    actionCountTxt: {
        fontSize: 16,
        color: '#FFFFFF',
        marginStart: 6
    },
    likeCmntBox: {
        flex: 3,
        flexDirection: 'row',
        alignItems: 'center',
    }
})