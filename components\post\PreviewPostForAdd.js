import { Image, ImageBackground, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext } from 'react'
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import useDefaultStyle from '../../theme/useDefaultStyle';
import ProgressiveImage from '../common/ProgressiveImage';
import LikeBtnComponent from '../common/LikeBtnComponent';
import DescriptionCaptionStyle from '../common/DescriptionCaptionStyle';
import EntutoTextView from '../common/EntutoTextView';
import { getValueFromReactions, hasImageUrlExist } from '../../utils/Utils';
import LinearGradient from 'react-native-linear-gradient';
import { AppStateContext } from '../..';
import ProfileImagePlaceholder from '../../assets/Images/full_user_image_place_holder.png';
import VerifiedIcon from '../../assets/Images/icon/verifiedicon.png';
import LIKE_ICON from '../../assets/Images/icon/like_icon.png';
import BOOKMARK_ICON from '../../assets/Images/icon/bookmark.png';
import COMMENT_ICON from '../../assets/Images/icon/comment.png'
import SHARE_ICON from '../../assets/Images/icon/share_icon.png';
import PlayBtnIcon from '../../assets/Images/icon/play_btn.png';


const PreviewPostForAdd = ({
    postFile = null,
    postType = "IMAGE",
    description = "",
    selectedReactions = [],
    navigation,
}) => {
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const { defaultStyle } = useDefaultStyle();
    const { fullUserDetails, changeUserDetails } = useContext(AppStateContext);

    const viewMediaBtnPress = () => {
        if (postType == "IMAGE") {
            showImage();
        }
        else if (postType == "VIDEO") {
            playVideo();
        }
    }
    const showImage = () => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: postFile
        });
    }
    const playVideo = () => {
        navigation.navigate("VideoDisplayScreen", {
            mediaUri: postFile,
            thumbnailUri: null
        })

    }
    const ImageOverlayNew = () => {
        return <LinearGradient
            colors={[
                '#000000',
                'transparent',
                'transparent',
                '#000000'
            ]}
            locations={[0, 0.18, 0.8, 0.97]}
            style={style.NSSLinearGradient}
        />
    }
    return (
        <View style={style.postContainer}>
            <View style={{ ...style.postCard, }}>
                <View style={{ ...style.postCardHeader, ...style.newPostHeader }}>
                    {/* <View style={style.headerImageBox}>
                        <ProgressiveImage
                            style={style.headerImage}
                            source={hasImageUrlExist(fullUserDetails._profile_picture) ? { uri: fullUserDetails._profile_picture } : null}
                            defaultImageSource={ProfileImagePlaceholder}

                        />

                    </View> */}
                    {/* <View style={style.headerTextBox}>

                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <EntutoTextView style={style.headerDisplayName}>{fullUserDetails._user_display_name}</EntutoTextView>
                            {
                                fullUserDetails._is_profile_verified == "YES" ?
                                    <Image
                                        style={style.verifiedIcon}
                                        source={VerifiedIcon}
                                        resizeMode={'contain'}
                                    />
                                    :
                                    null
                            }
                        </View>
                    </View> */}
                    {/* <View style={style.headerOptionIconBox}>
                        <Image
                            style={{ ...style.postActionCommentIcon, tintColor: "#FFF" }}
                            source={BOOKMARK_ICON}
                            resizeMode="contain"
                        />
                    </View> */}
                </View>
                <View style={{ ...style.postImageBox, position: 'relative' }}>
                    <View style={{ flex: 1, flexDirection: 'column', width: '100%', position: 'relative' }}>
                        <View style={{ ...style.postImageContainer, position: 'relative' }}>
                            <View>
                                <TouchableOpacity disabled onPress={() => viewMediaBtnPress()}>
                                    <ImageBackground
                                        source={hasImageUrlExist(postFile) ? { uri: postFile } : null}
                                        style={style.postImageContainerImg} >
                                        {/* <ImageOverlayNew /> */}
                                        {
                                            postType == "VIDEO" ?
                                                <View style={style.playBtnBox}>
                                                    <Image
                                                        style={style.playBtnBoxIcon}
                                                        source={PlayBtnIcon}
                                                        resizeMode="cover"
                                                    />
                                                </View>
                                                : null
                                        }

                                    </ImageBackground>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>



                </View>
                <View style={style.mainProfileBoxContainer}>
                    <View style={style.postActionIconBox}>
                        {
                            selectedReactions.map((item, i) => {
                                const dataObj = getValueFromReactions(item);
                                if (dataObj != null) {
                                    return <View style={{ ...style.postActionTextIconBox, paddingVertical: 11, }}
                                        key={i}>
                                        <Image
                                            style={style.postActionCommentIcon}
                                            source={dataObj.icon}
                                            resizeMode="contain"
                                        />
                                    </View>
                                }

                            })
                        }
                        <View style={{ ...style.postActionTextIconBox, }}>
                            <Image
                                style={style.postActionCommentIcon}
                                source={LIKE_ICON}
                            />

                        </View>
                        <View style={{ ...style.postActionTextIconBox, }}>
                            <Image
                                style={style.postActionCommentIcon}
                                source={COMMENT_ICON}
                                resizeMode="contain"
                            />
                        </View>
                        <View style={style.postActionTextIconBox}>
                            <Image
                                style={{ ...style.postActionCommentIcon, width: 24, height: 24 }}
                                source={SHARE_ICON}
                                resizeMode="contain" />
                        </View>

                    </View>
                    <View style={style.bellowProfileBox}>
                        <View style={style.nameWithFollowBox}>
                            <EntutoTextView style={style.bellowProfileName}>{fullUserDetails._user_display_name}</EntutoTextView>
                            <View style={style.bellowFollowBtn}>
                                <EntutoTextView style={style.bellowFollowBtnText}>FOLLOW</EntutoTextView>
                            </View>
                        </View>
                        <View style={style.postActionTextIconBox}>
                            <Image
                                style={{ ...style.postActionCommentIcon, marginTop: -6 }}
                                source={BOOKMARK_ICON}
                                resizeMode="contain" />
                        </View>

                    </View>
                    <View style={style.bellowDescription}>
                        <DescriptionCaptionStyle
                            validHandleList={[]}
                            mainText={description}
                            mainTextStyle={style.bellowCommentText}
                            highlightStyle={defaultStyle.boldTagTxt}
                        />
                    </View>


                </View>

            </View >
        </View>
    )
}

export default PreviewPostForAdd

const styles = theme => StyleSheet.create({
    postCard: {
        flexDirection: "column",
        position: 'relative',
    },
    postCardHeader: {
        flexDirection: "row",
        flex: 1,
        alignItems: 'center',
        paddingHorizontal: 4,
    },
    newPostHeader: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 999,
        backgroundColor: '#00000001',
        minHeight: 64,
        paddingTop: 8
    },
    headerImageBox: {
        height: 31.5,
        width: 31.5,
        marginRight: 10,
        borderRadius: 31.5,
        marginLeft: 18,

    },
    headerImage: {
        height: 31.5,
        width: 31.5,
        borderRadius: 31.5
    },
    headerTextBox: {
        flexDirection: 'column',
        alignContent: 'center'
    },
    headerDisplayName: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.postProfileNameText),//12
        color: "#FFFFFF",//#43180B
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
        flexDirection: 'row',
    },
    verifiedIcon: {
        width: 13,
        height: 13,
        marginLeft: 8,
    },
    headerOptionIconBox: {
        marginLeft: 'auto',
        flexDirection: 'row',
        alignItems: 'center',
        marginEnd: 16
    },
    postImageBox: {
        flex: 1,
        position: 'relative',
    },
    postImageContainer: {
        width: '100%',
        minHeight: 450,
        maxHeight: '100%',
        resizeMode: 'cover',
        position: 'relative',
        backgroundColor: '#CCC',
    },
    postImageContainerImg: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
        alignItems: 'center',
        justifyContent: 'center',
    },
    playBtnBox: {
        height: 63,
        width: 63,
        position: 'absolute',
        top: '43%',
        left: '43%',
        backgroundColor: '#00000080',
        borderRadius: 15,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.50,
        shadowRadius: 1.41,


        // elevation: 2,
    },
    playBtnBoxIcon: {
        width: 34,
        height: 40,
        // tintColor: '#000',
        backgroundColor: 'transparent'
    },
    postActionIconBox: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        marginVertical: 8,

    },
    postActionTextIconBox: {
        flexDirection: 'column',
        // marginRight: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    postActionCommentIcon: {
        width: 32,
        height: 32,
        tintColor: theme.colors.postInActiveIconColor,
    },
    mainProfileBoxContainer: {
        minHeight: 40,
        backgroundColor: theme.colors.backgroundColor,
        paddingVertical: 14,
        paddingTop: 0,
    },
    bellowProfileBox: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingTop: 7,
        paddingHorizontal: 14,
    },
    bellowDescription: {
        paddingTop: 10,
        paddingHorizontal: 14
    },
    nameWithFollowBox: {
        flexDirection: 'row',
    },
    bellowProfileName: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.postProfileNameText),
        color: theme.colors.primaryTextColor,
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
    },
    bellowFollowBtn: {
        height: 20,
        paddingHorizontal: 10,
        paddingVertical: 2,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: theme.colors.primaryTextColor,
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 16,
        flexDirection: 'row',
    },
    bellowFollowBtnText: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.postFollowBtnText),
        color: theme.colors.primaryTextColor,
    },
    bellowCommentText: {
        marginBottom: 12,
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSizeNew(theme.dimensions.postDescText),

    },
    NSSLinearGradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },


})