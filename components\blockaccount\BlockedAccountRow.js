import React, { useRef } from 'react'
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import ProgressiveImage from '../common/ProgressiveImage';
import SubheadingTxt from '../common/SubheadingTxt';
import SubheadingBodyTxt from '../common/SubheadingBodyTxt';
import EntutoTextView from '../common/EntutoTextView';
import { hasImageUrlExist } from '../../utils/Utils';
import { UserHandlePrefix } from '../../utils/Appconfig';
import ActionSheet from 'react-native-actions-sheet';
import UnBlockUnRestrictActionView from '../profile/UnBlockUnRestrictActionView';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';

const BlockedAccountRow = ({ navigation, data, isRestrict, ...props }) => {
    const unblockUnresSheetRef = useRef(null);

    const unblockBtnPress = () => {
        unblockUnresSheetRef.current?.show();
    }
    const unblockUnrestrictPress = (clickId, obj) => {
        unblockUnresSheetRef.current?.hide();
        if (clickId == "close") {
            props.rowOptionClick("close", {});
        }
    }
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
            <View style={style.profileImageBox}>
                <ProgressiveImage
                    style={style.profileImage}
                    source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                    defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                    resizeMode={'cover'}
                />


            </View>
            <View style={style.profileNameBox}>
                {/* <TouchableOpacity onPress={() => navigation.navigate('OthersProfileScreen', {
                    profileSeq: data.restricted_profile,
                })}> */}
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <SubheadingTxt>{data.display_name}</SubheadingTxt>
                    {
                        data.is_verified == "YES" ?
                            <Image
                                style={style.verifiedIcon}
                                source={require('../../assets/Images/icon/verifiedicon.png')}
                                resizeMode={'cover'}
                            />
                            : null
                    }
                </View>

                <SubheadingBodyTxt>{UserHandlePrefix}{data.user_handle}</SubheadingBodyTxt>
                {/* </TouchableOpacity> */}
            </View>
            <View style={{ marginLeft: 'auto' }}>
                <TouchableOpacity
                    onPress={() => unblockBtnPress()}
                    style={{ ...style.btn, backgroundColor: theme.colors.submitBtnBackground, marginEnd: 15 }}>
                    <EntutoTextView style={{ ...style.btnText, color: theme.colors.submitBtnText }}>
                        {
                            isRestrict ? "Un-Restrict" : "Un-Block"
                        }
                    </EntutoTextView>
                </TouchableOpacity>
            </View>
            <ActionSheet ref={unblockUnresSheetRef}
                statusBarTranslucent

                bounciness={4}
                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        unblockUnresSheetRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <UnBlockUnRestrictActionView profileSeq={data.restricted_profile} navigation={navigation}
                        unblockUnrestrictPress={(clickId, obj) => unblockUnrestrictPress(clickId, obj)}
                        restrictSeq={data.restrict_seq} type={isRestrict ? "RESTRICT" : "BLOCK"}
                    />
                </ScrollView>
            </ActionSheet >
        </View>
    )
}

export default BlockedAccountRow

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: 50,
        width: 50,
        borderRadius: 50,
    },
    profileNameBox: {
        flexDirection: 'column',
        marginHorizontal: 10,
    },
    verifiedIcon: {
        width: 16,
        height: 15,
        marginLeft: theme.dimensions.veritextLeftmargin,
        // position: 'absolute',
        // right: -6,
        // top: 20,
    },
    btn: {
        borderRadius: 5,
    },
    btnText: {
        textAlign: 'center',
        fontSize: theme.calculateFontSize(theme.dimensions.blockAccountBtnText),
        fontWeight: '700',
        paddingVertical: 5,
        paddingHorizontal: 16,
    },
})
