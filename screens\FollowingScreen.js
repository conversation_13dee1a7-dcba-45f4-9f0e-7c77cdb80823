import React, { useEffect, useState } from 'react'
import { FlatList, RefreshControl, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import CustomStatusBar from '../components/common/CustomStatusBar';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import FollowingItemRow from '../components/following/FollowingItemRow';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const FollowingScreen = ({ navigation }) => {
    const [selectedTab, setselectedTab] = useState("ALL");
    const [errorMsg, seterrorMsg] = useState("");
    const [refreshKey, setrefreshKey] = useState(Math.random());
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const onTabChange = (item) => {

        // setselectedTab(item);
    }
    const [followingList, setfollowingList] = useState([])
    const [progressLoading, setprogressLoading] = useState(true);
    const renderItem = ({ item }) => {
        return (
            <FollowingItemRow data={item} navigation={navigation} profile_seq={item.following_profile_seq} isFans={false} />
        );
    };
    useEffect(() => {
        getFollowingListService();
    }, []);
    function getFollowingListService() {
        let hashMap = {
            _action_code: "11:GET_FOLLOWING",
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setfollowingList([...[], ...data.data]);
            setprogressLoading(false);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setlistRefresh(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setfollowingList([]);
                setprogressLoading(false);
                seterrorMsg(errorMessage);
            }

        });
    }
    const handleRefresh = () => {

    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Following" navigation={navigation} showBorderBottom={false} />
            {/* <View style={defaultStyle.tabBar}>
                <TouchableOpacity
                    style={[defaultStyle.tabBarLabel, selectedTab === "ALL" ? {
                        borderBottomWidth: Dimensions.tabBorderBottomWidth,
                        borderBottomColor: Colors.tabActiveBootomBorderColor,
                    } : null]}
                    onPress={() => onTabChange("ALL")}>
                    <Text style={[defaultStyle.tabBarLabelTxt, selectedTab === "ALL" ? {
                        color: Colors.tabActiveColor
                    } : null]}>
                        All
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[defaultStyle.tabBarLabel, selectedTab === "ACTIVE" ? {
                        borderBottomWidth: Dimensions.tabBorderBottomWidth,
                        borderBottomColor: Colors.tabActiveBootomBorderColor,
                    } : null]}
                    onPress={() => onTabChange("ACTIVE")}>
                    <Text style={[defaultStyle.tabBarLabelTxt, selectedTab === "ACTIVE" ? {
                        color: Colors.tabActiveColor
                    } : null]}>
                        Active
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[defaultStyle.tabBarLabel, selectedTab === "EXPIRED" ? {
                        borderBottomWidth: Dimensions.tabBorderBottomWidth,
                        borderBottomColor: Colors.tabActiveBootomBorderColor,
                    } : null]}
                    onPress={() => onTabChange("EXPIRED")}>
                    <Text style={[defaultStyle.tabBarLabelTxt, selectedTab === "EXPIRED" ? {
                        color: Colors.tabActiveColor
                    } : null]}>
                        Expired
                    </Text>
                </TouchableOpacity>
            </View> */}
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside}>
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        :
                        <>
                            {
                                progressLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <FlatList
                                        data={followingList}
                                        renderItem={renderItem}
                                        initialNumToRender={10}
                                        keyExtractor={(item, index) => `${index}`}
                                        refreshControl={
                                            <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                                        }
                                    />
                            }
                        </>
                }

            </View>

        </>
    )
}

export default FollowingScreen;

const styles = StyleSheet.create({})
