import React, { useEffect, useState } from 'react'
import { StyleSheet, Text, View } from 'react-native'
import FlashMessage, { showMessage } from 'react-native-flash-message';
import { Snackbar } from 'react-native-paper';
import useSTheme from '../../theme/useSTheme';
import ErrorMessagePopup from './ErrorMessagePopup';

const CustomSnackbar = ({ snackbarClick = null, snackMsg,
    snackType = "FAILED", snackCloseTime = 7000, displaySnackbar = false,
    snackUndoBtn = false, refreshSnack, insideFlashRef = null, showInsideFlashRef = false, ...props }) => {
    const [showSnackBox, setshowSnackBox] = useState(false);
    const [backColor, setbackColor] = useState("#ff3232");
    const [textColor, settextColor] = useState("#FFFFFF");

    const [showErrorPopup, setShowErrorPopup] = useState(false);
    const [showErrorPopupKey, setShowErrorPopupKey] = useState(Math.random());

    const theme = useSTheme();
    useEffect(() => {
        if (displaySnackbar) {
            let backC = theme.colors.snackbarFailureBackground;
            let txtC = theme.colors.snackbarFailureText;
            if (snackType == "SUCCESS") {
                backC = theme.colors.snackbarSuccessBackground;
                txtC = theme.colors.snackbarSuccessText;

            }
            setbackColor(backC);
            settextColor(txtC);
            if (snackType == "SUCCESS") {
                if (showInsideFlashRef) {
                    if (insideFlashRef != null) {
                        insideFlashRef.current.showMessage({
                            message: snackMsg,
                            type: "default",
                            backgroundColor: backC, // background color
                            color: txtC, // text color
                            style: {
                                textAlign: 'center',
                            },
                            autoHide: true,
                            duration: snackCloseTime,
                            hideStatusBar: true,
                        });
                    }
                }
                else {
                    showMessage({
                        message: snackMsg,
                        type: "default",
                        backgroundColor: backC, // background color
                        color: txtC, // text color
                        style: {
                            textAlign: 'center',
                        },
                        autoHide: true,
                        duration: snackCloseTime,
                        hideStatusBar: true,
                    });
                }
            }
            else {
                setShowErrorPopup(true);
                setShowErrorPopupKey(Math.random())
            }


            setshowSnackBox(true);
        }
        else {
            setshowSnackBox(false);
        }
    }, [refreshSnack]);
    const onDismissSnackBar = () => setshowSnackBox(false);
    return (
        <>
            {/* {
                snackbarClick != null ?
                    <></>
                    <Snackbar

                        visible={showSnackBox}
                        // theme={{ colors: { accent: '#F3997B', surface: '#000000', onSurface: "#F2EBE9", } }}
                        theme={{ colors: { accent: backColor, surface: textColor, onSurface: backColor, } }}
                        onDismiss={onDismissSnackBar}
                        onPress={onDismissSnackBar}
                        action={{
                            label: 'Undo',
                            onPress: () => {
                                // Do something

                            },
                        }}
                    >
                        {snackMsg}
                    </Snackbar>

                    :
                    <></>
                <Snackbar
                    visible={showSnackBox}
                    // theme={{ colors: { accent: '#F3997B', surface: '#000000', onSurface: "#F2EBE9", } }}
                    theme={{ colors: { accent: backColor, surface: textColor, onSurface: backColor, } }}

                    onDismiss={onDismissSnackBar}


                >
                    {snackMsg}
                </Snackbar>

            } */}
            {
                snackType != "SUCCESS" ?
                    <>
                        {
                            showErrorPopup ?
                                <ErrorMessagePopup
                                    visiblePopup={showErrorPopup}
                                    visiblePopupKey={showErrorPopupKey}
                                    errorBoxTitle={"Error!"}
                                    errorBoxMsg={snackMsg}
                                />
                                : null
                        }

                    </>
                    : null
            }
            {
                showInsideFlashRef ?
                    <FlashMessage ref={insideFlashRef} />
                    : null
            }

        </>

    )
}

export default CustomSnackbar;

const styles = StyleSheet.create({})
