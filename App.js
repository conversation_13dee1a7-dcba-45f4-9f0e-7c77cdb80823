/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 * @flow strict-local
 */
import React, {useEffect} from 'react';
import AppNavigator from './navigation';
import {LogBox, Alert} from 'react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {NavigationContainer, useNavigation} from '@react-navigation/native';
import {NotifierWrapper} from 'react-native-notifier';
import {
  DefaultTheme,
  DarkTheme,
  Provider as PaperProvider,
} from 'react-native-paper';
import useSTheme from './theme/useSTheme';

LogBox.ignoreLogs(['VirtualizedLists should never be nested']); //Not display Reanimated 2 Warring

const config = {
  screens: {
    SinglePostScreen: {
      path: 'post',
      parse: {
        postSeq: id => `${id}`,
      },
    },
    OthersProfileScreen: {
      path: 'profile',
      parse: {
        profileSeq: id => `${id}`,
      },
    },
    SignupScreen: 'signup',
  },
};
const linking = {
  prefixes: ['https://www.sotrue.co.in', 'https://sotrue.co.in'],
  config,
};

const App = () => {
  const theme = useSTheme();
  const paperTheme = {
    ...DefaultTheme,
    roundness: 6,
    colors: {
      ...DefaultTheme.colors,
      primary: theme.colors.primaryColor,
      accent: theme.colors.primaryColor,
      background: theme.colors.backgroundColor,
      surface: theme.colors.backgroundColor,
      text: theme.colors.primaryTextColor,
      error: theme.colors.errorColor,
    },
  };
  return (
    // <SafeAreaProvider>
    <PaperProvider theme={paperTheme}>
      <NavigationContainer
        linking={linking}
        screenOptions={{
          statusBarStyle: 'light-content',
          statusBarBackgroundColor: theme.colors.backgroundColor,
        }}>
        <NotifierWrapper>
          <AppNavigator />
        </NotifierWrapper>
      </NavigationContainer>
    </PaperProvider>

    // </SafeAreaProvider>
  );
};
export default App;
