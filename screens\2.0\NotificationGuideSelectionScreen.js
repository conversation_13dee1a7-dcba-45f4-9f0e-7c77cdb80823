import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  ScrollView,
  Dimensions,
} from 'react-native';
import React, {useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useDefaultStyle from '../../theme/useDefaultStyle';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../../components/common/LoginSignUpLinearGrad';
import ModuleAppBar from '../../components/loginModule/ModuleAppBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoTextView from '../../components/common/EntutoTextView';
import LoginModuleTitle from '../../components/loginModule/LoginModuleTitle';
import PrimaryButton from '../../components/common/PrimaryButton';

import KabirFull from '../../assets/Images/Kabir–fullbody.png';
import PallviFull from '../../assets/Images/Palvi–fullbody.png';
import KabirHalf from '../../assets/Images/Kabir.png';
import PallviHalf from '../../assets/Images/Pallvi.png';

const NotificationGuideSelectionScreen = ({navigation}) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();
  const [showLoading, setShowLoading] = useState(false);
  const [selectedGuide, setSelectedGuide] = useState(null);
  const [flippedCard, setFlippedCard] = useState(null);

  const guides = [
    {
      id: 'pallavi',
      name: 'Meet Palvi',
      fullImage: PallviFull,
      halfImage: PallviHalf,
      description:
        'Kind, helpful, flirtatious and a polite young woman who enjoys watching films ',
    },
    {
      id: 'kabir',
      name: 'Meet Kabir',
      fullImage: KabirFull,
      halfImage: KabirHalf,
      description:
        'A confident, and dominating individual who enjoys partying on the weekends and hitting the gym.',
    },
  ];

  const handleGuideSelection = guideId => {
    setSelectedGuide(guideId);
    setFlippedCard(guideId);
  };

  const handleNextPress = () => {
    if (selectedGuide) {
      navigation.navigate('HomeScreen', {
        screen: 'HomeFeed', // This targets the specific tab
        params: {cameFrom: 'NotificationGuideSelectionScreen'},
      });
    }
  };

  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <CustomProgressDialog showLoading={showLoading} />
      <View
        style={{
          flex: 1,
          position: 'relative',
          // justifyContent: 'space-between',
          // backgroundColor: 'green',
          backgroundColor: theme.colors.backgroundColor,
        }}>
        {/* <LoginSignUpLinearGrad /> */}
        <ModuleAppBar navigation={navigation} />
        <View
          style={{
            ...defaultStyle.loginModuleContainer,
            // backgroundColor: 'red',
            flex: 1,
          }}>
          <LoginModuleTitle
            firstTitleText="Select Your SoTrue Guide!"
            style={{marginVertical: 10, fontWeight: 'bold'}}
          />

          <EntutoTextView textType="caption">
            Select a personality of your choice who will guide you toward the
            best SoTrue Experience.
          </EntutoTextView>

          <View style={style.guidesContainer}>
            {guides.map(guide => (
              <TouchableOpacity
                key={guide.id}
                style={[
                  style.guideCard,
                  selectedGuide === guide.id && style.selectedGuideCard,
                ]}
                onPress={() => handleGuideSelection(guide.id)}>
                {flippedCard === guide.id ? (
                  // Flipped card layout: 75% left content, 25% right image
                  <View style={style.flippedCardContent}>
                    <View style={style.flippedTextSection}>
                      <EntutoTextView style={style.flippedGuideName}>
                        {guide.name}
                      </EntutoTextView>
                      <EntutoTextView style={style.guideDescription}>
                        {guide.description}
                      </EntutoTextView>
                    </View>
                    <View style={style.flippedImageSection}>
                      <Image
                        source={guide.fullImage}
                        style={style.flippedGuideImage}
                        resizeMode="center"
                      />
                    </View>
                  </View>
                ) : (
                  // Normal card layout
                  <>
                    <Image
                      source={guide.halfImage}
                      style={style.guideImage}
                      resizeMode="center"
                    />
                  </>
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Next Button */}
          <View style={style.nextButtonContainer}>
            <PrimaryButton
              label="All Set"
              onPress={handleNextPress}
              disabled={!selectedGuide}
              uppercase={false}
            />
          </View>
        </View>
      </View>
    </>
  );
};

export default NotificationGuideSelectionScreen;

const styles = theme =>
  StyleSheet.create({
    container: {
      flex: 1,
      position: 'relative',
    },
    contentContainer: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
    },
    captionText: {
      marginBottom: 16,
    },
    guidesContainer: {
      flex: 1,
      height: '100%',
      flexDirection: 'column',
      justifyContent: 'space-around',
      marginTop: 20,
      marginBottom: 20,
    },
    guideCard: {
      height: '45%',
      minHeight: 220,
      marginBottom: 25,
      flexDirection: 'column',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: 'white',
    },
    guideImage: {
      height: '100%',
    },
    selectedGuideCard: {
      borderColor: theme.colors.primaryColor,
    },
    guideInfo: {
      alignItems: 'center',
      marginTop: 'auto',
    },
    guideName: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 8,
      color: theme.colors.primaryTextColor,
    },
    // Flipped card styles
    flippedCardContent: {
      flex: 1,
      flexDirection: 'row',
      width: '100%',
      height: '100%',
      padding: 16,
    },
    flippedTextSection: {
      flex: 0.75,
      paddingRight: 16,
      justifyContent: 'center',
    },
    flippedImageSection: {
      flex: 0.4,
      justifyContent: 'center',
      alignItems: 'center',
    },
    flippedGuideName: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 12,
      color: theme.colors.primaryTextColor,
    },
    guideDescription: {
      fontSize: 14,
      lineHeight: 20,
      color: theme.colors.primaryTextColor,
      textAlign: 'left',
    },
    flippedGuideImage: {
      width: '100%',
      height: '100%',
    },
    // Next button styles
    nextButtonContainer: {
      marginTop: 20,
      width: '100%',
    },
  });
