import React from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { createDrawerNavigator } from '@react-navigation/drawer';
import { BottomNavigator } from './BottomNavigator';
import { MainSideDrawer } from '../components/MainSideDrawer';

const Drawer = createDrawerNavigator();
const HomeDrawerNavigator = (props) => {
    return (
        <Drawer.Navigator screenOptions={{ headerShown: false }} drawerContent={() => <MainSideDrawer {...props} />}>
            <Drawer.Screen name="HomeScreenNav" component={BottomNavigator} />
        </Drawer.Navigator>
    )
}

export default HomeDrawerNavigator;

const styles = StyleSheet.create({})
