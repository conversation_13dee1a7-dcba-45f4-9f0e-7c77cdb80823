import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import useSThemedStyles from '../../theme/useSThemedStyles';
import ArrowIcon from '../../assets/Images/icon/Arrow.png';
import { Image } from 'react-native';

const ModuleAppBar = ({ navigation }) => {
    const goBackPrevious = () => {
        navigation.goBack(null);
    }
    const style = useSThemedStyles(styles);
    return (
        <View style={style.appBar}>
            <View>
                <TouchableOpacity onPress={() => goBackPrevious()}>
                    <View style={{ paddingHorizontal: 20 }}>
                        <Image style={style.arrowIcon}
                            source={ArrowIcon} />
                    </View>
                </TouchableOpacity>
            </View>
        </View>
    )
}

export default ModuleAppBar

const styles = theme => StyleSheet.create({
    appBar: {
        height: theme.dimensions.loginModuleAppBarHeight,
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: theme.colors.appBarBackgroundColor,

    },
    arrowIcon: {
        height: 24,
        width: 24,
        resizeMode: "contain",
        tintColor: "#AAB2B7"
    }
})