import { Platform, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import Slider from '@react-native-community/slider'
import Animated, { Easing } from 'react-native-reanimated'
import useSTheme from '../../theme/useSTheme'

const ReelProgressBar = ({ value = 0, currentTime = 0, onSliderStart = null, onSliderValueCompleteChange = null, duration = 0 }) => {
    const [progress, setProgress] = useState(value);
    const theme = useSTheme();
    useEffect(() => {
        if (Platform.OS == 'ios') {
            setProgress(calcPercentage(currentTime, duration));
        }
    }, [currentTime, duration])
    const calcPercentage = (x, y) => (x / y) * 100;
    const handleSliderStart = (value) => {
        onSliderStart && onSliderStart(value);
    };
    const handleSliderValueCompleteChange = (value) => {
        onSliderValueCompleteChange && onSliderValueCompleteChange(value);
    };
    const handleValueChange = (value) => {
        if (Platform.OS == 'android') {
            setProgress(calcPercentage(value, duration));
        }
    }

    return (
        <View style={{ ...styles.progressBarContainer }}>
            <Animated.View
                style={[
                    styles.progressBarContainer,
                    {
                        width: `${progress}%`,
                        backgroundColor: theme.colors.primaryColor,
                        position: 'absolute',
                        top: 0,
                        margin: 0,
                        zIndex: 9998,
                    },
                ]}
            />
            <Slider
                style={{ width: '100%', height: 30, flex: 1, zIndex: 9999, padding: 0, margin: 0, transform: [{ scaleY: 4 }] }}
                minimumValue={0}
                maximumValue={duration}
                // step={1}
                thumbTintColor='transparent'
                value={currentTime}
                onSlidingComplete={handleSliderValueCompleteChange}
                onSlidingStart={handleSliderStart}
                disabled={false}
                onValueChange={handleValueChange}
                maximumTrackTintColor='transparent'
                minimumTrackTintColor='transparent'
            />
        </View>
    )
}
export default ReelProgressBar

const styles = StyleSheet.create({
    progressBarContainer: {
        // flex: 1,
        height: 8,
        backgroundColor: '#716351',
        justifyContent: 'center',
    }
})