import { FlatList, Keyboard, Platform, RefreshControl, StyleSheet, Text, View } from 'react-native'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import ShowsItem from './ShowsItem';
import useSTheme from '../../theme/useSTheme';
import { checkValueLength } from '../../utils/Utils';
import { AppStateContext } from '../..';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { ActivityIndicator } from 'react-native-paper';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import TempData from '../../data/TempData';

const PlayListSearchComponent = ({
    searchText = "",
    navigation,
    itemCallback = null
}) => {
    const theme = useSTheme();
    const [showLoading, setShowLoading] = useState(false);
    const [bottomLoading, setBottomLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState("");
    const [errorMsgKey, setErrorMsgKey] = useState(Math.random());
    const [isNoDataFound, setIsNoDataFound] = useState(false);
    const RowsPerPage = 10;//Don't change this value this is fixed
    const [startRecord, setStartRecord] = useState(0);
    const [selectedQuery, setSelectedQuery] = useState("");
    const [progressLoading, setProgressLoading] = useState(false);

    useEffect(() => {
        const timeOutVal = setTimeout(() => {
            if (!showLoading && !bottomLoading) {
                if (searchText.length > 1) {
                    setShowLoading(true);
                    setErrorMsg("");
                    setShowsData([])
                    getSearchService(searchText, 0)
                }
                else {
                    if (searchText.length == 0) {
                        setShowLoading(true);
                        setErrorMsg("");
                        setShowsData([])
                        getSearchService("", 0)
                    }
                }
            }
        }, 500);
        return () => {
            clearTimeout(timeOutVal)
        }
    }, [searchText]);
    const [showsData, setShowsData] = useState([]);

    const ItemSeparatorComponent = () => {
        return <View style={{ height: 16 }} />
    }
    const showItemBtnPress = (clickID, obj) => {
        if (clickID == "SUBMIT") {
            const selectedOBj = showsData[obj.index];
            TempData.showData = selectedOBj;
            // navigation.navigate("PlaylistScreen", {
            //     showSeq: selectedOBj.show_seq
            // })
            navigation.navigate("PlaylistSeasonScreen", {
                showSeq: selectedOBj.show_seq
            })
        }

    }
    const renderItem = useCallback(
        ({ item, index }) => {
            const titleVal = checkValueLength(item.grid_title) ? item.grid_title : item.title;
            return <ShowsItem index={index} title={titleVal} showsMedia={item.thumb_file}
                showItemBtnPress={showItemBtnPress} showSeq={item.show_seq} data={item} navigation={navigation} />
        },
        [showsData]
    );
    function getSearchService(query, stRecord) {
        setStartRecord(stRecord);
        setSelectedQuery(query)
        let hashMap = {
            _action_code: "11:DO_PLAYLIST_SEARCH",
            _start_row: stRecord,
            _rows_page: RowsPerPage,
        }
        if (checkValueLength(query)) {
            hashMap.search_str = query;
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            setBottomLoading(false);
            setProgressLoading(false);
            let list = [...showsData];
            if (parseInt(stRecord) == 0) {
                list = data.data;
            }
            else {
                list = list.concat(data.data);
            }
            if (parseInt(stRecord) == 0 && checkValueLength(query)) {
                if (itemCallback) {
                    itemCallback("STORE_QUERY", { query: query })
                }
            }
            if (itemCallback) {
                itemCallback("STORE_BACKUP_PLAYLIST", { query: query, data: list });
            }
            setShowsData(list);
            setErrorMsg("");
            setErrorMsgKey(Math.random())
            if (data.data.length < 9) {
                setIsNoDataFound(true);
            }
            else {
                setIsNoDataFound(false);
            }

        }, (errorCode, errorMessage, data) => { // failure method
            setProgressLoading(false);
            if (_RedirectionErrorList.includes(errorCode)) {
                setShowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                setBottomLoading(false);
                setIsNoDataFound(true);
                if (parseInt(stRecord) == 0) {
                    setShowsData([]);
                    setErrorMsg(errorMessage);
                    setErrorMsgKey(Math.random());
                }
            }
        });
    }
    const listFooterComponent = useCallback(
        () => {
            return (
                <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 15 }}>
                    <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={'large'} />
                </View>
            );
        },
        [],
    );
    const onEndShowReached = () => {
        if (!bottomLoading && !isNoDataFound) {
            let startRec = startRecord + RowsPerPage;
            setBottomLoading(true);
            getSearchService(selectedQuery, startRec)
        }
    }
    const handleRefresh = () => {
        setBottomLoading(true);
        getSearchService(selectedQuery, 0)
    }
    const [keyboardHeight, setKeyboardHeight] = useState(80)
    const handleShowKeyboard = (e) => {
        const height = e.endCoordinates.height;
        setKeyboardHeight(height);
    };

    const handleHideKeyboard = () => {
        setKeyboardHeight(80);
    };

    React.useEffect(() => {
        if (Platform.OS == 'android') {
            return;
        }
        const keyboardDidShowSubscribtion = Keyboard.addListener(
            'keyboardDidShow',
            handleShowKeyboard,
        );
        const keyboardWillHideSubscribtion = Keyboard.addListener(
            'keyboardWillHide',
            handleHideKeyboard,
        );
        return () => {
            Keyboard.removeSubscription?.(keyboardDidShowSubscribtion);
            Keyboard.removeSubscription?.(keyboardWillHideSubscribtion);
        };
    }, []);

    return (
        <View style={{ paddingHorizontal: 24, marginTop: 10 }}>
            {
                showLoading ?
                    <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                        <ActivityIndicator animating={true} />
                    </View>
                    : null
            }
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsgKey} />
                    : null
            }
            <FlatList
                onScrollBeginDrag={() => Keyboard.dismiss()}
                keyboardShouldPersistTaps={'always'}
                contentContainerStyle={{ paddingBottom: keyboardHeight, backgroundColor: theme.colors.backgroundColor }}
                removeClippedSubviews={true}
                data={showsData}
                numColumns={2}
                renderItem={renderItem}
                keyExtractor={(item, index) => `${item.show_seq}_${index}`}
                // keyExtractor={keyExtractor}
                ItemSeparatorComponent={ItemSeparatorComponent}
                onEndReached={onEndShowReached}
                maxToRenderPerBatch={1000}
                windowSize={60}
                updateCellsBatchingPeriod={50}
                initialNumToRender={50}
                disableVirtualization
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                ListFooterComponent={bottomLoading && listFooterComponent}
                getItemLayout={(data, index) => {
                    return { index, length: 200, offset: 200 * index }
                }}
                refreshControl={
                    <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                }
            />
        </View>
    )
}

export default PlayListSearchComponent

const styles = StyleSheet.create({})