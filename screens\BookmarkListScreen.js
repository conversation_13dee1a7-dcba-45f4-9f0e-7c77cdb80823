import React, { useEffect, useState } from 'react'
import { FlatList, RefreshControl, StyleSheet, Text, View } from 'react-native';
import CustomStatusBar from '../components/common/CustomStatusBar';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import PostCard from '../components/post/PostCard';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const BookmarkListScreen = ({ route, navigation }) => {
    const [bookmarkList, setbookmarkList] = useState([])
    const [progressLoading, setprogressLoading] = useState(true);
    const [errorMsg, seterrorMsg] = useState("");
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const rowOptionClick = (clickID, obj) => {
        if (clickID == "close") {
            setprogressLoading(true);
            getBookmarkService();
        }
    }
    const postCardClick = (clickID, obj) => {
        if (clickID == "REMOVE_BOOKMARK") {
            setprogressLoading(true);
            getBookmarkService();
        }
    }
    const renderItem = ({ item }) => {
        return (
            <PostCard itemData={item} navigation={navigation} isMyProfile={false} postCardClick={postCardClick}
                forceBookmark={true} showBookmarkWarrings={true} />
        );
    };
    useEffect(() => {
        setprogressLoading(true);
        getBookmarkService();
    }, []);
    const handleRefresh = () => {
        setprogressLoading(true);
        getBookmarkService();
    }
    function getBookmarkService() {
        let hashMap = {
            _action_code: "11:GET_BOOKMARKS",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            setbookmarkList([...[], ...data.data]);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setprogressLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setprogressLoading(false);
                setbookmarkList([]);
                seterrorMsg(errorMessage);
            }
        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Bookmarks"
                showBackBtn={true} navigation={navigation}
                showBorderBottom={false} />
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside}>
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        :
                        <>
                            {
                                progressLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <FlatList
                                        data={bookmarkList}
                                        renderItem={renderItem}
                                        keyExtractor={(item, index) => index.toString()}
                                        refreshControl={
                                            <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                                        }
                                    />
                            }
                        </>
                }
            </View>

        </>
    )
}

export default BookmarkListScreen;

const styles = StyleSheet.create({})
