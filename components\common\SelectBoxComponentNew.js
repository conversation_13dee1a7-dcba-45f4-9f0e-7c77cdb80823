import { Image, Pressable, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { FlatList } from 'react-native-gesture-handler';
import SuccessFailureMsgBox from './SuccessFailureMsgBox';
import EntutoTextView from './EntutoTextView';
import CustomStatusBar from './CustomStatusBar';
import HeadingTxt from './HeadingTxt';
import CustomSnackbar from './CustomSnackbar';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import { color } from 'react-native-reanimated';

const SelectBoxComponentNew = ({ list, selectedValue, title, labelField = "config_key", valueField = "display_value",
    maxSelectedValue, multiSelect, selectBoxClick }) => {
    const { defaultStyle } = useDefaultStyle();


    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);
    const [searchQuery, setsearchQuery] = useState("");
    const [isTyped, setisTyped] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [selectDataList, setselectDataList] = useState([]);
    const [selectDataListB, setselectDataListB] = useState([]);
    const [selectedV, setselectedV] = useState([]);

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const modalFlashRef = useRef(null);
    useEffect(() => {
        let selV = [];
        if (multiSelect) {
            selV = selectedValue;
        }
        else {
            selV.push(selectedValue);
        }
        list.map(obj => {
            obj.isChecked = false;
            obj.config_key = obj[labelField];
            obj.display_value = obj[valueField];

            if (selV.includes(obj.config_key)) {
                obj.isChecked = true;
            }
        });
        setselectedV(selV);
        setselectDataList(list);
        setselectDataListB(list);
    }, [list])


    const onChangeSearch = query => {

        let currentList = [];
        let newList = [];
        if (query.length != 0) {
            setisTyped(true);
            currentList = selectDataListB;
            newList = currentList.filter((obj) => {
                const lc = obj.display_value.toLowerCase();
                const filter = query.toString().toLowerCase();
                return lc.includes(filter);
            })
        }
        else {
            newList = selectDataListB;
            setisTyped(false);
        }
        setselectDataList([...[], ...newList]);
        if (newList.length == 0) {
            seterrorMsg('"' + query + '" not found!');
        }
        else {
            seterrorMsg("")
        }
        setsearchQuery(query);
    }
    const clearSearchTxt = () => {
        setsearchQuery("");
        seterrorMsg("")
        setselectDataList([...[], ...selectDataListB]);
        setisTyped(false);
    }

    const doneButtonPress = () => {
        if (multiSelect) {
            selectBoxClick("DONE", { selectedItem: selectedV })
        }
        else {
            let selValue = "";
            if (selectedV.length != 0) {
                selValue = selectedV[0];
            }
            selectBoxClick("DONE", { selectedItem: selValue })
        }

    }
    const goBackPrevious = () => {
        selectBoxClick("BACK", {})
    }
    const listRowClick = (value, isChecked) => {
        let listData = selectDataListB;
        let selectedVTemp = selectedV;
        if (isChecked) {
            if (multiSelect) {
                if (selectedVTemp.length >= maxSelectedValue) {
                    //Error
                    let errMsg = "Maximum " + maxSelectedValue + " selection allowed.";
                    setSnackbarMsg(errMsg);
                    setdisplaySnackbar(true);
                    setrefreshSnackBar(Math.random());
                    return;
                }
            }
        }
        let selV = [];
        if (multiSelect) {

        }


        listData.map(obj => {
            if (multiSelect) {
                if (obj.config_key == value) {
                    obj.isChecked = isChecked;
                }
            }
            else {
                obj.isChecked = false;
                if (obj.config_key == value) {
                    obj.isChecked = isChecked;
                }
            }
            if (obj.isChecked) {
                selV.push(obj.config_key);
            }
        });
        setselectedV([...[], ...selV]);
        setdisableUpdateBtn(false);
        // setselectDataList([...[], ...listData]);
    }
    const renderSelectRow = ({ item }) => {
        return (
            <View style={{
                overflow: 'hidden', borderBottomColor: "#00000050",
                borderBottomWidth: 0.5,
            }}>
                <Pressable
                    android_ripple={{ color: theme.colors.pressableRippleColor, borderless: true }}
                    onPress={() => listRowClick(item.config_key, !item.isChecked)}>
                    <View style={{ ...defaultStyle.ListCardStyle, ...style.selectRowBox }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <View style={style.profilePageBox}>
                                <Image source={{ uri: "http://************/playlist_poster.jpg" }} style={style.profilePageIcon} />

                            </View>
                            <EntutoTextView style={style.selectRowBoxTxt}>{item.display_value}</EntutoTextView>

                        </View>
                        <View style={{ marginLeft: 'auto', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                            {/* item.isChecked  */}
                            {
                                selectedV.includes(item.config_key) ?
                                    <TouchableOpacity
                                        onPress={() => listRowClick(item.config_key, !item.isChecked)}>
                                        <Image style={style.tickIcon}
                                            resizeMode="contain"
                                            source={require('../../assets/Images/icon/list_tick_icon.png')} />
                                    </TouchableOpacity>
                                    : null
                            }

                        </View>
                    </View>
                </Pressable>
            </View>
        );
    };
    return (
        <>
            <View style={[style.appBar,]} >
                <View style={{ paddingHorizontal: 8, zIndex: 1000 }}>
                    <TouchableOpacity onPress={() => goBackPrevious()}>
                        <View style={{ paddingHorizontal: 12, }}>
                            <Image style={style.arrowIcon}
                                resizeMode="cover"
                                source={require('../../assets/Images/icon/Arrow.png')} />
                        </View>
                    </TouchableOpacity>
                </View>
                {/* <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, justifyContent: 'center', alignItems: 'center' }}>
                    <HeadingTxt >{title}</HeadingTxt>
                </View> */}
                <View style={{ marginLeft: 'auto', }}>
                    <TouchableOpacity
                        onPress={() => doneButtonPress()}
                        disabled={disableUpdateBtn}
                    ><EntutoTextView style={{ ...defaultStyle.postBtn, color: '#FFF', opacity: disableUpdateBtn ? 0.4 : 1 }}>Done</EntutoTextView></TouchableOpacity>
                </View>

            </View >

            <View style={{ ...defaultStyle.container, backgroundColor: theme.colors.backgroundColor, paddingTop: 15 }}>
                <View style={style.searchBarBox}>
                    <View
                        style={style.searchBar}>
                        <TextInput
                            style={style.input}
                            placeholder="Search..."
                            placeholderTextColor={theme.colors.primaryTextColor}
                            value={searchQuery}
                            autoCorrect={false}
                            onChangeText={onChangeSearch}
                            selectionColor={theme.colors.primaryColor}
                        />
                        {isTyped && (
                            <TouchableOpacity onPress={() => clearSearchTxt()}>
                                <Image
                                    source={require('../../assets/Images/icon/close_icon.png')}
                                    style={style.searchCrossIcon}
                                />
                            </TouchableOpacity>
                        )}
                        <Image
                            source={require('../../assets/Images/icon/search_icon.png')}
                            style={style.searchIcon}
                        />
                    </View>
                </View>


            </View>
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside} >
                    <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                    // </View>
                    : null
            }
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                <FlatList
                    keyboardShouldPersistTaps={'handled'}
                    contentContainerStyle={{ paddingBottom: 20, backgroundColor: theme.colors.backgroundColor }}
                    data={selectDataList}
                    renderItem={renderSelectRow}
                    keyExtractor={(item, index) => `${index}`} />
            </View>


            <CustomSnackbar snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar}
                refreshSnack={refreshSnackBar} showInsideFlashRef={true} insideFlashRef={modalFlashRef} />

        </>
    )
}

export default SelectBoxComponentNew

const styles = theme => StyleSheet.create({
    searchBarBox: {
        alignItems: "center",
        flexDirection: "row",
    },
    searchBar: {
        flex: 1,
        flexDirection: "row",
        backgroundColor: theme.colors.backgroundColor,
        borderRadius: 18,
        alignItems: "center",
        borderWidth: 1,
        borderColor: '#707070',
    },
    searchIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        marginRight: theme.dimensions.searchInputIconMH,
    },
    input: {
        // fontSize: theme.dimensions.searchTextInputSize,
        fontSize: 14,
        marginHorizontal: 8,
        flex: 1,
        color: theme.colors.inputTextColor
    },
    searchCrossIcon: {
        height: 15,
        width: 15,
        marginRight: 8,
        tintColor: "#FFF"
    },
    selectRowBox: {
        flexDirection: 'row',
        alignItems: 'center',

        marginBottom: 0,
        marginTop: 0,
        height: 60,
    },
    selectRowBoxTxt: {
        color: theme.colors.primaryTextColor,
        fontSize: 14,
        fontWeight: '600',
        paddingVertical: 15,
        minWidth: 200,
    },
    appBar: {
        height: 56,
        flexDirection: "row",
        alignItems: "center",
        paddingRight: 8,
        backgroundColor: theme.colors.backgroundColor,

    },
    borderBottom: {
        borderBottomWidth: 0.5,
        borderBottomColor: theme.colors.topHeaderBottomColor,
    },
    arrowIcon: {
        height: 20,
        width: 12,
        tintColor: "#FFF"

    },
    tickIcon: {
        height: 24,
        width: 24,
        tintColor: theme.colors.primaryColor
    },
    profilePageBox: {
        height: 40,
        width: 40,
        borderRadius: 20,
        marginRight: 16,
    },
    profilePageIcon: {
        height: 40,
        width: 40,
        borderRadius: 20,
        resizeMode: 'cover',
    }
})