
import React, { useState } from 'react';
import useSThemedStyles from './useSThemedStyles';
import { StyleSheet } from 'react-native';
import Dimensions from '../constants/Dimensions';
export const DefaultStyleContext = React.createContext();

const PLAYLIST_ITEM_IMAGE_WIDTH = 109;
const PLAYLIST_ITEM_IMAGE_HEIGHT = 192;
const PLAYLIST_ITEM_HEIGHT = 270;
const PLAYLIST_ITEM_WIDTH = 109;
const DefaultStyleProvider = ({ children }) => {
    const style = useSThemedStyles(styles)
    const theme = {
        defaultStyle: style,
    };

    return (
        <DefaultStyleContext.Provider value={theme}>{children}</DefaultStyleContext.Provider>
    );
};

export default DefaultStyleProvider;
const styles = theme => StyleSheet.create({
    container2: {
        paddingHorizontal: 15,
        marginBottom: 15,
        backgroundColor: theme.colors.primaryColor,
    },
    container: {
        paddingHorizontal: 15,
        paddingBottom: 15,
    },
    loginModuleContainer: {
        paddingHorizontal: 20,
        marginBottom: 15,
    },
    loginModuleFormContainer: {
        marginTop: 64,
    },
    ListCardStyle: {
        marginVertical: 15,
        marginHorizontal: 15,
        flex: 1,
    },
    withoutPapercontainer: {
        marginHorizontal: 10,
    },
    overlay: {
        flex: 1,
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: '#C4C4C4',
        width: '100%',
        height: '100%',
    },
    tabBar: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme.colors.backgroundColor,
    },
    tabBarLabel: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    tabBarLabelTxt: {
        color: theme.colors.tabInActiveColor,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultTabBarLabelTxt),
        fontFamily: theme.getFontFamily('bold'),
        paddingVertical: 15,
        textTransform: 'uppercase',
        flexWrap: 'nowrap'
    },
    postBtn: {
        color: theme.colors.primaryColor,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultPostBtnTxt),
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
        marginRight: 8,
    },
    popupHeadTxt: {
        color: theme.colors.mainHeadingColor,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultPopupHeadTxt),
        fontFamily: theme.getFontFamily('bold'),
        marginTop: 16,
        zIndex: 4,
    },
    popupBodyTxt: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultPopupBodyTxt),
        marginTop: 10
    },
    popupWarringTxt: {
        color: '#FF4963',
        fontSize: theme.calculateFontSize(theme.dimensions.defaultPopupWarringTxt),
        marginTop: 10
    },
    popupBtn: {
        flex: 1,
        borderRadius: 0,
    },
    popupBtnText: {
        textAlign: 'center',
        paddingVertical: 16,
        paddingHorizontal: 16,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultPopupBtnText),
        color: '#43180B',
    },
    popupBox: {
        paddingHorizontal: 16,
        paddingBottom: 16,
    },
    fullOneVectorImage: {
        height: Dimensions.screenHeight,
        width: Dimensions.screenWidth / 2,
        position: 'absolute',
        right: 0,
        top: 0,
    },
    dropdownContainer: {
        backgroundColor: 'transparent',
        marginVertical: 8,
        position: 'relative',
    },
    dropdownMain: {
        height: 54,
        borderColor: theme.colors.dropdownBorderColor,
        borderWidth: 0.5,
        paddingStart: 10,

    },
    dropdownLabel: {
        position: 'absolute',
        backgroundColor: theme.colors.backgroundColor,
        left: 10,
        top: -6,
        zIndex: 999,
        paddingHorizontal: 2,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultDropdownLabel),
        color: theme.colors.dropdownLabelColor,
        fontFamily: theme.getFontFamily(),
    },
    dropdownPlaceholderStyle: {
        fontSize: theme.calculateFontSize(theme.dimensions.defaultDropdownPlaceholder),
        color: theme.colors.dropdownPlaceHolderColor,
        fontFamily: theme.getFontFamily(),
    },
    dropdownSelectedTextStyle: {
        fontSize: theme.calculateFontSize(theme.dimensions.defaultDropdownSelectedText),
        color: theme.colors.dropdownSelectedTextColor,
        fontFamily: theme.getFontFamily(),
    },
    dropdownInputSearchStyle: {
        height: 40,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultDropdownSelectedText),
        fontFamily: theme.getFontFamily(),
    },
    dropdownIcon: {
        marginRight: 5,
    },
    dropdownItem: {
        padding: 17,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

    },
    dropdownTextItem: {
        flex: 1,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultDropdownSelectedText),
        fontFamily: theme.getFontFamily(),
    },
    dropdownSelectedStyle: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5,
        backgroundColor: '#FDAA6A1A',
        shadowColor: '#000',
        marginTop: 8,
        marginRight: 12,
        paddingHorizontal: 12,
        paddingVertical: 8,
    },
    dropdownTextSelectedStyle: {
        marginRight: 5,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultDropdownSelectedText),
        color: '#FDAA6A',
        fontFamily: theme.getFontFamily(),
    },
    dropdownTextNotSelectedStyle: {
        marginRight: 5,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultDropdownSelectedText),
        fontFamily: theme.getFontFamily(),
    },
    inputUnderLineTxt: {
        color: theme.colors.inputUnderLineTxt,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultInputUnderLineTxt),
        fontFamily: theme.getFontFamily(),
    },
    inputUnderLineView: {
        marginTop: 6,
    },
    errorBoxOutside: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        marginBottom: 15,

    },
    verificationCameraBox: {
        width: 250,
        height: 167,
        backgroundColor: '#F2EBE9',
        borderRadius: 15,
        marginTop: 20,
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderStyle: 'dashed',
        borderColor: theme.colors.primaryColor,
        elevation: 2,

    },
    veriImage: {
        width: 250,
        height: 167,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 15,
    },
    veriCameraIconStyle: {
        width: 20,
        height: 20,
    },
    veriCameraIconTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.defaultVerCameraIconTxt),
        fontWeight: '400',
        textAlign: 'center',
        color: theme.colors.primaryColor,
        marginTop: 15,
        fontFamily: theme.getFontFamily(),
    },
    veriCameraIconExtraTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.defaultVerCameraIconExtraTxt),
        fontWeight: '400',
        textAlign: 'center',
        color: theme.colors.primaryTextColor,
        marginTop: 4,
        fontFamily: theme.getFontFamily(),
    },
    veriCameraBox: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 15,
    },
    veriCameraBtnBox: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    veriCameraBoxBtn: {
        paddingVertical: 8,
        color: '#FFFFFF',
    },
    veriChangeBox: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        flexDirection: 'row',
        borderBottomEndRadius: 15,
        borderBottomStartRadius: 15,

    },
    veriLinearGradient: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        borderRadius: 15,
        borderTopRightRadius: 0,
        borderTopLeftRadius: 0,
    },
    underInputTxtBox: {
        marginTop: 6,
    },
    underInputTxt: {
        color: 'grey',
        fontSize: theme.calculateFontSize(theme.dimensions.defaultUnderInputTxt),
        fontFamily: theme.getFontFamily(),
    },
    listUnderLineTxt: {
        color: 'grey',
        fontSize: theme.calculateFontSize(theme.dimensions.defaultListUnderLineTxt),
        fontFamily: theme.getFontFamily(),
    },
    listUnderLineView: {
        marginTop: 6,
    },
    customDropdown: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: theme.colors.dropdownBorderColor,
        // backgroundColor: '#FFF',
        // borderBottomColor: '#00000050',
        // borderBottomWidth: 0.5,
        paddingBottom: 10,
        paddingTop: 10,
    },
    customMultiSelectBox: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        flex: 4,
    },
    customMultiSelectSingleTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.defaultCustomMultiSelectSingleTxt),
        paddingLeft: 8,
        fontWeight: "600",
    },
    customMultiSelectSinglePH: {
        fontSize: theme.calculateFontSize(theme.dimensions.defaultCustomMultiSelectSingleTxt),
        color: 'grey',
        paddingLeft: 8,
        fontFamily: theme.getFontFamily(),
    },
    errorTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.defaultErrorTxt),
        marginTop: 6,
        fontFamily: theme.getFontFamily(),
    },
    orTxtLine: {
        height: 0.5,
        flex: 1,
        backgroundColor: '#00000020',
    },
    orTxtBox: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    inputUnderCountBox: {
        position: 'absolute',
        right: 0,
        bottom: -15,
    },
    inputUnderCountBoxTxt: {
        color: theme.colors.primaryColor,
        fontSize: theme.calculateFontSize(theme.dimensions.defaultInputUnderCountBoxTxt),
        fontFamily: theme.getFontFamily(),
    },
    boxWithSwitchBoxDivider: {
        flex: 1,
        borderWidth: 0.5,
        borderColor: '#000000',
        opacity: 0.2
    },
    errorIndicatorBox: {
        flex: 1,
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        // backgroundColor: '#C4C4C450',
        width: '100%',
        height: '100%',
        zIndex: 999,
    },
    boldTagTxt: {
        color: theme.colors.primaryColor,
        fontFamily: theme.getFontFamily('bold'),
    },
    signupTextBox: {
        paddingStart: 24,
        paddingEnd: 24,
        marginTop: 40
    },
    signupText: {
        color: '#FFFFFF',
        fontSize: theme.calculateFontSize(theme.dimensions.defaultSignupText),
        marginBottom: 4,
        fontFamily: theme.getFontFamily(),
    },
    signupSmallText: {
        color: '#FFFFFF',
        fontSize: theme.calculateFontSize(theme.dimensions.defaultSignupSmallText),
        marginBottom: 4,
        marginTop: 8,
        fontFamily: theme.getFontFamily(),
    },
    signUpFormBox: {
        minHeight: 100,
        backgroundColor: '#FFFFFF',
        borderTopEndRadius: 24,
        borderBottomEndRadius: 24,
        flex: 1,
        marginEnd: 44,
        marginTop: 40,
        paddingHorizontal: 24,
        paddingVertical: 15,
    },
    signUpBtnBox: {
        marginTop: 100,
        flexDirection: 'row',
        marginBottom: 80
    },
    signUpBtn: {
        minHeight: 58,
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
        borderTopEndRadius: 58,
        borderBottomEndRadius: 58,
    },
    signUpBtnText: {
        color: '#FFFFFF',
        fontSize: theme.calculateFontSize(theme.dimensions.defaultSignUpBtnText),
        fontFamily: theme.getFontFamily(),
    },
    warringInterestText: {
        color: "#FF0000",
        marginTop: 8, marginBottom: 8
    },
    PIcontainer: {
        flex: 0.5,
        height: PLAYLIST_ITEM_HEIGHT,
        width: PLAYLIST_ITEM_WIDTH,
        alignItems: 'center'
    },
    PIshowsImageBox: {
        backgroundColor: "#FFF",
        elevation: 1,
    },
    PIshowsImage: {
        height: PLAYLIST_ITEM_IMAGE_HEIGHT,
        maxHeight: PLAYLIST_ITEM_IMAGE_HEIGHT,
        width: PLAYLIST_ITEM_IMAGE_WIDTH,
        resizeMode: 'cover',
    },
    PItitleBox: {
        width: PLAYLIST_ITEM_IMAGE_WIDTH,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 5,
        marginTop: 10,
        marginBottom: 10,
    },
    PItitle: {
        color: theme.colors.PItitleC,
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistCardTitleText),//12
        textAlign: 'center',
        fontFamily: theme.getFontFamily('bold'),
    },
    defaultModalView: {
        backgroundColor: theme.colors.backgroundColor,
        borderRadius: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 3,
        paddingHorizontal: 15,
        paddingBottom: 20,
        paddingTop: 10,
        width: Dimensions.screenWidth - 30,
    },
    centeredView: {
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 22,
    },
    backdropModalView: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    popupActionBox: {
        marginTop: 30,
        flexDirection: 'row',
        justifyContent: 'center',
    },
})