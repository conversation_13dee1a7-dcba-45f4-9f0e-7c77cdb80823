import { Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useCallback, useEffect, useState } from 'react'
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';

const CustomTabButtonGroup = ({
    tabList = [],
    selectedTab = "",
    boxStyle = {},
    onTabValueChange = null
}) => {

    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    const [selectedTabV, setSelectedTabV] = useState("");

    useEffect(() => {
        setSelectedTabV(selectedTab);
    }, []);
    const onTabChange = useCallback((tabValue) => {
        setSelectedTabV(tabValue);
        if (onTabValueChange) {
            onTabValueChange(tabValue)
        }
    },
        [],
    )

    return (
        <View style={{ ...style.topTabView, ...boxStyle }}>
            {tabList.map((item, index) => {
                return <CustomTabButtonGroupItem key={index} selectedTab={selectedTabV}
                    tabLabel={item.label}
                    tabValue={item.value}
                    onTabChange={onTabChange}
                />
            })}
        </View>
    )
}

export default React.memo(CustomTabButtonGroup);
const CustomTabButtonGroupItem = ({
    selectedTab, tabLabel = "", tabValue = "", onTabChange
}) => {
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    return <View style={[style.topTabBarLabel, { zIndex: 99 }, selectedTab === tabValue ? {
        borderBottomWidth: theme.dimensions.tabBorderBottomWidth,
        borderBottomColor: theme.colors.primaryColor,

    } : null]}>
        <Pressable style={{ width: '100%', }}
            android_ripple={{ color: theme.colors.pressableRippleColor, borderless: true }}
            onPress={() => onTabChange(tabValue)}>
            <View style={{ alignItems: 'center' }}>
                <Text style={[style.topTabBarLabelTxt, selectedTab === tabValue ? {
                    color: theme.colors.primaryColor
                } : null]} allowFontScaling={false}>
                    {tabLabel}
                </Text>
            </View>
        </Pressable>
    </View>

}

const styles = theme => StyleSheet.create({
    topTabView: {
        flexDirection: 'row',
    },
    topTabBarLabel: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    topTabBarLabelTxt: {
        color: theme.colors.profileCatInActiveColor,
        fontSize: theme.calculateFontSizeNew(theme.dimensions.profileTabText),
        fontFamily: theme.getFontFamily('bold'),
        paddingBottom: 8,
        flexWrap: 'nowrap'
    },
})