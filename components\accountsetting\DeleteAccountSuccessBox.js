import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import EntutoTextView from '../common/EntutoTextView'
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSTheme from '../../theme/useSTheme'
import { PopupPositiveButton } from '../common/PopupButton'

const DeleteAccountSuccessBox = ({ boxPopupClick, successMsg = "" }) => {
    const signoutApp = () => {
        boxPopupClick("SIGNOUT", {})
    }
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    return (
        <View>
            <View style={defaultStyle.popupBox}>
                <EntutoTextView style={defaultStyle.popupHeadTxt}>
                    Sotrue Account Deactivation
                </EntutoTextView>
                <EntutoTextView style={{ ...defaultStyle.popupBodyTxt, fontSize: theme.dimensions.accDeActiveSuccessText }}>
                    {successMsg}
                </EntutoTextView>
                <View style={{ flexDirection: 'row', flex: 1, marginTop: 26, marginBottom: 20, justifyContent: 'center', alignItems: 'center' }}>
                    <View style={{}}>
                        <PopupPositiveButton
                            onPress={() => signoutApp()}
                            btnText={"Sign Out"} />
                    </View>
                </View>
            </View>
        </View>
    )
}

export default DeleteAccountSuccessBox

const styles = StyleSheet.create({})