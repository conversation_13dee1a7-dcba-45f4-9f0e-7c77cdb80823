import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import Colors from '../../constants/Colors';
import EntutoTextView from './EntutoTextView';
import {Snackbar} from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FlashMessage, {showMessage} from 'react-native-flash-message';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import ErrorMessagePopup from './ErrorMessagePopup';

const SuccessFailureMsgBox = ({
  alertMsg,
  alertType = 'FAILED',
  isPopupBox = false,
  alertKey = 1,
  alertCloseTime = 10000,
  visibleAllTime = false,
}) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const [visible, setVisible] = React.useState(false);
  const [visibleSnack, setvisibleSnack] = React.useState(true);

  const [showErrorPopup, setShowErrorPopup] = useState(false);
  const [showErrorPopupKey, setShowErrorPopupKey] = useState(Math.random());

  useEffect(() => {
    setVisible(true);
    // if (!visibleAllTime) {
    //     let timer1 = setTimeout(() => setVisible(false), alertCloseTime);

    //     return () => {
    //         clearTimeout(timer1);
    //     };
    // }

    if (visibleAllTime) {
      setvisibleSnack(false);
    } else {
      let bgColor = theme.colors.snackbarFailureBackground;
      let textColor = theme.colors.snackbarFailureText;
      if (alertType == 'SUCCESS') {
        bgColor = theme.colors.snackbarSuccessBackground;
        textColor = theme.colors.snackbarSuccessText;

        showMessage({
          message: alertMsg,
          type: 'default',
          backgroundColor: bgColor, // background color#B20303
          color: textColor, // text color#FFFFFF
          style: {
            textAlign: 'center',
          },
          autoHide: true,
          duration: alertCloseTime,
          hideStatusBar: true,
        });
      } else {
        setShowErrorPopup(true);
        setShowErrorPopupKey(Math.random());
      }

      setvisibleSnack(true);
    }
  }, [alertKey]);
  const onDismissSnackBar = () => setVisible(false);
  return (
    <>
      {visible ? (
        <>
          {visibleSnack ? (
            <>
              {showErrorPopup ? (
                <ErrorMessagePopup
                  visiblePopup={showErrorPopup}
                  visiblePopupKey={showErrorPopupKey}
                  errorBoxTitle={'Error!'}
                  errorBoxMsg={alertMsg}
                />
              ) : null}
            </>
          ) : (
            // <Snackbar
            //     visible={visible}
            //     theme={{ colors: { accent: '#ff3232', surface: '#FFFFFF', onSurface: "#ff3232", } }}
            //     onDismiss={() => onDismissSnackBar()}
            //     action={{
            //         label: <MaterialCommunityIcons name='close' color={'#FFFFFF'} size={24} />,
            //         labelStyle: {
            //             color: '#FFFFFF',
            //         },

            //         onPress: () => {
            //             // Do something
            //         },
            //     }}>
            //     {alertMsg}
            // </Snackbar>
            <View style={{...style.errorBox}}>
              <EntutoTextView style={{...style.errorBoxTxt}}>
                {alertMsg}
              </EntutoTextView>
            </View>
          )}
        </>
      ) : null}
    </>
  );
};

export default SuccessFailureMsgBox;

const styles = theme =>
  StyleSheet.create({
    errorBox: {
      padding: 15,
      width: '100%',
      // backgroundColor: theme.colors.errorBoxBackground,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 100,
    },
    errorBoxTxt: {
      color: theme.colors.errorBoxText,
      textAlign: 'center',
      fontSize: theme.calculateFontSize(theme.dimensions.failureErrorBoxText),
    },
  });
