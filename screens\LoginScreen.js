import React, {useContext, useEffect, useState} from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  Modal,
  Alert,
  Platform,
  Text,
} from 'react-native';
import Video from 'react-native-video';
import Colors from '../constants/Colors';
import EntutoTextView from '../components/common/EntutoTextView';
import LoginImageTop from '../components/Login/LoginImageTop';
import EntutoEditText from '../components/common/EntutoEditText';

import ServerConnector from '../utils/ServerConnector';
import {AppStateContext} from '..';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import PrimaryButton from '../components/common/PrimaryButton';
import Dimensions from '../constants/Dimensions';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import PasswordIcon from '../assets/Images/icon/password.png';
import GoogleSignButton from '../components/common/GoogleSignButton';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {WEB_CLIENT_ID} from '../utils/Appconfig';
import {_getFirstTimeUser, _setFirstTimeUser} from '../utils/AuthLogin';
import CustomStatusBar from '../components/common/CustomStatusBar';
import {
  AppleButton,
  appleAuth,
} from '@invertase/react-native-apple-authentication';
import PlaceholderImage from '../assets/Images/full_user_image_place_holder.png';
import GoogleIcon from '../assets/Images/icon/google_icon.png';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import LoginWithButton from '../components/common/LoginWithButton';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ErrorMessages from '../constants/ErrorMessages';
import {CommonActions} from '@react-navigation/native';
import appData from '../data/Data';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import {Switch} from 'react-native-paper';
import useDefaultStyle from '../theme/useDefaultStyle';
import ThemeColorComponent from '../components/ThemeColorComponent';
import LINE_ONE_IMAGE from '../assets/Images/line/line1.png';
import LINE_TWO_IMAGE from '../assets/Images/line/line4.png';
import LINE_THREE_IMAGE from '../assets/Images/line/line3.png';
import LINE_FOUR_IMAGE from '../assets/Images/line/line2.png';
import sotrue_video from '../assets/videos/sotrue_text_animation.mp4';
import {
  adjustFontConfig,
  getDeviceType,
  getFontSize,
} from '../theme/ResponsiveFont';
import {
  moderateScaleCustom,
  moderateScalePixel,
} from '../theme/ResponsiveMatrix';

const LoginScreen = ({route, navigation}) => {
  const [showLoading, setShowLoading] = useState(false);
  const {
    changeUserDetails,
    isFirstTimeUser,
    changeFirstTimeUser,
    changeAcceptTerms,
  } = useContext(AppStateContext);
  const [errorMsg, setErrorMsg] = useState('');
  const [refreshKey, setRefreshKey] = useState(Math.random());
  const {ErrorMsg} = route.params;
  const [videoError, setVideoError] = useState(false);

  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();

  useEffect(() => {
    setErrorMsg(ErrorMsg);
    changeAcceptTerms(false);
    GoogleSignin.configure({
      webClientId: WEB_CLIENT_ID,
      offlineAccess: true,
    });
    GoogleSignin.signOut();
    setShowLoading(false);
  }, []);

  useEffect(() => {
    if (appleAuth.isSupported) {
      // onCredentialRevoked returns a function that will remove the event listener. useEffect will call this function when the component unmounts
      return appleAuth.onCredentialRevoked(async () => {
        // console.log('If this function executes, User Credentials have been Revoked');
      });
    }
  }, []);

  const handleVideoError = error => {
    console.log('Video playback error:', error);
    setVideoError(true);
  };

  const letsGoBtnPress = () => {
    navigation.navigate('QuickSignInScreen');
    // navigation.replace("QuickSignUpScreen", {
    //     _mobile: "",
    //     _selectedEmailID: "",
    //     data: {},
    // })
    // navigation.navigate("QuickSignUpPersonalizeScreen", {
    //     mobile: "",
    //     _data: {},
    // })
  };

  return (
    <>
      <CustomStatusBar translucent={true} hidden={false} />
      <CustomProgressDialog showLoading={showLoading} />
      <View style={{flex: 1, position: 'relative', backgroundColor: 'white'}}>
        {/* Background Video */}
        <View style={style.videoContainer}>
          <View style={style.videoPaddingContainer}>
            <Video
              source={sotrue_video}
              style={style.backgroundVideo}
              resizeMode="contain"
              repeat={true}
              playInBackground={false}
              playWhenInactive={false}
              onError={handleVideoError}
              muted={true}
            />
          </View>
        </View>

        {/* Fallback background if video fails */}
        {videoError && <LoginSignUpLinearGrad />}

        <ScrollView keyboardShouldPersistTaps="handled">
          <View style={{...defaultStyle.container, marginBottom: 40}}>
            {/* Button at the bottom */}
            <View style={style.loginBox}>
              <PrimaryButton
                label={`Let's Go!`}
                onPress={() => letsGoBtnPress()}
              />
            </View>
          </View>
        </ScrollView>
      </View>

      {errorMsg.length != 0 ? (
        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
      ) : null}
    </>
  );
};

export default LoginScreen;

const styles = theme =>
  StyleSheet.create({
    videoContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      bottom: 0,
      right: 0,
      zIndex: -1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    videoPaddingContainer: {
      width: '90%', // Adjust this percentage to control horizontal padding
      height: '100%',
    },
    backgroundVideo: {
      width: '100%',
      height: '100%',
    },
    loginBox: {
      marginTop: Dimensions.screenHeight * 0.8, // Position button at approximately 80% of screen height
      paddingHorizontal: 30,
    },
  });
