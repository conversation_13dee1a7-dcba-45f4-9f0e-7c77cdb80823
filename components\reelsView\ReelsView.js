import { Animated, Dimensions, Platform, StyleSheet, Text, View } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import ReelsRow from './ReelsRow';
import { checkValueLength } from '../../utils/Utils';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppStateContext } from '../..';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { RefreshControl } from 'react-native';
import useSTheme from '../../theme/useSTheme';

const { height, width } = Dimensions.get('window');
const ReelsView = ({ postList, onReelsEnd, currentProfileSeq, navigation, cameFrom, onSwipeRefresh }) => {
    const isIOS = Platform.OS === 'ios';
    const insets = useSafeAreaInsets();
    const [displayHeight, setDisplayHeight] = useState(0);
    const theme = useSTheme();
    const { fullUserDetails, __commentObj, homepagePostDataBackup } = useContext(AppStateContext);

    const [postData, setPostData] = useState([])
    useEffect(() => {
        const timeOutVal = setTimeout(() => {
            // console.log("postList", postList)
            let tempList = [];
            postList.forEach((item, index) => {
                tempList.push({
                    id: index,
                    post: item,
                })
            })
            setPostData(tempList);
        }, 500);

        return () => {
            clearTimeout(timeOutVal);
        }
    }, [postList])



    const refFlatList = useRef();
    const [scrollY] = useState(new Animated.Value(0));
    const [scrollInfo, setScrollInfo] = useState({ isViewable: true, index: 0 });

    const viewabilityConfig = { viewAreaCoveragePercentThreshold: 80 };
    const onViewableItemsChanged = useRef(viewableItems => {
        const info = {
            isViewable: viewableItems.changed[0].isViewable,
            index: viewableItems.changed[0].index,
        };
        setScrollInfo(info);
    });
    const transitionAnimation = index => {
        const rowHeight = displayHeight * index;
        return {
            opacity: scrollY.interpolate({
                inputRange: [
                    rowHeight,
                    rowHeight + displayHeight / 2,
                    rowHeight + displayHeight,
                ],
                outputRange: [1, 0.2, 0],
                useNativeDriver: true,
                extrapolate: 'identity',
            }),
        };
    };
    const getItemLayout = (item, index) => ({
        length: displayHeight,
        offset: displayHeight * index,
        index,
    });

    const onLayout = ({ nativeEvent }) => {
        setDisplayHeight((!isIOS && nativeEvent.layout.height) || height);
    };
    const onEndReached = () => {
        console.log("onEndReached");
        onReelsEnd(1);
    };
    const keyExtractor = (item, index) => {
        return `${item.id}_${index}`;
    };
    const renderItem = ({ item, index }) => {
        const scrollIndex = scrollInfo?.index || 0;
        const isNext = index >= scrollIndex - 1 && index <= scrollIndex + 1;
        return (
            <ReelsRow
                item={item}
                isNext={isNext}
                index={index}
                transitionAnimation={transitionAnimation}
                visible={scrollInfo}
                displayHeight={displayHeight}

                isVisible={scrollIndex === index}
                navigation={navigation}
                cameFrom={cameFrom}
                insets={insets}
                fullUserDetails={fullUserDetails}
                __commentObj={__commentObj}
                appTheme={theme}
                homepagePostDataBackup={homepagePostDataBackup}
            />
        );
    };
    return (
        <View style={styles.flexContainer} onLayout={onLayout}>
            <BottomSheetModalProvider>
                <Animated.FlatList

                    pagingEnabled
                    showsVerticalScrollIndicator={false}
                    ref={refFlatList}
                    automaticallyAdjustContentInsets={true}
                    onViewableItemsChanged={onViewableItemsChanged.current}
                    viewabilityConfig={viewabilityConfig}
                    onScroll={Animated.event(
                        [
                            {
                                nativeEvent: { contentOffset: { y: scrollY } },
                            },
                        ],
                        {
                            useNativeDriver: false,
                        },
                    )}
                    data={postData}
                    renderItem={renderItem}
                    getItemLayout={getItemLayout}
                    keyExtractor={keyExtractor}
                    onEndReachedThreshold={20}
                    onEndReached={onEndReached}
                    removeClippedSubviews={true}
                    refreshControl={
                        <RefreshControl tintColor={theme.colors.primaryColor} refreshing={false}
                            onRefresh={() => onSwipeRefresh()} />
                    }
                />
            </BottomSheetModalProvider>
        </View>
    )
}

export default ReelsView

const styles = StyleSheet.create({
    flexContainer: {
        flex: 1,
        backgroundColor: 'transparent',
    },
})