import React, { useState } from 'react';
import CustomStatusBar from '../components/common/CustomStatusBar';
import ProfileComponent from '../components/profile/ProfileComponent';
import { decryptOnlyEncryptNumber } from '../utils/Utils';

const OthersProfileScreen = ({ route, navigation }) => {
    const { profileSeq, id } = route.params;
    const profileMediaType = route.params.hasOwnProperty("tabType") ? route.params.tabType : "ALL"
    return (
        <>
            <CustomStatusBar translucent={true} hidden={false} backgroundColor={'transparent'} />
            <ProfileComponent key={profileSeq == undefined ? id != undefined ? id : -1 : profileSeq} profileMediaType={profileMediaType}
                navigation={navigation} profileSeq={profileSeq == undefined ? id != undefined ? decryptOnlyEncryptNumber(id) : -1 : profileSeq} isOthersProfile={true} />
        </>

    )
}

export default OthersProfileScreen;
