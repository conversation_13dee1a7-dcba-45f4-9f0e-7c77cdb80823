import { Dimensions, Image, StyleSheet, TouchableOpacity, View } from 'react-native'
import React from 'react'
import DEFAULT_IMAGE_VALUE from "../../assets/Images/default_image.jpg"
import EntutoTextView from '../common/EntutoTextView';
import { hasImageUrlExist, onMaxTextLengthReached } from '../../utils/Utils';
import { MaxPlaylistGridTxtLimit } from '../../utils/Appconfig';
import useDefaultStyle from '../../theme/useDefaultStyle';

const EpisodeItem = ({ title = "", index, showsMedia = null, rowItemPress = null }) => {
    const { defaultStyle } = useDefaultStyle();
    const itemBtnPress = () => {
        if (rowItemPress) {
            rowItemPress("SUBMIT", { index: index })
        }
    }
    return (
        <View style={defaultStyle.PIcontainer}>
            <TouchableOpacity onPress={() => itemBtnPress()}>
                <View style={defaultStyle.PIshowsImageBox}>
                    <Image
                        source={hasImageUrlExist(showsMedia) ? { uri: showsMedia } : DEFAULT_IMAGE_VALUE}
                        style={defaultStyle.PIshowsImage} />
                </View>
            </TouchableOpacity>
            <View style={defaultStyle.PItitleBox}>
                <EntutoTextView style={defaultStyle.PItitle}>
                    {onMaxTextLengthReached(title, MaxPlaylistGridTxtLimit, "")}
                </EntutoTextView>
            </View>
        </View>
    )
}

export default EpisodeItem
