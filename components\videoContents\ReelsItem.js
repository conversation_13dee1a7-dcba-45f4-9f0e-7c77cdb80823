import { Platform, Pressable, StyleSheet, Text, View, Animated, Image } from 'react-native'
import React, { useEffect, useRef, useState, } from 'react'
import { useSafeAreaFrame, useSafeAreaInsets } from 'react-native-safe-area-context';
import Dimensions from '../../constants/Dimensions';
import EntutoTextView from '../common/EntutoTextView';
import useSTheme from '../../theme/useSTheme';
import PlaylistPlaceholder from "../../assets/Images/default_image.jpg"
import LikeBtnComponent from '../common/LikeBtnComponent';
import FullSInActiveLike from '../../assets/Images/icon/like_icon.png';
import FullSActiveLike from '../../assets/Images/icon/like_icon.png';
import FullCommentIcon from '../../assets/Images/icon/comment.png'
import PlayViewCount from '../../assets/Images/icon/play_view_icon.png'
import SHARE_ICON from '../../assets/Images/icon/share_icon.png';
import ReelProgressBar from './ReelProgressBar';
import Video from 'react-native-video';
import { hasImageUrlExist } from '../../utils/Utils';
import LinearGradient from 'react-native-linear-gradient';

const ReelsItem = ({ videoUrl = null }) => {
    const insets = useSafeAreaInsets();
    const frame = useSafeAreaFrame();
    const BottomGap = Platform.OS == 'ios' ? 10 : insets.bottom
    const AVL_HEIGHT = frame.height - BottomGap; // For top display minus -insets.top 
    const videoPlayerRef = useRef(null);
    const theme = useSTheme();
    const VideoOverlay = () => {
        return <LinearGradient
            colors={[
                '#000000',
                'transparent',
                'transparent',
                '#000000'
            ]}
            locations={[0, 0.2, 0.8, 0.97]}
            style={styles.videoOverlayGrad}
        />
    }
    return (
        <View style={{ height: AVL_HEIGHT, width: Dimensions.screenWidth, backgroundColor: theme.colors.backgroundColor }}>
            <View style={{ ...styles.videoResolutionContainer, zIndex: 1 }}>
                <ResolutionSwitch />
            </View>
            <View style={{ ...styles.playListCountContainer, zIndex: 1 }}>
                <EntutoTextView style={styles.playlistProfileText}>sidhanthkakar</EntutoTextView>
                <View style={styles.playListBox}>
                    <View style={styles.playlistCircleBox} />
                    <Image source={PlaylistPlaceholder} style={styles.playlistCover} />
                    <EntutoTextView style={styles.playListCountText}>3/12</EntutoTextView>
                </View>
            </View>
            <View style={{ ...styles.likeBtnBox, left: 24, zIndex: 1 }}>
                <LikeBtnComponent
                    activeTintColor={"#FFFFFF"}
                    inActiveTintColor={"#FFFFFF"}
                    inActiveIcon={FullSInActiveLike}
                    activeIcon={FullSActiveLike}
                    isLike={true}
                    style={styles.iconImage} />
                <LikeBtnComponent
                    activeTintColor={"#FFFFFF"}
                    inActiveTintColor={"#FFFFFF"}
                    inActiveIcon={FullCommentIcon}
                    activeIcon={FullCommentIcon}
                    style={styles.iconImage} />
                <LikeBtnComponent
                    activeTintColor={"#FFFFFF"}
                    inActiveTintColor={"#FFFFFF"}
                    inActiveIcon={FullSInActiveLike}
                    activeIcon={FullSActiveLike}
                    style={styles.iconImage} />

            </View>
            <View style={{ ...styles.likeBtnBox, right: 24, zIndex: 1 }}>
                <LikeBtnComponent
                    activeTintColor={"#FFFFFF"}
                    inActiveTintColor={"#FFFFFF"}
                    inActiveIcon={PlayViewCount}
                    activeIcon={PlayViewCount}
                    isLike={true}
                    style={styles.iconImage} />
                <LikeBtnComponent
                    activeTintColor={"#FFFFFF"}
                    inActiveTintColor={"#FFFFFF"}
                    inActiveIcon={FullCommentIcon}
                    activeIcon={FullCommentIcon}
                    style={styles.iconImage} />
                <LikeBtnComponent
                    activeTintColor={"#FFFFFF"}
                    inActiveTintColor={"#FFFFFF"}
                    inActiveIcon={SHARE_ICON}
                    activeIcon={SHARE_ICON}
                    style={styles.iconImage} />

            </View>
            <View style={{ ...styles.reelProgressBarContainer, zIndex: 1 }}>
                <ReelProgressBar value={50} />
            </View>

            <View style={{
                width: '100%',
                height: '100%',
                position: 'relative',
                backgroundColor: '#000',
            }}>
                <Video
                    ref={videoPlayerRef}
                    poster={hasImageUrlExist(videoUrl) ? videoUrl : null}
                    posterResizeMode={'cover'}
                    source={{ uri: videoUrl }}
                    resizeMode='contain'
                    repeat={true}
                    muted={false}
                    onError={(e) => console.log(e)}

                    style={{ ...styles.videoBox, height: AVL_HEIGHT }}
                />
                <VideoOverlay />
            </View>

        </View>
    )
}

export default ReelsItem
const ResolutionSwitch = () => {
    const [value, setValue] = useState(false);
    const [animatedValue] = useState(new Animated.Value(value ? 1 : 0));
    useEffect(() => {
        Animated.timing(animatedValue, {
            toValue: value ? 1 : 0,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, [value]);
    const translateX = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [1, 30], // Adjust the distance of the switch head
    });
    const toggleSwitch = () => {
        const newValue = !value;
        setValue(newValue);
    };
    const theme = useSTheme();
    return (
        <Pressable onPress={toggleSwitch}
            style={{ ...styles.resolutionSwitch, backgroundColor: value ? theme.colors.primaryColor + "30" : "#383b37" }}>
            <View style={styles.innerContainer}>
                <Animated.View
                    style={{
                        transform: [{ translateX }],
                    }}>
                    <View style={{ ...styles.thumbBox, backgroundColor: value ? theme.colors.primaryColor : "#50524e" }} >
                        <EntutoTextView style={styles.thumbBoxText}>{value ? "HD" : "SD"}</EntutoTextView>
                    </View>
                </Animated.View>
            </View>
        </Pressable>
    )
}

const styles = StyleSheet.create({
    videoResolutionContainer: {
        position: 'absolute',
        top: 32,
        right: 24,
        zIndex: 1,
    },
    resolutionSwitch: {
        width: 60,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#333',
        justifyContent: 'center',
    },
    innerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        position: 'relative',
    },
    thumbBox: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',

    },
    thumbBoxText: {
        fontSize: 11,
        color: '#FFF',

    },
    playListCountContainer: {
        position: 'absolute',
        top: 70,
        left: 24,
    },
    playlistProfileText: {
        fontSize: 12,
        color: '#FFF',
    },
    playListBox: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 6,
        paddingLeft: 6,
        position: 'relative',
        // borderWidth: 1,
        height: 32,
    },
    playlistCircleBox: {
        width: 26,
        height: 26,
        borderRadius: 14,
        backgroundColor: '#000000',
        position: 'absolute',
        left: 0,
        top: 3,
        bottom: 0,
        right: 0,
        borderWidth: 1,
        borderColor: '#707070',
    },
    playlistCover: {
        width: 30,
        height: 32,
        resizeMode: 'contain',
        borderRadius: 2,
    },
    playListCountText: {
        marginLeft: 8,
    },
    likeBtnBox: {
        position: 'absolute',
        bottom: 40,
    },
    iconImage: {
        width: 24,
        height: 24,
        marginTop: 24,

    },
    reelProgressBarContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,

    },
    videoBox: {
        width: Dimensions.screenWidth
    },
    videoOverlayGrad: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
    },
})