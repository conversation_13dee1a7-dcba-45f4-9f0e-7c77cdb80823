import React, { useState } from 'react'
import { Image, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import Video from 'react-native-video';
import Dimensions from '../constants/Dimensions';
const ScreenWidth = Dimensions.screenWidth;
const ImageDisplayScreen = ({ route, navigation }) => {
    const { mediaUri } = route.params;
    const [isImgPortation, setisImgPortation] = useState(false);
    const closeVideo = () => {
        navigation.goBack();
    }
    return (
        <View style={styles.container}>
            <StatusBar hidden={true} />
            <View style={styles.videoCrossBox}>
                <TouchableOpacity onPress={() => closeVideo()}>
                    <Image
                        style={styles.arrowIcon}
                        source={require('../assets/Images/icon/Arrow.png')}
                        resizeMode="contain"

                    />
                </TouchableOpacity>
            </View>
            <View>
                <Image
                    style={styles.contentVideo}
                    source={{ uri: mediaUri }}
                    resizeMode="contain"
                // onLoad={item => {
                //     const { width, height } = item.nativeEvent.source;
                //     let isPortrait = height > width;
                //     setisImgPortation(isPortrait);
                // }}
                // resizeMode={`${isImgPortation ? 'cover' : "contain"}`}
                />
            </View>
        </View>
    )
}

export default ImageDisplayScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'black',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
    },
    contentVideo: {
        width: ScreenWidth,
        // aspectRatio: 1,
        backgroundColor: '#000',
        flex: 1,
        height: 231,
        zIndex: 2
    },
    videoCrossBox: {
        position: 'absolute',
        top: Platform.OS=='ios'?50: 20,
        left: 7,
        zIndex: 3,
        padding: 10,
        borderRadius: 24,
        backgroundColor: '#00000040'
    },
    arrowIcon: {
        height: 24,
        width: 24,
        marginEnd: 5,
        opacity: 0.8,
        zIndex: 3,
        tintColor:"#FFF"
    },
})
