import React, { useState } from 'react'
import { Dimensions, Image, LogBox, StyleSheet, Text, View } from 'react-native'
import Video from 'react-native-video';
import Colors from '../../constants/Colors';
import EntutoTextView from '../common/EntutoTextView';
const ScreenWidth = Dimensions.get('window').width;
const ScreenHeight = Dimensions.get('window').height;
const Story = ({ ...props }) => {
    const { story } = props;
    const { url, type, status, block_reason, media_cover } = story || {};
    const [isPortation, setIsPortation] = useState(false);
    const [isImgPortation, setisImgPortation] = useState(false);
    const [heightScaled, setHeightScaled] = useState(231);
    return (
        <View style={styles.container}>
            {status === 'BLOCKED' ? (
                <>
                    <Image
                        source={{ uri: url }}
                        onLoadEnd={props.onImageLoaded}
                        style={styles.blockImage} />
                    <View style={styles.blockErrMsgMainBox}>
                        <View style={styles.blockErrMsgBox}>
                            <EntutoTextView style={styles.blockErrMsgHeading} >Story Blocked</EntutoTextView>
                            <EntutoTextView style={styles.blockErrMsg}>{block_reason}</EntutoTextView>
                        </View>
                    </View>
                </>
            ) : <>
                {type === 'IMAGE' ? (
                    <Image
                        source={{ uri: url }}
                        onLoadEnd={props.onImageLoaded}
                        style={styles.content}

                        width={ScreenWidth}
                        onLoad={item => {
                            const { width, height } = item.nativeEvent.source;
                            let isPortrait = height > width;
                            setisImgPortation(Math.random());
                            let diff = height - width;
                            if (diff > 0 && diff <= 200) {
                                isPortrait = false;
                            }
                            setisImgPortation(isPortrait);
                        }}
                        resizeMode={`${isImgPortation ? 'cover' : "contain"}`}
                    // resizeMode="contain"
                    />
                ) : null}
                {type === 'VIDEO' ? (
                    <Video
                        source={{ uri: url, cache: { size: 150, expiresIn: 3600 } }}
                        poster={media_cover}
                        posterResizeMode={'cover'}
                        paused={props.pause} //|| props.isNewStory
                        // onError={(e) => console.log(e)}
                        onLoad={item => {
                            const { width, height } = item.naturalSize;
                            const heightScaled = height * (ScreenWidth / width);
                            // let isPortrait = height > width;
                            // setIsPortation(height > width);
                            setHeightScaled(heightScaled);
                            props.onVideoLoaded(item);

                            // console.warn(width, height, heightScaled);
                            // console.warn('É PAISAGEM?', isPortrait);
                        }}
                        style={
                            isPortation
                                ? [styles.contentVideoPortation, { height: heightScaled }]
                                : [styles.contentVideo, { height: heightScaled }]
                        }
                    />
                ) : null}

            </>}
        </View>
    )
}

export default Story

const styles = StyleSheet.create({
    container: {
        flex: 1,
        width: '100%',
        height: '100%',
        backgroundColor: 'black',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative'
    },
    content: { width: '100%', height: ScreenHeight, },
    contentVideo: {
        width: ScreenWidth,
        // aspectRatio: 1,
        backgroundColor: '#000',
        flex: 1,
        height: 231,
    },
    contentVideoPortation: {
        width: ScreenWidth,
        // aspectRatio: 1,
        backgroundColor: '#000',
        flex: 1,
        // height: 231,
    },
    imageContent: {
        width: '100%',
        height: '100%',
        flex: 1,
    },
    loading: {
        backgroundColor: 'black',
        height: '100%',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    blockErrMsgMainBox: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.backgroundColor,
        zIndex: 2,

    },
    blockErrMsgBox: {
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 15,
        marginBottom: 15,

    },
    blockErrMsgHeading: {
        fontSize: 20,
        fontWeight: '600',
        paddingHorizontal: 15,
        color: Colors.primaryColor
    },
    blockErrMsg: {
        fontSize: 18,
        paddingHorizontal: 15,
        color: Colors.errorColor
    },
    blockImage: {
        width: 10,
        height: 10,
    }
})
