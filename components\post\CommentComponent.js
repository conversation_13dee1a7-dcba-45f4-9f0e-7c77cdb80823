import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { FlatList, Keyboard, KeyboardAvoidingView, Platform, RefreshControl, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native'
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { AppStateContext } from '../..';
import CommentRow from '../comments/CommentRow';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { encodeHtmlEntitessData } from '../../utils/Utils';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import NotiRowPlaceholder from '../placeholder/NotiRowPlaceholder';
import ProfileImageComponent from '../common/ProfileImageComponent';
import EntutoTextView from '../common/EntutoTextView';
import CustomSnackbar from '../common/CustomSnackbar';
import appData from '../../data/Data';
import { BottomSheetFlatList, BottomSheetFooter, BottomSheetModal, BottomSheetScrollView, BottomSheetTextInput } from '@gorhom/bottom-sheet';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import TempData from '../../data/TempData';


const CommentComponent = ({ postSeq, postProfileSeq, navigation, isSheetComment,
    refreshKey, openPopup, commentComponentClick }) => {
    const { fullUserDetails, changeCommentObj, userProfileImage, homepagePostDataBackup } = useContext(AppStateContext);
    const __ProfileImage = fullUserDetails.hasOwnProperty("_profile_picture") ? fullUserDetails._profile_picture : "";
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    const [commentsList, setcommentsList] = useState([]);
    const [progressLoading, setprogressLoading] = useState(true);
    const [commentTxt, setcommentTxt] = useState("");
    const [showPostButton, setshowPostButton] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const textInputRef = useRef(null);
    const [replyToUser, setreplyToUser] = useState("")
    const [isReply, setisReply] = useState(false);
    const [replyCommentSeq, setreplyCommentSeq] = useState(-1)

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [snackBarType, setsnackBarType] = useState("FAILED");

    const [isCommentModified, setisCommentModified] = useState(false);
    const [profileImage, setprofileImage] = useState(null);

    const openCommentSheetRef = useRef(null);

    useEffect(() => {
        setprofileImage(userProfileImage);
    }, [userProfileImage]);

    useEffect(() => {
        if (openPopup) {
            openCommentSheetRef.current?.present();
        }
    }, [refreshKey])


    useEffect(() => {
        setprogressLoading(true);
        getPostCommentService();
    }, []);

    const commentRowClick = (clickID, obj) => {
        if (clickID == "DELETE") {
            deletePostCommentService(obj.commentSeq);
        }
        if (clickID == "REPLY") {
            setreplyToUser(obj.commentUserName);
            setreplyCommentSeq(obj.commentSeq);
            setisReply(true);
            textInputRef.current.focus();
        }
        if (clickID == "DELETE_OTHERS") {
            deletePostOtherCommentService(obj.commentSeq);
        }
    }
    const renderItem = useCallback(
        (item, i) => {
            let self_comment = false;
            if (item.self_comment == "YES") {
                self_comment = true;
            }
            let self_like = false;
            if (item.self_like == "YES") {
                self_like = true;
            }
            return <CommentRow key={i} navigation={navigation} currentProfileSeq={__ProfileSeq}
                selfComment={self_comment} selfLike={self_like} data={item} postSeq={postSeq}
                postProfileSeq={postProfileSeq}
                commentRowClick={commentRowClick} />;
        },
        []
    );
    const handleRefresh = () => {
        setprogressLoading(true);
        seterrorMsg("");
        getPostCommentService();
    }
    const onCommentChangeText = (text) => {
        if (text.length != 0) {
            setshowPostButton(true);
        }
        else {
            setshowPostButton(false);
        }
        setcommentTxt(text);
    }
    const commentBoxBlur = (e) => {
        if (!isSheetComment) {
            setshowPostButton(false);
        }

    }
    function getPostCommentService() {
        let hashMap = {
            _action_code: "11:GET_POST_COMMENTS",
            post_seq: postSeq,

        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            let currentUserCommented = "NO";
            data.data.map(obj => {
                if (obj.comment_profile_seq == postProfileSeq) {
                    currentUserCommented = "YES";
                }
            })
            if (isCommentModified) {
                changeCommentObj({
                    postSeq: postSeq, commentRight: currentUserCommented,
                    postCommentRefresh: Math.random(),
                })
            }
            setcommentsList([...[], ...data.data]);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setprogressLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setprogressLoading(false);
                setcommentsList([]);
                seterrorMsg(errorMessage);
            }
        });
    }
    const submitPost = () => {
        Keyboard.dismiss();
        if (commentTxt.length != 0) {
            if (isReply) {
                submitPostCommentCommentService(commentTxt);
            }
            else {
                submitPostCommentService(commentTxt);
            }

        }
    }
    function submitPostCommentService(cmtTxt) {
        seterrorMsg("");
        setcommentTxt("");
        let encodeSt = encodeHtmlEntitessData(cmtTxt);
        let hashMap = {
            _action_code: "11:SUBMIT_POST_COMMENT",
            post_seq: postSeq,
            comment: encodeSt,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(true);
            seterrorMsg("");
            setcommentTxt("");
            setshowPostButton(false);
            Keyboard.dismiss();
            getPostCommentService();
            setisCommentModified(true)
            setSnackbarMsg(data.msg);
            setdisplaySnackbar(true);
            setsnackBarType("SUCCESS");
            setrefreshSnackBar(Math.random());

            homepagePostDataBackup.postStatusChangeData.push(
                {
                    post_seq: postSeq,
                    type: "COMMENT",
                    action_code: "SUBMIT_POST_COMMENT",
                    reactionType: "",
                    count: data.data.comment_count,
                    profileSeq: -1

                }
            )
            changeCommentObj({
                postSeq: postSeq, commentRight: "YES",
                postCommentRefresh: Math.random(),
            })

            commentComponentClick("SUBMIT", { comment_count: data.data.comment_count });
            appData._homePagePostRefresh = "YES";
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.comment) {
                            setSnackbarMsg(data.data.comment);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            return
                        }
                    }
                }
                setSnackbarMsg(errorMessage);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
            }
        });
    }
    function submitPostCommentCommentService(cmtTxt) {
        let encodeSt = encodeHtmlEntitessData(cmtTxt);
        seterrorMsg("");
        setcommentTxt("");
        let hashMap = {
            _action_code: "11:SUBMIT_COMMENT_COMMENT",
            post_seq: postSeq,
            comment_seq: replyCommentSeq,
            comment: encodeSt,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(true);
            seterrorMsg("");
            setcommentTxt("");
            setshowPostButton(false);
            Keyboard.dismiss();
            setisCommentModified(true)
            setreplyToUser("");
            setisReply(false);
            setreplyCommentSeq(-1);
            appData._homePagePostRefresh = "YES";
            homepagePostDataBackup.postStatusChangeData.push(
                {
                    post_seq: postSeq,
                    type: "COMMENT",
                    action_code: "SUBMIT_POST_COMMENT",
                    reactionType: "",
                    count: data.data.comment_count,
                    profileSeq: -1
                }
            )
            changeCommentObj({
                postSeq: postSeq, commentRight: "YES",
                postCommentRefresh: Math.random(),
            })
            getPostCommentService();
            commentComponentClick("SUBMIT", { comment_count: data.data.comment_count });
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.comment) {
                            setSnackbarMsg(data.data.comment);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            return
                        }
                    }
                }
                setSnackbarMsg(errorMessage);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
            }
        });
    }
    function deletePostCommentService(commentSeq) {
        let hashMap = {
            _action_code: "11:REMOVE_POST_COMMENT",
            post_seq: postSeq,
            comment_seq: commentSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(true);
            seterrorMsg("");
            setcommentTxt("");
            setshowPostButton(false);
            Keyboard.dismiss();
            setisCommentModified(true)
            getPostCommentService();
            appData._homePagePostRefresh = "YES";
            homepagePostDataBackup.postStatusChangeData.push(
                {
                    post_seq: postSeq,
                    type: "COMMENT",
                    action_code: "REMOVE_POST_COMMENT",
                    reactionType: "",
                    count: data.data.comment_count,
                    profileSeq: -1
                }
            )
            commentComponentClick("SUBMIT", { comment_count: data.data.comment_count });
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setSnackbarMsg(errorMessage);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
            }
        });
    }
    const clearReplyBtnPress = () => {
        setreplyToUser("");
        setisReply(false);
        setreplyCommentSeq(-1);
    }
    function deletePostOtherCommentService(commentSeq) {
        let hashMap = {
            _action_code: "11:DELETE_USER_POST_COMMENT",
            post_seq: postSeq,
            comment_seq: commentSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(true);
            seterrorMsg("");
            setcommentTxt("");
            setshowPostButton(false);
            Keyboard.dismiss();
            setisCommentModified(true)
            getPostCommentService();
            appData._homePagePostRefresh = "YES";
            homepagePostDataBackup.postStatusChangeData.push(
                {
                    post_seq: postSeq,
                    type: "COMMENT",
                    action_code: "REMOVE_POST_COMMENT",
                    reactionType: "",
                    count: data.data.comment_count,
                    profileSeq: -1
                }
            )

            commentComponentClick("SUBMIT", { comment_count: data.data.comment_count });
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setSnackbarMsg(errorMessage);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
            }
        });
    }
    const handleInputChange = useCallback(
        ({
            nativeEvent: { text },
        }) => {
            setcommentTxt(text);


        },
        []
    );
    const renderFooter = useCallback(
        props => (
            <BottomSheetFooter {...props}>
                <View style={style.footerContainer}>
                    <View style={style.inputBox}>
                        <BottomSheetTextInput ref={textInputRef}
                            style={[style.input, isReply && { paddingLeft: 8, }]}
                            onChange={handleInputChange}
                            // multiline={true}
                            value={commentTxt}
                            placeholder="Add a Comment" />
                    </View>
                    {
                        showPostButton &&
                        <TouchableOpacity onPress={() => submitPost()}>
                            <EntutoTextView style={style.postBtn}>POST</EntutoTextView>
                        </TouchableOpacity>
                    }

                </View>
            </BottomSheetFooter>
        ),
        []
    );
    const snapPoints = useMemo(() => ["40%", "80%"], []);
    const dismissModal = () => {
        commentComponentClick("CLOSE", {});
    }
    return (
        <>
            <BottomSheetModal
                ref={openCommentSheetRef}
                enableOverDrag={true}
                snapPoints={snapPoints}
                keyboardBlurBehavior={'restore'}
                keyboardBehavior="extend"
                android_keyboardInputMode="adjustResize"
                onDismiss={() => dismissModal()}
                backgroundStyle={{
                    backgroundColor: theme.colors.backgroundColor
                }}


            // footerComponent={renderFooter}
            >
                <View style={style.commentsView}>
                    <EntutoTextView style={style.commentsText}>Comments</EntutoTextView>
                </View>
                <View style={style.footerContainer}>
                    <View style={style.inputBox}>
                        <BottomSheetTextInput ref={textInputRef}
                            style={[style.input, isReply && { paddingLeft: 8, }]}
                            onChangeText={text => onCommentChangeText(text)}
                            multiline={true}
                            value={commentTxt}
                            onBlur={Keyboard.dismiss}
                            placeholder="Add a Comment"
                            placeholderTextColor={"#CCC"} />
                        {
                            showPostButton &&
                            <TouchableOpacity onPress={() => submitPost()} style={style.postBtn}>
                                <EntutoTextView style={style.postBtnText}>POST</EntutoTextView>
                            </TouchableOpacity>
                        }
                    </View>

                </View>
                <BottomSheetScrollView
                    contentContainerStyle={style.contentContainer}>
                    <View >
                        {
                            errorMsg.length != 0 ?
                                <View style={defaultStyle.errorBoxOutside}>
                                    <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                                </View>
                                :
                                <>
                                    {
                                        progressLoading ?
                                            <NotiRowPlaceholder />
                                            :
                                            <>
                                                {commentsList.map(renderItem)}
                                            </>
                                    }
                                </>
                        }


                    </View>
                </BottomSheetScrollView>
                {/*  */}
            </BottomSheetModal>
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />
        </>
    )
}

export default CommentComponent;

const styles = theme => StyleSheet.create({
    contentContainer: {
        paddingBottom: 80,
        backgroundColor: theme.colors.backgroundColor,
    },
    footerContainer: {
        backgroundColor: theme.colors.backgroundColor,
        minHeight: 76,
        paddingVertical: 5,
        flexDirection: 'row',
        width: theme.dimensions.screenWidth,
        alignItems: 'center'
    },
    profileImage: {
        width: 36,
        height: 36,
        borderRadius: 36,
        marginLeft: 16,
    },
    inputBox: {
        alignItems: 'center',
        flexDirection: 'row',
        flex: 1,
        borderColor: "#FFF",
        borderWidth: 0.5,
        borderRadius: 1,
        marginLeft: 10,
        marginRight: 16,
        marginVertical: 8,
        minHeight: 48,
    },
    input: {
        flex: 1,
        paddingLeft: 20,
        paddingRight: 16,
        color: theme.colors.inputTextColor,


    },
    replyBox: {
        alignItems: 'center',
        backgroundColor: theme.colors.backgroundColor,
        marginStart: 8,
        borderRadius: 10,
        padding: 4,
        flexDirection: 'row',

    },
    replyTxt: {
        fontSize: theme.calculateFontSize(12)
    },
    crossBox: {
        height: 16,
        width: 16,
        borderRadius: 16,
        padding: 2,
        backgroundColor: theme.colors.darkGray,
        alignItems: 'center',
        justifyContent: 'center',
        marginStart: 4,
    },

    postBtn: {
        backgroundColor: theme.colors.primaryColor,
        margin: 4,
        height: 42,
        justifyContent: 'center',
        alignItems: 'center',

    },
    postBtnText: {
        color: "#FFFFFF",
        fontSize: theme.calculateFontSize(14),
        paddingHorizontal: 20,
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
    },
    commentsView: {
        borderBottomColor: '#CCC',
        borderBottomWidth: 1,
    },
    commentsText: {
        color: "#FFFFFF",
        fontSize: theme.calculateFontSize(18),
        fontFamily: theme.getFontFamily('bold'),
        paddingHorizontal: 16,
        paddingBottom: 16,
        paddingTop: 8
    }
})
