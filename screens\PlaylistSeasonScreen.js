import {
  FlatList,
  Image,
  ImageBackground,
  Modal,
  Platform,
  Pressable,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Animated,
} from 'react-native';
import {useCallback, useEffect, useState, useRef} from 'react';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import LinearGradient from 'react-native-linear-gradient';
import {
  checkValueLength,
  hasImageUrlExist,
  onMaxTextLengthReached,
  pluralize,
  decodeHtmlEntitessData,
  secondsToHms,
  encryptOnlyNumber,
} from '../utils/Utils';
import BackBtn from '../assets/Images/icon/back.png';
import TickIcon from '../assets/Images/icon/list_tick_icon.png';
import SHARE_ICON from '../assets/Images/icon/share_icon.png';
import EntutoTextView from '../components/common/EntutoTextView';
import CustomStatusBar from '../components/common/CustomStatusBar';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {Dropdown} from 'react-native-element-dropdown';
import Share from 'react-native-share';

import ServerConnector from '../utils/ServerConnector';
import {RedirectionUrlFunction} from '../utils/RedirectionUrl';
import {
  _RedirectionErrorList,
  MaxPlaylistDescTxtLimit,
  MaxPlaylistTitleTxtLimit,
  MaxPlaylistClipTxtLimit,
} from '../utils/Appconfig';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import TempData from '../data/TempData';
import ProgressiveImage from '../components/common/ProgressiveImage';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
const PlaylistSeasonScreen = ({navigation, route}) => {
  const {showSeq} = route.params;
  const insets = useSafeAreaInsets();
  const COVER_HEIGHT = Platform.OS == 'ios' ? 219 - insets.top / 2 : 219;
  const theme = useSTheme();
  const style = useSThemedStyles(styles);

  const [showLoading, setShowLoading] = useState(true);
  const [progressLoading, setProgressLoading] = useState(false);
  const [_, setPageErrorMessage] = useState('');
  const [showDetailsData, setShowDetailsData] = useState({
    showTitle: '',
    showBannerImage: null,
    showDescription: '',
    showSeasonCount: 0,
    showEpisodeCount: 0,
    yearOfReleased: '',
    viewType: '',
  });

  // New state variables for the refactored UI
  const [seasonList, setSeasonList] = useState([]);
  const [selectedSeason, setSelectedSeason] = useState('');
  const [clipList, setClipList] = useState([]);
  const [castList, setCastList] = useState(['Sid', 'Varenyam']);
  const [writersList, setWritersList] = useState(['John Doe', 'Jane Smith']);
  const [directorsList, setDirectorsList] = useState(['Steven Spielberg']);
  const [genresList, setGenresList] = useState(['Drama', 'Comedy']);
  const [showCreditsPopup, setShowCreditsPopup] = useState(false);
  const [seasonDropdownDisplay, setSeasonDropdownDisplay] = useState(false); // Controls whether to show the dropdown
  const [seasonLoading, setSeasonLoading] = useState(false);
  const [currentEpisodeSeq, setCurrentEpisodeSeq] = useState(null);

  useEffect(() => {
    getSeasonsService();
  }, []);

  // Lorem ipsum text for descriptions
  const loremIpsumShowDescription =
    'Show Description Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem.';

  const loremIpsumSeasonDescription =
    'Season Description  voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.';

  const loremIpsumClipDescription =
    'Neque porro quisquam est, labore et dolore magnam.';

  // Get seasons for the dropdown
  const getSeasonsService = () => {
    setShowLoading(true);
    let hashMap = {
      _action_code: '11:GET_PLAYLIST_EPISODES',
      type: 'SHOW',
      sequence: showSeq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        let viewType = 'SEASON';
        if (data.type == 'EPISODE') {
          viewType = 'EPISODE';
        }

        const mediaData = [];
        const seasonOptions = [];

        data.data.map(item => {
          let dataObj = {};
          dataObj.id = viewType + '_' + Math.random();
          dataObj.viewTypeSeq =
            viewType == 'EPISODE' ? item.episode_seq : item.season_seq;
          dataObj.media_file =
            viewType == 'EPISODE' ? item.thumb_file : item.cover_image;
          dataObj.preview_file =
            viewType == 'EPISODE' ? item.preview_file : null;
          dataObj.view_type = viewType;
          dataObj.title = viewType == 'EPISODE' ? item.title : item.title;
          // Use lorem ipsum for descriptions
          dataObj.description = item.description;
          dataObj.banner_file =
            viewType == 'EPISODE' ? item.banner_file : item.banner_file;
          dataObj.release_year = item.release_year;
          mediaData.push(dataObj);

          // Create season options for dropdown
          if (viewType == 'SEASON') {
            seasonOptions.push({
              label: `Season ${item.title}`,
              value: item.season_seq,
              description: item.description, // Use lorem ipsum for season descriptions
            });
          }
        });

        if (data.hasOwnProperty('show')) {
          setShowDetailsData({
            showTitle: data.show.title, // 50 char title
            showBannerImage: checkValueLength(data.show.banner_file)
              ? data.show.banner_file
              : data.show.thumb_file,
            // showDescription: loremIpsumShowDescription, // Use lorem ipsum for show description
            showDescription: data.show.description,
            showSeasonCount: mediaData.length,
            showEpisodeCount: mediaData.length,
            yearOfReleased: data.show.release_year,
            viewType: viewType,
          });

          // Set cast, writers, directors, and genre lists if available
          if (data.show.hasOwnProperty('cast') && data.show.cast.length > 0) {
            setCastList(data.show.cast);
          }

          if (
            data.show.hasOwnProperty('writers') &&
            data.show.writers.length > 0
          ) {
            setWritersList(data.show.writers);
          }

          if (
            data.show.hasOwnProperty('directors') &&
            data.show.directors.length > 0
          ) {
            setDirectorsList(data.show.directors);
          }

          if (
            data.show.hasOwnProperty('genres') &&
            data.show.genres.length > 0
          ) {
            setGenresList(data.show.genres);
          }
        }

        setSeasonList(seasonOptions);
        setSeasonDropdownDisplay(seasonOptions.length > 0);

        // Don't auto-select the first season - leave it unselected
        // if (seasonOptions.length > 0) {
        //   setSelectedSeason(seasonOptions[0].value);
        //   getClipsForSeason(seasonOptions[0].value);
        // }

        setShowLoading(false);
        setProgressLoading(false);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        setProgressLoading(false);
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          setSeasonList([]);
          setPageErrorMessage(errorMessage);
        }
      },
    );
  };

  // Get clips for the selected season
  const getClipsForSeason = seasonSeq => {
    setSeasonLoading(true);

    // First get episodes for the selected season
    let clipsHashMap = {
      _action_code: '11:GET_PLAYLIST_CLIPS',

      season_seq: seasonSeq,
    };
    const connector = new ServerConnector();
    connector.postData(
      clipsHashMap,
      clipsData => {
        // success method for getting clips
        setClipList(clipsData.data || []);
        setSeasonLoading(false);
      },
      (errorCode, errorMessage, data) => {
        // failure method for getting clips
        setSeasonLoading(false);
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          setClipList([]);
          setPageErrorMessage(errorMessage);
        }
      },
    );
  };
  const backButtonPress = () => {
    navigation.goBack(null);
  };

  const sharePlaylistLink = async () => {
    // Submit share count to server
    submitShareCountService();

    // Create share link for the playlist
    // Since creationOfCopyLink doesn't have a SHOW type yet, we'll create the URL manually
    // using the same pattern as posts and profiles
    let encryptedShowSeq = encryptOnlyNumber(showSeq);
    let copyLinkText = 'https://sotrue.co.in/show/?id=' + encryptedShowSeq;

    // Configure share options
    const shareOptions = {
      message: 'Check out this amazing show on SoTrue!\n',
      url: copyLinkText,
    };

    // Open share dialog
    try {
      const shareResponse = await Share.open(shareOptions);
    } catch (error) {
      // Handle error silently
      console.log('Share error:', error.message);
    }
  };

  function submitShareCountService() {
    // Use the same action code as for posts but with show_seq parameter
    let hashMap = {
      _action_code: '11:UPDATE_SHARE_COUNT',
      show_seq: showSeq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      () => {
        // success method - nothing to do
      },
      () => {
        // failure method - nothing to do
      },
    );
  }
  const HeaderComponent = () => {
    // State for expanded description
    const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

    // Get the appropriate description based on whether a season is selected
    const getDescription = () => {
      // If a season is selected and there's a season-specific description, use that
      const seasonObj = seasonList.find(
        season => season.value === selectedSeason,
      );
      if (selectedSeason && seasonObj && seasonObj.description) {
        return seasonObj.description;
      }
      // Otherwise use the show description
      return showDetailsData.showDescription;
    };

    const description = getDescription();

    // Toggle description expansion
    const toggleDescription = () => {
      setIsDescriptionExpanded(!isDescriptionExpanded);
    };

    return (
      <>
        <View style={style.fullWidthContainer}>
          <ProgressiveImage
            source={
              hasImageUrlExist(showDetailsData.showBannerImage)
                ? {uri: showDetailsData.showBannerImage}
                : null
            }
            style={{
              ...style.playlistCoverImage,
              height: COVER_HEIGHT,
              width: '100%',
            }}
            resizeMode="cover"
            defaultImageSource={require('../assets/Images/default_image.jpg')}>
            {/* {theme.appThemeType == 'DARK' ? (
              <LinearGradient
                colors={[
                  theme.colors.playlistLCOne,
                  theme.colors.playlistLCTwo,
                  theme.colors.playlistLCThree,
                  theme.colors.playlistLCFour,
                ]}
                style={style.linearGradient}
              />
            ) : (
              <LinearGradient
                colors={[
                  theme.colors.playlistLCOne,
                  theme.colors.playlistLCThree,
                  theme.colors.playlistLCFour,
                ]}
                style={style.linearGradient}
              />
            )} */}
          </ProgressiveImage>
          <View
            style={{
              ...style.playlistCoverContainer,
              minHeight: COVER_HEIGHT,
              marginBottom: 15,
            }}>
            <View
              style={{
                ...style.topHeader,
                marginTop: Platform.OS == 'ios' ? 10 : 40,
                flexDirection: 'row',
                justifyContent: 'space-between',
                width: '100%',
                paddingHorizontal: 16, // Add horizontal padding to ensure icons are within visible area
              }}>
              <View>
                <Pressable
                  onPress={() => backButtonPress()}
                  android_ripple={{
                    color: theme.colors.pressableRippleColor,
                    borderless: true,
                    radius: 30,
                  }}>
                  <View style={{padding: 10}}>
                    <Image
                      style={style.backBtnIcon}
                      resizeMode="contain"
                      source={BackBtn}
                    />
                  </View>
                </Pressable>
              </View>
              <View>
                <Pressable
                  onPress={() => sharePlaylistLink()}
                  android_ripple={{
                    color: theme.colors.pressableRippleColor,
                    borderless: true,
                    radius: 30,
                  }}>
                  <View style={{padding: 10}}>
                    <Image
                      style={style.backBtnIcon}
                      resizeMode="contain"
                      source={SHARE_ICON}
                      tintColor="#FFFFFF"
                    />
                  </View>
                </Pressable>
              </View>
            </View>
          </View>
        </View>
        <ContentWrapper>
          <View style={style.playlistTitleBox}>
            <View style={style.titleBox}>
              <EntutoTextView style={style.masterBannerTitle}>
                {/* Show title with up to 50 characters */}
                {onMaxTextLengthReached(showDetailsData.showTitle, 50)}
              </EntutoTextView>
            </View>
            <View style={style.yearCountBox}>
              <EntutoTextView style={style.yearCountBoxText}>
                {/* {showDetailsData.yearOfReleased} */}
                2025
              </EntutoTextView>
              <EntutoTextView style={{...style.yearCountBoxText, marginTop: 6}}>
                {clipList.length > 0 ? (
                  <>
                    {pluralize('Slice', clipList.length) +
                      ' ' +
                      clipList.length}
                  </>
                ) : (
                  ''
                )}
              </EntutoTextView>
            </View>
          </View>
          <View style={style.descBox}>
            {/* Description with "...more" functionality */}
            {description && description.length > MaxPlaylistDescTxtLimit ? (
              <>
                <EntutoTextView style={style.descBoxText}>
                  {isDescriptionExpanded
                    ? description
                    : onMaxTextLengthReached(
                        description,
                        MaxPlaylistDescTxtLimit,
                        '',
                      )}
                  <EntutoTextView
                    style={{
                      color: theme.colors.primaryColor,
                      fontWeight: 'bold',
                    }}
                    onPress={toggleDescription}>
                    {isDescriptionExpanded ? ' less' : '...more'}
                  </EntutoTextView>
                </EntutoTextView>
              </>
            ) : (
              <EntutoTextView style={style.descBoxText}>
                {description || 'No description available'}
              </EntutoTextView>
            )}
          </View>
        </ContentWrapper>
      </>
    );
  };
  // Handle season dropdown change
  const seasonValueChangeHandler = value => {
    setSelectedSeason(value);
    getClipsForSeason(value);
  };

  // Render dropdown item
  const renderDropdownItem = item => {
    return (
      <View style={style.dropdownItem}>
        <EntutoTextView allowFontScaling={false} style={style.dropdownItemText}>
          {item.label}
        </EntutoTextView>
        {/* {item.value === selectedSeason && (
          <Image
            style={style.expandBtnIcon}
            resizeMode="contain"
            source={TickIcon}
          />
        )} */}
      </View>
    );
  };

  // Handle clip item press
  const clipItemPress = (clickID, obj) => {
    if (clickID == 'ITEM') {
      const dataObj = clipList[obj.index];
      storePlaylistResumeData(dataObj.post_seq);
      navigation.navigate('VideoContentScreen', {
        postSeq: dataObj.post_seq,
        postProfileSeq: dataObj.profile_seq,
        episodeSeq: currentEpisodeSeq, // Use the current episode sequence
        cameFrom: 'EPISODE',
      });
    }
  };

  // Store playlist resume data
  const storePlaylistResumeData = postSeq => {
    const tempList = TempData.playlistResumeData || [];
    let episodeExist = false;
    tempList.forEach(item => {
      if (item.episodeSeq === selectedSeason) {
        episodeExist = true;
        item.playPostSeq = postSeq;
      }
    });
    if (!episodeExist) {
      tempList.push({episodeSeq: selectedSeason, playPostSeq: postSeq});
    }
    TempData.refreshPlayList = 'YES';
    TempData.playlistResumeData = tempList;
  };
  // Render episode clip item
  const EpisodeClipItem = ({data, index}) => {
    // Use lorem ipsum for clip descriptions
    const clipDescription = loremIpsumClipDescription;
    const [applyRatio, setApplyRatio] = useState(false);

    // Create bounce animation value
    const bounceAnim = useRef(new Animated.Value(1)).current;
    useEffect(() => {
      if (hasImageUrlExist(data.media_cover)) {
        Image.getSize(data.media_cover, (width, height) => {
          let isApplied = false;
          if (parseInt(width) > 0 && parseInt(height) > 0) {
            let computeValue = parseInt(height) / parseInt(width);
            if (computeValue >= 1.15) {
              isApplied = true;
            }
          }
          setApplyRatio(isApplied);
        });
      }

      // Only animate if this is the first item
      if (index === 0) {
        // Create bounce animation sequence
        const createBounceSequence = () => {
          return Animated.sequence([
            Animated.spring(bounceAnim, {
              toValue: 1.2,
              friction: 3,
              useNativeDriver: true,
            }),
            Animated.spring(bounceAnim, {
              toValue: 1,
              friction: 3,
              useNativeDriver: true,
            }),
            Animated.delay(500),
          ]);
        };

        // Create 4 bounce sequences
        Animated.sequence([
          createBounceSequence(),
          createBounceSequence(),
          createBounceSequence(),
          createBounceSequence(),
        ]).start();
      }
    }, []);

    return (
      <View>
        <TouchableOpacity onPress={() => clipItemPress('ITEM', {index: index})}>
          <View style={style.episodeClipItemContainer}>
            <View style={style.episodeClipItemLeftContainer}>
              <Animated.Image
                source={
                  hasImageUrlExist(data.media_cover)
                    ? {uri: data.media_cover}
                    : null
                }
                style={[
                  style.episodeClipItemLeftImage,
                  applyRatio ? style.episodeClipItemLeftImageRatio : null,
                  {transform: [{scale: bounceAnim}]},
                ]}
              />
            </View>
            <View style={style.episodeClipItemRightContainer}>
              <EntutoTextView
                allowFontScaling={false}
                style={{...style.episodeClipItemLeftText, marginBottom: 1}}
                numberOfLines={3}>
                {onMaxTextLengthReached(
                  data.episode_title,
                  MaxPlaylistClipTxtLimit,
                  '',
                )}
              </EntutoTextView>
              <EntutoTextView
                allowFontScaling={false}
                style={{...style.episodeClipItemLeftText, marginBottom: 0}}
                numberOfLines={2}>
                {onMaxTextLengthReached(
                  data.episode_description,
                  MaxPlaylistClipTxtLimit,
                  '',
                )}
              </EntutoTextView>
              <View style={style.bottomTextBox}>
                <EntutoTextView style={style.episodeClipItemClipText}>
                  Slice {index + 1}
                </EntutoTextView>
                <EntutoTextView style={style.episodeClipItemClipText}>
                  {secondsToHms(data.video_duration)}
                </EntutoTextView>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const keyExtractor = useCallback(
    (item, index) => `${item.post_seq}_${index}`,
    [],
  );

  const ItemSeparatorComponent = () => {
    return <View style={{height: 10}} />;
  };

  // Season dropdown component
  const SeasonDropdownComponent = () => {
    if (!seasonDropdownDisplay || seasonList.length === 0) return null;

    return (
      <View>
        <Dropdown
          style={style.dropdown}
          placeholderStyle={{
            ...style.dropdownPlaceholderStyle,
            color: theme.colors.primaryColor,
          }}
          selectedTextStyle={style.dropdownSelectedTextStyle}
          inputSearchStyle={style.dropdownInputSearchStyle}
          containerStyle={style.dropdownContainerStyle}
          itemTextStyle={{color: theme.colors.primaryTextColor}}
          activeColor={`${theme.colors.primaryColor}30`}
          data={seasonList}
          maxHeight={200}
          labelField="label"
          valueField="value"
          placeholder="Select Season"
          value={selectedSeason}
          onChange={item => {
            seasonValueChangeHandler(item.value);
          }}
          renderRightIcon={() => (
            <Image
              style={[
                style.expandBtnIcon,
                {
                  transform: [{rotate: '-90deg'}],
                },
              ]}
              resizeMode="contain"
              source={BackBtn}
            />
          )}
          renderItem={renderDropdownItem}
        />
      </View>
    );
  };

  // Cast section component
  const CastSectionComponent = () => {
    if (!castList || castList.length === 0) return null;

    // Function to handle "...more" text press
    const handleMorePress = () => {
      setShowCreditsPopup(true);
    };

    // Get first two cast members for display
    const displayCast = castList.slice(0, 2);
    const hasMoreCast = true;

    return (
      <View>
        <View style={style.castRow}>
          <EntutoTextView style={style.castLabel}>
            Cast & Creators:&nbsp;
          </EntutoTextView>
          <View style={style.castNamesContainer}>
            {displayCast.map((item, index) => (
              <EntutoTextView key={`cast_${index}`} style={style.castName}>
                {item.name || item}
                {index < displayCast.length - 1 ? ', ' : ''}
              </EntutoTextView>
            ))}
            {hasMoreCast && (
              <TouchableOpacity onPress={handleMorePress}>
                <EntutoTextView style={style.moreText}>...more</EntutoTextView>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Credits Popup */}
        <Modal
          animationType="fade"
          transparent={true}
          visible={showCreditsPopup}
          onRequestClose={() => setShowCreditsPopup(false)}>
          <TouchableOpacity
            style={style.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowCreditsPopup(false)}>
            <View style={style.modalContent}>
              <ScrollView>
                {/* Cast Section */}
                <View style={style.creditsSection}>
                  <EntutoTextView style={style.creditsSectionTitle}>
                    Cast
                  </EntutoTextView>
                  <View style={style.creditsList}>
                    {castList.map((item, index) => (
                      <EntutoTextView
                        key={`popup_cast_${index}`}
                        style={style.creditsItem}>
                        {item.name || item}
                      </EntutoTextView>
                    ))}
                  </View>
                </View>

                {/* Writers Section */}
                {writersList.length > 0 && (
                  <View style={style.creditsSection}>
                    <EntutoTextView style={style.creditsSectionTitle}>
                      Writers
                    </EntutoTextView>
                    <View style={style.creditsList}>
                      {writersList.map((item, index) => (
                        <EntutoTextView
                          key={`popup_writer_${index}`}
                          style={style.creditsItem}>
                          {item.name || item}
                        </EntutoTextView>
                      ))}
                    </View>
                  </View>
                )}

                {/* Directors Section */}
                {directorsList.length > 0 && (
                  <View style={style.creditsSection}>
                    <EntutoTextView style={style.creditsSectionTitle}>
                      Director
                    </EntutoTextView>
                    <View style={style.creditsList}>
                      {directorsList.map((item, index) => (
                        <EntutoTextView
                          key={`popup_director_${index}`}
                          style={style.creditsItem}>
                          {item.name || item}
                        </EntutoTextView>
                      ))}
                    </View>
                  </View>
                )}

                {/* Genre Section */}
                {genresList.length > 0 && (
                  <View style={style.creditsSection}>
                    <EntutoTextView style={style.creditsSectionTitle}>
                      Genre
                    </EntutoTextView>
                    <View style={style.creditsList}>
                      {genresList.map((item, index) => (
                        <EntutoTextView
                          key={`popup_genre_${index}`}
                          style={style.creditsItem}>
                          {item.name || item}
                        </EntutoTextView>
                      ))}
                    </View>
                  </View>
                )}
              </ScrollView>
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
    );
  };

  const handleRefresh = () => {
    setProgressLoading(true);
    getSeasonsService();
  };

  // Content wrapper with consistent horizontal padding
  const ContentWrapper = ({children, noPadding}) => (
    <View style={noPadding ? null : style.contentWrapper}>{children}</View>
  );

  return (
    <>
      <CustomStatusBar
        translucent={true}
        hidden={true}
        backgroundColor={'transparent'}
      />
      <CustomProgressDialog showLoading={showLoading || seasonLoading} />
      <View
        style={{
          ...style.container,
          backgroundColor: theme.colors.backgroundColor,
        }}>
        <FlatList
          keyboardShouldPersistTaps={'handled'}
          contentContainerStyle={{
            paddingBottom: 80,
            minHeight: clipList.length === 0 ? 500 : 'auto',
          }}
          removeClippedSubviews
          data={clipList}
          ListHeaderComponent={
            <>
              <HeaderComponent />
              <ContentWrapper>
                <CastSectionComponent />
                <SeasonDropdownComponent />
              </ContentWrapper>
            </>
          }
          initialNumToRender={8}
          renderItem={({item, index}) => (
            <ContentWrapper>
              <EpisodeClipItem data={item} index={index} />
            </ContentWrapper>
          )}
          keyExtractor={keyExtractor}
          ItemSeparatorComponent={ItemSeparatorComponent}
          refreshControl={
            <RefreshControl
              tintColor={theme.colors.primaryColor}
              refreshing={progressLoading}
              onRefresh={() => handleRefresh()}
            />
          }
          ListEmptyComponent={
            !showLoading &&
            !seasonLoading && (
              <ContentWrapper>
                <View style={style.emptyContainer}>
                  <EntutoTextView style={style.emptyText}>
                    {selectedSeason
                      ? 'No clips available for this season'
                      : 'Select a segment to see the clips'}
                  </EntutoTextView>
                </View>
              </ContentWrapper>
            )
          }
        />
      </View>
    </>
  );
};

export default PlaylistSeasonScreen;

const styles = theme =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.backgroundColor,
      position: 'relative',
      width: '100%',
      height: '100%',
    },
    fullWidthContainer: {
      width: '100%',
      overflow: 'hidden',
    },
    playlistCoverContainer: {
      width: '100%',
      position: 'relative',
      zIndex: 1,
    },
    linearGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    playlistCoverImage: {
      width: '100%',
      resizeMode: 'cover',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      aspectRatio: 16 / 9, // 1920x1080 aspect ratio
    },
    topHeader: {
      // marginHorizontal removed as we're using paddingHorizontal in the inline style
      flexDirection: 'row',
    },
    backBtnIcon: {
      height: 24,
      width: 24,
      tintColor: '#FFFFFF',
      resizeMode: 'contain',
    },
    expandBtnIcon: {
      height: 24,
      width: 24,
      tintColor: theme.colors.primaryColor,
      resizeMode: 'contain',
    },

    playlistTitleBox: {
      flexDirection: 'row',
      minHeight: 50,
    },
    titleBox: {
      maxHeight: theme.dimensions.playlistTitleBoxHeight,
      marginTop: 2,
      flex: 1,
    },
    masterBannerTitle: {
      fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistTitleText),
      color: theme.colors.playlistBannerTitleColor,
      flexWrap: 'wrap',
      width: '100%', // Ensure it can accommodate up to 50 characters
    },
    yearCountBox: {
      justifyContent: 'center',
    },
    yearCountBoxText: {
      fontSize: theme.calculateFontSizeNew(
        theme.dimensions.playlistSeasonCountText,
      ), //12
      color: theme.colors.playlistBannerYearCountColor,
    },
    descBox: {
      marginTop: theme.dimensions.playlistDescTopMargin,
      // marginBottom: 16,
    },
    descBoxText: {
      fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistDescText), //11
      color: theme.colors.descriptionBoxText,
      flexWrap: 'wrap',
      width: '100%', // Ensure it can accommodate up to 240 characters
    },
    // Common content wrapper with consistent padding
    contentWrapper: {
      paddingHorizontal: 28,
    },
    // Cast section styles
    castRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 24,
    },
    castLabel: {
      fontSize: theme.calculateFontSizeNew(12),
      color: theme.colors.descriptionBoxText,
      fontWeight: 'bold',
    },
    castNamesContainer: {
      flex: 1,
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    castName: {
      fontSize: theme.calculateFontSizeNew(12),
      color: theme.colors.descriptionBoxText,
    },
    moreText: {
      fontSize: theme.calculateFontSizeNew(12),
      color: theme.colors.primaryColor,
      marginLeft: 4,
      fontWeight: 'bold',
    },
    // Credits popup styles
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      width: '80%',
      maxHeight: '70%',
      backgroundColor: theme.colors.backgroundColor,
      borderRadius: 8,
      padding: 20,
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    creditsSection: {
      marginBottom: 20,
      alignItems: 'center',
    },
    creditsSectionTitle: {
      fontSize: theme.calculateFontSizeNew(16),
      color: theme.colors.primaryColor,
      fontWeight: 'bold',
      marginBottom: 8,
      textAlign: 'center',
    },
    creditsList: {
      width: '100%',
      alignItems: 'center',
    },
    creditsItem: {
      fontSize: theme.calculateFontSizeNew(14),
      color: theme.colors.primaryTextColor,
      marginBottom: 4,
      textAlign: 'center',
    },
    // Legacy styles kept for compatibility

    castImage: {
      width: 70,
      height: 70,
      borderRadius: 35,
      marginBottom: 8,
    },
    castRole: {
      fontSize: theme.calculateFontSizeNew(12),
      color: theme.colors.secondaryTextColor,
      textAlign: 'center',
    },
    // Season dropdown styles
    dropdown: {
      height: 40,
      minWidth: 250,
      borderRadius: 4,
      marginBottom: 15,
      color: theme.colors.primaryColor,
      position: 'relative', // Ensure proper positioning of absolute elements
      alignSelf: 'flex-start', // Only take up as much width as content requires
      fontFamily: theme.getFontFamily(),
    },
    dropdownPlaceholderStyle: {
      fontSize: theme.calculateFontSizeNew(14),
      color: theme.colors.primaryColor,
      textAlign: 'left', // Ensure text is left-aligned
      fontFamily: theme.getFontFamily(),
    },
    dropdownSelectedTextStyle: {
      fontSize: theme.calculateFontSizeNew(14),
      color: theme.colors.primaryColor,
      fontWeight: 'bold',
      textAlign: 'left', // Ensure text is left-aligned
      paddingHorizontal: 10, // Add some padding for better appearance
      fontFamily: theme.getFontFamily(),
    },
    dropdownInputSearchStyle: {
      height: 40,
      fontSize: theme.calculateFontSizeNew(14),
      color: theme.colors.primaryTextColor,
      fontFamily: theme.getFontFamily(),
    },
    dropdownContainerStyle: {
      backgroundColor: theme.colors.backgroundColor,
      minWidth: 250,
      paddingHorizontal: 8,
      paddingVertical: 12,
      borderRadius: 4,
      fontFamily: theme.getFontFamily(),
    },
    dropdownIcon: {
      marginRight: 5,
    },
    dropdownItem: {
      padding: 10,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.primaryColor,
      backgroundColor: theme.colors.backgroundColor,
      marginBottom: 4,
    },
    dropdownItemText: {
      fontSize: theme.calculateFontSizeNew(14),
      color: theme.colors.primaryColor,
      textAlign: 'center',
      flex: 1,
    },
    icon: {
      marginLeft: 5,
    },
    // Clips section styles

    // Episode clip item styles
    episodeClipItemContainer: {
      flexDirection: 'row',
      // paddingVertical: 1,
      // backgroundColor: 'red',
    },
    episodeClipItemLeftContainer: {
      height: 74,
      width: 131,
      overflow: 'hidden',
      backgroundColor: theme.colors.backgroundColor,
    },
    episodeClipItemLeftImage: {
      height: 74,
      width: 131,
      resizeMode: 'cover',
    },
    episodeClipItemLeftImageRatio: {
      height: undefined,
      aspectRatio: 9 / 16,
    },
    episodeClipItemRightContainer: {
      marginStart: 12,
      marginEnd: 12,
      flex: 1,
    },
    episodeClipItemLeftText: {
      fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistDescText),
      color: '#A1A1A1',
      flex: 1,
      flexWrap: 'wrap',
      width: '100%', // Ensure it can accommodate up to 80 characters
    },
    bottomTextBox: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      fontWeight: 'bold',
      fontWeight: 'bold',
    },
    episodeClipItemClipText: {
      fontSize: theme.calculateFontSizeNew(11),
      color: '#A1A1A1',
      fontWeight: 'bold',
    },
    // Empty state styles
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
      marginTop: 40,
      height: 200,
    },
    emptyText: {
      fontSize: theme.calculateFontSizeNew(16),
      color: '#A1A1A1',
      textAlign: 'center',
      width: '100%',
    },
  });
// Now we are refactoring this entire UI
// OLD behaviour => PlaylistSeasonScreen contains Seasons of the show , on clicking it we navigate to PlaylistSeasonEpisodeScreen where there are Episodes of that season on clicking the episode card we go to PlaylistEpisodeScreen where there are rows of clips of that season's episode
// NEW BEHAVIOUR => THE PlaylistSeasonScreen
