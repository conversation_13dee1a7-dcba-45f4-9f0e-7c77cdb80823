import { Image, Pressable, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { FlatList } from 'react-native-gesture-handler';
import SuccessFailureMsgBox from './SuccessFailureMsgBox';
import EntutoTextView from './EntutoTextView';
import CustomStatusBar from './CustomStatusBar';
import HeadingTxt from './HeadingTxt';
import CustomSnackbar from './CustomSnackbar';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import ProgressiveImage from './ProgressiveImage';
import { hasImageUrlExist, preventDoubleClick } from '../../utils/Utils';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import TempData from '../../data/TempData';
import appData from '../../data/Data';
import { requestLocationPermission } from '../../utils/PermissionManager';
// import Geolocation from 'react-native-geolocation-service';
import { GOOGLE_MAP_KEY } from '../../utils/Appconfig';
import CustomProgressDialog from './CustomProgressDialog';


const LocationBoxComponent = ({ selectedValue, title, selectBoxClick }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [disableUpdateBtn, setDisableUpdateBtn] = useState(true);
    const [searchQuery, setSearchQuery] = useState("");
    const [isTyped, setIsTyped] = useState(false);
    const [errorMsg, setErrorMsg] = useState("");
    const [selectDataList, setSelectDataList] = useState([]);
    const [selectDataListB, setSelectDataListB] = useState([]);
    const [selectedV, setSelectedV] = useState([]);
    const [shoLoading, setShoLoading] = useState(true);
    const [isFirstTime, setIsFirstTime] = useState(true);
    useEffect(() => { // Nearby Location feature is closed for Now
        // if (parseFloat(TempData.userCurrentLocation.latitude) == 0) {
        //     getDeviceLocation();
        // }
        // else {
        //     if (preventDoubleClick(appData.buttonClickTime)) {
        //         appData.buttonClickTime = new Date();
        //         // nearbyPlaces(TempData.userCurrentLocation.latitude, TempData.userCurrentLocation.longitude);
        //         if (TempData.userPlacesData.length == 0) {
        //             nearbyPlaces(TempData.userCurrentLocation.latitude, TempData.userCurrentLocation.longitude);
        //         } else {
        //             createTheList(TempData.userPlacesData);
        //         }
        //     }
        // }
    }, [selectedValue]);

    const createTheList = (listData = []) => {
        let tempList = [];
        listData.forEach((item, index) => {
            let dataRow = {}
            dataRow.isChecked = false;
            dataRow.config_key = item;
            dataRow.display_value = item;
            if (selectedValue == item) {
                dataRow.isChecked = true;
            }
            tempList.push(dataRow);
        });
        setSelectedV([...[], ...[selectedValue]]);
        setShoLoading(false);
        setSelectDataList(tempList);
        setSelectDataListB(tempList);
        setIsFirstTime(false)
    }
    const getDeviceLocation = () => {
        setShoLoading(false)
        // const checkPermission = requestLocationPermission();
        // checkPermission.then(res => {
        //     if (res) {
        //         Geolocation.getCurrentPosition(
        //             (position) => {
        //                 if (preventDoubleClick(appData.buttonClickTime)) {
        //                     appData.buttonClickTime = new Date();
        //                     TempData.userCurrentLocation.latitude = position.coords.latitude;
        //                     TempData.userCurrentLocation.longitude = position.coords.longitude;
        //                     nearbyPlaces(position.coords.latitude, position.coords.longitude);
        //                 }
        //             },
        //             (error) => {
        //                 setShoLoading(false);
        //                 // See error code charts below.
        //                 // console.log(error.code, error.message);
        //             },
        //             { enableHighAccuracy: true, timeout: 15000, maximumAge: 1000 }
        //         );
        //     }
        //     else {
        //         setShoLoading(false);
        //     }
        // })
    }
    const nearbyPlaces = async (lat, lng) => {
        try {
            const inputLatLng = "" + lat + "," + lng;
            const radius = 500;
            const url = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${inputLatLng}&radius=${radius}&key=${GOOGLE_MAP_KEY}`
            const response = await fetch(url);
            const data = await response.json();
            const placeNames = [];
            data.results.map(item => {
                placeNames.push(item.name);
            });
            TempData.userPlacesData = placeNames;
            createTheList(placeNames);

        } catch (error) {
            TempData.userPlacesData = [];
        } finally {
            setShoLoading(false);
        }
    }
    const onChangeSearch = query => {
        if (query.length != 0) {
            setIsTyped(true);
        }
        else {
            setIsTyped(false);
        }
        setSearchQuery(query);
    }
    useEffect(() => {
        const timeOutValue = setTimeout(() => {
            if (!isFirstTime) {
                searchedList(searchQuery);
            }
        }, 500);

        return () => {
            return clearTimeout(timeOutValue)
        }
    }, [searchQuery]);
    const searchedList = (query) => {
        let currentList = [];
        let newList = [];
        if (query.length != 0) {
            setIsTyped(true);
            currentList = selectDataListB;
            newList = currentList.filter((obj) => {
                const lc = obj.display_value.toLowerCase();
                const filter = query.toString().toLowerCase();
                return lc.includes(filter);
            })
        }
        else {
            newList = selectDataListB;
            setIsTyped(false);
        }
        setSelectDataList([...[], ...newList]);
        if (newList.length == 0) {
            setErrorMsg('"' + query + '" not found in this list!');
        }
        else {
            setErrorMsg("")
        }
    }

    const clearSearchTxt = () => {
        setSearchQuery("");
        setErrorMsg("")
        setSelectDataList([...[], ...selectDataListB]);
        setIsTyped(false);
    }

    const doneButtonPress = () => {

        let selValue = "";
        if (selectedV.length != 0) {
            selValue = selectedV[0];
        }
        selectBoxClick("DONE", { selectedItem: selValue })

    }
    const goBackPrevious = () => {
        selectBoxClick("BACK", {})
    }
    const listRowClick = (value, isChecked) => {
        let listData = selectDataListB;
        let selectedVTemp = selectedV;
        let selV = [];
        listData.map(obj => {

            obj.isChecked = false;
            if (obj.config_key == value) {
                obj.isChecked = isChecked;
            }
            if (obj.isChecked) {
                selV.push(obj.config_key);
            }
        });
        setSelectedV([...[], ...selV]);
        setDisableUpdateBtn(false);
        // setSelectDataList([...[], ...listData]);
    }
    const renderSelectRow = ({ item }) => {
        return (
            <View style={{
                overflow: 'hidden', borderBottomColor: "#00000050",
                borderBottomWidth: 0.5,
            }}>
                <Pressable
                    android_ripple={{ color: theme.colors.pressableRippleColor, borderless: true }}
                    onPress={() => listRowClick(item.config_key, !item.isChecked)}>
                    <View style={{ ...defaultStyle.ListCardStyle, ...style.selectRowBox }}>
                        <View style={{ flexDirection: 'row' }}>
                            {
                                item.hasOwnProperty("image") ?
                                    <ProgressiveImage
                                        style={style.profilePageIcon}
                                        source={hasImageUrlExist(item.image) ? { uri: item.image } : null}
                                        defaultImageSource={require('../../assets/Images/full_user_image_place_holder.png')}
                                        resizeMode={'cover'}
                                    />
                                    : null
                            }

                            <EntutoTextView style={style.selectRowBoxTxt}>{item.display_value}</EntutoTextView>

                        </View>
                        <View style={{ marginLeft: 'auto', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                            {/* item.isChecked  */}
                            {
                                selectedV.includes(item.config_key) ?
                                    <TouchableOpacity
                                        onPress={() => listRowClick(item.config_key, !item.isChecked)}>
                                        <MaterialIcons
                                            name="check-circle"
                                            size={20}
                                            color={theme.colors.primaryColor} />
                                        {/* <Image style={style.tickIcon}
                                            resizeMode="contain"
                                            source={require('../../assets/Images/icon/list_tick_icon.png')} /> */}
                                    </TouchableOpacity>
                                    : null
                            }

                        </View>
                    </View>
                </Pressable>
            </View>
        );
    };
    const addThisValue = () => {
        selectBoxClick("DONE", { selectedItem: searchQuery })
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={shoLoading} />

            <View style={[style.appBar, style.borderBottom]} >
                <View style={{ paddingHorizontal: 8, zIndex: 1000 }}>
                    <TouchableOpacity onPress={() => goBackPrevious()}>
                        <View style={{ paddingHorizontal: 12, }}>
                            <Image style={style.arrowIcon}
                                resizeMode="cover"
                                source={require('../../assets/Images/icon/Arrow.png')} />
                        </View>
                    </TouchableOpacity>
                </View>


                <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, justifyContent: 'center', alignItems: 'center' }}>
                    <HeadingTxt >{title}</HeadingTxt>
                </View>
                <View style={{ marginLeft: 'auto', }}>
                    <TouchableOpacity
                        onPress={() => doneButtonPress()}
                        disabled={disableUpdateBtn}
                    ><EntutoTextView style={{ ...defaultStyle.postBtn, opacity: disableUpdateBtn ? 0.4 : 1 }}>Done</EntutoTextView></TouchableOpacity>
                </View>

            </View >
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                <View style={{ ...defaultStyle.container, backgroundColor: theme.colors.backgroundColor, paddingTop: 15 }}>
                    <View style={style.searchBarBox}>
                        <View
                            style={style.searchBar}>
                            <TextInput
                                style={style.input}
                                placeholder="Search..."
                                placeholderTextColor={theme.colors.inputPlaceholderColor}
                                value={searchQuery}
                                autoCorrect={false}
                                onChangeText={onChangeSearch}
                                selectionColor={theme.colors.primaryColor}
                            />
                            {isTyped && (
                                <TouchableOpacity onPress={() => clearSearchTxt()}>
                                    <Image
                                        source={require('../../assets/Images/icon/close_icon.png')}
                                        style={style.searchCrossIcon}
                                    />
                                </TouchableOpacity>
                            )}
                            <Image
                                source={require('../../assets/Images/icon/search_icon.png')}
                                style={style.searchIcon}
                            />
                        </View>
                    </View>


                </View>
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside} >
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                            {
                                searchQuery.length != 0 ?
                                    <View>
                                        <TouchableOpacity onPress={() => addThisValue()}>
                                            <View style={style.addToLocationBox}>
                                                <EntutoTextView style={style.addToLocationBoxText}>Add this as Location</EntutoTextView>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    : null
                            }

                        </View>
                        : null
                }
                <FlatList
                    keyboardShouldPersistTaps={'handled'}
                    contentContainerStyle={{ paddingBottom: 20 }}
                    data={selectDataList}
                    renderItem={renderSelectRow}
                    keyExtractor={(item, index) => `${index}`} />
            </View>


        </>
    )
}

export default LocationBoxComponent

const styles = theme => StyleSheet.create({
    searchBarBox: {
        alignItems: "center",
        flexDirection: "row",
    },
    searchBar: {
        flex: 1,
        flexDirection: "row",
        backgroundColor: theme.colors.backgroundColor,
        borderRadius: 1,
        alignItems: "center",
        borderWidth: 1,
        borderColor: '#707070',
        minHeight: 48
    },
    searchIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        marginRight: theme.dimensions.searchInputIconMH,
        tintColor: "#CCC"
    },
    input: {
        fontSize: theme.calculateFontSize(14),
        marginHorizontal: 8,
        flex: 1,
        color: theme.colors.inputTextColor
    },
    searchCrossIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        marginRight: 8,
        tintColor: "#CCC"
    },
    selectRowBox: {
        flexDirection: 'row',
        alignItems: 'center',

        marginBottom: 0,
        marginTop: 0,
        height: 60,
    },
    selectRowBoxTxt: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(14),
        fontWeight: '600',
        paddingVertical: 15,
        minWidth: 200,
    },
    appBar: {
        height: 56,
        flexDirection: "row",
        alignItems: "center",
        paddingRight: 8,
        backgroundColor: theme.colors.backgroundColor,
    },
    borderBottom: {
        borderBottomWidth: 0.5,
        borderBottomColor: theme.colors.topHeaderBottomColor,
    },
    arrowIcon: {
        height: 20,
        width: 12,
        tintColor: "#FFF"

    },
    tickIcon: {
        height: 24,
        width: 24
    },
    tickIcon: {
        height: 24,
        width: 24,
        tintColor: theme.colors.primaryColor
    },
    profilePageBox: {
        height: 40,
        width: 40,
        borderRadius: 20,
        marginRight: 16,
    },
    profilePageIcon: {
        height: 40,
        width: 40,
        borderRadius: 20,
        resizeMode: 'cover',
        marginEnd: 14
    },
    addToLocationBox: {
        marginTop: -10
    },
    addToLocationBoxText: {
        color: theme.colors.primaryColor,
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
    }
})