import { FlatList, Image, Platform, RefreshControl, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import CustomStatusBar from '../components/common/CustomStatusBar';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import PostCard from '../components/post/PostCard';
import ServerConnector from '../utils/ServerConnector';
import { _RedirectionErrorList, _UnauthErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import { AuthValidation } from '../utils/AuthValidation';
import { ActivityIndicator } from 'react-native-paper';
import { AppStateContext, SinglePostContext } from '..';
import appData from '../data/Data';
import Dimensions from '../constants/Dimensions';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const UnlockSinglePostScreen = ({ route, navigation }) => {
    const { postSeq } = route.params;
    const serviceExecute = route.params.hasOwnProperty("isServiceExecute") ? route.params.isServiceExecute : "YES"
    const [postList, setpostList] = useState([])
    const [progressLoading, setprogressLoading] = useState(true);
    const [errorMsg, seterrorMsg] = useState("");
    const { fullUserDetails } = useContext(AppStateContext);
    const { singleProfileObj } = useContext(SinglePostContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    useEffect(() => {
        setprogressLoading(true);
        getPostUserDataService();
    }, []);
    function getPostUserDataService() {
        let hashMap = {
            _action_code: "11:GET_USER_POST",
            post_seq: postSeq
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            setpostList([...[], ...data.data]);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setprogressLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (_UnauthErrorList.includes(errorCode)) {
                    AuthValidation(errorCode, data, navigation);
                }
                setprogressLoading(false);
                seterrorMsg(errorMessage);
            }
        });
    }
    const postCardClick = (clickID, obj) => {
        if (clickID == "DELETE_POST") {
            appData._profilePagePostRefresh = true;
            navigation.goBack(null);
        }

    }
    const renderItem = ({ item }) => {
        return (
            <PostCard itemData={item} navigation={navigation}
                isMyProfile={__ProfileSeq == item.profile_seq} postCardClick={postCardClick}
                forceBookmark={false} fullScreen={true} showSharePopup={true} />
        );
    };
    const handleRefresh = () => {
        setprogressLoading(true);
        getPostUserDataService();
    }
    const backBtnPress = () => {
        if (navigation.canGoBack()) {
            navigation.goBack(null);
        }
        else {
            navigation.replace("HomeScreen");
        }
    }
    return (
        <View style={{ flex: 1, position: 'relative', backgroundColor: theme.colors.backgroundColor }}>
            {/* <CustomStatusBar translucent={true} hidden={false} /> */}
            <StatusBar hidden={true} />
            {/* <HomeTopNavigationBar showBackBtn={true} showBorderBottom={false} title="Post" navigation={navigation} /> */}
            <View style={styles.mediaBackBtnBox}>
                <TouchableOpacity onPress={() => backBtnPress()}>
                    <Image
                        style={{ ...styles.mediaBackBtn, tintColor: errorMsg.length != 0 && "#000" }}
                        source={require('../assets/Images/icon/single_post_back.png')}
                        resizeMode='contain'
                    />
                </TouchableOpacity>
            </View>
            {
                errorMsg.length != 0 ?
                    <View style={defaultStyle.errorBoxOutside}>
                        <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                    </View>
                    : null
            }

            {
                progressLoading ?
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                        <ActivityIndicator size={'large'} />
                    </View>
                    :
                    <View style={{ backgroundColor: '#000', flex: 1 }}>
                        {
                            postList.map(item => {
                                return <PostCard key={item.post_seq} itemData={item} navigation={navigation}
                                    isMyProfile={__ProfileSeq == item.profile_seq} postCardClick={postCardClick}
                                    forceBookmark={false} fullScreen={true} showSharePopup={true} />
                            })
                        }

                    </View>
                // <FlatList
                //     contentContainerStyle={{ maxHeight:Dimensions.screenHeight - 40 }}
                //     data={postList}
                //     renderItem={renderItem}
                //     keyExtractor={(item, index) => index.toString()}
                // refreshControl={
                //     <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                // }
                // />
            }
        </View>
    );
};

export default UnlockSinglePostScreen;

const styles = StyleSheet.create({
    mediaBackBtnBox: {
        position: 'absolute',
        top: Platform.OS == 'ios' ? 48 : 32,
        left: 24,
        zIndex: 3
    },
    mediaBackBtn: {
        height: 20,
        width: 20,
        marginEnd: 5,
        opacity: 0.8,
        zIndex: 3
    },
});
