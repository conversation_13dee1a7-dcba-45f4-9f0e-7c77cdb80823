import { UNAUTH_USER_ERR_KEY } from "./Appconfig";
import { _clearAllData } from "./AuthLogin";
import { CommonActions } from '@react-navigation/native';
import appData from "../data/Data";

export function AuthValidation(errorCode, data, navigation) {
    if (errorCode == UNAUTH_USER_ERR_KEY) {

        if (data.data.hasOwnProperty("user_id")) {
            _clearAllData();
            appData._userDetails = null;
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'LoginScreen',
                            params: { ErrorMsg: "Please login again!" },
                        },
                    ],
                })
            );
            // navigation.replace("LoginScreen", {
            //     ErrorMsg: "Please login again!",
            // });
        }
    }
}