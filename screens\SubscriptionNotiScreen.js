import React, { useContext, useEffect, useState } from 'react'
import { StyleSheet, FlatList, View, RefreshControl } from 'react-native'
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox'
import NotificationRow from '../components/notifications/NotificationRow'
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder'
import { DEFAULT_NOTI_LIST_COUNT, MAX_NOTI_LIST_COUNT, _RedirectionErrorList } from '../utils/Appconfig'
import { RedirectionUrlFunction } from '../utils/RedirectionUrl'
import ServerConnector from '../utils/ServerConnector'
import NotificationHeader from '../components/common/NotificationHeader'
import { PageRefreshContext } from '..'
import Dimensions from '../constants/Dimensions'
import useDefaultStyle from '../theme/useDefaultStyle'
import useSTheme from '../theme/useSTheme'

const SubscriptionNotiScreen = ({ notificationMenuPress, ...props }) => {
    const [subList, setsubList] = useState([]);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setshowLoading] = useState(true);
    const SubscribeMsg = " has subscribed to your post!";

    const [displayCount, setDisplayCount] = useState(DEFAULT_NOTI_LIST_COUNT);
    const [listRefreshValue, setListRefreshValue] = useState(1);
    const [viewAllBtnValue, setViewAllBtnValue] = useState(true);

    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const { notificationRefresh, changeNotificationRefresh } = useContext(PageRefreshContext);


    useEffect(() => {
        setshowLoading(true);
        setViewAllBtnValue(true);
        seterrorMsg("");
        getNotiSubscriberService();

    }, [notificationRefresh]);
    function getNotiSubscriberService() {
        let hashMap = {
            _action_code: "11:GET_NOTIFICATIONS_SUBSCRIBES",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false);
            setsubList([...[], ...data.data]);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                setsubList([]);
                seterrorMsg(errorMessage);
            }
        });
    }

    const renderItem = ({ item }) => {
        let subsTime = item.subscribed_on;
        if (item.type == "PROFILE") {
            subsTime = item.paid_on;
        }
        return (
            <NotificationRow rowType={"SUBS"} data={item} afterTxt={SubscribeMsg} profileSeq={item.profile_seq} timeTxt={subsTime} />
        );
    };

    const handleRefresh = () => {
        setshowLoading(true);
        seterrorMsg("");
        notificationMenuPress("Press", { type: "SUBS" })
        getNotiSubscriberService();
    }
    const viewAllBtnPress = () => {
        if (!viewAllBtnValue) {
            setDisplayCount(DEFAULT_NOTI_LIST_COUNT);
        }
        else {
            notificationMenuPress("Press", { type: "SUBS" })
            setDisplayCount(MAX_NOTI_LIST_COUNT);
        }
        setViewAllBtnValue(!viewAllBtnValue);
        setListRefreshValue(Math.random())
    }
    const notificationRowClick = () => {
        notificationMenuPress("Press", { type: "SUBS" })
    }
    async function updateNotificationViewService() {
        let hashMap = {
            _action_code: "11:UPDATE_NOTIFICATION_VIEW",
            view_tab: "SUBSCRIBE"
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

        }, (errorCode, errorMessage, data) => { // failure method

        });
    }
    return (
        <>
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                <NotificationHeader headerTitle="Subscriptions" showViewAllBtn={subList.length > DEFAULT_NOTI_LIST_COUNT} headerValue="SUBS" viewAllBtnPress={viewAllBtnPress}
                    viewAllBtnLabel={viewAllBtnValue ? "View All" : "Close All"} />

                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside}>
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        :
                        <>
                            {
                                showLoading ?
                                    <NotiRowPlaceholder countVal={2} />
                                    :
                                    <View key={listRefreshValue} style={{ paddingBottom: Dimensions.notificationListGap }}>
                                        {
                                            subList.map((item, index) => {
                                                let subsTime = item.subscribed_on;
                                                if (item.type == "PROFILE") {
                                                    subsTime = item.paid_on;
                                                }
                                                return <NotificationRow key={index} rowType={"SUBS"} data={item} afterTxt={SubscribeMsg} profileSeq={item.profile_seq} timeTxt={subsTime} rowClick={notificationRowClick} />

                                            })
                                        }
                                    </View>

                                // <FlatList
                                //     contentContainerStyle={{ paddingBottom: 20 }}
                                //     data={subList}
                                //     renderItem={renderItem}
                                //     keyExtractor={(item, index) => `${index}`}
                                //     scrollEnabled={false}
                                //     refreshControl={
                                //         <RefreshControl refreshing={showLoading} onRefresh={() => handleRefresh()} />
                                //     } />

                            }
                        </>
                }
            </View>
        </>
    )
}

export default SubscriptionNotiScreen

const styles = StyleSheet.create({

})
