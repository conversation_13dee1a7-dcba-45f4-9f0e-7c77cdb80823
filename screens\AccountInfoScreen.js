import Slider from '@react-native-community/slider';
import React, { useContext, useEffect, useState, useRef } from 'react'
import { Alert, Keyboard, KeyboardAvoidingView, Modal, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import EntutoEditText from '../components/common/EntutoEditText';
import EntutoTextView from '../components/common/EntutoTextView';
import HeadingTxt from '../components/common/HeadingTxt';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import CustomStatusBar from '../components/common/CustomStatusBar';
import { CountryList } from '../constants/Constants';
import ServerConnector from '../utils/ServerConnector';
import { CurrencySymbol, _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import CustomSnackbar from '../components/common/CustomSnackbar';
import ConfirmationPopup from '../components/common/ConfirmationPopup';
import EntutoFlushMessage from '../components/common/EntutoFlushMessage';
import { AppStateContext } from '..';
import SelectBoxComponent from '../components/common/SelectBoxComponent';
import ErrorMessages from '../constants/ErrorMessages';
import { hasDataExist, _getPanNoFromGst, getDisplayNameFromArray } from '../utils/Utils';
import ListItem from '../components/ListItem';
import ActionSheet from 'react-native-actions-sheet';
import DeleteAccount from '../components/accountsetting/DeleteAccount';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import EntutoNewSwitch from '../components/common/EntutoNewSwitch';
import FormButtonGroup from '../components/common/FormButtonGroup';


const thumb = require('../assets/Images/icon/slider_thumb.png');
const DEFAULT_MARGIN_BOTTOM = 20
const AccountInfoScreen = ({ route, navigation }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    const bankDetailsAdd = route.params != undefined ? route.params.showBankDetails : "NO";
    const locationMandatory = route.params != undefined ? route.params.locationMandatory : "NO";

    const { fullUserDetails, changeUserDetails } = useContext(AppStateContext);
    const __is_profile_verified = fullUserDetails.hasOwnProperty("_is_profile_verified") ? fullUserDetails._is_profile_verified : "NO";

    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [snackBarType, setsnackBarType] = useState("FAILED");

    const [emailID, setemailID] = useState("");
    const [emailIDErr, setemailIDErr] = useState("");

    const [mobileNo, setmobileNo] = useState("");
    const [mobileNoErr, setmobileNoErr] = useState("");

    const [selection, setSelection] = useState("NOTPAID");
    const [selectionAcType, setselectionAcType] = useState("SAVINGS");
    const [mainAccountType, setMainAccountType] = useState("BANK_ACC");
    const [validUPI, setValidUPI] = useState(false);
    const [showAccountBox, setshowAccountBox] = useState(true);

    const [sliderValue, setsliderValue] = useState(19);
    const [sliderTopValue, setsliderTopValue] = useState(19);
    const [sliderDefaultValue, setsliderDefaultValue] = useState(19);

    const [countryVal, setcountryVal] = useState("");
    const [countryData, setcountryData] = useState(CountryList);

    const [stateVal, setstateVal] = useState("");
    const [stateData, setstateData] = useState([]);
    const [showStateField, setshowStateField] = useState(false);

    const [accountNo, setaccountNo] = useState("");
    const [accountNoErr, setaccountNoErr] = useState("");

    const [ifscCode, setifscCode] = useState("");
    const [ifscCodeErr, setifscCodeErr] = useState("");

    const [accountName, setaccountName] = useState("");
    const [accountNameErr, setaccountNameErr] = useState("");

    const [gstValue, setgstValue] = useState("");
    const [gstValueErr, setgstValueErr] = useState("");

    const [panNo, setpanNo] = useState("");
    const [panNoErr, setpanNoErr] = useState("");

    const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);

    const [showAccountBoxBak, setshowAccountBoxBak] = useState(false);

    const [showConfirmPopup, setshowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());

    const [confirmTitle, setconfirmTitle] = useState("Confirmation");
    const [confirmMsg, setconfirmMsg] = useState("Confirmation");
    const [warringsData, setwarringsData] = useState({});

    const [showVerificationAlert, setshowVerificationAlert] = useState(false);
    const scrollRef = useRef();
    const deleteAccountRef = useRef(null);

    useEffect(() => {
        if (bankDetailsAdd == "YES") {
            setshowAccountBox(true);
            setshowAccountBoxBak(true);
        }
    }, [bankDetailsAdd]);

    useEffect(() => {
        setShowLoading(true);
        getUserAccountSettingService();
    }, []);
    function getUserAccountSettingService() {
        let hashMap = {
            _action_code: "11:GET_ACCOUNT_SETTING",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

            let profileType = "NOTPAID";
            if (data.data[0].account_type === "PAID") {
                profileType = "PAID";
            }
            setSelection(profileType);
            let email_id = ""
            if (data.data[0].email_id !== null) {
                email_id = data.data[0].email_id;
            }
            setemailID(email_id);

            let mobile_number = ""
            if (data.data[0].mobile_number !== null) {
                mobile_number = data.data[0].mobile_number;
            }
            setmobileNo(mobile_number);
            let bank_account = "";
            let showAccountB = showAccountBox;
            let showAccountBV = showAccountBoxBak;
            if (data.data[0].bank_account !== null) {
                bank_account = data.data[0].bank_account;
                if (data.data[0].bank_account.length !== 0) {
                    showAccountB = true;
                    showAccountBV = true;

                }
            }
            setaccountNo(bank_account);
            if (bankDetailsAdd != "YES") {
                setshowAccountBox(showAccountB);
                setshowAccountBoxBak(showAccountBV);
            }
            let ifsc_code = ""
            if (data.data[0].ifsc_code !== null) {
                ifsc_code = data.data[0].ifsc_code;
            }
            setifscCode(ifsc_code);
            let priceValue = sliderDefaultValue;
            if (data.data[0].paid_account_fee > 0) {
                priceValue = parseFloat(data.data[0].paid_account_fee);
            }
            setsliderValue(priceValue);
            let country = ""
            if (hasDataExist(data.data[0].country)) {
                country = data.data[0].country;
            }
            setcountryVal(country);
            let state = ""
            if (hasDataExist(data.data[0].state)) {
                state = data.data[0].state;
            }
            setstateVal(state)
            if (data.data[0].country === "INDIA") {
                getStateListService("STATE_INDIA");
                setshowStateField(true);
            }
            let gstin = "";
            if (data.data[0].gstin !== null) {
                gstin = data.data[0].gstin;
            }
            setgstValue(gstin);

            let account_holder = "";
            if (data.data[0].account_holder !== null) {
                account_holder = data.data[0].account_holder;
            }
            setaccountName(account_holder);

            let pan = "";
            if (data.data[0].pan !== null) {
                pan = data.data[0].pan;
            }
            setpanNo(pan);

            let bank_account_type = "SAVINGS";
            if (data.data[0].bank_account_type !== null) {
                bank_account_type = data.data[0].bank_account_type;
            }
            setselectionAcType(bank_account_type);

            seterrorMsg("");
            setShowLoading(false);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                seterrorMsg(errorMessage);
                setShowLoading(false);
            }
        });
    }
    React.useEffect(
        () =>
            navigation.addListener('beforeRemove', (e) => {
                if (disableUpdateBtn) {
                    // If we don't have unsaved changes, then we don't need to do anything
                    return;
                }

                // Prevent default behavior of leaving the screen
                e.preventDefault();

                // Prompt the user before leaving the screen
                Alert.alert(
                    ErrorMessages.discardChangesTitle,
                    ErrorMessages.discardChangesMsg,
                    [
                        { text: "Don't leave", style: 'cancel', onPress: () => { } },
                        {
                            text: 'Discard',
                            style: 'destructive',
                            // If the user confirmed, then we dispatch the action we blocked earlier
                            // This will continue the action that had triggered the removal of the screen
                            onPress: () => navigation.dispatch(e.data.action),
                        },
                    ]
                );
            }),
        [navigation, disableUpdateBtn]
    );
    const selectionOptionChange = (pType) => {
        let freeToPaidMsg = ErrorMessages.selFreeToPaidConfirmMsg;
        let paidToFreeMsg = ErrorMessages.selPaidToFreeConfirmMsg;

        if (selection !== pType) {
            let warringsPopupMsg = paidToFreeMsg
            if (pType === "PAID") {
                warringsPopupMsg = freeToPaidMsg;
            }
            setconfirmMsg(warringsPopupMsg);
            setshowConfirmPopup(true);
            setshowConfirmPopupKey(Math.random());
            setwarringsData({ profileType: pType });
        }
    }
    const emailIDChangeHandler = (text) => {
        // setemailID(text);
        // setemailIDErr("");
    }
    const mobileNoChangeHandler = (text) => {
        setmobileNo(text);
        setmobileNoErr("");
        setdisableUpdateBtn(false);
    }
    const accountNoChangeHandler = (text) => {
        setaccountNo(text);
        setaccountNoErr("");
        setdisableUpdateBtn(false);
    }
    const ifscCodeChangeHandler = (text) => {
        let uppercasetext = text.toUpperCase();
        setifscCode(uppercasetext);
        setifscCodeErr("");
    }
    const accountNameChangeHandler = (text) => {
        let uppercasetext = text.toUpperCase();
        setaccountName(uppercasetext);
        // let letterSpaceRegex = /^[a-zA-Z\s]*$/;
        // if (letterSpaceRegex.test(uppercasetext)) {

        // }
        setaccountNameErr("");
        setdisableUpdateBtn(false);
    }
    const gstValueChangeHandler = (text) => {
        let uppercasetext = text.toUpperCase();
        setgstValue(uppercasetext);
        setgstValueErr("");

        if (text.length > 14) {
            let panN = _getPanNoFromGst(text);
            if (panNo.length != 0) {
                if (panNo != panN) {
                    setgstValueErr(ErrorMessages.panGstMisMatchErr);
                }
            }
            else {
                setpanNo(panN);
            }
        }
        setdisableUpdateBtn(false);
    }

    const panNoChangeHandler = (text) => {
        let uppercasetext = text.toUpperCase();
        setpanNo(uppercasetext);
        setpanNoErr("");
        setdisableUpdateBtn(false);
    }

    const handleCountryValueChange = (itemValue) => {
        setcountryVal(itemValue);
        setdisableUpdateBtn(false);
        if (itemValue == "INDIA") {
            getStateListService("STATE_INDIA");
            setshowStateField(true);
        }
        else {
            setshowStateField(false);
        }
    }
    function getStateListService(countryCodeType) {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: countryCodeType,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

            setstateData(data.data);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setstateData([]);
            }
        });
    }
    const updateBtnPress = () => {
        seterrorMsg("");
        Keyboard.dismiss();
        let isFormValid = true;
        if (locationMandatory == "YES") {
            if (countryVal === null || countryVal.length === 0) {
                setSnackbarMsg(ErrorMessages.countryNameErr);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
                isFormValid = false;
                return;
            }
            if (stateVal === null || stateVal.length === 0) {
                setSnackbarMsg(ErrorMessages.stateNameErr);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
                isFormValid = false;
                return;
            }
        }
        if (selection === "PAID" || showAccountBoxBak) { //********
            // if (accountNo.length === 0) {
            //     setaccountNoErr(ErrorMessages.accountNoErr)
            //     isFormValid = false;
            // }

            // if (countryVal === null || countryVal.length === 0) {
            //     setSnackbarMsg("Country name is required");
            //     setdisplaySnackbar(true);
            //     setrefreshSnackBar(Math.random());
            //     isFormValid = false;
            //     return;
            // }
            // if (stateVal === null || stateVal.length === 0) {
            //     setSnackbarMsg("state name is required");
            //     setdisplaySnackbar(true);
            //     setrefreshSnackBar(Math.random());
            //     isFormValid = false;
            //     return;
            // }
        }
        if (gstValue.length != 0) {
            let panN = _getPanNoFromGst(gstValue);
            if (panNo.length != 0) {
                if (panNo != panN) {
                    setgstValueErr(ErrorMessages.panGstMisMatchErr);
                    isFormValid = false;
                }
            }
        }
        if (accountNo.length != 0) {
            if (ifscCode.length === 0) {
                setifscCodeErr(ErrorMessages.ifscCodeErr);
                isFormValid = false;
            }

            if (accountName.length === 0) {
                setaccountNameErr(ErrorMessages.accountNameErr);
                isFormValid = false;
            }

            if (selectionAcType.length === 0) {
                setSnackbarMsg(ErrorMessages.accountTypeErr);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
                isFormValid = false;
                return;
            }
            if (mobileNo.length == 0) {
                setSnackbarMsg(ErrorMessages.accMobileNoErr);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
                isFormValid = false;
                return;
            }

        }
        if (isFormValid) {
            setShowLoading(true);
            submitAccountDataService();
        }

    }
    function submitAccountDataService() {

        let hashMap = {
            _action_code: "11:SAVE_ACCOUNT_SETTING",
            email: emailID,
            mobile: mobileNo,
            profile_type: selection,

        }
        hashMap.country = "";
        hashMap.state = "";
        if (countryVal != null) {
            if (countryVal.length !== 0) {
                hashMap.country = countryVal;
                hashMap.state = stateVal;
            }

        }
        if (accountNo !== null) {
            if (accountNo.length != 0) {
                hashMap.bank_account = accountNo;
            }
        }
        if (ifscCode !== null) {
            if (ifscCode.length != 0) {
                hashMap.ifsc_code = ifscCode;
            }
        }
        if (gstValue !== null) {
            if (gstValue.length !== 0) {
                hashMap.gstn = gstValue;
            }
        }
        if (panNo !== null) {
            if (panNo.length !== 0) {
                hashMap.pan = panNo;
            }
        }
        if (accountName !== null) {
            if (accountName.length !== 0) {
                hashMap.account_holder = accountName;
            }
        }
        if (selectionAcType !== null) {
            if (selectionAcType.length !== 0) {
                hashMap.account_type = selectionAcType;
            }
        }
        if (selection === "PAID") {
            hashMap.viewer_fees = parseInt(sliderValue);
        }
        // console.log("hashMap", hashMap);
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            // getUserProfileService();
            setSnackbarMsg(data.msg);
            setdisplaySnackbar(true);
            setsnackBarType("SUCCESS");
            setrefreshSnackBar(Math.random());
            setdisableUpdateBtn(true)
            setShowLoading(false);
            let hasStateCity = "NO";
            if (stateVal !== null) {
                if (stateVal.length !== 0) {
                    hasStateCity = "YES";
                }
            }
            let hasBankDetails = "NO";
            if (accountNo !== null) {
                if (accountNo.length !== 0) {
                    hasBankDetails = "YES";
                }
            }
            let userDeatails = {
                _user_account_type: selection,
                _has_state_city: hasStateCity,
                _has_bank_details: hasBankDetails,
            }
            changeUserDetails(userDeatails);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.email) {
                            setemailIDErr(data.data.email);
                            fieldErrorShown = true;
                        }
                        if (data.data.mobile) {
                            setmobileNoErr(data.data.mobile);
                            fieldErrorShown = true;
                        }
                        if (data.data.bank_account) {
                            setaccountNoErr(data.data.bank_account);
                            fieldErrorShown = true;
                        }
                        if (data.data.ifsc_code) {
                            setifscCodeErr(data.data.ifsc_code);
                            fieldErrorShown = true;
                        }
                        if (data.data.account_holder) {
                            setaccountNameErr(data.data.account_holder);
                            fieldErrorShown = true;
                        }
                        if (data.data.gstin) {
                            setgstValueErr(data.data.gstin);
                            fieldErrorShown = true;
                        }
                        if (data.data.pan) {
                            setpanNoErr(data.data.pan);
                            fieldErrorShown = true;
                        }
                        if (data.data.account_type) {
                            setSnackbarMsg(data.data.account_type);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.country) {
                            setSnackbarMsg(data.data.country);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.state) {
                            setSnackbarMsg(data.data.state);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.profile_type) {
                            setSnackbarMsg(data.data.profile_type);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.cover_file) {
                            setSnackbarMsg(data.data.cover_file);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            return;
                        }
                        if (data.data.profile_file) {
                            setSnackbarMsg(data.data.profile_file);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());

                            return;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    setSnackbarMsg(errorMessage);
                    setdisplaySnackbar(true);
                    setsnackBarType("FAILED");
                    setrefreshSnackBar(Math.random());
                }
            }
        });
    }
    const confirmPopupPress = (clickID, obj) => {
        if (clickID == "positive") {
            if (obj.profileType == "PAID") {
                if (__is_profile_verified == "YES") {
                    setSelection(obj.profileType);
                    setdisableUpdateBtn(false);
                    setshowAccountBox(true);
                }
                else {
                    onPressTouch()
                    setshowVerificationAlert(true);
                }
            }
            else {
                setSelection(obj.profileType);
                setdisableUpdateBtn(false);
                // if (!showAccountBoxBak) { //******** Changes
                //     setshowAccountBox(false);
                // }
            }
        }
    }
    const [countryModalVisible, setcountryModalVisible] = useState(false);
    const selectCountryBoxClick = (clickID, obj) => {
        setcountryModalVisible(false);
        if (clickID == "DONE") {
            if (obj.selectedItem == "INDIA") {
                getStateListService("STATE_INDIA");
                setshowStateField(true);
            }
            else {
                setshowStateField(false);
            }
            setdisableUpdateBtn(false);
            setcountryVal(obj.selectedItem);
        }
    }
    const [stateModalVisible, setstateModalVisible] = useState(false);
    const selectStateBoxClick = (clickID, obj) => {
        setstateModalVisible(false);
        if (clickID == "DONE") {
            setdisableUpdateBtn(false);
            setstateVal(obj.selectedItem);
        }
    }
    const onPressTouch = () => {
        scrollRef.current?.scrollTo({
            y: 0,
            animated: true,
        });
    }
    const deleteAccountPress = () => {
        deleteAccountRef.current?.show();
    }
    const deleteAccountActionClick = (clickId) => {
        if (clickId == "negetive") {
            deleteAccountRef.current?.hide();
        }
    }
    const CustomSwitchBox = ({
        value = "",
        selectedValue = "",
        onChange,
        label,

    }) => {
        return (
            <View style={style.switchBoxContainer}>

                <Text style={style.switchBoxLabel}>{label}</Text>

            </View>
        )

    }
    const hanldeMainAccType = (type) => {
        setMainAccountType(type);
    }
    const editUpiVPAPress = () => {
        setValidUPI(false);
    }
    const checkUpiVPABtn = () => {
        setValidUPI(true);
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar title="Account Info" showBackBtn={true} navigation={navigation} showTopButton={true}
                showBorderBottom={false}

                buttonComponent={<TouchableOpacity
                    onPress={() => updateBtnPress()}
                    disabled={disableUpdateBtn}>
                    <EntutoTextView style={{ ...defaultStyle.postBtn, opacity: disableUpdateBtn ? 0.4 : 1 }}>UPDATE</EntutoTextView></TouchableOpacity>} />
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside}>
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                    // </View>
                    : null
            }
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                enabled={Platform.OS == 'ios' ? true : true}

                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
                <ScrollView ref={scrollRef}
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    style={{ backgroundColor: theme.colors.backgroundColor }}>

                    <View style={defaultStyle.container}>
                        {
                            showVerificationAlert ?
                                <EntutoFlushMessage text={ErrorMessages.accVerifiedFlushMsg} />
                                : null
                        }
                        <View style={style.formTiltleBox}>
                            <HeadingTxt>Personal Information</HeadingTxt>
                        </View>
                        <View style={style.inputgap}>
                            <EntutoEditText
                                labelTxt="Email ID"
                                placeholderTxt="Email ID"
                                maxLength={64}
                                value={emailID}
                                onChangeText={text => emailIDChangeHandler(text)}
                                showErrorField={emailIDErr.length}
                                errorMsg={emailIDErr}
                                disabledField={true}

                            />
                            <View style={defaultStyle.inputUnderLineView}>
                                <EntutoTextView style={defaultStyle.inputUnderLineTxt}>To change the email please contact with the Admin</EntutoTextView>
                            </View>
                        </View>
                        <View style={style.inputgap}>
                            <EntutoEditText
                                keyboardType="number-pad"
                                labelTxt="Mobile Number"
                                placeholderTxt="Mobile Number"
                                maxLength={64}
                                value={mobileNo}
                                onChangeText={text => mobileNoChangeHandler(text)}
                                showErrorField={mobileNoErr.length}
                                errorMsg={mobileNoErr}
                                disabledField={true}

                            />
                            <View style={defaultStyle.inputUnderLineView}>
                                <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                    A 10 digit number above, but YOU are number 1!
                                </EntutoTextView>
                            </View>
                            <View style={style.boxWithSwitchBoxDivider} />
                        </View>

                        <View style={style.chooseOptionBox}>
                            <View style={style.chooseOptionBoxTitle}>
                                <EntutoTextView>Paid Account</EntutoTextView>
                            </View>
                            <View style={{ marginLeft: 'auto' }}>
                                <EntutoNewSwitch selectedValue={selection === "PAID"} onChange={() => selectionOptionChange(selection === "PAID" ? "NOTPAID" : "PAID")} />
                            </View>


                            {/* <EntutoTextView style={style.addMediaTxt}>
                                Account Type
                            </EntutoTextView>
                            <View style={style.optionBox}>
                                <View style={style.btnGroup}>
                                    <TouchableOpacity
                                        style={[style.btn, selection === "NOTPAID" ? { backgroundColor: theme.colors.primaryColor } : null]}
                                        onPress={() => selectionOptionChange("NOTPAID")}>
                                        <Text style={[style.btnText, selection === "NOTPAID" ? { color: "#FFFFFF" } : null]}>Free</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[style.btn, selection === "PAID" ? { backgroundColor: theme.colors.primaryColor, } : null, { marginLeft: 10 }]}
                                        onPress={() => selectionOptionChange("PAID")}>
                                        <Text style={[style.btnText, selection === "PAID" ? { color: "#FFFFFF" } : null]}>Paid</Text>
                                    </TouchableOpacity>

                                </View>
                            </View> */}
                        </View>

                        {
                            selection === "PAID" ?
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <View style={{ flex: 1, }}>
                                        <Slider
                                            style={{ ...style.slider, }}
                                            minimumValue={19}
                                            maximumValue={99}
                                            minimumTrackTintColor={theme.colors.primaryColor}
                                            maximumTrackTintColor="#CCC"
                                            // thumbTintColor="#F3997B"

                                            tapToSeek={true}
                                            thumbImage={thumb}
                                            step={1}
                                            value={sliderValue}
                                            // onValueChange={(value) => { setsliderValue(value) }}
                                            onSlidingComplete={(value) => { setdisableUpdateBtn(false); setsliderValue(value) }}
                                        />
                                    </View>
                                    <View style={{
                                        // borderColor: '#F3997B',
                                        // borderWidth: 1, padding: 8,
                                        // borderRadius: 5,
                                        // backgroundColor: '#F3997B',
                                        width: 80,
                                        alignItems: 'center',
                                    }}>
                                        <HeadingTxt style={{ color: theme.colors.primaryColor, fontSize: theme.calculateFontSize(theme.dimensions.sliderPriceHeadingText) }}>Set Price</HeadingTxt>
                                        <HeadingTxt style={{ color: theme.colors.primaryColor, fontSize: theme.calculateFontSize(theme.dimensions.sliderPriceText) }}>{CurrencySymbol} {sliderValue}.00</HeadingTxt>
                                    </View>


                                </View>
                                : null

                        }
                        {/* <View style={{ borderBottomColor: '#ccc', borderBottomWidth: 1 }} /> */}
                        <View style={defaultStyle.inputUnderLineView}>
                            {
                                selection === "NOTPAID" ?
                                    <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                        Your fans can see all your content, unless you set a price for it
                                    </EntutoTextView>
                                    :
                                    <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                        Set a price for your fans to view your entire catalog
                                    </EntutoTextView>

                            }
                        </View>
                        <View style={{ ...defaultStyle.boxWithSwitchBoxDivider, marginVertical: 8 }} />
                        <View style={style.formTiltleBox}>
                            <HeadingTxt>Account Information</HeadingTxt>
                        </View>
                        {
                            showAccountBox ?
                                <View>
                                    {/* <View>
                                        <FormButtonGroup
                                            dataList={[
                                                { label: "UPI", value: "UPI_ACC" },
                                                { label: "Bank Details", value: "BANK_ACC" },
                                            ]}
                                            selectedValue={mainAccountType}
                                            onChange={hanldeMainAccType}
                                        />
                                    </View> */}
                                    {
                                        mainAccountType == "UPI_ACC" ?
                                            <View style={style.inputgap}>
                                                <EntutoEditText
                                                    labelTxt="UPI VPA"
                                                    placeholderTxt="UPI VPA"
                                                    maxLength={64}
                                                    value={accountNo}
                                                    onChangeText={text => accountNoChangeHandler(text)}
                                                    showErrorField={accountNoErr.length}
                                                    errorMsg={accountNoErr}
                                                    disabledField={validUPI}
                                                    paddingEndValue={60}
                                                    endComponent={
                                                        <View style={{ flexDirection: 'row', alignItems: 'center', width: 60, justifyContent: 'center', }}>
                                                            {
                                                                validUPI ?
                                                                    <View style={{ height: '100%' }}>
                                                                        <TouchableOpacity onPress={() => editUpiVPAPress()}>
                                                                            <MaterialIcons
                                                                                color={theme.colors.primaryColor}
                                                                                name="mode-edit"
                                                                                size={24} />

                                                                        </TouchableOpacity>
                                                                    </View>
                                                                    :
                                                                    <View style={{ height: '100%' }}>
                                                                        <TouchableOpacity onPress={() => checkUpiVPABtn()}>
                                                                            <EntutoTextView style={{ color: theme.colors.primaryColor }}>Verify</EntutoTextView>
                                                                        </TouchableOpacity>
                                                                    </View>
                                                            }
                                                        </View>
                                                    }
                                                />

                                            </View>
                                            : null

                                    }
                                    {
                                        mainAccountType == "BANK_ACC" ?
                                            <>


                                                <View style={style.inputgap}>
                                                    <EntutoEditText
                                                        labelTxt="Account Number"
                                                        placeholderTxt="Account Number"
                                                        maxLength={64}
                                                        value={accountNo}
                                                        onChangeText={text => accountNoChangeHandler(text)}
                                                        showErrorField={accountNoErr.length}
                                                        errorMsg={accountNoErr}

                                                    />
                                                    <View style={defaultStyle.inputUnderLineView}>
                                                        <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                                            {`All your details are 100% safe with us!`}
                                                        </EntutoTextView>
                                                    </View>
                                                </View>
                                                <View style={style.inputgap}>
                                                    <EntutoEditText
                                                        labelTxt="IFSC Code"
                                                        placeholderTxt="IFSC Code"
                                                        maxLength={64}
                                                        value={ifscCode}
                                                        onChangeText={text => ifscCodeChangeHandler(text)}
                                                        showErrorField={ifscCodeErr.length}
                                                        errorMsg={ifscCodeErr}
                                                        autoCapitalize={'characters'}


                                                    />
                                                    <View style={defaultStyle.inputUnderLineView}>
                                                        <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                                            {`So we can credit your earnings!`}
                                                        </EntutoTextView>
                                                    </View>
                                                </View>
                                                <View style={style.inputgap}>
                                                    <EntutoEditText
                                                        labelTxt="Account Name"
                                                        placeholderTxt="Account Name"
                                                        maxLength={64}
                                                        value={accountName}
                                                        onChangeText={text => accountNameChangeHandler(text)}
                                                        showErrorField={accountNameErr.length}
                                                        errorMsg={accountNameErr}
                                                        autoCapitalize={'characters'}


                                                    />
                                                    <View style={defaultStyle.inputUnderLineView}>
                                                        <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                                            {`Double check the above details so we pay only YOU!`}
                                                        </EntutoTextView>
                                                    </View>
                                                </View>
                                                <View>
                                                    <FormButtonGroup
                                                        dataList={[
                                                            { label: "Saving", value: "SAVINGS" },
                                                            { label: "Current", value: "CURRENT" },
                                                        ]}
                                                        selectedValue={selectionAcType}
                                                        onChange={setselectionAcType}
                                                    />
                                                </View>
                                            </>
                                            : null}
                                    {/* <View style={{ ...style.chooseOptionBox, }}>
                                        <EntutoTextView style={style.addMediaTxt}>
                                            Type
                                        </EntutoTextView>
                                        <View style={style.optionBox}>
                                            <View style={style.btnGroup}>
                                                <TouchableOpacity
                                                    style={[style.btn, selectionAcType === "SAVINGS" ? { backgroundColor: theme.colors.primaryColor } : null]}
                                                    onPress={() => setselectionAcType("SAVINGS")}>
                                                    <Text style={[style.btnText, selectionAcType === "SAVINGS" ? { color: "#FFFFFF" } : null]}>Saving</Text>
                                                </TouchableOpacity>
                                                <TouchableOpacity style={[style.btn, selectionAcType === "CURRENT" ? { backgroundColor: theme.colors.primaryColor, } : null, { marginLeft: 10 }]}
                                                    onPress={() => setselectionAcType("CURRENT")}>
                                                    <Text style={[style.btnText, selectionAcType === "CURRENT" ? { color: "#FFFFFF" } : null]}>Current</Text>
                                                </TouchableOpacity>

                                            </View>
                                        </View>
                                    </View> */}

                                    {/* <View style={{ borderBottomColor: '#ccc', borderBottomWidth: 1 }} />
                                    <View style={defaultStyle.inputUnderLineView}>
                                        <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                            So what’s your type?
                                        </EntutoTextView>
                                    </View> */}
                                    <View style={style.inputgap}>
                                        <EntutoEditText
                                            labelTxt="GST"
                                            placeholderTxt="GST"
                                            maxLength={15}
                                            value={gstValue}
                                            onChangeText={text => gstValueChangeHandler(text)}
                                            showErrorField={gstValueErr.length}
                                            errorMsg={gstValueErr}
                                            autoCapitalize={'characters'}


                                        />
                                        <View style={defaultStyle.inputUnderLineView}>
                                            <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                                {`Purely for tax purposes, if you want!`}
                                            </EntutoTextView>
                                        </View>
                                    </View>
                                    <View style={style.inputgap}>
                                        <EntutoEditText
                                            labelTxt="PAN Number"
                                            placeholderTxt="PAN Number"
                                            maxLength={12}
                                            value={panNo}
                                            onChangeText={text => panNoChangeHandler(text)}
                                            showErrorField={panNoErr.length}
                                            errorMsg={panNoErr}
                                            autoCapitalize={'characters'}


                                        />
                                        <View style={defaultStyle.inputUnderLineView}>
                                            <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                                {`Same old, same old!`}
                                            </EntutoTextView>
                                        </View>
                                    </View>

                                </View>
                                : null
                        }
                        <Modal
                            animationType="fade"
                            visible={countryModalVisible}
                            style={{ margin: 0, flex: 1 }}>
                            <SelectBoxComponent
                                selectBoxClick={selectCountryBoxClick}
                                list={countryData}
                                selectedValue={countryVal}
                                title="Select Country"
                                maxSelectedValue={1}
                                multiSelect={false}
                                labelField="label"
                                valueField="value"
                            />
                        </Modal>
                        <View style={style.inputgap}>
                            <TouchableOpacity onPress={() => setcountryModalVisible(true)}>
                                <View style={defaultStyle.customDropdown}>

                                    <View style={defaultStyle.customMultiSelectBox}>
                                        {
                                            countryVal.length != 0 ?
                                                <EntutoTextView style={defaultStyle.customMultiSelectSingleTxt}>{getDisplayNameFromArray(countryData, countryVal, "value", "label")}</EntutoTextView>
                                                :
                                                <EntutoTextView style={defaultStyle.customMultiSelectSinglePH}>Select Country</EntutoTextView>
                                        }
                                    </View>
                                    <MaterialIcons style={defaultStyle.dropdownIcon}
                                        color={theme.colors.dropdownInActiveColor}
                                        name="keyboard-arrow-right" size={theme.dimensions.dropdownRightIcon} />

                                </View>
                            </TouchableOpacity>
                            <View style={{ ...defaultStyle.inputUnderLineView, marginTop: 8, }}>
                                <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                    Where are you? A secret only we know!
                                </EntutoTextView>
                            </View>
                        </View>
                        {
                            showStateField ?
                                <>
                                    <Modal
                                        animationType="fade"
                                        visible={stateModalVisible}
                                        style={{ margin: 0, flex: 1 }}>
                                        <SelectBoxComponent
                                            selectBoxClick={selectStateBoxClick}
                                            list={stateData}
                                            selectedValue={stateVal}
                                            title="Select State"
                                            maxSelectedValue={1}
                                            multiSelect={false}
                                            labelField="display_value"
                                            valueField="config_key"
                                        />
                                    </Modal>
                                    <View style={style.inputgap}>
                                        <TouchableOpacity onPress={() => setstateModalVisible(true)}>
                                            <View style={defaultStyle.customDropdown}>

                                                <View style={defaultStyle.customMultiSelectBox}>
                                                    {
                                                        stateVal.length != 0 ?
                                                            <EntutoTextView style={defaultStyle.customMultiSelectSingleTxt}>{getDisplayNameFromArray(stateData, stateVal, "config_key", "display_value")}</EntutoTextView>
                                                            :
                                                            <EntutoTextView style={defaultStyle.customMultiSelectSinglePH}>Select State</EntutoTextView>
                                                    }
                                                </View>
                                                <MaterialIcons style={defaultStyle.dropdownIcon}
                                                    color={theme.colors.dropdownInActiveColor}
                                                    name="keyboard-arrow-right" size={theme.dimensions.dropdownRightIcon} />

                                            </View>
                                        </TouchableOpacity>
                                        <View style={defaultStyle.inputUnderLineView}>
                                            <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                                {`Don’t wait it, State it!`}
                                            </EntutoTextView>
                                        </View>
                                    </View>
                                </>
                                : null
                        }
                        {/* <View style={style.deletetAccountBtnBox}>

                        <TouchableOpacity onPress={() => deleteAccountPress()}>
                            <EntutoTextView
                                style={style.deletetAccountBtn} >
                                Delete Accounts
                            </EntutoTextView>
                        </TouchableOpacity>
                    </View> */}


                    </View>

                </ScrollView>
            </KeyboardAvoidingView>
            <CustomSnackbar snackCloseTime={3000} snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />
            <ActionSheet ref={deleteAccountRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        deleteAccountRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <DeleteAccount refVal={deleteAccountRef} navigation={navigation}
                        deleteAccountActionClick={(clickId, obj) => deleteAccountActionClick(clickId, obj)} />
                </ScrollView>
            </ActionSheet >
            {
                showConfirmPopup &&
                <ConfirmationPopup
                    visiblePopupKey={showConfirmPopupKey}
                    visiblePopup={showConfirmPopup}
                    title={confirmTitle}
                    messagebody={confirmMsg}
                    positiveButton="Yes"
                    negativeButton="No"
                    data={warringsData}
                    popupClick={(clickID, data) => { confirmPopupPress(clickID, data) }}
                />
            }

        </>
    )
}

export default AccountInfoScreen;

const styles = theme => StyleSheet.create({
    addMediaTxt: {
        fontSize: 17,
        color: theme.colors.mainHeadingColor,
        fontWeight: '700'
    },
    chooseOptionBox: {
        flexDirection: 'row',
        paddingHorizontal: 10,
        paddingVertical: 18,
        paddingTop: 0,
        alignItems: 'center',
    },
    optionBox: {
        marginLeft: 'auto',
        width: 160,

    },
    btnGroup: {
        flexDirection: 'row',
        alignItems: "center",
    },
    btn: {
        flex: 1,
        backgroundColor: '#F2EBE9',
        width: 74,
        borderRadius: 5,
    },
    btnText: {
        textAlign: 'center',
        paddingVertical: 7,
        fontSize: 15,
        color: theme.colors.primaryColor
    },
    slider: {
        width: 'auto',
        opacity: 1,
        height: 50,
        marginTop: 10,

    },
    deletetAccountBtn: {
        flex: 1,
        color: theme.colors.primaryColor,
        fontSize: 17,
        fontWeight: '600'
    },
    deletetAccountBtnBox: {
        marginTop: 10,
        paddingTop: 15,
        paddingBottom: 15,
        borderTopColor: theme.colors.borderBottomColor,
        borderTopWidth: 1,
    },
    inputgap: {
        marginBottom: 20,
    },
    formTiltleBox: {
        marginBottom: 15,
        marginTop: 15,
    }

})
