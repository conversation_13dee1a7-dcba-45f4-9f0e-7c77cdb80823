import {Image, TouchableOpacity, View} from 'react-native';
import React from 'react';
import DEFAULT_IMAGE_VALUE from '../../assets/Images/default_image.jpg';
import EntutoTextView from '../common/EntutoTextView';
import {hasImageUrlExist, onMaxTextLengthReached} from '../../utils/Utils';
import {MaxPlaylistGridTxtLimit} from '../../utils/Appconfig';
import useDefaultStyle from '../../theme/useDefaultStyle';

const ShowsItem = ({
  title = '',
  index,
  showsMedia = null,
  showItemBtnPress = null,
  showSeq = -1,
}) => {
  const {defaultStyle} = useDefaultStyle();
  const itemBtnPress = () => {
    if (showItemBtnPress) {
      showItemBtnPress('SUBMIT', {showSeq: showSeq, index: index});
    }
  };
  // For debugging
  console.log('ShowsItem - Rendering with:', {
    title,
    showsMedia,
    hasImage: hasImageUrlExist(showsMedia),
  });

  return (
    <View style={defaultStyle.PIcontainer}>
      <TouchableOpacity onPress={() => itemBtnPress()}>
        <View style={defaultStyle.PIshowsImageBox}>
          <Image
            source={
              hasImageUrlExist(showsMedia)
                ? {uri: showsMedia}
                : DEFAULT_IMAGE_VALUE
            }
            style={defaultStyle.PIshowsImage}
            // Add onError handler to use default image if loading fails
            onError={e =>
              console.log('Image loading error:', e.nativeEvent.error)
            }
          />
        </View>
      </TouchableOpacity>
      <View style={defaultStyle.PItitleBox}>
        <EntutoTextView style={defaultStyle.PItitle}>
          {onMaxTextLengthReached(
            title || 'Playlist Item',
            MaxPlaylistGridTxtLimit,
            '',
          )}
        </EntutoTextView>
      </View>
    </View>
  );
};

export default ShowsItem;
