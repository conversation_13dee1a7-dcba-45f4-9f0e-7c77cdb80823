import React, { useEffect, useState } from 'react'
import { RefreshControl, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { FlatList } from 'react-native-gesture-handler';
import CustomStatusBar from '../components/common/CustomStatusBar';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import MySubsPostRow from '../components/subscribers/MySubsPostRow';
import MySubsProfileRow from '../components/subscribers/MySubsProfileRow';
import { DefaultRowsPerPage, _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const MySubscribersScreen = ({ navigation, route }) => {
    const [selectedTab, setselectedTab] = useState("POST");
    const _showOneTab = route.params != undefined ? route.params.hasOwnProperty("_showOneTab") ? route.params._showOneTab : false : false;
    const _showOneTabType = route.params != undefined ? route.params.hasOwnProperty("_showOneTabType") ? route.params._showOneTabType : "" : "";

    const [subsPostList, setsubsPostList] = useState([]);
    const [subsProfileList, setsubsProfileList] = useState([]);

    const [progressLoading, setprogressLoading] = useState(true);
    const RowsPerPage = DefaultRowsPerPage;
    const [errorMsg, seterrorMsg] = useState("");

    const [subHeaderTitle, setsubHeaderTitle] = useState("")
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    useEffect(() => {
        setprogressLoading(true);
        let title = "My Subscribers";
        if (_showOneTab) {
            if (_showOneTabType == "PROFILE") {
                setselectedTab("PROFILE")
                title = "Subscribers";
                getProfileSubsService(0, RowsPerPage, true)
            }
            else {
                setselectedTab("POST")
                title = "Subscribers";
                getPostSubsService(0, RowsPerPage, true);
            }
        }
        else {
            getPostSubsService(0, RowsPerPage, true);
        }
        setsubHeaderTitle(title)

    }, [])

    const renderPostItem = ({ item }) => {
        return (
            <MySubsPostRow navigation={navigation} data={item} />
        );
    };
    const renderProfileItem = ({ item }) => {
        return (
            <MySubsProfileRow navigation={navigation} data={item} />
        );
    };
    const onTabChange = (item) => {
        seterrorMsg("");
        setselectedTab(item);
        setprogressLoading(true);
        if (item == "PROFILE") {
            getProfileSubsService(0, RowsPerPage, true)
        }
        else {
            getPostSubsService(0, RowsPerPage, true)
        }
    }
    const handleRefresh = () => {

    }

    function getPostSubsService(startRecord, rowsPerPage, isFirstSearch) {
        let hashMap = {
            _action_code: "11:GET_USER_POST_SUBSCRIBERS",
            _start_row: startRecord,
            _rows_page: rowsPerPage,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            seterrorMsg("");
            setsubsPostList(data.data);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (isFirstSearch) {
                    setprogressLoading(false);
                    seterrorMsg(errorMessage);
                    setsubsPostList([]);
                }

            }
        });
    }
    function getProfileSubsService(startRecord, rowsPerPage, isFirstSearch) {
        let hashMap = {
            _action_code: "11:GET_USER_PROFILE_SUBSCRIBER_PAID",
            status: "ALL",
            _start_row: startRecord,
            _rows_page: rowsPerPage,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            seterrorMsg("");
            setsubsProfileList(data.data);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (isFirstSearch) {
                    setprogressLoading(false);
                    seterrorMsg(errorMessage);
                    setsubsProfileList([]);
                }

            }
        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title={subHeaderTitle} showBackBtn={true} navigation={navigation}
                showBorderBottom={false} />
            {
                !_showOneTab ?
                    <View style={{ ...defaultStyle.tabBar, }}>
                        <TouchableOpacity
                            style={[defaultStyle.tabBarLabel, selectedTab === "POST" ? {
                                borderBottomWidth: theme.dimensions.tabBorderBottomWidth,
                                borderBottomColor: theme.colors.tabActiveBootomBorderColor,
                            } : null]}
                            onPress={() => onTabChange("POST")}>
                            <Text allowFontScaling={false} style={[defaultStyle.tabBarLabelTxt, selectedTab === "POST" ? {
                                color: theme.colors.tabActiveColor
                            } : null]}>
                                POST
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[defaultStyle.tabBarLabel, selectedTab === "PROFILE" ? {
                                borderBottomWidth: theme.dimensions.tabBorderBottomWidth,
                                borderBottomColor: theme.colors.tabActiveBootomBorderColor,
                            } : null]}
                            onPress={() => onTabChange("PROFILE")}>
                            <Text allowFontScaling={false} style={[defaultStyle.tabBarLabelTxt, selectedTab === "PROFILE" ? {
                                color: theme.colors.tabActiveColor
                            } : null]}>
                                PROFILE
                            </Text>
                        </TouchableOpacity>

                    </View>
                    : null
            }

            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside} >
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        : null
                }
                {
                    selectedTab == "POST" ?
                        <>
                            {
                                progressLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <FlatList
                                        data={subsPostList}
                                        renderItem={renderPostItem}
                                        keyExtractor={(item, index) => index.toString()}
                                        refreshControl={
                                            <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                                        }
                                    />
                            }

                        </>
                        : null

                }
                {
                    selectedTab == "PROFILE" ?
                        <>
                            {
                                progressLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <FlatList
                                        contentContainerStyle={{ paddingBottom: 20 }}
                                        data={subsProfileList}
                                        renderItem={renderProfileItem}
                                        keyExtractor={(item, index) => index.toString()}
                                        refreshControl={
                                            <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                                        }
                                    />
                            }
                        </>
                        : null

                }
            </View>
        </>
    )
}

export default MySubscribersScreen;

const styles = StyleSheet.create({

})
