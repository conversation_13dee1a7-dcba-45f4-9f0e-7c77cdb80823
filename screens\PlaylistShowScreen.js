import { FlatList, Image, ImageBackground, Platform, Pressable, RefreshControl, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import LinearGradient from 'react-native-linear-gradient';
import { hasImageUrlExist } from '../utils/Utils';
import BackBtn from '../assets/Images/icon/back.png';
import EntutoTextView from '../components/common/EntutoTextView';
import CustomStatusBar from '../components/common/CustomStatusBar';

import ShowsItem from '../components/search/ShowsItem';
import ServerConnector from '../utils/ServerConnector';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import { _RedirectionErrorList } from '../utils/Appconfig';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import TempData from '../data/TempData';
import ProgressiveImage from '../components/common/ProgressiveImage';

const COVER_HEIGHT = 350;
const PlaylistShowScreen = ({ navigation, route }) => {
    const { reqUserSeq } = route.params;
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [showLoading, setShowLoading] = useState(true);
    const [progressLoading, setProgressLoading] = useState(false);
    const [pageErrorMessage, setPageErrorMessage] = useState("");
    const [episodeDetails, setEpisodeDetails] = useState({
        masterBannerTitle: "",
        masterBannerImage: null,
        profileCount: 0
    });
    useEffect(() => {
        TempData.showData = {};
        getAllShowsService();
    }, []);
    const [showList, setShowList] = useState([]);
    const getAllShowsService = () => {
        setShowLoading(true);
        let hashMap = {
            _action_code: "11:GET_PLAYLIST_SHOWS",
            req_user_seq: reqUserSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setPageErrorMessage("");

            setEpisodeDetails(prevState => {
                return {
                    ...prevState,
                    profileCount: data.view_count,
                    masterBannerImage: data.master_image,
                    masterBannerTitle: data.display_name,
                }
            });
            setShowList(data.data);
            setShowLoading(false);
            setProgressLoading(false)
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            setProgressLoading(false);
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowList([]);
                setPageErrorMessage(errorMessage)
            }
        });
    }
    const backButtonPress = () => {
        navigation.goBack(null)
    }
    const HeaderComponent = () => {
        return <>
            {
                pageErrorMessage.length != 0 ?
                    <>
                        <View style={{ ...style.playlistCoverContainer, marginBottom: 10 }}>
                            <View style={{ ...style.topHeader, marginTop: Platform.OS == 'ios' ? 10 : 48 }}>
                                <View>
                                    <Pressable onPress={() => backButtonPress()}
                                        android_ripple={{
                                            color: theme.colors.pressableRippleColor, borderless: true,
                                            radius: 30,
                                        }}>
                                        <View style={{ padding: 10, }}>
                                            <Image
                                                style={style.backBtnIcon}
                                                resizeMode='contain'
                                                source={BackBtn}
                                            />
                                        </View>
                                    </Pressable>
                                </View>
                            </View>
                        </View>
                        <SuccessFailureMsgBox visibleAllTime={true} alertMsg={pageErrorMessage} alertKey={pageErrorMessage.length} />
                    </>
                    :
                    <>
                        <ProgressiveImage
                            source={hasImageUrlExist(episodeDetails.masterBannerImage) ? { uri: episodeDetails.masterBannerImage } : null}
                            style={{ ...style.playlistCoverImage, }}
                            defaultImageSource={require("../assets/Images/full_user_image_place_holder.png")} >
                            <LinearGradient
                                colors={['#111111', '#00000070', '#11111170', '#000']}
                                // locations={[0, 0.45, 0.45, 0.9]}
                                style={style.linearGradient} />
                        </ProgressiveImage>
                        <View style={{
                            ...style.playlistCoverContainer, height: COVER_HEIGHT,
                            marginBottom: 10
                        }}>
                            <View style={{ ...style.topHeader, marginTop: Platform.OS == 'ios' ? 10 : 48 }}>
                                <View>
                                    <Pressable onPress={() => backButtonPress()}
                                        android_ripple={{
                                            color: theme.colors.pressableRippleColor, borderless: true,
                                            radius: 30,
                                        }}>
                                        <View style={{ padding: 10, }}>
                                            <Image
                                                style={style.backBtnIcon}
                                                resizeMode='contain'
                                                source={BackBtn}
                                            />
                                        </View>
                                    </Pressable>
                                </View>
                                <View style={style.playlistTitleBox}>
                                    <View style={{ marginTop: 10 }}>
                                        <EntutoTextView style={style.masterBannerTitle}>
                                            {episodeDetails.masterBannerTitle}
                                        </EntutoTextView>
                                    </View>
                                </View>
                                <View style={style.playListCountBox}>
                                    <EntutoTextView style={style.playListCountValue}>{episodeDetails.profileCount}</EntutoTextView>
                                    <EntutoTextView style={style.playListCountLabel}>Views</EntutoTextView>
                                </View>
                            </View>
                        </View>
                        {
                            !showLoading ?
                                <View style={{ marginHorizontal: 16, marginTop: 1, marginBottom: 20 }}>
                                    <EntutoTextView style={style.playlistAllEpisodeText}>All Shows</EntutoTextView>
                                </View>
                                : null
                        }

                    </>
            }
        </>
    }
    const showItemBtnPress = (clickID, obj) => {
        if (clickID == "SUBMIT") {
            const selectedOBj = showList[obj.index];
            TempData.showData = selectedOBj;
            navigation.navigate("PlaylistScreen", {
                showSeq: selectedOBj.show_seq
            })
        }

    }
    const renderItem = useCallback(
        ({ item, index }) => {
            return <ShowsItem index={index} title={item.title} showsMedia={item.thumb_file}
                showItemBtnPress={showItemBtnPress} showSeq={item.show_seq} />
        },
        [showList]
    );
    const keyExtractor = useCallback((item) => `${item.id}`);
    const ItemSeparatorComponent = () => {
        return <View style={{ height: 16 }} />
    }
    const handleRefresh = () => {
        setProgressLoading(true);
        getAllShowsService();
    }
    return (
        <>
            <CustomStatusBar translucent={true} hidden={false} backgroundColor={'transparent'} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <View style={{ ...style.container, backgroundColor: theme.colors.backgroundColor, }}>
                <FlatList
                    keyboardShouldPersistTaps={'handled'}
                    contentContainerStyle={{ paddingBottom: 80, }}
                    removeClippedSubviews
                    data={showList}
                    numColumns={2}
                    ListHeaderComponent={HeaderComponent}
                    initialNumToRender={8}
                    renderItem={renderItem}
                    keyExtractor={keyExtractor}
                    ItemSeparatorComponent={ItemSeparatorComponent}
                    refreshControl={
                        <RefreshControl tintColor={theme.colors.primaryColor} refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                    }
                />
            </View>

        </>
    )
}

export default PlaylistShowScreen

const styles = theme => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.backgroundColor,
        position: 'relative',
    },
    playlistCoverContainer: {
        width: '100%',
        position: 'relative',
        zIndex: 1,
    },
    linearGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    playlistCoverImage: {
        width: '100%',
        height: COVER_HEIGHT,
        resizeMode: 'cover',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0

    },
    topHeader: {
        marginHorizontal: 24,
        flexDirection: 'row',
    },
    backBtnIcon: {
        height: 24,
        width: 24,
        tintColor: '#FFFFFF',
        resizeMode: 'contain',


    },
    playListCountBox: {
        marginLeft: 'auto'
    },
    playListCountValue: {
        fontSize: theme.calculateFontSize(20),
        color: '#FFFFFF',
        fontWeight: 'bold'
    },
    playListCountLabel: {
        fontSize: theme.calculateFontSize(10),
        color: '#FFFFFF',

    },
    playlistTitleBox: {
        position: 'absolute',
        top: 90,
        left: 46,
        right: 46,
        alignItems: 'center',
    },
    playListLogo: {
        height: 139,
        width: 139,
        resizeMode: 'contain',
        tintColor: "#FFF",
    },

    playlistDescription: {
        marginTop: 10,
        fontSize: theme.calculateFontSize(12),
        color: '#FFFFFF',
        textAlign: 'center'
    },
    reactionBox: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: 24,
        marginTop: 16

    },
    reactionIcon: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
    },
    playBtnBox: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        height: 35,
        borderColor: theme.colors.buttonBorderColor,
        borderWidth: 1,
        borderRadius: 1,
        paddingHorizontal: 50,
        marginTop: 20,
    },
    btnIcon: {
        width: 16,
        height: 16,
        resizeMode: 'contain',
        tintColor: theme.colors.primaryColor,
        marginRight: 11,
    },
    btnText: {
        fontSize: theme.calculateFontSize(15),
        color: theme.colors.buttonTextColor,
        fontWeight: 'bold',
    },
    playlistAllEpisodeText: {
        fontSize: theme.calculateFontSize(20),
        color: '#FFFFFF',
        fontWeight: 'bold',
    },
    masterBannerTitle: {
        fontSize: theme.calculateFontSize(18),
    },
    episodeClipItemContainer: {
        flexDirection: 'row',
        marginBottom: 16,
        marginHorizontal: 16,
        alignItems: 'center',

    },
    episodeClipItemLeftContainer: {
        height: 72,
        width: 72,
        borderRadius: 14,
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: "#FFFFFF",

    },
    episodeClipItemLeftImage: {
        height: 72,
        width: 72,
        borderRadius: 14,
        resizeMode: 'cover'
    },
    episodeClipItemRightContainer: {
        marginStart: 12,
    },
    episodeClipItemLeftText: {
        fontSize: theme.calculateFontSize(14),
        color: '#FFFFFF',

    },
    playListCountBox: {
        marginLeft: 'auto',
        alignItems: 'flex-end',
    },
    playListCountValue: {
        fontSize: theme.calculateFontSize(16),
        color: '#FFFFFF',
        fontWeight: 'bold',
        textAlign: 'right',
    },
    playListCountLabel: {
        fontSize: theme.calculateFontSize(10),
        color: '#FFFFFF',

    },

})