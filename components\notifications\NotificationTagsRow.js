import { useNavigation } from '@react-navigation/native';
import React, { useContext } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { AppStateContext } from '../..';
import Colors from '../../constants/Colors';
import Dimensions from '../../constants/Dimensions';
import { UserHandlePrefix } from '../../utils/Appconfig';
import { hasImageUrlExist } from '../../utils/Utils';
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import SubheadingBodyTxt from '../common/SubheadingBodyTxt';
import SubheadingTxt from '../common/SubheadingTxt';
import useDefaultStyle from '../../theme/useDefaultStyle';

const NotificationTagsRow = ({ rowType, profileSeq, timeTxt, rowClick = null, afterTxt, data, ...props }) => {
    const { fullUserDetails } = useContext(AppStateContext);
    const navigation = useNavigation();
    const { defaultStyle } = useDefaultStyle();

    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const goToProfile = (profileSeq) => {
        if (rowClick) {
            rowClick()
        }
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }
    const goToPost = (postSeq) => {
        if (rowClick) {
            rowClick()
        }
        navigation.navigate('SinglePostScreen', {
            postSeq: postSeq,
        });
    }
    const goToStory = (storySeq) => {
        if (rowClick) {
            rowClick()
        }
        navigation.navigate('SingleStoryStatusScreen', {
            storySeq: storySeq,
        });
    }
    const moreButtonPress = () => {
        let dataType = data.type;
        if (dataType == "POST" || dataType == "POST_CAPTION") {
            goToPost(data.content_seq)
        }
        else if (dataType == "PROFILE_BIO") {
            goToProfile(data.content_seq)
        }
        else if (dataType == "STORY_CAPTION") {
            goToStory(data.content_seq)
        }
    }
    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...styles.cardView }}>
            <View style={styles.profileImageBox}>
                <TouchableOpacity onPress={() => goToProfile(profileSeq)}>
                    <ProgressiveImage
                        style={styles.profileImage}
                        source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                        defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                        resizeMode={'cover'}
                    />
                </TouchableOpacity>
            </View>
            <View style={styles.profileNameBox}>
                <View style={{ flex: 1, flexDirection: 'row', marginBottom: 6, flexWrap: 'wrap', }}>
                    <TouchableOpacity onPress={() => goToProfile(profileSeq)}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <SubheadingTxt>{data.display_name}</SubheadingTxt>
                            {
                                data.is_verified == "YES" ?
                                    <Image
                                        style={styles.verifiedIcon}
                                        source={require('../../assets/Images/icon/verifiedicon.png')}
                                        resizeMode={'contain'}
                                    />
                                    : null
                            }
                        </View>


                    </TouchableOpacity>
                    <View style={{ marginLeft: 'auto' }}>
                        <SubheadingBodyTxt>{timeTxt}</SubheadingBodyTxt>
                    </View>
                </View>


                <View style={{ flex: 1, flexDirection: 'row', flexWrap: 'wrap', }}>
                    <SubheadingBodyTxt>{data.msg}</SubheadingBodyTxt>
                    <TouchableOpacity onPress={() => moreButtonPress()}>
                        <EntutoTextView style={styles.moreTextColor}>more details...</EntutoTextView></TouchableOpacity>
                </View>
            </View>

        </View>
    )
}

export default NotificationTagsRow;

const styles = StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: 31,
        width: 31,
        borderRadius: 31,
    },
    postImage: {
        height: 32,
        width: 32,
        borderRadius: 2,
    },
    profileNameBox: {
        flex: 1,
        flexDirection: 'column',
        //marginHorizontal: 10,
        marginLeft: 10,
        marginRight: 8,

    },
    verifiedIcon: {
        width: 10,
        height: 9,
        marginLeft: Dimensions.veritextLeftmargin,
        // position: 'absolute',
        // right: -4,
        // top: 15,
    },
    moreTextColor: {
        color: Colors.errorColor
    }
})
