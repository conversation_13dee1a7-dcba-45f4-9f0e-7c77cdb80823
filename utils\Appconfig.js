export const UserHandlePrefix = '@';
export const SESSION_ERR_KEY = 'E002';
export const UNAUTH_ERR_KEY = 'E001';
export const UNAUTH_USER_ERR_KEY = 'E006';
export const LOGIN_FAILED_ERR_KEY = 'UE006';
export const _RedirectionErrorList = [
  SESSION_ERR_KEY,
  UNAUTH_ERR_KEY,
  LOGIN_FAILED_ERR_KEY,
];
export const _UnauthErrorList = [UNAUTH_USER_ERR_KEY];
export const isProductionPush = false;
export const MaxStoryTxtLimit = 10;
export const MaxReplyUserTxtLimit = 12;
export const DefaultRowsPerPage = 10;
export const CurrencySymbol = '';
export const DiffTimeRefresh = 8;
export const WEB_CLIENT_ID =
  '286305401510-12r2rb58rq9dc2u4h6d5ac1al1523eel.apps.googleusercontent.com';
export const TERMS_AND_COND_URL = 'https://www.sotrue.co.in/terms_and_use.html';
export const TAGGED_SYMBOL = '@';
export const DEFAULT_MAX_FILE_SIZE = 60;
export const KEYWARDS_ARRAY_SIZE = 30;
export const MAX_TIMES_FOR_RESET = 5;
export const DEFAULT_NOTI_LIST_COUNT = 3;
export const MAX_NOTI_LIST_COUNT = 15;
export const APP_IN_TEST_MODE = false;
// export const PLAYLIST_URL = "https://www.sotrue.co.in/sotrueapp/playlist/index.html";//Test Server
export const PLAYLIST_URL =
  'https://www.sotrue.club/sotrueapp/playlist/index.html'; //Production Server
export const GOOGLE_MAP_KEY = 'AIzaSyBegXvkE0Ro-ZEfr4DQeGIOqzYDOwYdLWs';
export const HOME_PAGE_REFRESH_TIME = 420; //7 mins
export const MaxPlaylistGridTxtLimit = 24;
export const MaxPlaylistTitleTxtLimit = 50;
export const MaxPlaylistDescTxtLimit = 180;
export const MaxPlaylistClipTxtLimit = 80;
