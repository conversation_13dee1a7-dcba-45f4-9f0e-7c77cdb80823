import React from 'react'
import { Image } from 'react-native'

const ProfileImageComponent = ({ source, ...props }) => {
    let imgUrl = require("../../assets/Images/full_user_image_place_holder.png");
    if (source != null) {
        if (source.length != 0) {
            imgUrl = { uri: source };
        }
    }
    return (
        <Image
            source={imgUrl}
            style={{
                ...props.style
            }}
            {...props}
        />
    )
}

export default ProfileImageComponent
