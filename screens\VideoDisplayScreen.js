import React, { useState } from 'react'
import { Image, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import Video from 'react-native-video';
import VideoPlayer from '../components/common/VideoPlayer';
import Dimensions from '../constants/Dimensions';
const ScreenWidth = Dimensions.screenWidth;
const VideoDisplayScreen = ({ route, navigation }) => {
    const { mediaUri, thumbnailUri } = route.params;
    // console.log("mediaUri",mediaUri)
    const [isPortation, setIsPortation] = useState(false);
    const [heightScaled, setHeightScaled] = useState(231);
    const closeVideo = () => {
        navigation.goBack();
    }
    return (
        <View style={styles.container}>
            <StatusBar hidden={true} />
            <View style={styles.videoCrossBox}>
                <TouchableOpacity onPress={() => closeVideo()}>
                    <Image
                        style={styles.arrowIcon}
                        source={require('../assets/Images/icon/Arrow.png')}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
            {/* <View> */}
            <VideoPlayer
                source={{ uri: mediaUri}}
                navigator={navigation}
                tapAnywhereToPause={false}
                toggleResizeModeOnFullscreen={true}
                isFullScreen={true}
                thumbnail={thumbnailUri}
                disableBack={true}
                disableVolume={false}
                controlTimeout={5000}
                paused={true}
                seekColor={'#f3997b'}
                mainColor={'#f3997b'}
                autoPlay={true}
            />
            {/* <Video
                    source={{ uri: mediaUri }}//'http://192.168.43.134/ElephantsDream.mp4'
                    onLoad={item => {
                        const { width, height } = item.naturalSize;
                        const heightScaled = height * (ScreenWidth / width);
                        let isPortrait = height > width;
                        setIsPortation(height > width);
                        setHeightScaled(heightScaled);
                    }}
                    controls={true}
                    style={
                        isPortation
                            ? [styles.contentVideoPortation, { height: heightScaled }]
                            : [styles.contentVideo, { height: heightScaled }]
                    }
                /> */}
            {/* </View> */}
        </View>
    )
}

export default VideoDisplayScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'black',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
    },
    contentVideo: {
        width: ScreenWidth,
        aspectRatio: 1,
        backgroundColor: '#000',
        flex: 1,
        height: 231,
        zIndex: 2
    },
    contentVideoPortation: {
        width: ScreenWidth,
        aspectRatio: 1,
        backgroundColor: '#000',
        flex: 1,
        height: 231,
        zIndex: 2
    },
    videoCrossBox: {
        position: 'absolute',
        top: Platform.OS=='ios'?50: 20,
        left: 15,
        zIndex: 3
    },
    arrowIcon: {
        height: 24,
        width: 24,
        marginEnd: 5,
        opacity: 0.8,
        zIndex: 3,
        tintColor:"#FFF"
    },
})
