import { <PERSON>ert, Animated, AppState, BackHandler, Dimensions, Image, Platform, Linking, Pressable, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View, Keyboard } from 'react-native'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import useDefaultStyle from '../../theme/useDefaultStyle';
import ArrowIcon from '../../assets/Images/icon/Arrow.png';
import SearchIcon from '../../assets/Images/icon/search_icon.png';
import ThreeDotIcon from '../../assets/Images/icon/three_dot.png';
import PostingProgressBar from '../../components/postingJourney/PostingProgressBar';
import EntutoTextView from '../../components/common/EntutoTextView';
import PostingJourneyProgressBar from '../../components/postingJourney/PostingJourneyProgressBar';
import DateIcon from '../../assets/Images/icon/calendar_icon.png';
import EntutoNewSwitch from '../../components/common/EntutoNewSwitch';
import Slider from '@react-native-community/slider';
import { Modal } from 'react-native';
import SelectBoxComponentNew from '../../components/common/SelectBoxComponentNew';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoEditText from '../../components/common/EntutoEditText';
import { _RedirectionErrorList, DEFAULT_MAX_FILE_SIZE, TAGGED_SYMBOL } from '../../utils/Appconfig';
import ServerConnector from '../../utils/ServerConnector';
import OptionSelectionItem from '../../components/common/OptionSelectionItem';
import SelectBoxComponent from '../../components/common/SelectBoxComponent';
import DateTimePicker from '@react-native-community/datetimepicker';
import { checkValueLength, dateDbFormat, DateDisplayFormat, DBDateTimeFormat, dbTimeFormat, DisplayTimeFormat, encodeHtmlEntitessData, preventDoubleClick } from '../../utils/Utils';
import GalleryImageDisplayComponent from '../../components/GalleryImageDisplayComponent';
import VirtualizedList from '../../components/common/VirtualizedList';
import { ActivityIndicator, Button, FAB } from 'react-native-paper';
import { moveFile, stat, TemporaryDirectoryPath, unlink as deleteFile } from 'react-native-fs';
import SuccessFailureMsgBox from '../../components/common/SuccessFailureMsgBox';
import { getRealPath } from 'react-native-compressor';
import ErrorMessages from '../../constants/ErrorMessages';
import MimeTypeList from '../../utils/MimeTypeList';
import appData from '../../data/Data';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { AppStateContext } from '../..';
import ConfirmationPopup from '../../components/common/ConfirmationPopup';
import PreviewPostForAdd from '../../components/post/PreviewPostForAdd';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import CustomSnackbar from '../../components/common/CustomSnackbar';
import { PermissionsAndroid } from 'react-native';
import { openSettingForStorage, requestStoragePermission } from '../../utils/PermissionManager';
import PrimaryButton from '../../components/common/PrimaryButton';
import useAppIsActive from '../../components/common/useAppIsActive';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Popover from 'react-native-popover-view';
import { REACTION_LIST } from '../../data/MetaData';
import { CameraRoll } from "@react-native-camera-roll/camera-roll";
import LocationBoxComponent from '../../components/common/LocationBoxComponent';
import TempData from '../../data/TempData';


const PostingJourneyScreen = ({ navigation }) => {
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const { defaultStyle } = useDefaultStyle();
    const [disabledBtn, setDisabledBtn] = useState(true);
    const [showLoading, setShowLoading] = useState(false);
    const [disablePreviewBtn, setDisablePreviewBtn] = useState(true);
    const [previewBox, setPreviewBox] = useState(false);

    //Caption
    const descInputRef = useRef(null);
    const [description, setDescription] = useState("");
    const [showTagPopup, setShowTagPopup] = useState(false);
    const [tagName, setTagName] = useState("");
    const [selectedTopics, setSelectedTopics] = useState([]);
    const [showTopicSearchModal, setShowTopicSearchModal] = useState(false);
    const [topicsData, setTopicsData] = useState([]);
    const [displayTopicList, setDisplayTopicList] = useState([]);
    const [randomInterestList, setRandomInterestList] = useState([]);
    const [selectedReactions, setSelectedReactions] = useState([]);
    const [selectedReactionsErr, setSelectedReactionsErr] = useState("");
    const [showScheduleBox, setShowScheduleBox] = useState(false);
    const [scheduleTime, setScheduleTime] = useState(new Date());
    const [showSchedulePicker, setShowSchedulePicker] = useState(false);
    const [showScheduleTimePicker, setShowScheduleTimePicker] = useState(false);

    const [showExpiryBox, setShowExpiryBox] = useState(false);
    const [expiryTime, setExpiryTime] = useState(new Date());
    const [showExpiryPicker, setShowExpiryPicker] = useState(false);

    const [reactionList, setReactionList] = useState([])


    // Location and Tags
    const [locationList, setLocationList] = useState([]);
    const [displayLocationList, setDisplayLocationList] = useState([]);
    const [selectedLocation, setSelectedLocation] = useState("");
    const [openLocationListPopup, setOpenLocationListPopup] = useState(false);

    const [selectedTagPeoples, setSelectedTagPeoples] = useState([]);
    const [tagPeopleList, setTagPeopleList] = useState([]);
    const [displayTagPeopleList, setDisplayTagPeopleList] = useState([]);
    const [openTagPeopleListPopup, setOpenTagPeopleListPopup] = useState(false);

    //Monetizing
    const [activeMonetizing, setActiveMonetizing] = useState(false);
    const [monetizingValue, setMonetizingValue] = useState(9);
    const [sliderValue, setSliderValue] = useState(9);

    //Media
    const [postMediaData, setPostMediaData] = useState(null);
    const [selectedTabType, setSelectedTabType] = useState("PLAY");

    let videoDestUri = null;

    //others
    const [completedStep, setCompletedStep] = useState(1);
    const [screenName, setScreenName] = useState("IMAGE");
    const { fullUserDetails, changeUserDetails } = useContext(AppStateContext);
    let __hasBankDetails = fullUserDetails.hasOwnProperty("_has_bank_details") ? fullUserDetails._has_bank_details : "NO";
    let __is_profile_verified = fullUserDetails.hasOwnProperty("_is_profile_verified") ? fullUserDetails._is_profile_verified == "YES" ? true : false : false;
    let __is_profile_paid = fullUserDetails.hasOwnProperty("_user_account_type") ? fullUserDetails._user_account_type == "PAID" ? true : false : false;

    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const __MAX_FILE_SIZE = fullUserDetails.hasOwnProperty("_max_file_size") ? fullUserDetails._max_file_size : DEFAULT_MAX_FILE_SIZE;
    const mediaSizeErrorMsg = `Oh no! Your content exceeds the temporary ${__MAX_FILE_SIZE}MB limit. Please try posting something else?`
    const [showConfirmPopup, setShowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setShowConfirmPopupKey] = useState(Math.random());
    const [showAccountVerification, setShowAccountVerification] = useState(false);
    const modalFlashRef = useRef(null);
    const modalFlashTagsRef = useRef(null);
    const modalFlashCaptionRef = useRef(null);
    const modalFlashPriceRef = useRef(null);

    const [snackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setDisplaySnackbar] = useState(false);
    const [refreshSnackBar, setRefreshSnackBar] = useState(Math.random());

    const [isStoragePermissionGranted, setIsStoragePermissionGranted] = useState(false);
    const [storageSpinner, setStorageSpinner] = useState(true);
    const [selectedFlatListIndex, setSelectedFlatListIndex] = useState(0)
    const [permissionClicked, setPermissionClicked] = useState(false);
    const [tabChangeManual, setTabChangeManual] = useState(false);

    const ModalTopGap = 56;

    useEffect(() => {
        const timeout = setTimeout(() => {
            setStorageSpinner(false);
        }, 2000);

        return () => {
            clearTimeout(timeout)
        }
    }, []);
    //UseEffect
    useEffect(() => {

        checkStoragePermission();
        const timeoutV = setTimeout(() => {
            var future = new Date();
            future.setDate(future.getDate() + 30);
            setExpiryTime(future);
            setScheduleTime(future);
            if (!__is_profile_verified) {
                getUserProfileService();
            }
            getInterestListService();

        }, 1000);
        const timeoutV2 = setTimeout(() => {

            getUserTagListListService();
            // getLocationListService();

        }, 2000);
        return () => {
            clearTimeout(timeoutV);
            clearTimeout(timeoutV2);
        }
    }, []);
    useEffect(() => {
        const tempList = JSON.parse(JSON.stringify(REACTION_LIST));
        tempList.forEach((item, index) => {
            tempList[index].is_selected = false;
        });
        setReactionList(tempList);
    }, []);
    const requestStorageAccessPermission = useCallback(() => {
        // request permissions...
        if (preventDoubleClick(appData.permissionTime)) {
            appData.permissionTime = new Date();
            checkStoragePermission();
        }

    }, []);
    useAppIsActive(() => requestStorageAccessPermission());

    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            if (completedStep == 4) {
                setPreviewBox(true);
            }
            if (appData.isRedirectToAccountScreen == "YES") {
                // console.log("Inside completedStep", completedStep)
                // console.log("screenName", screenName)
                appData.isRedirectToAccountScreen = "NO";
                setScreenName("PRICE");

            }

            // if (!isStoragePermissionGranted) {
            //     checkStoragePermission();
            // }
        });
        return unsubscribe;
    }, [navigation]);
    useEffect(() => {
        __hasBankDetails = fullUserDetails.hasOwnProperty("_has_bank_details") ? fullUserDetails._has_bank_details : "NO";
        __is_profile_verified = fullUserDetails.hasOwnProperty("_is_profile_verified") ? fullUserDetails._is_profile_verified == "YES" ? true : false : false;
        __is_profile_paid = fullUserDetails.hasOwnProperty("_user_account_type") ? fullUserDetails._user_account_type == "PAID" ? true : false : false;
    }, [fullUserDetails]);
    const descriptionChangeHandler = (text) => {
        let txtNewLineArr = text.split("\n");
        let lastNewLineWord = txtNewLineArr[txtNewLineArr.length - 1];
        let textStArr = lastNewLineWord.split(" ");
        let lastIndex = textStArr.length - 1;
        let lastWord = textStArr[lastIndex]
        if (lastWord == TAGGED_SYMBOL) {//.charAt(0)
            setShowTagPopup(true);
            setTagName(lastWord);
        }
        else {
            setTagName("");
            setShowTagPopup(false);
        }

        setDescription(text)
        setDisabledBtn(false)
        setSnackbarMsg("");
        setDisplaySnackbar(false)
    }
    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
        return () => {
            BackHandler.removeEventListener('hardwareBackPress', handleBackButtonClick);
        };
    }, []);
    function handleBackButtonClick() {
        // console.log("Back Press")
        goBackPrevious()
        return true;
    }
    const goBackPrevious = () => {
        setDisplaySnackbar(false)
        if (previewBox) {
            setPreviewBox(false);
        }
        else {
            if (screenName == "IMAGE") {
                navigation.goBack(null);
            }
            else if (screenName == "CAPTION") {
                setScreenName("IMAGE")
            }
            else if (screenName == "PRICE") {
                setScreenName("IMAGE")
            }
            else if (screenName == "TAGS") {
                setScreenName("IMAGE")
            }
        }

    }

    const barBtnPress = (type) => {
        if (!isStoragePermissionGranted) {
            return;
        }
        setSnackbarMsg("");
        setDisplaySnackbar(false)
        setRefreshSnackBar(Math.random());
        if (type == "PRICE") {
            if (!__is_profile_paid) {
                if (completedStep > 1) {
                    setScreenName(type);
                }
            }

        }
        else if (type == "TAGS") {
            if (completedStep > 2) {
                setScreenName(type)
            }
        }
        else if (type == "CAPTION") {
            setScreenName(type)
        }

    }
    const doneBtnPress = () => {
        setDisplaySnackbar(false)
        if (screenName == "CAPTION") {
            if (description.length == 0) {
                if (activeMonetizing || __is_profile_paid) {
                    setSnackbarMsg(ErrorMessages.addPostCaptionErr);
                    setDisplaySnackbar(true)
                    setRefreshSnackBar(Math.random());
                    return;
                }
            }
            if (selectedTopics.length == 0) {
                setSnackbarMsg(ErrorMessages.addPostNoInterestErr);
                setDisplaySnackbar(true)
                setRefreshSnackBar(Math.random());
                return;
            }
            if (selectedReactions.length == 0) {
                setSnackbarMsg(ErrorMessages.addPostReactionsErr);
                setDisplaySnackbar(true)
                setRefreshSnackBar(Math.random());
                return;
            }
            if (selectedTopics.length != 0 && selectedTopics.length < 3) {
                setSnackbarMsg(ErrorMessages.addPostInterestErr);
                setDisplaySnackbar(true)
                setRefreshSnackBar(Math.random());
                return;
            }
            if (completedStep < 2) {
                if (__is_profile_paid) {
                    setCompletedStep(3)
                }
                else {
                    setCompletedStep(2)
                }

            }

        }
        else if (screenName == "PRICE") {
            if (!__is_profile_paid) {
                if (activeMonetizing) {
                    if (__hasBankDetails == "NO") { //TODO
                        setShowConfirmPopup(true);
                        setShowConfirmPopupKey(Math.random())
                        return;
                    }
                }
                if (completedStep < 3) {
                    setCompletedStep(3)
                }
            }

        }
        else if (screenName == "TAGS") {
            if (completedStep < 4) {
                setCompletedStep(4);
            }
            setDisablePreviewBtn(false);
        }
        setScreenName("IMAGE")

    }

    const handleScheduleBox = (value) => {
        setShowScheduleBox(!showScheduleBox);
    }

    const handleExpiryBox = (value) => {
        setShowExpiryBox(!showExpiryBox);
    }

    const handleMonetizing = (value) => {
        let mValue = !activeMonetizing
        if (!__is_profile_verified) {
            if (mValue) {
                setShowAccountVerification(true);
                return;
            }
        }
        if (!__is_profile_paid) {
            if (mValue) {
                if (__hasBankDetails == "NO") { //TODO                    
                    setShowConfirmPopup(true);
                    setShowConfirmPopupKey(Math.random())
                    return;
                }
            }
        }
        setActiveMonetizing(mValue);
    }

    const openSchedulePicker = () => {
        setShowSchedulePicker(true);
    }
    const onScheduleDateChange = (event, selectedDate) => {
        const currentDate = selectedDate || scheduleTime;
        setShowSchedulePicker(Platform.OS === 'ios');
        if (Platform.OS === 'ios') {
            setScheduleTime(currentDate);
            setDisabledBtn(false)
            setSnackbarMsg("");
            setDisplaySnackbar(false)
        }
        if (Platform.OS === 'android') {
            setShowScheduleTimePicker(true);
        }
    };
    const onScheduleTimeChange = (event, selectedDate) => {
        const currentDate = selectedDate || scheduleTime;
        setShowSchedulePicker(Platform.OS === 'ios');

        let formatDateTime = dateDbFormat(scheduleTime) + " " + dbTimeFormat(currentDate);
        if (Platform.OS === 'android') {
            setShowScheduleTimePicker(false);
        }
        setScheduleTime(new Date(formatDateTime));
        setDisabledBtn(false)
        setSnackbarMsg("");
        setDisplaySnackbar(false)
    };
    const scheduleIosDoneBtnpress = () => {
        setShowSchedulePicker(false);
    }
    const scheduleIosCancelBtnpress = () => {
        setShowSchedulePicker(false);
    }
    const openExpiryDatePicker = () => {
        setShowExpiryPicker(true);
    }
    const onExpiryDateChange = (event, selectedDate) => {
        const currentDate = selectedDate || expiryTime;
        setShowExpiryPicker(Platform.OS === 'ios');
        setExpiryTime(currentDate);
        setDisabledBtn(false)
        setSnackbarMsg("");
        setDisplaySnackbar(false)
    };
    const expiryIosDoneBtnpress = () => {
        setShowExpiryPicker(false);
    }
    const expiryIosCancelBtnpress = () => {
        setShowExpiryPicker(false);
    }
    const getLocationListService = () => {
        let hashMap = {
            _action_code: "11:GET_LOCATION_LIST",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let tempList = [];
            data.data.map(item => {
                tempList.push({
                    label: item,
                    value: item,
                    isChecked: false,
                })
            });
            setLocationList(tempList);
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            setLocationList([]);
        });
    }
    const getUserTagListListService = () => {
        let hashMap = {
            _action_code: "11:GET_USER_TO_TAG",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let tempList = [];
            data.data.map(item => {
                tempList.push({
                    label: item.display_name,
                    value: item.profile_seq,
                    image: item.profile_picture,
                    isChecked: false,
                })
            });
            setTagPeopleList(tempList);
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            setTagPeopleList([]);
        });
    }
    const getInterestListService = () => {
        let hashMap = {
            _action_code: "11:GET_INTEREST_LIST",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let tempList = [];
            data.data.map(item => {
                tempList.push({
                    label: item.interest_name,
                    value: item.interest_name,
                    isChecked: false,
                })
            });
            setRandomInterestList(data.random)
            createUpdatedList([], tempList, data.random)
            setTopicsData(tempList);
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            setTopicsData([]);
        });
    }
    const searchTopicBtnPress = () => {
        setShowTopicSearchModal(true);
    }
    const selectTopicBoxClick = (clickID, obj) => {
        setShowTopicSearchModal(false);
        if (clickID == "DONE") {
            setSelectedTopics([...[], ...obj.selectedItem])
            createUpdatedList(obj.selectedItem, topicsData, randomInterestList);
        }
    }
    const createUpdatedList = (selectedItems, tempList, tempBackUplList) => {
        let tempDataList = JSON.parse(JSON.stringify(tempList));
        let list = []
        tempDataList.map((item, index) => {
            if (selectedItems.includes(item.value)) {
                item.isChecked = true;
                list.push(item)
            } else {
                if (tempBackUplList.includes(item.value)) {
                    list.push(item)
                }
                item.isChecked = false;
            }
        });
        setDisplayTopicList(list);
    }
    const onInterestItemPress = (clickID, obj) => {
        if (clickID == "ITEM_CLICK") {
            let tempTopicList = JSON.parse(JSON.stringify(displayTopicList));
            let list = [...tempTopicList];
            list[obj.index].isChecked = !list[obj.index].isChecked;
            if (list[obj.index].isChecked) {
                setSelectedTopics([...selectedTopics, obj.value])
            } else {
                setSelectedTopics(selectedTopics.filter((item) => item !== obj.value))
            }
            setDisplayTopicList(list);
        }
    }
    const onReactionItemSelect = (clickID, obj) => {
        if (clickID == "ITEM_CLICK") {
            let tempReactions = JSON.parse(JSON.stringify(selectedReactions));
            setSelectedReactionsErr("");
            if (tempReactions.includes(obj.value)) {
                tempReactions = tempReactions.filter(item => item != obj.value);
            }
            else {
                if (tempReactions.length == 2) {
                    setSelectedReactionsErr("Selection of maximum reaction 2");
                    return;
                }
                tempReactions.push(obj.value)
            }
            setSelectedReactions(tempReactions);
        }
    }
    const searchLocationBtnPress = () => {
        setOpenLocationListPopup(true);
    }
    const selectLocationBoxClick = (clickID, obj) => {
        setOpenLocationListPopup(false);
        if (clickID == "DONE") {
            setSelectedLocation(obj.selectedItem)
            // createUpdatedLocationList(obj.selectedItem, locationList);
        }
    }
    const createUpdatedLocationList = (selectedItems, tempList) => {
        let tempDataList = JSON.parse(JSON.stringify(tempList));
        let list = []
        tempDataList.map((item, index) => {
            if (selectedItems == item.value) {
                item.isChecked = true;
                list.push(item)
            } else {
                item.isChecked = false;
            }
        });
        setDisplayLocationList(list);
    }

    const searchPeopleBtnPress = () => {
        setOpenTagPeopleListPopup(true);
    }
    const selectPeopleBoxClick = (clickID, obj) => {
        setOpenTagPeopleListPopup(false);
        if (clickID == "DONE") {
            setSelectedTagPeoples([...[], ...obj.selectedItem])
            createUpdatedPeopleList(obj.selectedItem, tagPeopleList);
        }
    }
    const createUpdatedPeopleList = (selectedItems, tempList) => {
        let tempDataList = JSON.parse(JSON.stringify(tempList));
        let list = []
        tempDataList.map((item, index) => {
            if (selectedItems.includes(item.value)) {
                item.isChecked = true;
                list.push(item)
            } else {
                item.isChecked = false;
            }
        });
        setDisplayTagPeopleList(list);
    }
    const previewPost = () => {
        setSnackbarMsg("");
        setDisplaySnackbar(false)
        setRefreshSnackBar(Math.random());
        if (postMediaData != null) {
            let fileMaxSize = __MAX_FILE_SIZE * 1024 * 1000;
            if (postMediaData.node.image.fileSize > fileMaxSize) {
                setSnackbarMsg(ErrorMessages.addPostMediaSizeErr);
                setDisplaySnackbar(true)
                setRefreshSnackBar(Math.random());
                return;
            }
        }
        setPreviewBox(true);
    }
    const onMediaSelectionBtnPress = (clickID, medialData, index) => {
        setShowLoading(false);
        if (clickID == "SUCCESS") {
            setSelectedFlatListIndex(index);
            const fileMaxSize = __MAX_FILE_SIZE * 1024 * 1000;
            if (medialData.node.image.fileSize > fileMaxSize) {
                setSnackbarMsg(ErrorMessages.addPostMediaSizeErr);
                setDisplaySnackbar(true)
                setRefreshSnackBar(Math.random());
                return;
            }
            let tabType = "POST";
            const typeArray = medialData.node.type.split('/');
            let mediaType = medialData.node.type;
            if (typeArray.length > 0) {
                mediaType = typeArray[0];
            }

            if (mediaType == 'video') {
                tabType = "PLAY"
            }
            setTabChangeManual(false);
            setSelectedTabType(tabType);
            setPostMediaData(medialData)
        }
        if (clickID == "PERMISSION_ERROR") {

        }

    }
    const onTabSelect = (tabType) => {
        setSelectedTabType(tabType);
        setTabChangeManual(true)
        setPostMediaData(null);
        // setShowLoading(true);
    }
    const viewMediaBtnPress = () => {
        if (selectedTabType == "POST") {
            showImage();
        }
        else if (selectedTabType == "PLAY") {
            playVideo();
        }
    }
    const showImage = () => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: postMediaData.node.image.uri
        });
    }
    const playVideo = async () => {
        let mediaUriV = postMediaData.node.image.uri;
        if (Platform.OS == 'ios') {
            let actualPath = "";
            if (mediaUriV == selectedIosPH) {
                actualPath = convertedIosFile;
            }
            else {
                try {
                    const fileData = await CameraRoll.iosGetImageDataById(mediaUriV);
                    actualPath = fileData.node.image.filepath;
                    setselectedIosPH(mediaUriV)
                    setConvertedIosFile(actualPath)

                } catch (error) {

                }
            }
            navigation.navigate("VideoDisplayScreen", {
                mediaUri: actualPath,
                thumbnailUri: null
            })

        }
        else if (Platform.OS == 'android') {
            navigation.navigate("VideoDisplayScreen", {
                mediaUri: mediaUriV,
                thumbnailUri: null
            })
        }



    }
    const postBtnPress = () => {
        if (preventDoubleClick(appData.buttonClickTime)) {
            appData.buttonClickTime = new Date();
            setShowLoading(true);
            validateFile();
        }


    }
    const [selectedIosPH, setselectedIosPH] = useState("");
    const [convertedIosFile, setConvertedIosFile] = useState("")
    const validateFile = async () => {
        if (Platform.OS == 'ios') {
            try {
                // uri 'PH://xxxx'
                const mediaData = postMediaData.node;
                const iosPH = mediaData.image.uri;
                let uploadPath = "";
                if (iosPH == selectedIosPH) {
                    uploadPath = convertedIosFile;
                }
                else {
                    setselectedIosPH(iosPH)
                    // console.log("mediaData.image.uri", mediaData.image.uri)
                    const fileData = await CameraRoll.iosGetImageDataById(iosPH);
                    // console.log("fileData", fileData)
                    uploadPath = fileData.node.image.filepath;
                    setConvertedIosFile(uploadPath)
                }
                uploadToServer(uploadPath);
            }
            catch (error) {
                setSnackbarMsg(ErrorMessages.addPostMediaInvalidErr);
                setRefreshSnackBar(Math.random());
                setDisplaySnackbar(true)
                setShowLoading(false);
            }
        }
        else {
            const mediaData = postMediaData.node;
            uploadToServer(mediaData.image.uri);
            let mType = mediaData.type.split('/');
            let fileFormat = mType[mType.length - 1]
            if (fileFormat.toUpperCase() == "QUICKTIME") {
                fileFormat = "mov";
            }
            let fileName = dateDbFormat(new Date()) + "." + fileFormat;
            const destPath = `${TemporaryDirectoryPath}/${fileName}`;
            // console.log("destPath", destPath)

            // moveFile(mediaData.image.uri, destPath)
            //     .then(() => {
            //         uploadToServer(destPath);
            //     })
            //     .catch((err) => {
            //         // console.log(err.message);
            //         setSnackbarMsg(ErrorMessages.addPostMediaInvalidErr);
            //         setRefreshSnackBar(Math.random());
            //         setDisplaySnackbar(true)
            //         setShowLoading(false);
            //     });
        }


    }
    const uploadToServer = async (videoUri) => {
        videoDestUri = videoUri;
        const realPath = await getRealPath(videoUri);
        submitPost(realPath);
    }
    function submitPost(mediaUriValue) {
        TempData.backupMediaData = mediaUriValue
        let parts = mediaUriValue.split('.');
        let fileFormat = parts[parts.length - 1];
        let dbFileFormat = fileFormat;
        if (fileFormat.toUpperCase() == "MOV") {
            dbFileFormat = "QUICKTIME";
        }
        let viewerFee = 0;
        if (activeMonetizing) {
            viewerFee = monetizingValue;
        }
        let encodeSt = encodeHtmlEntitessData(description);
        let taggedUsers = [];
        selectedTagPeoples.map(taggedPeople => {
            taggedUsers.push({ profile_seq: taggedPeople });
        });

        let topics = [];
        selectedTopics.map(topic => {
            topics.push(topic);
        });
        let reactions = [];
        selectedReactions.map(reaction => {
            reactions.push(reaction);
        });


        let hashMap = {
            _action_code: "11:SUBMIT_POST",
            file_format: dbFileFormat.toUpperCase(),
            media_type: selectedTabType == "PLAY" ? "VIDEO" : "IMAGE",
            comments: encodeURIComponent(encodeSt),
            post_type: activeMonetizing ? "PAID" : "FREE",
            viewer_fee: viewerFee,
        }
        let captureFile = {
            uri: Platform.OS === 'android' ? mediaUriValue : mediaUriValue.replace('file://', ''),
            name: "media_" + dateDbFormat(new Date()) + "." + fileFormat,
            type: MimeTypeList[fileFormat]
        };
        let imageHashMap = [];
        if (postMediaData != null) {
            imageHashMap.push({ inputName: "File1", imageData: captureFile });
        }
        if (showExpiryBox) {
            hashMap.expire_on = dateDbFormat(expiryTime);
        }
        if (taggedUsers.length != 0) {
            hashMap.tagged_users = encodeURIComponent(JSON.stringify(taggedUsers));
        }
        if (topics.length != 0) {
            hashMap.topics = encodeURIComponent(JSON.stringify(topics));
        }
        if (reactions.length != 0) {
            hashMap.reactions = encodeURIComponent(JSON.stringify(reactions));
        }
        if (checkValueLength(selectedLocation)) {
            hashMap.location = encodeURIComponent(selectedLocation);
        }
        if (showScheduleBox) {
            hashMap.schedule = encodeURIComponent(DBDateTimeFormat(scheduleTime));
        }
        // console.log("hashMap", hashMap);
        // setShowLoading(false);
        // setSnackbarMsg("Test Data");
        // setDisplaySnackbar(true)
        // setRefreshSnackBar(Math.random());
        // return;
        let connector = new ServerConnector();
        connector.postDataMultiPart(hashMap, imageHashMap, (data) => { // success method
            setPreviewBox(false);
            setPostMediaData(null);
            setShowLoading(false);
            appData._profilePagePostRefresh = true;
            // if (Platform.OS != 'ios') {
            //     if (videoDestUri != null) {
            //         deleteTempFile(videoDestUri);
            //     }
            // }

            setTimeout(() => {
                navigation.replace("AddPostSuccessScreen", {
                    postSeq: data.data.post_seq
                });
            }, 500);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setDisabledBtn(false)
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.comments) {
                            setSnackbarMsg(data.data.comments);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.file_format) {
                            setSnackbarMsg(data.data.file_format);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.media_type) {
                            setSnackbarMsg(data.data.media_type);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.post_type) {
                            setSnackbarMsg(data.data.post_type);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.viewer_fee) {
                            setSnackbarMsg(data.data.viewer_fee);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.expire_on) {
                            setSnackbarMsg(data.data.expire_on);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.tagged_users) {
                            setSnackbarMsg(data.data.tagged_users);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.topics) {
                            setSnackbarMsg(data.data.topics);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.reactions) {
                            setSnackbarMsg(data.data.reactions);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.location) {
                            setSnackbarMsg(data.data.location);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                        else if (data.data.schedule) {
                            setSnackbarMsg(data.data.schedule);
                            setDisplaySnackbar(true)
                            setRefreshSnackBar(Math.random());
                            return;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    setSnackbarMsg(errorMessage);
                    setDisplaySnackbar(true)
                    setRefreshSnackBar(Math.random());
                }

            }
        });
    }
    const deleteTempFile = (tempUri) => {
        try {
            deleteFile(tempUri)
                .then(() => {
                    // console.log('FILE DELETED', tempUri);
                    if (tempUri == videoDestUri) {
                        videoDestUri = null;
                    }
                })
        } catch (error) {
            // console.log("DeletionErr", error);
        }
    }
    const confirmPopupClick = (clickID, data) => {
        setShowConfirmPopup(false);
        setShowConfirmPopupKey(Math.random())
        if (clickID == "positive") {
            setScreenName("IMAGE");
            appData.isRedirectToAccountScreen = "YES"
            navigation.navigate("AccountInfoScreen", {
                showBankDetails: "YES",
            });
        }
    }
    const verificationBtnPress = () => {
        navigation.navigate('VerificationsScreen');
    }
    function getUserProfileService() {
        let hashMap = {
            _action_code: "11:GET_USER_PROFILE",
            req_profile_seq: __ProfileSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let hasBankDetails = "NO";
            if (data.data[0].bank_account !== null) {
                if (data.data[0].bank_account.length !== 0) {
                    hasBankDetails = "YES";
                }
            }
            let userDetails = {
                _has_bank_details: hasBankDetails,
                _is_profile_verified: data.data[0].is_verified,
            };
            changeUserDetails(userDetails);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
            }
        });
    }

    async function hasAndroidPermission() {
        const getCheckPermissionPromise = () => {
            if (Platform.Version >= 33) {
                return Promise.all([
                    PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES),
                    PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO),
                ]).then(
                    ([hasReadMediaImagesPermission, hasReadMediaVideoPermission]) =>
                        hasReadMediaImagesPermission && hasReadMediaVideoPermission,
                );
            } else {
                return PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
            }
        };
        const hasPermission = await getCheckPermissionPromise();
        if (hasPermission) {
            return true;
        }
        const getRequestPermissionPromise = () => {
            if (Platform.Version >= 33) {
                return PermissionsAndroid.requestMultiple([
                    PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
                    PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
                ]).then(
                    (statuses) => {
                        let storagePermission = false;
                        if (statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES] ===
                            PermissionsAndroid.RESULTS.GRANTED &&
                            statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO] ===
                            PermissionsAndroid.RESULTS.GRANTED) {
                            storagePermission = true;
                        }
                        if (!storagePermission) {
                            openSettingForStorage();
                        }
                        return storagePermission;
                    }

                );
            } else {
                return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE).then((status) => status === PermissionsAndroid.RESULTS.GRANTED);
            }
        };
        return await getRequestPermissionPromise();
    }
    const takePermissionBtnClick = () => {
        appData.permissionTime = new Date();
        checkStoragePermission();
    }
    const checkStoragePermission = async () => {
        const checkPermission = requestStoragePermission();
        checkPermission.then(async res => {
            if (res) {
                if (Platform.OS === "android" && !(await hasAndroidPermission())) {
                    return;
                }
                setIsStoragePermissionGranted(true);
            }
        })
    }
    const showPermissionAlert = () => {
        Alert.alert("Error", "Please grant us access to your photo gallery for the best SoTrue experience!", [
            { text: 'Cancel', onPress: () => console.log('Cancel Pressed!') },
            { text: 'OK', onPress: () => Linking.openSettings("PHOTOS") },
        ]);
    };
    const showTopicInfo = () => {

    }
    const datePickerRef = useRef(null);
    const nextBtnPress = () => {
        if (screenName == "IMAGE") {
            if (postMediaData == null) {
                setSnackbarMsg(ErrorMessages.addPostNoMediaErr);
                setDisplaySnackbar(true)
                setRefreshSnackBar(Math.random());
                return;
            }
            else {
                setDisplaySnackbar(false)
            }
        }
        if (completedStep == 1) {
            setScreenName("CAPTION");
        }
        else if (completedStep == 2) {
            setScreenName("PRICE");
        }
        else if (completedStep == 3) {
            setScreenName("TAGS");
        }
        if (!disablePreviewBtn) {
            previewPost();
        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <BottomSheetModalProvider>
                <View style={{ ...style.container, position: 'relative' }}>
                    <BackBtnComponent doneBtnPress={doneBtnPress} goBackPrevious={goBackPrevious}
                        screenName={screenName} disablePreviewBtn={disablePreviewBtn} nextBtnPress={nextBtnPress}
                        showNextBtn={isStoragePermissionGranted} />
                    <View style={style.progressBarBox}>
                        <PostingProgressBar clickable={!previewBox} progressStep={completedStep} barBtnPress={barBtnPress} />
                    </View>

                    {/* <ScrollView
                        nestedScrollEnabled> */}
                    <View style={style.mainContainer}>
                        {
                            screenName == "IMAGE" ?
                                <CustomSnackbar snackMsg={snackbarMsg} displaySnackbar={displaySnackbar}
                                    refreshSnack={refreshSnackBar} />
                                : null
                        }


                        {
                            isStoragePermissionGranted ?
                                <>
                                    <View style={style.mediaDisplayBox}>
                                        {
                                            postMediaData != null ?
                                                <View style={{ position: 'relative' }}>

                                                    <TouchableOpacity onPress={() => viewMediaBtnPress()}>
                                                        <Image source={{ uri: postMediaData.node.image.uri }} style={style.mediaBoxImage} />
                                                        {
                                                            selectedTabType == "PLAY" ?
                                                                <View style={{
                                                                    position: 'absolute', top: 0, left: 0, right: 0, bottom: 0,
                                                                    justifyContent: 'center', alignItems: 'center', zIndex: 999,
                                                                    backgroundColor: '#00000050'
                                                                }}>
                                                                    <FAB
                                                                        style={{ backgroundColor: 'transparent', }}

                                                                        icon="play-circle-outline"
                                                                    />

                                                                </View>
                                                                : null
                                                        }
                                                    </TouchableOpacity>

                                                </View>
                                                : null
                                        }

                                        <View style={style.postTypeBox}>
                                            <PostTypeSwitchBox onTabSelect={onTabSelect} seletecTabValue={selectedTabType} />
                                        </View>
                                        <View style={style.postPreviewBox}>
                                            <TouchableOpacity style={{
                                                ...style.postPreviewBtn,
                                                backgroundColor: disablePreviewBtn ? "#00000029" : "#FFFFFF"
                                            }}
                                                onPress={() => previewPost()}
                                                disabled={disablePreviewBtn}>
                                                <EntutoTextView style={{
                                                    ...style.postPreviewText,
                                                    color: disablePreviewBtn ? "#FFFFFF" : "#000000"
                                                }}>
                                                    Preview</EntutoTextView>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                    <View style={{ height: 200 }}>
                                        <GalleryImageDisplayComponent tabChangeManual={tabChangeManual} MAX_FILE_SIZE={__MAX_FILE_SIZE} onMediaSelectionBtnPress={onMediaSelectionBtnPress} />
                                    </View>

                                </>
                                :
                                <View style={style.takePermissionBox}>
                                    {
                                        storageSpinner ?
                                            <ActivityIndicator size="large" color={theme.colors.primaryColor} />
                                            :
                                            <>

                                                <EntutoTextView style={style.takePermissionBoxText}>
                                                    {ErrorMessages.permissionStorageMsg}
                                                </EntutoTextView>
                                                <PrimaryButton label="Take Permission"
                                                    style={{ marginTop: 40, }} uppercase={false}
                                                    onPress={() => takePermissionBtnClick()} />

                                            </>
                                    }


                                </View>
                        }

                    </View>

                    {/* </ScrollView> */}

                    <Modal
                        animationType="fade"
                        ref={modalFlashCaptionRef}
                        onRequestClose={() => goBackPrevious()}
                        visible={screenName == "CAPTION"}>
                        <View style={{ margin: 0, flex: 1 }}>
                            {
                                Platform.OS == 'ios' ?
                                    <CustomStatusBar translucent={false} hidden={false} />
                                    : null
                            }
                            <CustomProgressDialog
                                showLoading={showLoading}
                            />
                            <BackBtnComponent doneBtnPress={doneBtnPress} goBackPrevious={goBackPrevious} screenName={screenName} />
                            <View style={style.captionContainer}>
                                <ScrollView
                                    keyboardShouldPersistTaps={'handled'}
                                    showsVerticalScrollIndicator={false}
                                    style={style.scrollViewContainer}
                                    onScrollBeginDrag={() => Keyboard.dismiss()}
                                >
                                    <View style={{ marginTop: 24, marginBottom: 100 }}>
                                        <HeadingText label='Caption' />
                                        <View style={style.captionInputBox}>
                                            <TextInput
                                                ref={descInputRef}
                                                value={description}
                                                placeholder='Add New Description'
                                                placeholderTextColor={"#ccc"}

                                                multiline
                                                maxLength={1000}
                                                style={style.inputContainer}
                                                onChangeText={(text) => descriptionChangeHandler(text)} />
                                            <View style={{ ...defaultStyle.inputUnderCountBox, }}>
                                                <EntutoTextView style={defaultStyle.inputUnderCountBoxTxt}>{description.length}/1000</EntutoTextView>
                                            </View>
                                        </View>
                                        <View style={style.subContainerGap}>
                                            <HeadingText label='Topics' showTooltip={false} tooltipText='Please select minimum of 6 content relevant topics, for the best experience.' />

                                            {
                                                selectedTopics.length == 0 ?
                                                    <EntutoTextView style={defaultStyle.warringInterestText}>
                                                        Please select minimum of 3 content relevant topics, for the best experience.
                                                    </EntutoTextView>
                                                    : null
                                            }

                                            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                                                {
                                                    displayTopicList.map((obj, i) => {
                                                        return <OptionSelectionItem
                                                            key={i}
                                                            index={i}
                                                            label={obj.label}
                                                            isChecked={obj.isChecked}
                                                            value={obj.value}
                                                            onItemSelected={onInterestItemPress}
                                                        />
                                                    })
                                                }
                                                <OptionSelectionItem isSearch={true} onItemSelected={searchTopicBtnPress} />
                                            </View>
                                        </View>
                                        <View style={style.subContainerGap}>
                                            <HeadingText label='Reactions' />
                                            {
                                                checkValueLength(selectedReactionsErr) ?
                                                    <EntutoTextView style={defaultStyle.warringInterestText}>
                                                        {selectedReactionsErr}
                                                    </EntutoTextView>
                                                    : null
                                            }
                                            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                                                {
                                                    reactionList.map((item, i) => {
                                                        return <OptionSelectionItem
                                                            key={i}
                                                            index={i}
                                                            label={item.label}
                                                            isChecked={selectedReactions.includes(item.value)}
                                                            value={item.value}
                                                            showImage={true}
                                                            imageValue={item.icon}
                                                            onItemSelected={onReactionItemSelect} />
                                                    })
                                                }
                                            </View>
                                        </View>
                                        <View style={style.subContainerGap}>
                                            <View style={{
                                                flexDirection: 'row', alignItems: 'center', marginBottom: 20,
                                            }}>
                                                <HeadingText label='Schedule' styleObj={{ marginBottom: 0 }} />
                                                <View style={{ marginStart: 16, zIndex: 999 }}>
                                                    <EntutoNewSwitch selectedValue={showScheduleBox} onChange={() => handleScheduleBox()} />
                                                </View>
                                            </View>
                                            {
                                                showScheduleBox ?
                                                    <View>
                                                        <TouchableOpacity onPress={() => openSchedulePicker()}>
                                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                                <EntutoTextView style={style.dateValueText}>{DateDisplayFormat(scheduleTime)}</EntutoTextView>
                                                                <Image source={DateIcon} style={style.dateIcon} />
                                                            </View>
                                                            <View style={{ marginTop: 10 }}>
                                                                <EntutoTextView style={style.dateValueText}>{DisplayTimeFormat(scheduleTime)}</EntutoTextView>
                                                            </View>
                                                        </TouchableOpacity>
                                                    </View>
                                                    : null
                                            }

                                        </View>
                                        <View style={style.subContainerGap}>
                                            <View style={{
                                                flexDirection: 'row', alignItems: 'center', marginBottom: 20,
                                            }}>
                                                <HeadingText label='Expiry' styleObj={{ marginBottom: 0 }} />
                                                <View style={{ marginStart: 16 }}>
                                                    <EntutoNewSwitch selectedValue={showExpiryBox} onChange={handleExpiryBox} />
                                                </View>
                                            </View>
                                            {
                                                showExpiryBox ?
                                                    <View>
                                                        <TouchableOpacity onPress={() => openExpiryDatePicker()}>
                                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                                <EntutoTextView style={style.dateValueText}>{DateDisplayFormat(expiryTime)}</EntutoTextView>
                                                                <Image source={DateIcon} style={style.dateIcon} />
                                                            </View>
                                                            {/* <View style={{ marginTop: 10 }}>
                                                        <EntutoTextView style={style.dateValueText}>12:31 PM</EntutoTextView>
                                                    </View> */}
                                                        </TouchableOpacity>
                                                    </View>
                                                    : null
                                            }

                                        </View>
                                    </View>

                                </ScrollView>

                            </View>
                            <CustomSnackbar snackMsg={snackbarMsg} displaySnackbar={displaySnackbar}
                                refreshSnack={refreshSnackBar} showInsideFlashRef={true} insideFlashRef={modalFlashCaptionRef} />
                            <Modal
                                animationType="fade"
                                visible={showTopicSearchModal}
                                style={{ margin: 0, flex: 1 }}>
                                <SelectBoxComponent
                                    selectBoxClick={selectTopicBoxClick}
                                    list={JSON.parse(JSON.stringify(topicsData))}
                                    selectedValue={selectedTopics}
                                    title="Select Topic"
                                    maxSelectedValue={30}
                                    multiSelect={true}
                                    labelField="label"
                                    valueField="value"
                                />

                            </Modal>
                            {
                                Platform.OS == 'android' ?
                                    <>
                                        {showSchedulePicker && (
                                            <DateTimePicker
                                                testID="dateTimePicker"
                                                value={scheduleTime}
                                                mode={Platform.OS === 'ios' ? 'datetime' : 'date'}
                                                is24Hour={true}
                                                display="default"
                                                minimumDate={new Date()}
                                                onChange={onScheduleDateChange}
                                            />
                                        )}
                                        {showScheduleTimePicker && (
                                            <DateTimePicker
                                                testID="timePicker"
                                                value={scheduleTime}
                                                mode={"time"}
                                                is24Hour={false}
                                                display="default"
                                                onChange={onScheduleTimeChange}
                                            />
                                        )}
                                        {showExpiryPicker && (
                                            <DateTimePicker
                                                testID="dateTimePicker"
                                                value={expiryTime}
                                                mode={'date'}
                                                is24Hour={true}
                                                display="default"
                                                minimumDate={new Date()}
                                                onChange={onExpiryDateChange}
                                            />
                                        )}
                                    </>
                                    : null
                            }




                        </View>
                        {
                            Platform.OS == 'ios' ?
                                <>
                                    {showSchedulePicker && (
                                        <View style={{ minHeight: 300, paddingBottom: 80, backgroundColor: theme.colors.backgroundColor }}>
                                            <DateTimePicker
                                                style={{ height: 200, backgroundColor: theme.colors.backgroundColor, marginBottom: 16, marginTop: 16 }}
                                                testID="dateTimePicker"
                                                value={scheduleTime}
                                                mode={'datetime'}
                                                is24Hour={true}
                                                display="spinner"
                                                textColor={theme.colors.dateTextColor}
                                                minimumDate={new Date()}
                                                onChange={onScheduleDateChange}
                                            />
                                            <View style={{ paddingHorizontal: 16 }}>
                                                <PrimaryButton label="Done" onPress={() => scheduleIosDoneBtnpress()} />
                                                <PrimaryButton label="Cancel" style={{ marginTop: 16 }} onPress={() => scheduleIosCancelBtnpress()} />
                                            </View>
                                        </View>
                                    )}
                                    {showExpiryPicker && (
                                        <View style={{ minHeight: 300, paddingBottom: 80, backgroundColor: theme.colors.backgroundColor }}>
                                            <DateTimePicker
                                                style={{ height: 200, backgroundColor: theme.colors.backgroundColor, marginBottom: 16, marginTop: 16 }}
                                                testID="dateTimePicker"
                                                value={expiryTime}
                                                mode={'date'}
                                                is24Hour={true}
                                                display="spinner"
                                                textColor={theme.colors.dateTextColor}
                                                minimumDate={new Date()}
                                                onChange={onExpiryDateChange}
                                            />
                                            <View style={{ paddingHorizontal: 16 }}>
                                                <PrimaryButton label="Done" onPress={() => expiryIosDoneBtnpress()} />
                                                <PrimaryButton label="Cancel" style={{ marginTop: 16 }} onPress={() => expiryIosCancelBtnpress()} />
                                            </View>
                                        </View>
                                    )}
                                </>
                                : null
                        }



                    </Modal>

                    <Modal
                        animationType="fade"
                        ref={modalFlashPriceRef}
                        visible={screenName == "PRICE"}
                        onRequestClose={() => goBackPrevious()}>
                        <View style={{ margin: 0, flex: 1, }}>
                            {
                                Platform.OS == 'ios' ?
                                    <CustomStatusBar translucent={false} hidden={false} />
                                    : null
                            }
                            <CustomProgressDialog
                                showLoading={showLoading}
                            />
                            <BackBtnComponent doneBtnPress={doneBtnPress} goBackPrevious={goBackPrevious} screenName={screenName} />
                            <View style={style.captionContainer}>
                                {
                                    showAccountVerification ?
                                        <View style={style.verificationBox}>
                                            <EntutoTextView style={{ color: theme.colors.accVerifiedFlushMsg }}>{ErrorMessages.accVerifiedFlushMsg}</EntutoTextView>
                                            <TouchableOpacity onPress={() => verificationBtnPress()}><EntutoTextView style={{ color: theme.colors.primaryColor }}> Click here for verification</EntutoTextView>
                                            </TouchableOpacity>
                                        </View>
                                        : null
                                }
                                <View style={{ ...style.scrollViewContainer, marginTop: Platform.OS == 'ios' ? 24 : 24 }}>
                                    <View style={{
                                        flexDirection: 'row', alignItems: 'center',
                                        marginBottom: 20,
                                        justifyContent: 'space-between'
                                    }}>
                                        <EntutoTextView style={style.headingLabel}>Monetise</EntutoTextView>
                                        <View style={{ marginStart: 16 }}>
                                            <EntutoNewSwitch selectedValue={activeMonetizing} onChange={handleMonetizing} />
                                        </View>
                                    </View>
                                    <View>
                                        <Slider
                                            style={{ ...style.slider, }}
                                            trackHeight={12}
                                            minimumValue={9}
                                            maximumValue={49}
                                            minimumTrackTintColor={activeMonetizing ? theme.colors.primaryColor : "#707070"}
                                            maximumTrackTintColor="#3A3939"
                                            thumbTintColor={activeMonetizing ? theme.colors.primaryColor : "#707070"}
                                            tapToSeek={true}
                                            step={1}
                                            disabled={!activeMonetizing}
                                            value={monetizingValue}
                                            onValueChange={(value) => { activeMonetizing ? setSliderValue(value) : null }}
                                            onSlidingComplete={(value) => { activeMonetizing ? setMonetizingValue(value) : null }}
                                        />
                                    </View>
                                    <View>
                                        <EntutoTextView style={style.sliderValue}>INR {monetizingValue}</EntutoTextView>
                                    </View>

                                </View>
                            </View>
                            <CustomSnackbar snackMsg={snackbarMsg} displaySnackbar={displaySnackbar}
                                refreshSnack={refreshSnackBar} showInsideFlashRef={true} insideFlashRef={modalFlashPriceRef} />

                            <Modal
                                animationType="fade"
                                transparent
                                contentContainerStyle={{ backgroundColor: theme.colors.backgroundColor, margin: 20, borderRadius: 10 }}
                                visible={showConfirmPopup}
                                style={{ margin: 0, flex: 1 }}
                                onRequestClose={() => confirmPopupClick("Cancel", {})}>
                                <View style={style.modelWrapper}>
                                    <View style={style.modalView}>
                                        <EntutoTextView style={style.modalViewHeading}>Confirmation</EntutoTextView>
                                        <EntutoTextView style={style.modalViewBody}>
                                            {ErrorMessages.paidPostBankDetailsConfirmMsg}
                                        </EntutoTextView>
                                        <View style={style.modalViewActionBox}>
                                            <Button onPress={() => confirmPopupClick("Cancel", {})}>No</Button>
                                            <Button onPress={() => confirmPopupClick("positive", {})}>Yes</Button>
                                        </View>
                                    </View>


                                </View>

                            </Modal>
                        </View>

                    </Modal>

                    <Modal
                        animationType="fade"
                        ref={modalFlashTagsRef}
                        visible={screenName == "TAGS"}
                        onRequestClose={() => goBackPrevious()}>
                        <View style={{ margin: 0, flex: 1, }}>
                            {
                                Platform.OS == 'ios' ?
                                    <CustomStatusBar translucent={false} hidden={false} />
                                    : null
                            }
                            <CustomProgressDialog
                                showLoading={showLoading}
                            />
                            <BackBtnComponent doneBtnPress={doneBtnPress} goBackPrevious={goBackPrevious} screenName={screenName} />
                            <View style={style.captionContainer}>
                                <ScrollView style={style.scrollViewContainer}
                                    keyboardShouldPersistTaps={'handled'}
                                    showsVerticalScrollIndicator={false}>
                                    <View style={{ marginTop: Platform.OS == 'ios' ? 10 : 1 }}>
                                        {/* <View style={style.subContainerGap}>
                                            <HeadingText label='Location' />
                                            {
                                                selectedLocation.length == 0 ?
                                                    <EntutoTextView style={defaultStyle.warringInterestText}>
                                                        Select a location that best fits your content.
                                                    </EntutoTextView>
                                                    : null
                                            }
                                            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                                                {
                                                    selectedLocation.length != 0 ?
                                                        <OptionSelectionItem
                                                            key={1}
                                                            index={1}
                                                            label={selectedLocation}
                                                            isChecked={true}
                                                            value={selectedLocation} />
                                                        : null
                                                }

                                                <OptionSelectionItem isSearch={true} onItemSelected={searchLocationBtnPress} />
                                            </View>
                                        </View> */}
                                        <View style={style.subContainerGap}>
                                            <HeadingText label='People' />
                                            {
                                                selectedTagPeoples.length == 0 ?
                                                    <EntutoTextView style={defaultStyle.warringInterestText}>
                                                        Tag any account which follows you as well!!
                                                    </EntutoTextView>
                                                    : null
                                            }

                                            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                                                {
                                                    displayTagPeopleList.map((obj, i) => {
                                                        return <OptionSelectionItem
                                                            key={i}
                                                            index={i}
                                                            label={obj.label}
                                                            isChecked={obj.isChecked}
                                                            value={obj.value} />
                                                    })
                                                }
                                                <OptionSelectionItem isSearch={true} onItemSelected={searchPeopleBtnPress} />
                                            </View>
                                        </View>
                                    </View>
                                </ScrollView>
                            </View>
                            <CustomSnackbar snackMsg={snackbarMsg} displaySnackbar={displaySnackbar}
                                refreshSnack={refreshSnackBar} showInsideFlashRef={true} insideFlashRef={modalFlashTagsRef} />
                            {/* <Modal
                                animationType="fade"
                                visible={openLocationListPopup}
                                style={{ margin: 0, flex: 1, paddingTop: ModalTopGap }}>
                                <LocationBoxComponent
                                    selectBoxClick={selectLocationBoxClick}
                                    list={JSON.parse(JSON.stringify(locationList))}
                                    selectedValue={selectedLocation}
                                    title="Select Location"
                                    multiSelect={false}
                                    labelField="label"
                                    valueField="value"
                                />
                            </Modal> */}
                            <Modal
                                animationType="fade"
                                visible={openTagPeopleListPopup}
                                style={{ margin: 0, flex: 1, paddingTop: ModalTopGap }}>
                                <SelectBoxComponent
                                    selectBoxClick={selectPeopleBoxClick}
                                    list={JSON.parse(JSON.stringify(tagPeopleList))}
                                    selectedValue={selectedTagPeoples}
                                    title="Select People"
                                    maxSelectedValue={10}
                                    multiSelect={true}
                                    labelField="label"
                                    valueField="value"
                                />
                            </Modal>
                        </View>
                    </Modal>
                    <Modal
                        animationType="fade"
                        ref={modalFlashRef}
                        visible={previewBox}
                        onRequestClose={() => goBackPrevious()}>
                        <View style={{ margin: 0, flex: 1 }}>
                            {
                                Platform.OS == 'ios' ?
                                    <CustomStatusBar translucent={false} hidden={false} />
                                    : null
                            }

                            <CustomProgressDialog
                                showLoading={showLoading}
                            />
                            <BackBtnComponent doneBtnPress={doneBtnPress} goBackPrevious={goBackPrevious}
                                screenName={screenName} showDoneBtn={false} />
                            <View style={{ ...style.captionContainer, marginTop: Platform.OS == 'ios' ? 10 : 1 }}>
                                <ScrollView style={{ ...style.scrollViewContainer, paddingHorizontal: 1 }}
                                    keyboardShouldPersistTaps={'handled'}
                                    showsVerticalScrollIndicator={false}>
                                    <View style={{}}>
                                        <View style={style.progressBarBox}>
                                            <PostingProgressBar clickable={!previewBox} progressStep={completedStep} barBtnPress={barBtnPress} />
                                        </View>
                                        <View style={{ flex: 1, flexDirection: 'row', paddingHorizontal: 23, }}>
                                            <HeadingText label='Preview' />
                                            <View style={{ marginLeft: 'auto' }}>
                                                <TouchableOpacity style={{
                                                    ...style.postPreviewBtn,
                                                    backgroundColor: "#FFFFFF"
                                                }}
                                                    onPress={() => postBtnPress()}
                                                    disabled={disablePreviewBtn}>
                                                    <EntutoTextView style={{
                                                        ...style.postPreviewText,
                                                        color: "#000000"
                                                    }}>Post</EntutoTextView>
                                                </TouchableOpacity>
                                            </View>
                                        </View>

                                        <View style={style.subContainerGap}>
                                            {
                                                postMediaData != null ?
                                                    <PreviewPostForAdd description={description} postFile={postMediaData.node.image.uri}
                                                        postType={selectedTabType == "PLAY" ? "VIDEO" : "IMAGE"} selectedReactions={selectedReactions}
                                                        navigation={navigation} />
                                                    : null
                                            }

                                        </View>
                                    </View>

                                </ScrollView>

                            </View>
                            <CustomSnackbar snackMsg={snackbarMsg} displaySnackbar={displaySnackbar}
                                refreshSnack={refreshSnackBar} showInsideFlashRef={true} insideFlashRef={modalFlashRef} />
                        </View>

                    </Modal>


                </View>
            </BottomSheetModalProvider>

            {/* {
                showConfirmPopup &&
                <ConfirmationPopup
                    visiblePopupKey={showConfirmPopupKey}
                    visiblePopup={showConfirmPopup}
                    title="Confirmation"
                    messagebody={ErrorMessages.paidPostBankDetailsConfirmMsg}
                    positiveButton="Yes"
                    negativeButton="No"
                    data={{}}
                    popupClick={(clickID, data) => { confirmPopupClick(clickID, data) }}
                />
            } */}
            {/* {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshErrorMsg} />
                    : null
            } */}

        </>
    )
}

export default PostingJourneyScreen;
const PostTypeSwitchBox = ({
    onTabSelect,
    seletecTabValue
}) => {
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [selectedTab, setSelectedTab] = useState("");
    useEffect(() => {
        setSelectedTab(seletecTabValue)
    }, [seletecTabValue])

    const handleTabChange = (tab) => {
        onTabSelect(tab);
        // setSelectedTab(tab);
    }
    return (
        <View style={{ ...style.postTypeSwitch, backgroundColor: theme.colors.postTypeButtonBackground }}>
            <TouchableOpacity onPress={() => handleTabChange("POST")}
                style={{
                    ...style.postTypeSwitchItem,
                    marginEnd: 10,
                    backgroundColor: selectedTab == "POST" ? theme.colors.primaryColor : theme.colors.postTypeButtonBackground
                }}>
                <EntutoTextView style={{ ...style.postTypeSwitchItemText, color: selectedTab == "POST" ? "#FFF" : "#111" }}>Post</EntutoTextView>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleTabChange("PLAY")}
                style={{
                    ...style.postTypeSwitchItem,
                    backgroundColor: selectedTab == "PLAY" ? theme.colors.primaryColor : theme.colors.postTypeButtonBackground
                }}>
                <EntutoTextView style={{ ...style.postTypeSwitchItemText, color: selectedTab == "PLAY" ? "#FFF" : "#111" }}>Play</EntutoTextView>
            </TouchableOpacity>

        </View>
    )
}
const HeadingText = ({ label = "", styleObj = {}, showTooltip = false, tooltipText = "" }) => {
    const [showPopover, setShowPopover] = useState(false);
    const touchable = useRef();
    const style = useSThemedStyles(styles);
    return <View style={{ ...style.headingLabelBox, ...styleObj, flexDirection: 'row', alignItems: 'center' }}>
        <EntutoTextView style={style.headingLabel}>{label}</EntutoTextView>
    </View>
}
const BackBtnComponent = ({ goBackPrevious, doneBtnPress, screenName, nextBtnPress = null,
    disablePreviewBtn = true, showNextBtn = false, showDoneBtn = true }) => {
    const style = useSThemedStyles(styles);
    const [nextBtnText, setNextBtnText] = useState("Next")
    useEffect(() => {
        if (!disablePreviewBtn) {
            setNextBtnText("Preview")
        }
        else {
            setNextBtnText("Next")
        }
    }, [disablePreviewBtn])
    const nextPress = () => {
        if (nextBtnPress) nextBtnPress()
    }

    return <View style={style.topBar}>
        <View>
            <TouchableOpacity onPress={() => goBackPrevious()}
            >
                <View style={{ padding: 10 }}>
                    <Image style={style.arrowIcon}
                        source={ArrowIcon} />
                </View>
            </TouchableOpacity>
        </View>
        {
            screenName == "IMAGE" && showNextBtn ?
                <View>
                    <TouchableOpacity onPress={() => nextPress()}>
                        <View style={{ padding: 10 }}>
                            <EntutoTextView style={style.nextBtnText}>{nextBtnText}</EntutoTextView>
                        </View>
                    </TouchableOpacity>
                </View>
                :
                <View>
                    {
                        showDoneBtn ?
                            <View>
                                <TouchableOpacity onPress={() => doneBtnPress()}>
                                    <View style={{ paddingHorizontal: 8, padding: 10 }}>
                                        <EntutoTextView>Done</EntutoTextView>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            : null
                    }

                </View>
        }

    </View>
}

const styles = theme => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.backgroundColor
    },
    topBar: {
        zIndex: 78,
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingVertical: 16,
        paddingHorizontal: 20,
        backgroundColor: theme.colors.backgroundColor,
    },
    arrowIcon: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
        tintColor: theme.colors.topHeaderColor
    },
    threeDotIcon: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
        tintColor: theme.colors.topHeaderColor
    },
    progressBarBox: {
        marginHorizontal: 20,
        marginTop: 16,
        marginBottom: 20,
    },
    mainContainer: {
        marginBottom: 80,
    },
    mediaDisplayBox: {
        height: Dimensions.get('screen').height / 2.5,//400
        position: 'relative',
        width: '100%',
        marginTop: 20,
        borderWidth: 1,
        borderColor: '#707070',
    },
    mediaBoxImage: {
        height: '100%',
        width: '100%',
        resizeMode: 'cover',
        opacity: 0.7
    },
    postTypeBox: {
        position: 'absolute',
        top: 20,
        left: 0,
        right: 0,
        zIndex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        height: 31,
        borderRadius: 1,
    },
    postTypeSwitch: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: 32,
        borderRadius: 1,
        alignItems: 'center',
        paddingHorizontal: 2,
        marginBottom: 8,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.20,
        shadowRadius: 1.41,
        elevation: 2,
    },
    postTypeSwitchItem: {
        height: 29,
        borderRadius: 1,
        paddingHorizontal: 32,
        justifyContent: 'center',
        alignItems: 'center',
    },
    postTypeSwitchItemText: {
        fontSize: theme.calculateFontSize(theme.dimensions.postingJSwitchText),
    },
    postPreviewBox: {
        position: 'absolute',
        bottom: 20,
        left: 0,
        right: 0,
        zIndex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        height: 31,
        borderRadius: 1,
    },
    postPreviewBtn: {
        height: 34,
        borderRadius: 1,
        paddingHorizontal: 50,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: "#00000029",
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.20,
        shadowRadius: 1.41,
        elevation: 2,

    },
    postPreviewText: {
        fontSize: theme.calculateFontSize(theme.dimensions.postingJPreviewText),
        color: "#FFF",
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
    },
    postingModelContainerL: {
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
    },
    captionContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        paddingTop: 54,
        zIndex: 77,
        backgroundColor: theme.colors.backgroundColor,
        paddingTop: Platform.OS == 'ios' ? 120 : 64
    },
    scrollViewContainer: {
        paddingHorizontal: 24,
        paddingBottom: 24,
    },
    headingLabelBox: {
        marginBottom: 20,
    },
    headingLabel: {
        fontSize: theme.calculateFontSize(theme.dimensions.postingJHeading),

    },
    subContainerGap: {
        marginTop: 24,
        zIndex: 999
    },
    captionInputBox: {
        borderWidth: 1,
        borderColor: "#707070",
        borderRadius: 1,
        height: 133,
        alignItems: 'flex-start',
        justifyContent: 'flex-start'
    },
    inputContainer: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        color: theme.colors.primaryTextColor,
        minHeight: Platform.OS == 'ios' ? 133 : 20,
        width: '100%',
    },
    itemContainer: {
        padding: theme.dimensions.loginModuleInputPadding,
        borderWidth: 1,
        justifyContent: "center",
        alignItems: "center",
        height: 36,
        borderRadius: 1,
        marginEnd: 8,
        marginVertical: 4,
        minWidth: 104,
        paddingHorizontal: 24,

    },
    searchIcon: {
        width: 17,
        height: 17,
        resizeMode: 'contain',
        tintColor: "#768390",
        paddingHorizontal: 25,

    },
    listItemIcon: {
        width: 20,
        height: 20,
        resizeMode: 'contain',
        tintColor: "#FFFFFF",
        marginEnd: 13,
    },
    dateValueText: {
        fontSize: theme.calculateFontSize(theme.dimensions.postingJDateText),
        color: theme.colors.primaryColor,
    },
    dateIcon: {
        width: 18,
        height: 18,
        resizeMode: 'contain',
        marginStart: 16
    },
    slider: {
        width: 'auto',
        opacity: 1,
        height: 50,
    },
    sliderValue: {
        fontSize: theme.calculateFontSize(theme.dimensions.postingJSliderText),
        color: theme.colors.primaryTextColor,
        textAlign: 'right'
    },
    verificationBox: {
        backgroundColor: 'rgb(232, 244, 253)',
        color: 'rgb(13, 60, 97)',
        paddingHorizontal: 20,
        paddingVertical: 15,
        marginVertical: 8,
        flexDirection: 'row',
        flexWrap: 'wrap',

    },
    takePermissionBox: {
        padding: 24,
        marginTop: 40,

    },
    takePermissionBoxText: {
        textAlign: 'center',

    },
    popupOverBox: {
        padding: 8,

    },
    nextBtnText: {
        color: theme.colors.primaryColor
    },
    modelWrapper: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    modalView: {
        backgroundColor: theme.colors.backgroundColor,
        borderRadius: 10,
        padding: 20,
        margin: 20,
    },
    modalViewHeading: {
        fontSize: theme.calculateFontSize(theme.dimensions.postingJModalHeadingText),
        marginBottom: 16,
    },
    modalViewBody: {
        fontSize: theme.calculateFontSize(theme.dimensions.postingJModalBodyText),
    },
    modalViewActionBox: {
        marginTop: 20,
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center'
    }
})
