import React from 'react'
import { StyleSheet } from 'react-native'
import { Button } from 'react-native-paper'
import Colors from '../../constants/Colors'
import Dimensions from '../../constants/Dimensions'

const EntutoButton = props => {
    return <Button {...props}
        theme={{ ...myTheme, ...props.theme }}
        style={{ ...styles.button, ...props.style }}
    >{props.label}</Button>
}

export default EntutoButton

const myTheme = {
    roundness: Dimensions.textInputRadius
}
const styles = StyleSheet.create({
    button: {
        width: '100%',
        padding: 8,
        color: Colors.black,
    },

})
