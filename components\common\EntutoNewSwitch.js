import { Animated, Pressable, StyleSheet, View } from "react-native";
import useSThemedStyles from "../../theme/useSThemedStyles";
import useSTheme from "../../theme/useSTheme";
import React, { useEffect, useState } from "react";

const EntutoNewSwitch = ({ selectedValue = false, onChange = null }) => {
    const [value, setValue] = useState(selectedValue);
    const [animatedValue] = useState(new Animated.Value(value ? 1 : 0));
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    useEffect(() => {
        Animated.timing(animatedValue, {
            toValue: value ? 1 : 0,
            duration: 300,
            useNativeDriver: false,
        }).start();
        setValue(selectedValue);
    }, [value, selectedValue]);
    const translateX = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [1, 28],
    });
    const toggleSwitch = () => {
        // const newValue = !value;
        // setValue(newValue);
        if (onChange) {
            onChange();
        }

    };

    return (
        <Pressable onPress={toggleSwitch}
            style={{
                ...style.switchContainer,
                backgroundColor: value ? theme.colors.primaryColor + "30" : theme.colors.switchInActiveC,
                borderColor: value ? theme.colors.primaryColor + "30" : theme.colors.switchInActiveC,
            }}>
            <View style={style.innerContainer}>
                <Animated.View
                    style={{
                        transform: [{ translateX }],
                    }}>
                    <View style={{
                        ...style.thumbBox,
                        borderWidth: 1,
                        backgroundColor: value ? theme.colors.primaryColor : theme.colors.switchInActiveThumbC,
                        borderColor: value ? theme.colors.primaryColor : theme.colors.switchInActiveThumbC,
                    }} >
                    </View>
                </Animated.View>
            </View>
        </Pressable>
    )
}
export default EntutoNewSwitch;
const styles = theme => StyleSheet.create({
    switchContainer: {
        width: 50,
        height: 25,
        borderRadius: 15,
        backgroundColor: '#333',
        justifyContent: 'center',
        borderWidth: 1,
    },
    innerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        position: 'relative',
    },
    thumbBox: {
        width: 25,
        height: 25,
        borderRadius: 15,
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',

    },
})