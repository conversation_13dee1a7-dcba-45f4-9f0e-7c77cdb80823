import React, { useEffect, useState } from 'react'
import { RefreshControl, StyleSheet, Text, View } from 'react-native'
import { FlatList } from 'react-native-gesture-handler';
import CustomStatusBar from '../components/common/CustomStatusBar';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import ReferralHistoryRow from '../components/settings/ReferralHistoryRow';
import { DefaultRowsPerPage, _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const ReferralHistoryScreen = ({ navigation }) => {
    const [referralHisList, setreferralHisList] = useState([]);
    const [progressLoading, setprogressLoading] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(true);
    const { defaultStyle } = useDefaultStyle();
    const renderHisItem = ({ item }) => {
        return (
            <ReferralHistoryRow navigation={navigation} data={item} />
        );
    };
    useEffect(() => {
        getReferralEarnedUsersService(0, DefaultRowsPerPage, true);
    }, [])
    function getReferralEarnedUsersService(startRecord, rowsPerPage, isFirstSearch) {
        let hashMap = {
            _action_code: "11:GET_REFERRAL_EARNED_USERS",
            _start_row: startRecord,
            _rows_page: rowsPerPage,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            seterrorMsg("");
            setreferralHisList(data.data);
            setShowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (isFirstSearch) {
                    seterrorMsg(errorMessage);
                    setreferralHisList([]);
                    setShowLoading(false);
                }
            }
        });
    }

    const handleRefresh = () => {

    }
    const theme = useSTheme();

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Referral History" showBackBtn={true} navigation={navigation}
                showBorderBottom={false} />

            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside} >
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        : null
                }
                {
                    showLoading ?
                        <NotiRowPlaceholder />
                        :
                        <FlatList
                            contentContainerStyle={{ paddingBottom: 20 }}
                            data={referralHisList}
                            renderItem={renderHisItem}
                            keyExtractor={(item, index) => index.toString()}
                            refreshControl={
                                <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                            }
                        />
                }

            </View>
        </>
    )
}

export default ReferralHistoryScreen;

const styles = StyleSheet.create({

})
