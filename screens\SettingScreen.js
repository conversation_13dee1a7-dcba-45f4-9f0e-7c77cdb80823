import React, { useCallback, useRef, useState, useContext, useEffect } from 'react'
import { Al<PERSON>, Linking, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import HomeTopNavigationBar from '../components/HomeTopNavigationBar'
import ListItem from '../components/ListItem'
import ConfirmationPopup from '../components/common/ConfirmationPopup';
import { _clearAllData } from '../utils/AuthLogin';
import ActionSheet from 'react-native-actions-sheet';
import HelpSetting from '../components/settings/HelpSetting';
import CustomStatusBar from '../components/common/CustomStatusBar';
import ErrorMessages from '../constants/ErrorMessages';
import CopyLinkActionView from '../components/post/CopyLinkActionView';;
import { AppStateContext } from '..';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { WEB_CLIENT_ID } from '../utils/Appconfig';
import { CommonActions } from '@react-navigation/native';
import appData from '../data/Data';
import Share from 'react-native-share';
import { creationOfCopyLink } from '../utils/Utils';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const SettingScreen = ({ navigation }) => {
    const helpBtnRef = useRef(null)
    const [showConfirmPopup, setshowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());
    const { defaultStyle } = useDefaultStyle();
    const { fullUserDetails } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const _is_gmail_login = fullUserDetails.hasOwnProperty("_is_gmail_login") ? fullUserDetails._is_gmail_login : "NO";

    useEffect(() => {
        GoogleSignin.configure({
            webClientId: WEB_CLIENT_ID,
            offlineAccess: true,
        });

    }, [])

    const accountSettingsPress = () => {
        navigation.navigate('AccountSettingsScreen');
    }
    const referEarnSettingsPress = () => {
        navigation.navigate('ReferAndEarnScreen');
    }
    const bookmarkSettingsPress = () => {
        navigation.navigate('BookmarkListScreen');
        // navigation.replace('SuccessfullVerificationScreen', {
        //     successMsg: "This Test Message",
        // })
        // navigation.navigate('VerificationsScreen');
    }
    const userPersonalizePress = () => {
        navigation.navigate('UserPersonalizeScreen');
        // navigation.navigate('QuickSignUpPersonalizeScreen');
    }
    const logoutBtnPress = () => {
        setshowConfirmPopup(true);
        setshowConfirmPopupKey(Math.random())
    }
    const logoutPopupClick = (clickID, data) => {
        if (clickID == "positive") {
            appData._copyLinkPostSeq = "";
            appData._copyLinkProfileSeq = "";
            _clearAllData();
            appData._userDetails = null;
            if (_is_gmail_login == "YES") {
                try {
                    GoogleSignin.signOut()
                } catch (error) {

                }

            }
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'LoginScreen',
                            params: { ErrorMsg: '' },
                        },
                    ],
                })
            );
            // navigation.replace("LoginScreen", {
            //     ErrorMsg: "",
            // });
        }
    }
    const helpBtnPress = () => {
        helpBtnRef.current?.show();
    }
    const helpBtnActionClick = (clickId) => {
        if (clickId == "negetive") {
            helpBtnRef.current?.hide();
        }
        if (clickId == "close") {
            helpBtnRef.current?.hide();
        }
    }
    const copyLinkBtnRef = useRef(null);
    const copyLinkBtnPress = () => {
        copyLinkBtnRef.current?.show();
    }

    const faqBtnPress = () => {
        faqUrlClick();
    }
    const faqUrl = "https://www.sotrue.co.in/faq.html";
    const faqUrlClick = useCallback(async () => {
        // Checking if the link is supported for links with custom URL scheme.
        const supported = await Linking.canOpenURL(faqUrl);

        if (supported) {
            // Opening the link with some app, if the URL scheme is "http" the web link should be opened
            // by some browser in the mobile
            await Linking.openURL(faqUrl);
        } else {
            Alert.alert(`Don't know how to open this URL: ${faqUrl}`);
        }
    }, [faqUrl]);

    const aboutBtnPress = () => {
        navigation.navigate('AboutSettingScreen');
    }
    const shareBtnPress = () => {
        onShare();
    }
    const onShare = async () => {
        let copyLinkText = creationOfCopyLink("PROFILE", __ProfileSeq);
        const shareOptions = {
            message: "Exclusive content on SoTrue\n",
            url: copyLinkText
        }
        try {
            const shareResponse = await Share.open(shareOptions);
        } catch (error) {
            // console.log(error.message);
        }
    };
    const theme = useSTheme();
    const shareYourProfile = () => {
        navigation.navigate('ShareYourProfileScreen');
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar showBackBtn={true} showBorderBottom={false} title="Settings" navigation={navigation} />
            <ScrollView
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                style={{ backgroundColor: theme.colors.backgroundColor }}
            >
                <View style={defaultStyle.container}>
                    <TouchableOpacity onPress={() => accountSettingsPress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/account.png')} label="Account" />{/* Settings */}
                    </TouchableOpacity>

                    <TouchableOpacity onPress={() => bookmarkSettingsPress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/bookmark.png')} label="Bookmarks" />
                    </TouchableOpacity>
                    {/* <TouchableOpacity onPress={() => copyLinkBtnPress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/copy_icon.png')} label="Copy Link" />
                    </TouchableOpacity> */}
                    {/* <TouchableOpacity onPress={() => shareBtnPress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/share_icon.png')} label="Share Link" />
                    </TouchableOpacity> */}
                    <TouchableOpacity onPress={() => referEarnSettingsPress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/refer_and_earn.png')} label="Refer and Earn" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => helpBtnPress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/help.png')} label="Help" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => aboutBtnPress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/info_icon.png')} label="About" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => userPersonalizePress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/info_icon.png')} label="Personalisation" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => shareYourProfile()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/share_icon.png')}
                            label="Share Your Profile" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => logoutBtnPress()}>
                        <ListItem showIcon={true} icon={require('../assets/Images/icon/logout.png')} label="Logout" />
                    </TouchableOpacity>

                </View>
            </ScrollView>
            <ActionSheet ref={helpBtnRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                closeOnTouchBackdrop={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        helpBtnRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}
                >
                    <HelpSetting refVal={helpBtnRef} settingsType="HELP" postSeq={0} navigation={navigation}
                        helpBtnActionClick={(clickId, obj) => helpBtnActionClick(clickId, obj)} />
                </ScrollView>
            </ActionSheet >
            <ActionSheet ref={copyLinkBtnRef}
                statusBarTranslucent
                bounceOnOpen={false}
                gestureEnabled={false}
                closeOnTouchBackdrop={true}
                defaultOverlayOpacity={0.3}
                overlayBackgroundColor="rgba(0,0,0,0.3)"
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        copyLinkBtnRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <CopyLinkActionView copyLinkID={__ProfileSeq} copyLinkType="PROFILE" />
                </ScrollView>
            </ActionSheet >

            {
                showConfirmPopup &&
                <ConfirmationPopup
                    visiblePopupKey={showConfirmPopupKey}
                    visiblePopup={showConfirmPopup}
                    title="Confirmation"
                    messagebody={ErrorMessages.logoutConfirmMsg}
                    positiveButton="Logout"
                    negativeButton="Cancel"
                    data={{}}
                    popupClick={(clickID, data) => { logoutPopupClick(clickID, data) }}
                />
            }
        </>
    )
}

export default SettingScreen

const styles = StyleSheet.create({})
