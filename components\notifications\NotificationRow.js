import { useNavigation } from '@react-navigation/native';
import React, { useContext, useEffect } from 'react'
import { Image, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { AppStateContext } from '../..';
import Dimensions from '../../constants/Dimensions';
import { hasImageUrlExist } from '../../utils/Utils';
import ProgressiveImage from '../common/ProgressiveImage';
import SubheadingBodyTxt from '../common/SubheadingBodyTxt';
import SubheadingTxt from '../common/SubheadingTxt';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSThemedStyles from '../../theme/useSThemedStyles';

const NotificationRow = ({ rowType, profileSeq, timeTxt, afterTxt, rowClick = null, data, ...props }) => {
    const { fullUserDetails } = useContext(AppStateContext);
    const navigation = useNavigation();
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const goToProfile = (profileSeq) => {
        if (rowClick) {
            rowClick()
        }
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }
    const goToPost = () => {
        if (rowClick) {
            rowClick()
        }
        navigation.navigate('SinglePostScreen', {
            postSeq: data.post_seq,
        });
    }
    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
            <View style={style.profileImageBox}>
                <TouchableOpacity onPress={() => goToProfile(profileSeq)}>
                    <View style={style.profileImageBoxInside}>
                        <ProgressiveImage
                            style={style.profileImage}
                            source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                            defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                            resizeMode={'cover'}
                        />
                    </View>
                </TouchableOpacity>
            </View>
            <View style={style.profileNameBox}>
                <View style={{ flex: 1, flexDirection: 'row', marginBottom: 6, flexWrap: 'wrap', }}>
                    <TouchableOpacity onPress={() => goToProfile(profileSeq)} style={{ flex: 1, }} >
                        <View >
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <SubheadingTxt>{data.display_name}</SubheadingTxt>
                                {
                                    data.is_verified == "YES" ?
                                        <Image
                                            style={style.verifiedIcon}
                                            source={require('../../assets/Images/icon/verifiedicon.png')}
                                            resizeMode={'contain'}
                                        />
                                        : null
                                }
                            </View>
                            <View >
                                <SubheadingBodyTxt>{timeTxt}</SubheadingBodyTxt>
                            </View>
                        </View>
                    </TouchableOpacity>
                    {
                        data.hasOwnProperty("media_file") ?
                            <View style={{ marginLeft: 'auto' }}>
                                <TouchableOpacity onPress={() => goToPost()}>
                                    <ProgressiveImage
                                        style={style.postImage}
                                        source={hasImageUrlExist(data.media_file) ? { uri: data.media_file } : null}
                                        defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                                        resizeMode={'cover'}
                                    />
                                </TouchableOpacity>
                            </View>
                            : null
                    }

                </View>


                {/* <View style={{ flex: 1, flexDirection: 'row', flexWrap: 'wrap', }}>
                    <SubheadingBodyTxt>{data.display_name}{afterTxt}</SubheadingBodyTxt>
                </View> */}
            </View>

        </View>
    )
}

export default NotificationRow;

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        marginVertical: 10,
        marginBottom: 15,
    },
    profileImageBox: {
        position: 'relative',
        marginRight: 8
    },
    profileImageBoxInside: {
        backgroundColor: theme.colors.notiProfileImageBoxBackground,
        height: 40,
        width: 40,
        borderRadius: 40,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: Platform.OS == 'ios' ? 2 : 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: Platform.OS == 'ios' ? 4 : 16,
        elevation: Platform.OS == 'ios' ? 2 : 12,
        justifyContent: 'center',
        alignItems: 'center',
    },
    profileImage: {
        height: 35,
        width: 35,
        borderRadius: 35,
    },
    postImage: {
        height: 32,
        width: 32,
        borderRadius: 2,
    },
    profileNameBox: {
        flex: 1,
        flexDirection: 'column',
        //marginHorizontal: 10,
        paddingLeft: 10,
        marginRight: 8,

    },
    verifiedIcon: {
        width: 10,
        height: 9,
        marginLeft: theme.dimensions.veritextLeftmargin,
        // position: 'absolute',
        // right: -4,
        // top: 15,
    },
})
