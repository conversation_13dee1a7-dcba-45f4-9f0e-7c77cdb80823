import React, { useEffect, useState } from 'react'
import { Animated, Pressable, StyleSheet, View } from 'react-native'
import { Switch } from 'react-native-paper'
import Dimensions from '../../constants/Dimensions'
import useSThemedStyles from '../../theme/useSThemedStyles'
import useSTheme from '../../theme/useSTheme'

// const EntutoSwitch = props => {
//     return <Switch {...props}
//         theme={{ ...myTheme, ...props.theme }}
//         style={{ ...styles.switch, ...props.style }}
//     />
// }

// export default EntutoSwitch

// const myTheme = {
//     roundness: Dimensions.textInputRadius
// }
// const styles = StyleSheet.create({
//     switch: {
//     },

// })
const EntutoSwitch = ({ value = false, onValueChange = null }) => {
    const [switchValue, setSwitchValue] = useState(value);
    const [animatedValue] = useState(new Animated.Value(switchValue ? 1 : 0));
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    useEffect(() => {
        Animated.timing(animatedValue, {
            toValue: switchValue ? 1 : 0,
            duration: 300,
            useNativeDriver: false,
        }).start();
        setSwitchValue(value);
    }, [switchValue, value]);
    const translateX = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [1, 28],
    });
    const toggleSwitch = () => {
        // const newValue = !switchValue;
        // setSwitchValue(newValue);
        if (onValueChange) {
            onValueChange();
        }

    };

    return (
        <Pressable onPress={toggleSwitch}
            style={{
                ...style.switchContainer,
                backgroundColor: switchValue ? theme.colors.primaryColor + "30" : theme.colors.switchInActiveC,
                borderColor: switchValue ? theme.colors.primaryColor + "30" : theme.colors.switchInActiveC
            }}>
            <View style={style.innerContainer}>
                <Animated.View
                    style={{
                        transform: [{ translateX }],
                    }}>
                    <View style={{
                        ...style.thumbBox,
                        borderWidth: 1,
                        backgroundColor: switchValue ? theme.colors.primaryColor : theme.colors.switchInActiveThumbC,
                        borderColor: switchValue ? theme.colors.primaryColor : theme.colors.switchInActiveThumbC,
                    }} >
                    </View>
                </Animated.View>
            </View>
        </Pressable>
    )
}
export default EntutoSwitch;
const styles = theme => StyleSheet.create({
    switchContainer: {
        width: 50,
        height: 25,
        borderRadius: 15,
        justifyContent: 'center',
    },
    innerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        position: 'relative',
    },
    thumbBox: {
        width: 25,
        height: 25,
        borderRadius: 15,
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',

    },
})
