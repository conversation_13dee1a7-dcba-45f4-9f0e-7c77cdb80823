import React, { useState } from 'react'
import { ActivityIndicator, Dimensions, Image, ImageBackground, StyleSheet, Text, TouchableOpacity, View } from 'react-native'

import { useNavigation } from '@react-navigation/native';
import GestureRecognizer from 'react-native-swipe-gestures';
import UserView from './UserView';
import Story from './Story';
import ProgressArray from './ProgressArray';
import RenderHTML from 'react-native-render-html';
import { decodeHtmlEntitessData } from '../../utils/Utils';
import DescriptionCaptionStyle from '../common/DescriptionCaptionStyle';
import Colors from '../../constants/Colors';



const SCREEN_WIDTH = Dimensions.get("window").width;
const SCREEN_HEIGHT = Dimensions.get("window").height;
const Stories = ({ dataStories, ...props }) => {

    const navigation = useNavigation();
    const { stories = [] } = dataStories || {};
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isModelOpen, setModel] = useState(false);
    const [isPause, setIsPause] = useState(false);
    const [isLoaded, setLoaded] = useState(false);
    const [duration, setDuration] = useState(6);
    const story = stories.length ? stories[currentIndex] : {};

    const changeStory = (evt) => {
        if (evt.locationX > SCREEN_WIDTH / 2) {
            nextStory();
        } else {
            prevStory();
        }
    };
    const nextStory = () => {
        if (stories.length - 1 > currentIndex) {
            setCurrentIndex(currentIndex + 1);
            setLoaded(false);
            setDuration(3);
        } else {
            props.onClose();
            //setCurrentIndex(0);
            //props.onStoryNext(false);
        }
    };

    const prevStory = () => {
        if (currentIndex > 0 && stories.length) {
            setCurrentIndex(currentIndex - 1);
            setLoaded(false);
            setDuration(3);
        } else {
            setCurrentIndex(0);
            //props.onStoryPrevious(false);
        }
    };
    const onImageLoaded = () => {
        setLoaded(true);
    };

    const onVideoLoaded = (length) => {
        setLoaded(true);
        setDuration(length.duration);
    };

    const onPause = (result) => {
        setIsPause(result);
    };

    const onReadMoreOpen = () => {
        setIsPause(true);
        setModel(true);
    };
    const onReadMoreClose = () => {
        setIsPause(false);
        setModel(false);
    };

    const closeButton = () => {
        navigation.goBack()
    }
    const loading = () => {
        if (!isLoaded) {
            return (
                <View style={styles.loading}>
                    <ActivityIndicator color="white" size={60} style={{ backgroundColor: '#00000080', zIndex: 3 }} />
                    <View style={{
                        position: 'absolute',
                        top: 0, left: 0, right: 0, bottom: 0,
                        alignItems: 'center', justifyContent: 'center',
                        zIndex: 1
                    }}>
                        <ImageBackground resizeMode='cover'
                            source={stories[currentIndex] != undefined ? stories[currentIndex].media_cover != null ? { uri: stories[currentIndex].media_cover } : null : null}
                            style={{
                                height: SCREEN_HEIGHT,
                                width: '100%',
                            }} >
                        </ImageBackground>

                    </View>

                </View>
            );
        }
    };
    const config = {
        velocityThreshold: 0.3,
        directionalOffsetThreshold: 80,
    };

    const onSwipeDown = () => {
        if (!isModelOpen) {
            props.onClose();
        } else {
            setModel(false);
        }
    };

    const onSwipeUp = () => {
        if (!isModelOpen) {
            setModel(true);
        }
    };

    return (
        // <GestureRecognizer
        //     onSwipeDown={onSwipeDown}
        //     onSwipeUp={onSwipeUp}
        //     config={config}
        //     style={styles.container}
        // >
        <TouchableOpacity
            activeOpacity={1}
            delayLongPress={300}
            onPress={(e) => changeStory(e.nativeEvent)}
            onLongPress={() => onPause(true)}
            onPressOut={() => onPause(false)}
            style={styles.container}
        >
            <View style={styles.container}>
                <Story
                    onImageLoaded={onImageLoaded}
                    pause={isPause}
                    isNewStory={props.isNewStory}
                    onVideoLoaded={onVideoLoaded}
                    story={story}
                />

                {loading()}

                <UserView
                    profileSeq={dataStories.profileSeq}
                    storySeq={stories[currentIndex] != undefined ? stories[currentIndex].storySeq : -1}
                    displayName={dataStories.displayName}
                    userHandle={dataStories.userHandle}
                    profilePic={dataStories.profilePic}
                    isVerified={dataStories.isVerified}
                    onPauseChange={(val) => onPause(val)}
                    //datePublication={stories[currentIndex].created}
                    onClosePress={props.onClose}
                    onStoryClosePress={props.onStoryClosePress}
                />
                <View style={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    alignItems: 'center',
                    backgroundColor: '#000',
                    opacity: 0.8,
                    width: '100%',

                }}>
                    <View style={{
                        fontSize: 14,
                        color: '#FFF',
                        padding: 10,
                    }}>
                        {stories[currentIndex] != undefined ?
                            <DescriptionCaptionStyle
                                validHandleList={stories[currentIndex].story_comment_tags}
                                mainText={decodeHtmlEntitessData(stories[currentIndex].caption)}
                                mainTextStyle={styles.captionTxt}
                                highlightStyle={styles.captionTagTxt}
                                extraFunction={props.onClose}
                                descType="STORY"
                            />

                            : <Text></Text>}

                        {/* <RenderHTML
                                contentWidth={400}
                                source={{ html: `<div style="font-size:14px;color:#FFFFFF;line-height: 21px;letter-spacing: -0.165px;">
                                ${stories[currentIndex] != undefined ? decodeHtmlEntitessData(stories[currentIndex].caption) : ""}</div> ` }}
                            /> */}
                    </View>
                    {/* <Text style={{
                            fontSize: 14,
                            color: '#FFF',
                            padding: 10,
                        }}>{stories[currentIndex] != undefined ? stories[currentIndex].caption : ""}</Text> */}
                </View>
                <ProgressArray
                    next={nextStory}
                    isLoaded={isLoaded}
                    duration={duration}
                    pause={isPause}
                    //isNewStory={props.isNewStory}
                    stories={stories}
                    currentIndex={currentIndex}
                    currentStory={stories[currentIndex]}
                    length={stories.map((_, i) => i)}
                    progress={{ id: currentIndex }}
                />
            </View>
        </TouchableOpacity>
        // </GestureRecognizer>
    )
}

export default Stories

const styles = StyleSheet.create({
    arrowIcon: {
        height: 24,
        width: 24,
        marginEnd: 10,
        opacity: 0.6
    },
    container: {
        flex: 1,
        width: "100%",
        justifyContent: "flex-start",
        alignItems: "center",
        // paddingTop: 30,
    },
    progressBarArray: {
        flexDirection: "row",
        position: "absolute",
        top: 10,
        width: "98%",
        height: 10,
        justifyContent: "space-between",
        alignItems: "center",
    },
    userView: {
        flexDirection: "row",
        position: "absolute",
        top: 55,
        width: "98%",
        alignItems: "center",
    },
    name: {
        fontSize: 18,
        fontWeight: "500",
        marginLeft: 12,
        color: "white",
    },
    time: {
        fontSize: 12,
        fontWeight: "400",
        marginTop: 3,
        marginLeft: 12,
        color: "white",
    },
    content: { width: "100%", height: "100%" },
    loading: {
        backgroundColor: "black",
        height: "100%",
        width: "100%",
        alignItems: "center",
        justifyContent: "center",
    },
    modal: {
        width: "100%",
        height: "90%",
        backgroundColor: "white",
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    bar: {
        width: 50,
        height: 8,
        backgroundColor: "gray",
        alignSelf: "center",
        borderRadius: 4,
        marginTop: 8,
    },
    captionTxt: {
        color: '#FFFFFF',
        fontSize: 14,
        paddingVertical: 4,
    },
    captionTagTxt: {
        color: '#FFFFFF',
        backgroundColor: Colors.primaryColor,
        borderRadius: 14,
        paddingHorizontal: 6,
        paddingVertical: 4,
        marginHorizontal: 2,
    }
})
