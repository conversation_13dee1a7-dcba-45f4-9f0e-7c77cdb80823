import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import EntutoTextView from '../common/EntutoTextView';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import BottomSheetLoader from '../common/BottomSheetLoader';
import ErrorMessages from '../../constants/ErrorMessages';
import {Dropdown} from 'react-native-element-dropdown';
import EntutoEditText from '../common/EntutoEditText';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import {checkValueLength} from '../../utils/Utils';
import {PopupNegativeButton, PopupPositiveButton} from '../common/PopupButton';

// Mock ServerConnector for simulating API calls
const MockServerConnector = {
  submitOTP: (data, successCallback, errorCallback) => {
    // Simulate network delay
    setTimeout(() => {
      // For demo purposes: OTP "123456" is considered valid, anything else fails
      console.log('hi');
      if (data.otp) {
        successCallback({
          status: 'SUCCESS',
          msg: 'Email validated successfully!',
        });
      } else {
        errorCallback('E001', 'Invalid OTP. Please try again.', null);
      }
    }, 1500);
  },

  resendOTP: (data, successCallback, errorCallback) => {
    // Simulate network delay
    setTimeout(() => {
      // 90% chance of success for resending OTP
      if (Math.random() < 0.9) {
        successCallback({
          status: 'SUCCESS',
          msg: 'OTP has been sent to your email.',
        });
      } else {
        // Sometimes show a successful error (special case in original code)
        if (Math.random() < 0.5) {
          errorCallback('UE103', 'OTP has been sent to your email.', null);
        } else {
          errorCallback(
            'E002',
            'Failed to send OTP. Please try again later.',
            null,
          );
        }
      }
    }, 1500);
  },
};

const EmailValidateComponent = ({
  refVal,
  navigation,
  emailID,
  mobileNo,
  ...props
}) => {
  const {defaultStyle} = useDefaultStyle();
  const theme = useSTheme();

  const [otpVal, setOtpVal] = useState('');
  const [otpVaErr, setOtpValErr] = useState('');

  const [showLoading, setShowLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [errorMsgType, setErrorMsgType] = useState('FAILED');
  const [refreshKey, setRefreshKey] = useState(Math.random());

  const [isSubmitDisable, setisSubmitDisable] = useState(true);

  const [minutes, setMinutes] = useState(1);
  const [seconds, setSeconds] = useState(30);
  const [disableResendOtpBtn, setDisableResendOtpBtn] = useState(true);

  useEffect(() => {
    let myInterval = setInterval(() => {
      if (seconds > 0) {
        setSeconds(seconds - 1);
      }
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(myInterval);
        } else {
          setMinutes(minutes - 1);
          setSeconds(59);
        }
      }
      if (minutes == 0 && seconds == 0) {
        setDisableResendOtpBtn(false);
      }
    }, 1000);
    return () => {
      clearInterval(myInterval);
    };
  }, [seconds, minutes]);

  const cancelBtnPress = () => {
    props.emailValidatePopupClick('negative', {});
  };

  const confirmDeleteBtnPress = () => {
    setErrorMsg('');
    setErrorMsgType('FAILED');
    setRefreshKey(Math.random());
    let isFormValid = true;
    if (otpVal.length === 0) {
      setOtpValErr(ErrorMessages.emailOTPRequired || 'OTP is required');
      isFormValid = false;
    }
    if (isFormValid) {
      setShowLoading(true);
      submitOTPService();
    }
  };

  function submitOTPService() {
    const requestData = {
      mobile: mobileNo,
      email: emailID,
      otp: otpVal,
    };

    MockServerConnector.submitOTP(
      requestData,
      data => {
        // success method
        setShowLoading(false);
        props.emailValidatePopupClick('success', {msg: data.msg});
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        setErrorMsg(errorMessage);
        setErrorMsgType('FAILED');
        setRefreshKey(Math.random());
      },
    );
  }

  const otpChangeHandler = text => {
    setOtpVal(text);
    if (checkValueLength(text)) {
      setisSubmitDisable(false);
    } else {
      setisSubmitDisable(true);
    }
    setOtpValErr('');
  };

  const resendOtpBtnPress = () => {
    setShowLoading(true);
    generateOtpEmailService();
  };

  const generateOtpEmailService = () => {
    const requestData = {
      mobile: mobileNo,
      email: emailID,
      type: 'EMAIL',
    };

    MockServerConnector.resendOTP(
      requestData,
      data => {
        // success method
        setShowLoading(false);
        setDisableResendOtpBtn(true);
        setSeconds(30);
        setMinutes(1);

        // Show success message
        setErrorMsg('OTP has been sent to your email');
        setErrorMsgType('SUCCESS');
        setRefreshKey(Math.random());
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);

        if (errorCode == 'UE103') {
          setErrorMsg(errorMessage);
          setErrorMsgType('SUCCESS');
          setRefreshKey(Math.random());
          setDisableResendOtpBtn(true);
          setSeconds(30);
          setMinutes(1);
        } else {
          setErrorMsg(errorMessage);
          setErrorMsgType('FAILED');
          setRefreshKey(Math.random());
        }
      },
    );
  };

  return (
    <View>
      <View style={defaultStyle.popupBox}>
        <EntutoTextView style={defaultStyle.popupHeadTxt}>
          Email Validate
        </EntutoTextView>

        {showLoading ? <BottomSheetLoader /> : null}

        {errorMsg.length != 0 ? (
          <SuccessFailureMsgBox
            alertMsg={errorMsg}
            alertKey={refreshKey}
            alertType={errorMsgType}
          />
        ) : null}
        <View style={{marginTop: 20}}>
          <EntutoEditText
            labelTxt="Enter OTP"
            placeholderTxt="OTP"
            value={otpVal}
            onChangeText={text => otpChangeHandler(text)}
            showErrorField={otpVaErr.length}
            errorMsg={otpVaErr}
          />

          <View style={{flexDirection: 'row', marginTop: 8}}>
            <EntutoTextView>Resend OTP in</EntutoTextView>

            <EntutoTextView
              style={{color: theme.colors.primaryColor, marginStart: 4}}>
              {minutes}:{seconds < 10 ? `0${seconds}` : seconds}
            </EntutoTextView>

            <TouchableOpacity
              disabled={disableResendOtpBtn}
              onPress={() => resendOtpBtnPress()}>
              <EntutoTextView
                style={{
                  color: theme.colors.primaryColor,
                  marginStart: 8,
                  opacity: disableResendOtpBtn ? 0.3 : 1,
                }}>
                Resend OTP
              </EntutoTextView>
            </TouchableOpacity>
          </View>
        </View>
        <View
          style={{
            flexDirection: 'row',
            flex: 1,
            marginTop: 16,
            marginBottom: 16,
          }}>
          <View style={{flex: 1}}>
            <PopupNegativeButton
              onPress={() => cancelBtnPress()}
              btnText="Cancel"
              style={{marginEnd: theme.dimensions?.popupBtnGap || 8}}
            />
          </View>
          <View style={{flex: 1}}>
            <PopupPositiveButton
              disabled={isSubmitDisable}
              onPress={() => confirmDeleteBtnPress()}
              btnText={'Validate'}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default EmailValidateComponent;

const styles = StyleSheet.create({});
