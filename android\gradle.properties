# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
# React Native dependency settings
reactNativeVersion=0.73.6
# Version of flipper SDK to use with React Native
FLIPPER_VERSION=0.99.0
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
org.gradle.jvmargs=-Xmx4096m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.configureondemand=true
MYAPP_UPLOAD_STORE_FILE=sotrue.keystore
MYAPP_UPLOAD_KEY_ALIAS=sotrue
MYAPP_UPLOAD_STORE_PASSWORD=June302022#90
MYAPP_UPLOAD_KEY_PASSWORD=June302022#90
hermesEnabled=true
# android.disableAutomaticComponentCreation=true
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64
# newArchEnabled=true
org.gradle.java.home=C:/Users/<USER>/.gradle/jdks/eclipse_adoptium-17-amd64-windows.2
newArchEnabled=false
