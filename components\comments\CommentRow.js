import React, { useState, useEffect, useContext } from 'react'
import { Image, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import Animated from 'react-native-reanimated';
import ErrorMessages from '../../constants/ErrorMessages';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import ServerConnector from '../../utils/ServerConnector';
import { decodeHtmlEntitessData, hasImageUrlExist } from '../../utils/Utils';
import ConfirmationPopup from '../common/ConfirmationPopup';
import EntutoTextView from '../common/EntutoTextView';
import LikeBtnComponent from '../common/LikeBtnComponent';
import ProgressiveImage from '../common/ProgressiveImage';
import SubheadingBodyTxt from '../common/SubheadingBodyTxt';
import SubheadingTxt from '../common/SubheadingTxt';
import SubCommentRow from './SubCommentRow';
import { AppStateContext } from '../..';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';

const CommentRow = ({ selfComment, selfLike, postProfileSeq, currentProfileSeq, data, postSeq, navigation, commentRowClick }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [likeComment, setlikeComment] = useState(selfLike);
    const { fullUserDetails } = useContext(AppStateContext);
    const [showConfirmPopup, setshowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());

    const [confirmTitle, setconfirmTitle] = useState("Confirmation");
    const [confirmMsg, setconfirmMsg] = useState("Confirmation");
    const [warringsData, setwarringsData] = useState({});
    const [postCommentTxt, setpostCommentTxt] = useState("");
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;

    const goToProfile = (profileSeq) => {
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }

    useEffect(() => {
        if (data.length != 0) {
            let decodeTxt = decodeHtmlEntitessData(data.comment)
            setpostCommentTxt(decodeTxt)
        }
    }, [data])


    const likeBtnPress = () => {
        if (likeComment) {
            removeCommentLike();
        }
        else {
            submitCommentLike();
        }
    }
    function submitCommentLike() {
        let hashMap = {
            _action_code: "11:SUBMIT_COMMENT_LIKE",
            comment_seq: data.comment_seq,
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setlikeComment(true);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }
    function removeCommentLike() {
        let hashMap = {
            _action_code: "11:REMOVE_COMMENT_LIKE",
            comment_seq: data.comment_seq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setlikeComment(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }
    const replyBtnPress = () => {
        commentRowClick("REPLY", { commentSeq: data.comment_seq, commentUserName: data.full_name })
    }
    const deleteBtnPress = () => {
        commentRowClick("DELETE", { commentSeq: data.comment_seq })
    }
    const SubCommentRowPress = (clickID, obj) => {
        if (clickID == "DELETE_U") {
            commentRowClick("DELETE", { commentSeq: obj.commentSeq })
        }

    }
    const deleteOtherCmntBtnPress = () => {
        setconfirmMsg(ErrorMessages.delCmntConfirmMsg);
        setshowConfirmPopup(true);
        setshowConfirmPopupKey(Math.random());
        setwarringsData({ clickType: "DELETE_OTHER_CMNT" });
    }
    const confirmPopupPress = (clickId, obj) => {
        if (clickId == "positive") {
            if (obj.clickType == "DELETE_OTHER_CMNT") {
                commentRowClick("DELETE_OTHERS", { commentSeq: data.comment_seq })
            }
        }
    }
    if (selfComment) {
        return (
            <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginStart: 'auto' }}>
                    <TouchableOpacity onPress={() => goToProfile(data.comment_profile_seq)}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', marginEnd: 10 }}>
                            <EntutoTextView style={style.commentProfileName}>{data.full_name}</EntutoTextView>
                            {
                                data.is_verified == "YES" ?
                                    <Image
                                        style={style.verifiedIcon}
                                        source={require('../../assets/Images/icon/verifiedicon.png')}
                                        resizeMode={'cover'}
                                    />
                                    : null
                            }
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => goToProfile(data.comment_profile_seq)}>
                        <View style={style.profileImageBox}>

                            <ProgressiveImage
                                style={style.profileImage}
                                source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                                defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                                resizeMode={'cover'}
                            />

                        </View>
                    </TouchableOpacity>

                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginStart: 'auto', }}>
                    <EntutoTextView style={style.commentText}>{postCommentTxt}</EntutoTextView>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center', marginStart: 'auto', marginTop: 8 }}>
                    {/* <View style={{ marginEnd: 15 }}>
                        <LikeBtnComponent likeButtonPress={likeBtnPress} isLike={likeComment} />
                    </View> */}
                    {
                        postProfileSeq == currentProfileSeq ?
                            <TouchableOpacity onPress={() => replyBtnPress()} >
                                <EntutoTextView style={style.commentReBtn}>Reply</EntutoTextView>
                            </TouchableOpacity>
                            : null
                    }
                    {
                        selfComment ?
                            <TouchableOpacity onPress={() => deleteBtnPress()} >
                                <EntutoTextView style={style.commentReBtn}>Delete</EntutoTextView>
                            </TouchableOpacity>
                            :
                            <>
                                {
                                    postProfileSeq == currentProfileSeq ?
                                        <TouchableOpacity onPress={() => deleteOtherCmntBtnPress()} >
                                            <EntutoTextView style={style.commentReBtn}>Delete</EntutoTextView>
                                        </TouchableOpacity>
                                        : null
                                }
                            </>

                    }


                </View>
                <View style={style.profileNameBox}>
                    <View style={{ flex: 1, flexDirection: 'row', marginBottom: 6, flexWrap: 'wrap', }}>


                        {/* <View style={{ marginLeft: 'auto', }}>
                            <SubheadingBodyTxt>{data.comment_time}</SubheadingBodyTxt>
                        </View> */}
                    </View>
                    {
                        data.sub_comment_list.map((obj, i) => {
                            return <SubCommentRow subdata={obj}
                                selfComment={selfComment}
                                postSeq={postSeq} key={i}
                                navigation={navigation}
                                SubCommentRowPress={SubCommentRowPress} />
                        })
                    }
                </View>
                {
                    showConfirmPopup &&
                    <ConfirmationPopup
                        visiblePopupKey={showConfirmPopupKey}
                        visiblePopup={showConfirmPopup}
                        title={confirmTitle}
                        messagebody={confirmMsg}
                        positiveButton="Yes"
                        negativeButton="No"
                        data={warringsData}
                        popupClick={(clickID, data) => { confirmPopupPress(clickID, data) }}
                    />
                }

            </View>
        )
    }
    else {
        return (
            <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <TouchableOpacity onPress={() => goToProfile(data.comment_profile_seq)}>
                        <View style={style.profileImageBox}>

                            <ProgressiveImage
                                style={style.profileImage}
                                source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                                defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                                resizeMode={'cover'}
                            />

                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => goToProfile(data.comment_profile_seq)}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', marginStart: 10 }}>
                            <EntutoTextView style={style.commentProfileName}>{data.full_name}</EntutoTextView>
                            {
                                data.is_verified == "YES" ?
                                    <Image
                                        style={style.verifiedIcon}
                                        source={require('../../assets/Images/icon/verifiedicon.png')}
                                        resizeMode={'cover'}
                                    />
                                    : null
                            }
                        </View>
                    </TouchableOpacity>
                </View>
                <View>
                    <EntutoTextView style={{ ...style.commentText, textAlign: 'left' }}>{postCommentTxt}</EntutoTextView>
                </View>
                <View style={{ flexDirection: 'row', marginTop: 15 }}>

                    {
                        postProfileSeq == currentProfileSeq ?
                            <TouchableOpacity onPress={() => replyBtnPress()} style={{ marginEnd: 10, }} >
                                <EntutoTextView style={style.commentReBtn}>Reply</EntutoTextView>
                            </TouchableOpacity>
                            : null
                    }
                    {
                        selfComment ?
                            <TouchableOpacity onPress={() => deleteBtnPress()} style={{ marginEnd: 10, }} >
                                <EntutoTextView style={style.commentReBtn}>Delete</EntutoTextView>
                            </TouchableOpacity>
                            :
                            <>
                                {
                                    postProfileSeq == currentProfileSeq ?
                                        <TouchableOpacity onPress={() => deleteOtherCmntBtnPress()} style={{ marginEnd: 10, }} >
                                            <EntutoTextView style={style.commentReBtn}>Delete</EntutoTextView>
                                        </TouchableOpacity>
                                        : null
                                }
                            </>

                    }
                    {/* <View >
                        <LikeBtnComponent likeButtonPress={likeBtnPress} isLike={likeComment} />
                    </View> */}
                </View>
                <View style={style.profileNameBox}>
                    <View style={{ flex: 1, flexDirection: 'row', marginBottom: 6, flexWrap: 'wrap', }}>


                        {/* <View style={{ marginLeft: 'auto', }}>
                            <SubheadingBodyTxt>{data.comment_time}</SubheadingBodyTxt>
                        </View> */}
                    </View>
                    {
                        data.sub_comment_list.map((obj, i) => {
                            return <SubCommentRow subdata={obj}
                                postSeq={postSeq} key={i}
                                selfComment={selfComment}
                                navigation={navigation}
                                SubCommentRowPress={SubCommentRowPress} />
                        })
                    }
                </View>

                {
                    showConfirmPopup &&
                    <ConfirmationPopup
                        visiblePopupKey={showConfirmPopupKey}
                        visiblePopup={showConfirmPopup}
                        title={confirmTitle}
                        messagebody={confirmMsg}
                        positiveButton="Yes"
                        negativeButton="No"
                        data={warringsData}
                        popupClick={(clickID, data) => { confirmPopupPress(clickID, data) }}
                    />
                }

            </View>
        )
    }
}

export default CommentRow;

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'column',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: 31,
        width: 31,
        borderRadius: 31,
    },
    profileNameBox: {
        flex: 1,
        flexDirection: 'column',
        //marginHorizontal: 10,
        marginLeft: 10,
        marginRight: 8,

    },
    verifiedIcon: {
        width: 10,
        height: 9,
        marginLeft: theme.dimensions.veritextLeftmargin,
        // position: 'absolute',
        // right: -4,
        // top: 15,
    },
    likebtn: {
        paddingHorizontal: 3,
        paddingVertical: 4,
    },
    likeIcon: {
        width: 20,
        height: 17,
    },
    commentReBtn: {
        color: theme.colors.primaryColor,
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
        marginRight: 10,


    },
    commentText: {
        textAlign: 'right',
        fontSize: theme.calculateFontSize(theme.dimensions.commentText),
        marginTop: 10,
        marginEnd: 15,
    },
    commentProfileName: {
        color: theme.colors.primaryColor,
        textAlign: 'right',
        fontSize: theme.calculateFontSize(theme.dimensions.commentProfileText),
    }

})
