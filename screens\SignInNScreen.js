import { ScrollView, StyleSheet, Text, Image, View, TouchableOpacity } from 'react-native'
import React, { useContext, useState } from 'react'
import CustomStatusBar from '../components/common/CustomStatusBar'
import EntutoTextView from '../components/common/EntutoTextView';
import EntutoEditText from '../components/common/EntutoEditText';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import { AppStateContext } from '..';
import ErrorMessages from '../constants/ErrorMessages';
import ServerConnector from '../utils/ServerConnector';
import { MAX_TIMES_FOR_RESET } from '../utils/Appconfig';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import ConfirmationPopup from '../components/common/ConfirmationPopup';
import Colors from '../constants/Colors';
import { CommonActions } from '@react-navigation/native';
import appData from '../data/Data';
import useDefaultStyle from '../theme/useDefaultStyle';
import OldLoginBackComponent from '../components/Login/OldLoginBackComponent';
import useSTheme from '../theme/useSTheme';

const SignInNScreen = ({ route, navigation }) => {
    const [userName, setUserName] = useState("");
    const [userNameErr, setUserNameErr] = useState("");
    const [password, setPassword] = useState("");
    const [passwordErr, setPasswordErr] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const { changeUserDetails, } = useContext(AppStateContext);
    const [errorMsg, setErrorMsg] = useState("");
    const [refreshKey, setRefreshKey] = useState(Math.random());
    const [wrongPasswordCount, setWrongPasswordCount] = useState(0);

    const theme = useSTheme();

    const [showConfirmPopup, setShowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setShowConfirmPopupKey] = useState(Math.random());
    const { defaultStyle } = useDefaultStyle();

    const loginBtnClick = () => {
        var isValid = true;
        setUserNameErr("");
        setPasswordErr("");
        if (userName.length === 0) {
            setUserNameErr(ErrorMessages.loginUserNameErr);
            isValid = false;
        }
        if (password.length === 0) {
            setPasswordErr(ErrorMessages.loginUserPasswordErr);
            isValid = false;
        }
        if (isValid) {
            setShowLoading(true);
            appLoginServiceCall();
        }
    }
    function appLoginServiceCall() {
        let hashMap = {
            _action_code: "11:APP_LOGIN",
            user_id: userName,
            password: password
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let userDetails = {
                _username: data.data.uid,
                _password: data.data.pwd,
                _profile_seq: data.data.profile_seq,
                _user_seq: data.data.user_seq,
                _user_handle: data.data.user_handle,
                _user_account_type: data.data.account_type,
                _user_display_name: "",
                _has_bank_details: "NO",
                _is_profile_verified: "NO",
                _is_gmail_login: "NO",
                _max_file_size: data.data.max_file_size,
            }
            changeUserDetails(userDetails);
            goToHomeScreen(data.data.user_seq)
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_id) {
                        setUserNameErr(data.data.user_id);
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        setPasswordErr(data.data.password);
                        fieldErrorShown = true;
                    }
                }
            }
            if (!fieldErrorShown) {
                if (errorCode === "UE006") {
                    if (wrongPasswordCount == MAX_TIMES_FOR_RESET) {
                        setShowConfirmPopup(true);
                        setShowConfirmPopupKey(Math.random());
                    }
                    if (wrongPasswordCount >= MAX_TIMES_FOR_RESET) {
                        setWrongPasswordCount(0);
                    }
                    else {
                        setWrongPasswordCount(prevState => prevState + 1);
                    }
                }
                setErrorMsg(errorMessage);
                setRefreshKey(Math.random())
            }
        });
    }
    const emailIdChangeHandler = (text) => {
        setUserName(text)
        setUserNameErr("");
    }
    const passwordChangeHandler = (text) => {
        setPassword(text);
        setPasswordErr("");
    }
    const confirmBtnPress = (clickID, data) => {
        if (clickID == "positive") {
            navigation.navigate('ForgotPasswordScreen', {
                navType: "RESET_PASS"
            })
        }
    }
    const goButtonPress = () => {
        loginBtnClick();
    }
    const signupBtnPress = () => {
        navigation.replace('SignupScreen')
    }
    const forgotPasswordClick = () => {
        navigation.navigate('ForgotPasswordScreen', {
            navType: "FORGOT_PASS"
        })
    }
    const backToHome = () => {
        navigation.goBack(null);
    }
    function goToHomeScreen() {
        if (appData._copyLinkPostSeq.length != 0) {
            let seqVal = appData._copyLinkPostSeq;
            appData._copyLinkPostSeq = "";
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'SinglePostScreen',
                            params: {
                                postSeq: seqVal,
                            },
                        },
                    ],
                })
            );
        }
        else if (appData._copyLinkProfileSeq.length != 0) {
            let seqVal = appData._copyLinkProfileSeq;
            appData._copyLinkProfileSeq = "";
            if (seqVal == currentProfileSeq) {
                navigation.dispatch(
                    CommonActions.reset({
                        index: 1,
                        routes: [
                            {
                                name: 'HomeScreen',
                                params: {
                                    screen: 'ProfileFeed',
                                },

                            },
                        ],
                    })
                );
            }
            else {
                navigation.dispatch(
                    CommonActions.reset({
                        index: 1,
                        routes: [
                            {
                                name: 'OthersProfileScreen',
                                params: {
                                    profileSeq: seqVal,
                                },
                            },
                        ],
                    })
                );

            }

        }
        else {
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'HomeScreen',
                        },
                    ],
                })
            );
        }

    }
    return (
        <>
            <CustomStatusBar translucent={true} hidden={false} />
            <View style={{ flex: 1, position: 'relative', backgroundColor: theme.colors.oldLoginBackground }}>
                <CustomProgressDialog
                    showLoading={showLoading}
                />
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View>
                        <View style={defaultStyle.signupTextBox}>
                            <OldLoginBackComponent navigation={navigation} />
                            <EntutoTextView style={defaultStyle.signupText}>Login using your</EntutoTextView>
                            <TouchableOpacity onPress={() => backToHome()}>
                                <EntutoTextView style={defaultStyle.signupText}>email ID</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                        <View style={defaultStyle.signUpFormBox}>
                            <EntutoEditText
                                mode={"flat"}
                                backgroundColor={{ backgroundColor: theme.colors.editTextBackgroundColor }}
                                activeUnderlineColor={true}
                                textColor={"#111111"}
                                showLeftIcon={false}
                                placeholderTxt="Email"
                                value={userName}
                                onChangeText={text => emailIdChangeHandler(text)}
                                showErrorField={userNameErr.length}
                                errorMsg={userNameErr}
                            />
                            <EntutoEditText
                                mode={"flat"}
                                backgroundColor={{ backgroundColor: theme.colors.editTextBackgroundColor }}
                                activeUnderlineColor={true}
                                textColor={"#111111"}
                                placeholderTxt="Password"
                                value={password}
                                onChangeText={text => passwordChangeHandler(text)}
                                showErrorField={passwordErr.length}
                                errorMsg={passwordErr}
                                secureEntryTxt={true}
                                showRightIcon={true}
                                returnKeyType="go"
                                onSubmitEditing={() => goButtonPress()}
                            />

                            <View style={{ justifyContent: 'flex-end', alignItems: 'flex-end' }}>
                                <TouchableOpacity onPress={(e) => forgotPasswordClick()}>
                                    <EntutoTextView style={styles.forgotHeadTxt}>Forgot Your Password?</EntutoTextView>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View style={defaultStyle.signUpBtnBox}>
                            <TouchableOpacity style={{ ...defaultStyle.signUpBtn, backgroundColor: '#111111' }}
                                onPress={() => loginBtnClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Login</EntutoTextView>
                            </TouchableOpacity>
                            <View style={{ flex: 1 }} />
                            {/* <TouchableOpacity style={defaultStyle.signUpBtn}
                                onPress={() => signupBtnPress()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Signup</EntutoTextView>
                            </TouchableOpacity> */}
                        </View>
                    </View>
                </ScrollView>
                {
                    errorMsg.length != 0 ?
                        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                        : null
                }
                {
                    showConfirmPopup &&
                    <ConfirmationPopup
                        visiblePopupKey={showConfirmPopupKey}
                        visiblePopup={showConfirmPopup}
                        title="Confirmation"
                        messagebody="Would you like to reset your password?"
                        positiveButton="Reset Password"
                        negativeButton="No"
                        data={{}}
                        popupClick={(clickID, data) => { confirmBtnPress(clickID, data) }}
                    />
                }
            </View>
        </>
    )
}

export default SignInNScreen;
const styles = StyleSheet.create({
    linearGrad: {
        position: 'absolute',
        width: '100%',
        height: '100%',
    },
    signupTextBox: {
        paddingStart: 24,
        paddingEnd: 24,
        marginTop: 60
    },
    signupText: {
        color: '#FFFFFF',
        fontSize: 34,
        marginBottom: 4
    },
    signUpFormBox: {
        minHeight: 200,
        backgroundColor: '#FFFFFF',
        borderTopEndRadius: 24,
        borderBottomEndRadius: 24,
        flex: 1,
        marginEnd: 44,
        marginTop: 40,
        paddingHorizontal: 24,
        paddingVertical: 15,
    },
    signUpBtnBox: {
        marginTop: 180,
        flexDirection: 'row',
    },
    signUpBtn: {
        minHeight: 58,
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
        borderTopEndRadius: 58,
        borderBottomEndRadius: 58,
    },
    signUpBtnText: {
        color: '#FFFFFF',
        fontSize: 16,
    },
    forgotHeadTxt: {
        marginTop: 18,
        marginBottom: 12,
        color: Colors.primaryColor,
        fontSize: 13,
        fontWeight: "600"
    },

})