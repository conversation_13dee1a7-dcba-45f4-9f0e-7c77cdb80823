import React, { useContext } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import ProgressiveImage from '../common/ProgressiveImage';
import SubheadingTxt from '../common/SubheadingTxt';
import SubheadingBodyTxt from '../common/SubheadingBodyTxt';
import EntutoTextView from '../common/EntutoTextView';
import { hasImageUrlExist } from '../../utils/Utils';
import { UserHandlePrefix } from '../../utils/Appconfig';
import { AppStateContext } from '../..';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';

const FollowingItemRow = ({ navigation, data, profile_seq, isFans }) => {
    const { fullUserDetails } = useContext(AppStateContext);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const goToProfile = (profileSeq) => {
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }
    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
            <View style={style.profileImageBox}>
                <ProgressiveImage
                    style={style.profileImage}
                    source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                    defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                    resizeMode={'cover'}
                />


            </View>
            <View style={style.profileNameBox}>
                <TouchableOpacity onPress={() => goToProfile(profile_seq)}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <SubheadingTxt>{data.display_name}</SubheadingTxt>
                        {
                            data.is_verified == "YES" ?
                                <Image
                                    style={style.verifiedIcon}
                                    source={require('../../assets/Images/icon/verifiedicon.png')}
                                    resizeMode={'contain'}
                                />
                                : null
                        }
                    </View>

                    <SubheadingBodyTxt>{UserHandlePrefix}{data.user_handle}</SubheadingBodyTxt>
                </TouchableOpacity>
            </View>
            <View style={{ marginLeft: 'auto' }}>
                {
                    !isFans ?
                        <TouchableOpacity
                            style={{ ...style.btn, backgroundColor: theme.colors.followedBoxBackground, marginEnd: 15 }}>
                            <EntutoTextView style={{ ...style.btnText, color: theme.colors.followedBoxText, }}>
                                Followed
                            </EntutoTextView>
                        </TouchableOpacity>
                        : null
                }

            </View>

        </View>
    )
}

export default FollowingItemRow

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: 50,
        width: 50,
        borderRadius: 50,
    },
    profileNameBox: {
        flexDirection: 'column',
        marginHorizontal: 10,
    },
    verifiedIcon: {
        width: 16,
        height: 15,
        marginLeft: theme.colors.veritextLeftmargin,
        // position: 'absolute',
        // right: -6,
        // top: 20,
    },
    btn: {
        borderRadius: 5,
    },
    btnText: {
        textAlign: 'center',
        fontSize: theme.calculateFontSize(theme.dimensions.rowBtnText),
        fontWeight: '700',
        paddingVertical: 5,
        paddingHorizontal: 16,
    },
})
