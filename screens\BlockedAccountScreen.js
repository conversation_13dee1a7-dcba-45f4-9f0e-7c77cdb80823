import React, { useEffect, useState } from 'react'
import { FlatList, RefreshControl, StyleSheet, Text, View } from 'react-native';
import BlockedAccountRow from '../components/blockaccount/BlockedAccountRow';
import CustomStatusBar from '../components/common/CustomStatusBar';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const BlockedAccountScreen = ({ route, navigation }) => {
    const [accountList, setaccountList] = useState([])
    const [progressLoading, setprogressLoading] = useState(true);
    const [errorMsg, seterrorMsg] = useState("");
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const rowOptionClick = (clickID, obj) => {
        if (clickID == "close") {
            setprogressLoading(true);
            getBlockAccountService();
        }
    }
    const renderItem = ({ item }) => {
        return (
            <BlockedAccountRow navigation={navigation} data={item} rowOptionClick={rowOptionClick} isRestrict={false} />
        );
    };
    useEffect(() => {
        setprogressLoading(true);
        getBlockAccountService();
    }, []);
    const handleRefresh = () => {
        setprogressLoading(true);
        getBlockAccountService();
    }
    function getBlockAccountService() {
        let hashMap = {
            _action_code: "11:GET_BLOCKED_ACCOUNTS",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            setaccountList([...[], ...data.data]);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setprogressLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setprogressLoading(false);
                setaccountList([]);
                seterrorMsg(errorMessage);
            }
        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Blocked Accounts"
                showBackBtn={true} navigation={navigation}
                showBorderBottom={false} />
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside}>
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        :
                        <>
                            {
                                progressLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <FlatList
                                        contentContainerStyle={{ paddingBottom: 20 }}
                                        data={accountList}
                                        renderItem={renderItem}
                                        keyExtractor={(item, index) => index.toString()}
                                        refreshControl={
                                            <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                                        }
                                    />
                            }
                        </>
                }
            </View>
        </>
    )
}

export default BlockedAccountScreen;

const styles = StyleSheet.create({})
