const appData = {
  appVersion: '1.0.0',
  loggedUserName: '',
  loggedPassword: '',
  loggedUserSeq: -1,
  loggedUserState: -1,
  __access_key: '',
  _userDetails: null,
  // _userNotificationGuide: "YES",  // or "NO"
  _userPersonalityType: '', // "MALE" or "FEMALE"
  _videoDurationLimit: 30,
  __isPageRefresh: 1,

  __HomePageRefresh: 1,
  __HomePageRefreshCheck: 1,

  _profilePageRefresh: false,
  _editProfileChangesRefresh: false,
  _profilePostPageRefresh: false,
  __paymentDataObj: null,
  __StoryPageRefreshCheck: 'NO',
  _profilePagePostRefresh: false,
  _homePagePostRefresh: 'NO',

  _selectedNotificationMenu: 0,

  _activeAppleAuthCode: '',
  _activeAppleAuthCodeTime: '',
  _copyLinkPostSeq: '',
  _copyLinkProfileSeq: '',

  videoViewExecuteTime: new Date(),

  buttonClickTime: new Date(),
  permissionTime: new Date(),
  isStoragePermission: false,
  commentPostSeq: '',
  commentPostCount: '',
  commentPostCountShow: 'NO',
  _defaultThemeColor: 'COLOR_1',
  _defaultAppTheme: 'DARK',
  isRedirectToAccountScreen: 'NO',

  backupAppThemeColor: {},
};

export default appData;
