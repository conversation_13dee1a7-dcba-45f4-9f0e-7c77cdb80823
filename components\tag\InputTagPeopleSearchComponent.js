import { FlatList, Image, Pressable, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import CustomStatusBar from '../common/CustomStatusBar'
import HeadingTxt from '../common/HeadingTxt'
import CustomActivityIndicator from '../common/CustomActivityIndicator'
import { ActivityIndicator } from 'react-native-paper'
import { getSecondsBetweenDates } from '../../utils/Utils'
import ServerConnector from '../../utils/ServerConnector'
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl'
import { TAGGED_SYMBOL, _RedirectionErrorList } from '../../utils/Appconfig'
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox'
import EntutoTextView from '../common/EntutoTextView'
import CustomSnackbar from '../common/CustomSnackbar'
import InputTagPeopleProfile from './InputTagPeopleProfile'
import ErrorMessages from '../../constants/ErrorMessages'
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSTheme from '../../theme/useSTheme'
import useSThemedStyles from '../../theme/useSThemedStyles'

const InputTagPeopleSearchComponent = ({ tagPeopleSearchPress, captionTxt = "", navigation, preSearchStr }) => {
    const [searchQuery, setSearchQuery] = useState("");
    const [isTyped, setisTyped] = useState(false);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [errorMsg, seterrorMsg] = useState("");
    const [errorMsgKey, seterrorMsgKey] = useState(Math.random());
    const [showLoading, setshowLoading] = useState(false);
    const [searchList, setsearchList] = useState([]);
    const [failedCount, setfailedCount] = useState(0);

    const RowsPerPage = 10;
    const [startRecord, setstartRecord] = useState(0);
    const [bottomLoading, setbottomLoading] = useState(false);
    const [isNoDataFound, setisNoDataFound] = useState(false);
    const [bottomReachTime, setbottomReachTime] = useState(new Date());

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [mainCaptionTxt, setmainCaptionTxt] = useState("");
    const modalFlashRef = useRef(null);
    useEffect(() => {
        setmainCaptionTxt(captionTxt);
    }, [captionTxt])

    useEffect(() => {
        if (preSearchStr.length != 0) {
            let st1 = preSearchStr;
            let formatSt = st1.substring(1);
            setSearchQuery(formatSt);
            if (formatSt.length != 0) {
                setshowLoading(true);
                getSearchResult(formatSt, 0, RowsPerPage);
            }
        }
    }, [preSearchStr]);

    useEffect(() => {
        delay = setTimeout(() => {
            if (searchQuery) {
                if (searchQuery.length >= 3) {
                    setshowLoading(true);
                    getSearchResult(searchQuery, 0, RowsPerPage);
                }
            }
        }, 500)
        return () => clearTimeout(delay)
    }, [searchQuery]);

    const onChangeSearch = query => {
        if (query.length != 0) {
            setisTyped(true);
        }
        else {
            setisTyped(false);
        }
        setSearchQuery(query);
    }
    const cancelBtnClick = () => {
        tagPeopleSearchPress("CANCEL", {})
    }
    const peopleRowPress = (dataObj) => {
        // tagPeopleSearchPress("SELECTED", dataObj)
        let tagList = getTagListFromWords(mainCaptionTxt);
        let selectedHandle = dataObj.user_handle;
        if (!tagList.includes(selectedHandle)) {
            setdisplaySnackbar(false);
            setrefreshSnackBar(Math.random());
            validateTagging(dataObj.profile_seq, dataObj);
        }
        else {
            setSnackbarMsg(ErrorMessages.duplicateHandleInsertErr);
            setdisplaySnackbar(true);
            setrefreshSnackBar(Math.random());
        }
    }
    const getTagListFromWords = (words) => {
        let newArr = words.replace(/\n/g, " ").split(" ")
        let tagList = [];
        newArr.map(word => {
            if (word.startsWith(TAGGED_SYMBOL)) {
                let tagValue = word.split(TAGGED_SYMBOL);
                if (tagValue.length > 1) {
                    if (tagValue[1].length != 0) {
                        tagList.push(tagValue[1]);
                    }
                }
            }
        });
        return tagList;
    }
    const renderSearchRow = ({ item }) => {
        return (
            <InputTagPeopleProfile data={item} isChecked={false} navigation={navigation}
                peopleRowPress={peopleRowPress} />
        );
    };
    function getSearchResult(query, stRecord, rowsPerPage) {
        setstartRecord(stRecord)
        let hashMap = {
            _action_code: "11:DO_SEARCH",
            search_str: query,
            _start_row: stRecord,
            _rows_page: rowsPerPage,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false);
            setbottomLoading(false);
            let list = [...searchList];
            if (parseInt(stRecord) == 0) {
                list = data.data;
            }
            else {
                list = list.concat(data.data);
            }

            setsearchList(list);
            seterrorMsg("");
            seterrorMsgKey(Math.random())
            setisNoDataFound(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                setbottomLoading(false);
                setisNoDataFound(true);
                if (parseInt(stRecord) == 0) {

                    setsearchList([]);
                    let errrMsg2 = "";
                    let failedCountV = failedCount + 1;
                    if (failedCount === 0) {
                        errrMsg2 = "Are you sure?";
                    }
                    else if (failedCount === 1) {
                        errrMsg2 = "We don’t have this.";
                    }
                    else {
                        failedCountV = 0;
                        errrMsg2 = "Ask again ";
                    }
                    setfailedCount(failedCountV);
                    seterrorMsg(errrMsg2);
                    seterrorMsgKey(Math.random());
                }
            }
        });
    }
    const handleEndRefresh = () => {
        if (!isNoDataFound) {
            let currentTime = new Date();
            let diffTime = getSecondsBetweenDates(bottomReachTime, currentTime);
            if (diffTime > 4) {
                let startRec = startRecord + RowsPerPage;
                setbottomLoading(true);
                getSearchResult(searchQuery, startRec, RowsPerPage);
                setbottomReachTime(new Date());
            }

        }
    }
    const clearSearchTxt = () => {
        setSearchQuery("");
        setisTyped(false);
        setsearchList([]);
    }
    function validateTagging(profileSeq, dataObj) {
        let hashMap = {
            _action_code: "11:VALIDATE_TAGGING",
            tag_profile_seq: profileSeq,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            tagPeopleSearchPress("SELECTED", dataObj)
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setSnackbarMsg(errorMessage);
                setdisplaySnackbar(true);
                setrefreshSnackBar(Math.random());
            }
        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />

            <View style={defaultStyle.container}>
                <View style={style.searchBarBox}>
                    <View
                        style={style.searchBar}>
                        <Image
                            source={require('../../assets/Images/icon/search_icon.png')}
                            style={style.searchIcon}
                        />
                        <TextInput
                            style={style.input}
                            placeholder="Search by name or username!"
                            placeholderTextColor={theme.colors.inputPlaceholderColor}
                            value={searchQuery}
                            autoCorrect={false}
                            autoFocus={true}
                            onChangeText={text => onChangeSearch(text)}
                            selectionColor={theme.colors.primaryColor}
                        />
                        {isTyped && (
                            <TouchableOpacity onPress={() => clearSearchTxt()}>
                                <Image
                                    source={require('../../assets/Images/icon/close_icon.png')}
                                    style={style.searchCrossIcon}
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                    <TouchableOpacity onPress={() => cancelBtnClick()}>
                        <HeadingTxt style={{ marginLeft: 13 }}>CANCEL</HeadingTxt>
                    </TouchableOpacity>
                </View>
            </View>
            {
                searchList.length != 0 ?
                    <View style={{ flexDirection: 'row', marginBottom: 10 }}>
                        <View
                            style={{
                                paddingHorizontal: 12,
                                borderBottomWidth: theme.dimensions.tabBorderBottomWidth,
                                borderBottomColor: theme.colors.tabActiveBootomBorderColor,
                            }} >
                            <EntutoTextView style={{ ...defaultStyle.tabBarLabelTxt, color: theme.colors.tabActiveColor }}>
                                Accounts
                            </EntutoTextView>
                        </View>
                    </View>
                    : null
            }

            <View>
                {
                    showLoading ?
                        <CustomActivityIndicator progress={showLoading} />
                        : null
                }
                {
                    errorMsg.length != 0 ?
                        <View style={{ ...defaultStyle.errorBoxOutside, marginTop: 10, minHeight: 30 }} >
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsgKey} />
                        </View>
                        : null
                }
            </View>

            <FlatList
                keyboardShouldPersistTaps={'handled'}
                contentContainerStyle={{ paddingBottom: 20, zIndex: 1000 }}
                data={searchList}
                renderItem={renderSearchRow}
                keyExtractor={(item, index) => `${index}`}
                onEndReached={handleEndRefresh}
                initialNumToRender={10}

            />
            {
                bottomLoading ?
                    <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 15 }}>
                        <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={'large'} />
                    </View>
                    : null
            }
            <CustomSnackbar snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar}
                refreshSnack={refreshSnackBar} showInsideFlashRef={true} insideFlashRef={modalFlashRef} />

        </>
    )
}

export default InputTagPeopleSearchComponent

const styles = theme => StyleSheet.create({
    searchBarBox: {
        alignItems: "center",
        flexDirection: "row",
        marginTop: 8,
    },
    searchBar: {
        flex: 1,
        flexDirection: "row",
        backgroundColor: theme.style.searchBarBackground,
        borderRadius: theme.dimensions.searchBarRadius,
        alignItems: "center",
    },
    searchIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        marginLeft: theme.dimensions.searchInputIconMH,
    },
    input: {
        fontSize: theme.calculateFontSize(theme.dimensions.searchTextInputSize),
        marginHorizontal: 8,
        flex: 1,
        color: theme.colors.inputTextColor,
        minHeight: 56,
    },
    searchCrossIcon: {
        height: 15,
        width: 15,
        marginRight: 8,
    },
    selFullTagBox: {
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 10,
    },
    selTagBox: {
        position: 'relative',
        width: 56,
        height: 56,
        borderRadius: 15,
        marginRight: 5,
        // overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center'
    },
    selTagBoxImage: {
        width: 52,
        height: 52,
        borderRadius: 15,
        borderColor: '#FFFFFF',
        borderWidth: 2,
    },
    crossIconBox: {
        position: 'absolute',
        bottom: 20,
        right: 6,
        backgroundColor: '#CCC',
        borderRadius: 20,
        padding: 6
    },
    crossIconBoxIcon: {
        height: 15,
        width: 15,
    },
    saveIconBox: {
        borderWidth: 1,
        flex: 1,
        paddingHorizontal: 10,
        justifyContent: 'center',
        // borderLeftWidth: 1,
        borderColor: theme.colors.borderBottomColor
    },
    saveIcon: {
        color: theme.colors.primaryColor
    }
})