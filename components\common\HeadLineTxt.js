import React, { useContext } from 'react'
import { StyleSheet, } from 'react-native'
import EntutoTextView from './EntutoTextView';
import useSTheme from '../../theme/useSTheme';

const HeadLineTxt = props => {
    const theme = useSTheme();
    return <EntutoTextView style={{
        ...styles.default,
        color: theme.colors.mainHeadingColor,
        fontSize: theme.calculateFontSize(theme.dimensions.headlineTxt),
        ...props.style
    }}>{props.children}</EntutoTextView>
}

export default HeadLineTxt;

const styles = StyleSheet.create({
    default: {
        fontWeight: "bold"
    }
})
