import React, { useContext, useEffect, useState } from 'react'
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, Modal, Alert, Platform, ImageBackground } from 'react-native'
import EntutoTextView from '../components/common/EntutoTextView';
import ServerConnector from '../utils/ServerConnector';
import { AppStateContext } from '..';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { WEB_CLIENT_ID } from '../utils/Appconfig';
import { _getFirstTimeUser, _setFirstTimeUser } from '../utils/AuthLogin';
import CustomStatusBar from '../components/common/CustomStatusBar';
import { AppleButton, appleAuth } from '@invertase/react-native-apple-authentication';
import ProgressiveImage from '../components/common/ProgressiveImage';
import { hasImageUrlExist } from '../utils/Utils';
import PlaceholderImage from '../assets/Images/full_user_image_place_holder.png';
import GoogleIcon from '../assets/Images/icon/google_icon.png'
import LoginWithButton from '../components/common/LoginWithButton';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { CommonActions } from '@react-navigation/native';
import appData from '../data/Data';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import useDefaultStyle from '../theme/useDefaultStyle';
import LoginProfileBackImage from '../assets/Images/backgroundOldLogin.png';
import ArrowIcon from '../assets/Images/icon/Arrow.png';
import OldLoginBackComponent from '../components/Login/OldLoginBackComponent';

const LoginOldFlowScreen = ({ route, navigation }) => {
    const [showLoading, setShowLoading] = useState(false);
    const { changeUserDetails, isFirstTimeUser, changeFirstTimeUser, changeAcceptTerms } = useContext(AppStateContext)
    const [errorMsg, setErrorMsg] = useState("");
    const [refreshKey, setRefreshKey] = useState(Math.random());
    const { ErrorMsg } = route.params;

    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const { defaultStyle } = useDefaultStyle();

    const [profileData, setProfileData] = useState({
        profile1: {},
        profile2: {},
        profile3: {},
        profile4: {},
        profile5: {},
    });
    const [showProfiles, setShowProfiles] = useState({
        profile1: false,
        profile2: false,
        profile3: false,
        profile4: false,
        profile5: false,
    });

    const [_profile_seq_val, set_profile_seq_val] = useState(-1);

    useEffect(() => {
        setErrorMsg(ErrorMsg);
        changeAcceptTerms(false);
        getLandingProfileService();
        GoogleSignin.configure({
            webClientId: WEB_CLIENT_ID,
            offlineAccess: true,
        });
        GoogleSignin.signOut();
        setShowLoading(false);
    }, []);
    useEffect(() => {
        if (appleAuth.isSupported) {
            // onCredentialRevoked returns a function that will remove the event listener. useEffect will call this function when the component unmounts
            return appleAuth.onCredentialRevoked(async () => {
                // console.log('If this function executes, User Credentials have been Revoked');
            });
        }
    }, []);
    useEffect(() => {
        // if (isFirstTimeUser) {
        //     changeFirstTimeUser(false);
        //     navigation.navigate('AppIntroSliderScreen');
        // }
    }, []);

    function getLandingProfileService() {
        let hashMap = {
            _action_code: "11:GET_LANDING_PROFILES",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let dataP1 = {};
            let dataP2 = {};
            let dataP3 = {};
            let dataP4 = {};
            let dataP5 = {};
            let showP1 = false;
            let showP2 = false;
            let showP3 = false;
            let showP4 = false;
            let showP5 = false;


            data.data.map((obj, i) => {
                if (i == 0) {
                    showP1 = true;
                    dataP1 = obj;
                }
                if (i == 1) {
                    showP2 = true;
                    dataP2 = obj;
                }
                if (i == 2) {
                    showP3 = true;
                    dataP3 = obj;
                }
                if (i == 3) {
                    showP4 = true;
                    dataP4 = obj;
                }
                if (i == 4) {
                    showP5 = true;
                    dataP5 = obj;
                }
            })
            setProfileData({
                profile1: dataP1,
                profile2: dataP2,
                profile3: dataP3,
                profile4: dataP4,
                profile5: dataP5,
            });
            setShowProfiles({
                profile1: showP1,
                profile2: showP2,
                profile3: showP3,
                profile4: showP4,
                profile5: showP5,
            })

        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);

        });
    }
    const googleLoginBtnPress = async () => {

        try {
            await GoogleSignin.hasPlayServices();
            const userInfo = await GoogleSignin.signIn();
            if (userInfo.hasOwnProperty("idToken")) {
                setShowLoading(true);
                let userEmail = userInfo.user.email;
                appGmailLoginServiceCall(userEmail, userInfo)
            }

        } catch (error) {
            if (error.code === statusCodes.SIGN_IN_CANCELLED) {
                // user cancelled the login flow
                // setErrorMsg("user cancelled the login flow");
                // setRefreshKey(Math.random())
            } else if (error.code === statusCodes.IN_PROGRESS) {
                // operation (e.g. sign in) is in progress already
                setErrorMsg("operation (e.g. sign in) is in progress already");
                setRefreshKey(Math.random())
            } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
                // play services not available or outdated
                setErrorMsg("play services not available or outdated");
                setRefreshKey(Math.random())
            } else {
                // some other error happened
                // console.log("error", error)
                // setErrorMsg(error);
                // setRefreshKey(Math.random())
            }
        }
    }
    function appGmailLoginServiceCall(gmailId, response) {

        let hashMap = {
            _action_code: "11:GMAIL_LOGIN",
            user_id: gmailId,
            password: response.idToken,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let userDeatails = {
                _username: data.data.uid,
                _password: data.data.pwd,
                _profile_seq: data.data.profile_seq,
                _user_seq: data.data.user_seq,
                _user_handle: data.data.user_handle,
                _user_account_type: data.data.account_type,
                _user_display_name: "",
                _has_bank_details: "NO",
                _is_profile_verified: "NO",
                _is_gmail_login: "YES",
                _max_file_size: data.data.max_file_size,
            }
            changeUserDetails(userDeatails);
            goToHomeScreen(data.data.profile_seq);
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            GoogleSignin.signOut();
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_id) {
                        setErrorMsg(data.data.user_id);
                        setRefreshKey(Math.random())
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        setErrorMsg(data.data.password);
                        setRefreshKey(Math.random())

                        fieldErrorShown = true;
                    }
                }
            }
            if (!fieldErrorShown) {
                setErrorMsg(errorMessage);
                setRefreshKey(Math.random())
                // if (errorCode === "UE006") {
                //     navigation.navigate("SignupGmailScreen", {
                //         userInfo: response,
                //     })
                // }
                // else {
                //     setErrorMsg(errorMessage);
                //     setRefreshKey(Math.random())
                // }
            }
        });
    }
    async function onAppleButtonPress() {
        if (appleAuth.isSupported) {
            try {
                const appleAuthRequestResponse = await appleAuth.performRequest({
                    requestedOperation: appleAuth.Operation.LOGIN,
                    // Note: it appears pu`tting FULL_NAME f`irst is important, see issue #293
                    requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
                });

                // console.log(appleAuthRequestResponse);

                if (!appleAuthRequestResponse.authorizationCode) {
                    setErrorMsg('Apple Sign-In failed - no authorization code returned');
                    setRefreshKey(Math.random())
                }
                const { authorizationCode } = appleAuthRequestResponse;
                appAppleLoginServiceCall(authorizationCode);

            } catch (error) {
                // console.log("Type",error.message)
                if (error.message != "The operation couldn’t be completed. (com.apple.AuthenticationServices.AuthorizationError error 1001.)") {
                    setErrorMsg(error.message);
                    setRefreshKey(Math.random())
                }
                // setErrorMsg(error.message);
                // setRefreshKey(Math.random())
            }
        }
        else {
            setErrorMsg("Logging in with Apple is not supported.");
            setRefreshKey(Math.random())
        }

    }
    function appAppleLoginServiceCall(authorizationCode) {

        let hashMap = {
            _action_code: "11:APPLE_LOGIN",
            password: authorizationCode,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let userDeatails = {
                _username: data.data.uid,
                _password: data.data.pwd,
                _profile_seq: data.data.profile_seq,
                _user_seq: data.data.user_seq,
                _user_handle: data.data.user_handle,
                _user_account_type: data.data.account_type,
                _user_display_name: "",
                _has_bank_details: "NO",
                _is_profile_verified: "NO",
                _is_gmail_login: "YES",
                _max_file_size: data.data.max_file_size,
            }
            changeUserDetails(userDeatails);
            goToHomeScreen(data.data.profile_seq);
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_id) {
                        setErrorMsg(data.data.user_id);
                        setRefreshKey(Math.random())
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        setErrorMsg(data.data.password);
                        setRefreshKey(Math.random())

                        fieldErrorShown = true;
                    }
                }

            }
            if (!fieldErrorShown) {
                setErrorMsg(errorMessage);
                setRefreshKey(Math.random())
                // if (errorCode === "UE006") {
                //     navigation.navigate("SignupAppleScreen", {
                //         userInfo: {
                //             user_email: "",
                //             password_val: authorizationCode,
                //             full_name: "",
                //         },
                //     })
                // }
                // else {
                //     setErrorMsg(errorMessage);
                //     setRefreshKey(Math.random())
                // }
            }
        });
    }
    const loginBtnPress = () => {
        navigation.navigate('SignInNScreen');
    }
    function goToHomeScreen(currentProfileSeq) {
        if (appData._copyLinkPostSeq.length != 0) {
            let seqVal = appData._copyLinkPostSeq;
            appData._copyLinkPostSeq = "";
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'SinglePostScreen',
                            params: {
                                postSeq: seqVal,
                            },
                        },
                    ],
                })
            );
        }
        else if (appData._copyLinkProfileSeq.length != 0) {
            let seqVal = appData._copyLinkProfileSeq;
            appData._copyLinkProfileSeq = "";
            if (seqVal == currentProfileSeq) {
                navigation.dispatch(
                    CommonActions.reset({
                        index: 1,
                        routes: [
                            {
                                name: 'HomeScreen',
                                params: {
                                    screen: 'ProfileFeed',
                                },

                            },
                        ],
                    })
                );
            }
            else {
                navigation.dispatch(
                    CommonActions.reset({
                        index: 1,
                        routes: [
                            {
                                name: 'OthersProfileScreen',
                                params: {
                                    profileSeq: seqVal,
                                },
                            },
                        ],
                    })
                );
            }
        }
        else {
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'HomeScreen',
                        },
                    ],
                })
            );
        }

    }

    return (
        <>
            <CustomStatusBar translucent={true} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <View style={{ flex: 1, position: 'relative', backgroundColor: theme.colors.oldLoginBackground }}>
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View style={{ ...defaultStyle.container, marginBottom: 40, }}>
                        <OldLoginBackComponent navigation={navigation} style={{ marginTop: 48 }} />
                        <View style={style.cardBox}>

                            <ImageBackground source={LoginProfileBackImage} style={style.cardBoxBackgroundImage}
                                borderRadius={39} />
                            <View style={style.cardTextBox}>
                                <EntutoTextView style={style.cardBoxText}>Experience Exclusive Content</EntutoTextView>
                            </View>

                            {
                                showProfiles.profile1 ?
                                    <View style={style.imageBox1}>
                                        <CardProfileImage profileObj={profileData.profile1} />
                                    </View>
                                    : null
                            }
                            {
                                showProfiles.profile2 ?
                                    <View style={style.imageBox2}>
                                        <CardProfileImage profileObj={profileData.profile2} />
                                    </View>
                                    : null
                            }
                            {
                                showProfiles.profile3 ?
                                    <View style={style.imageBox3}>
                                        <CardProfileImage profileObj={profileData.profile3} />
                                    </View>
                                    : null
                            }
                            {
                                showProfiles.profile4 ?
                                    <View style={style.imageBox4}>
                                        <CardProfileImage profileObj={profileData.profile4} />
                                    </View>
                                    : null
                            }
                            {
                                showProfiles.profile5 ?
                                    <View style={style.imageBox5}>
                                        <CardProfileImage profileObj={profileData.profile5} />
                                    </View>
                                    : null
                            }
                        </View>
                        <View style={style.loginBox}>
                            <LoginWithButton btnIcon={GoogleIcon} btnText="Login with Google"
                                btnTextColor="#FFFFFF" backColor="#3AA0FF" iconBackColor="#FFFFFF"
                                onPress={() => googleLoginBtnPress()} />
                            {
                                Platform.OS == 'ios' ?
                                    <View>
                                        {
                                            appleAuth.isSupported ?
                                                <LoginWithButton btnIcon={null} btnText="Login with Apple"
                                                    btnTextColor="#2E3E5C" backColor="#FFFFFF"
                                                    iconBackColor="#000000"
                                                    showIcon={false}
                                                    showMatIcon={true}
                                                    matIcon={<MaterialCommunityIcons name='apple' color={'#FFFFFF'} size={24} />}
                                                    onPress={() => onAppleButtonPress()} />
                                                : null
                                        }
                                    </View>

                                    : null
                            }

                            <LoginWithButton
                                matIcon={<MaterialCommunityIcons name='email' color={'#FFFFFF'} size={24} />}
                                btnTextColor="#FFFFFF"
                                backColor="#111111"
                                showIcon={false}
                                showMatIcon={true}
                                btnText="Login with Email"
                                iconBackColor={theme.colors.transparentColor}
                                onPress={() => loginBtnPress()} />
                            {/* <View style={style.createAccTextBox}>
                                <View style={{ marginRight: 16 }}>
                                    <EntutoTextView style={{ ...style.createAccText, }}>Don’t have any account?</EntutoTextView>
                                </View>
                                <TouchableOpacity onPress={() => signupBtnClick()}>
                                    <View>
                                        <EntutoTextView style={style.createAccText}>Sign Up</EntutoTextView>
                                    </View>
                                </TouchableOpacity>
                            </View> */}
                        </View>
                    </View>


                </ScrollView>
            </View>

            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                    : null
            }
        </>
    )
}

export default LoginOldFlowScreen;

const CardProfileImage = ({ profileObj = {} }) => {
    const style = useSThemedStyles(styles);
    return (
        <View style={style.imageBox}>
            <ProgressiveImage
                style={style.profileImg}
                source={hasImageUrlExist(profileObj.profile_picture) ? { uri: profileObj.profile_picture } : null}
                defaultImageSource={PlaceholderImage}
            />
        </View>
    );
}
const styles = theme => StyleSheet.create({
    cardBox: {
        position: 'relative',
        marginHorizontal: 30,
        height: 400,
        backgroundColor: theme.colors.primaryColor,//#f1caba',
        shadowColor: "#f1caba",
        shadowOffset: {
            width: 0,
            height: 5,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,
        elevation: 2,
        borderRadius: 39,
        justifyContent: 'center',
        alignItems: 'center',

    },
    cardBoxBackgroundImage: {
        height: 400,
        width: '100%',
        position: 'absolute',
        top: 0,
        borderRadius: 39,

    },
    imageBox: {
        height: 65,
        width: 65,
        borderRadius: 65,
        borderWidth: 4,
        borderColor: '#FFFFFF',
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center'
    },
    profileImg: {
        height: 65,
        width: 65,
        borderRadius: 65,
    },
    imageBox1: {
        position: 'absolute',
        top: -16,
        right: 16,
    },
    imageBox2: {
        position: 'absolute',
        top: 30,
        left: -16,
    },
    imageBox3: {
        position: 'absolute',
        top: 180,
        right: -24,
    },
    imageBox4: {
        position: 'absolute',
        bottom: 24,
        left: 8,
    },
    imageBox5: {
        position: 'absolute',
        bottom: -2,
        right: 16,
    },
    cardBoxText: {
        color: '#FFF',
        fontSize: theme.calculateFontSize(theme.dimensions.loginCardText),
        textAlign: 'center',
        lineHeight: 30
    },
    cardTextBox: {
        justifyContent: 'center',
        alignItems: 'center',
        width: 120,
    },
    loginBox: {
        marginTop: 60,
    },
    createAccTextBox: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 10
    },
    createAccText: {
        fontFamily: 'Roboto-Italic',
        fontSize: theme.calculateFontSize(15),
        color: '#FFFFFF'
    },
    ticketCode: {
        alignSelf: "center",
    },
})
