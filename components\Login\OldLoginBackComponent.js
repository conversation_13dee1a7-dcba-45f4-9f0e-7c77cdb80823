import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import ArrowIcon from '../../assets/Images/icon/Arrow.png'

const OldLoginBackComponent = ({ navigation, style = {} }) => {
    const backBtnPress = () => {
        if (navigation.canGoBack()) {
            navigation.goBack(null);
        }
        else {
            navigation.replace("LoginScreen", { ErrorMsg: "" });
        }
        navigation.goBack(null);
    }
    return (
        <View style={{ marginBottom: 20, ...style }}>
            <TouchableOpacity onPress={() => backBtnPress()}>
                <Image source={ArrowIcon} style={{ width: 24, height: 24, resizeMode: 'contain', tintColor: "#FFF" }} />
            </TouchableOpacity>
        </View>
    )
}

export default OldLoginBackComponent

const styles = StyleSheet.create({})