import React, { useContext } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import { CurrencySymbol, UserHandlePrefix } from '../../utils/Appconfig';
import ProfileImagePlaceholder from '../../assets/Images/full_user_image_place_holder.png'
import { hasImageUrlExist } from '../../utils/Utils';
import { AppStateContext } from '../..';
import useDefaultStyle from '../../theme/useDefaultStyle';


const ReferralHistoryRow = ({ navigation, data }) => {
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    const { fullUserDetails } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const goToProfile = (profileSeq) => {
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }
    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
            <View>
                <ProgressiveImage
                    style={style.subsProfileBox}
                    source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                    defaultImageSource={ProfileImagePlaceholder}
                    resizeMode={'cover'}
                />
                {/* {
                    data.is_verified == "YES" ?
                        <Image
                            style={style.verifiedIcon}
                            source={require('../../assets/Images/icon/verifiedicon.png')}
                            resizeMode={'cover'}
                        />
                        : null
                } */}
            </View>
            <View style={{ marginStart: 15 }}>
                <TouchableOpacity onPress={() => goToProfile(data.profile_seq)}>
                    <View style={{ flexDirection: 'row' }}>
                        <EntutoTextView style={style.profileName}>{data.display_name}</EntutoTextView>
                    </View>
                </TouchableOpacity>
                <TouchableOpacity onPress={() => goToProfile(data.profile_seq)}>
                    <View >
                        <View>
                            <EntutoTextView style={style.profileHandle}>{UserHandlePrefix}{data.user_handle}</EntutoTextView>
                        </View>
                    </View>
                </TouchableOpacity>

            </View>
            <View style={style.postRightBox}>
                {/* <EntutoTextView style={style.priceVal}>{CurrencySymbol}75.00</EntutoTextView> */}
                <EntutoTextView style={style.dateVal}>{data.used_on}</EntutoTextView>

            </View>
        </View>
    )
}

export default ReferralHistoryRow;

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    subsProfileBox: {
        width: 50,
        height: 50,
        borderRadius: 50,
    },
    verifiedIcon: {
        width: 16,
        height: 15,
        position: 'absolute',
        right: -6,
        top: 20,
    },
    subsPostProfile: {
        width: 20,
        height: 20,
        borderRadius: 20,
    },

    profileName: {
        color: theme.colors.primaryTextColor,
        fontWeight: '700',
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileNameText),
    },
    profileHandle: {
        color: theme.colors.primaryTextColor,
        fontWeight: '400',
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileIDText),
        marginTop: 4,
    },
    postRightBox: {
        marginLeft: 'auto',
        alignItems: 'flex-end',
    },
    priceVal: {
        color: theme.colors.primaryColor,
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfilePriceText),
        fontWeight: '700',
    },
    dateVal: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileDateText),
        fontWeight: '400',
    }

})
