import React, { useEffect, useState } from 'react'
import { View } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import Colors from '../../constants/Colors';
import useSTheme from '../../theme/useSTheme';

const CustomActivityIndicator = ({ progress, ...props }) => {
    const [startInd, setstartInd] = useState(false);
    const theme = useSTheme();
    useEffect(() => {
        setstartInd(progress)
    }, [progress])
    return (
        <View style={{ alignItems: 'center', justifyContent: 'center', marginTop: 10 }}>
            <ActivityIndicator animating={startInd} color={theme.colors.primaryColor} size={40} {...props} />
        </View>
    )
}

export default CustomActivityIndicator
