import React, { useEffect, useState } from 'react'
import { Image, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { ActivityIndicator } from 'react-native-paper';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import Stories from '../components/StoryComponent/Stories';
import appData from '../data/Data';
import { _RedirectionErrorList, _UnauthErrorList } from '../utils/Appconfig';
import { AuthValidation } from '../utils/AuthValidation';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import useDefaultStyle from '../theme/useDefaultStyle';

const SingleStoryStatusScreen = ({ route, navigation }) => {
    const [storyList, setstoryList] = useState([]);
    const [hasStory, sethasStory] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setshowLoading] = useState(true);
    const { storySeq } = route.params;
    const { defaultStyle } = useDefaultStyle();
    useEffect(() => {
        getProfileStory();
    }, [])
    const closeButton = () => {
        navigation.goBack();
    }
    function getProfileStory() {
        let hashMap = {
            _action_code: "11:GET_STORY_DETAILS",
            story_seq: storySeq,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

            let storyListObj = {};
            storyListObj.displayName = data.data[0].display_name;
            storyListObj.profilePic = data.data[0].profile_picture;
            storyListObj.userHandle = data.data[0].user_handle;
            storyListObj.isVerified = data.data[0].is_verified;
            storyListObj.profileSeq = data.data[0].profile_seq;
            storyListObj.caption = data.data[0].story_comments;
            storyListObj.stories = [];
            data.data.map((obj, i) => {
                let dataRow = {};
                dataRow.id = obj.story_seq;
                dataRow.storySeq = obj.story_seq;
                dataRow.url = obj.media_file;
                dataRow.type = obj.media_type;
                dataRow.caption = obj.story_comments;
                dataRow.story_comment_tags = obj.story_comment_tags;
                dataRow.media_cover = obj.media_cover;
                dataRow.status = obj.status;
                dataRow.block_reason = obj.block_reason;
                dataRow.duration = 3;
                storyListObj.stories[i] = dataRow;
            });
            sethasStory(true);
            setshowLoading(false)

            setstoryList(storyListObj);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (_UnauthErrorList.includes(errorCode)) {
                    AuthValidation(errorCode, data, navigation);
                }

                appData.__StoryPageRefreshCheck = "YES";
                seterrorMsg(errorMessage);
                sethasStory(false);
                setstoryList({});
                setshowLoading(false)
            }

        });
    }
    const backBtnProfileClick = () => {
        navigation.goBack();
    }

    return (
        <View style={{ flex: 1, position: 'relative' }}>
            <StatusBar hidden={true} />
            {
                !hasStory ?
                    <View style={styles.profileBakBtnBox}>
                        <TouchableOpacity onPress={() => backBtnProfileClick()}
                            style={{ paddingHorizontal: 2, paddingVertical: 8, }}>
                            <View style={{ paddingHorizontal: 2, paddingVertical: 8, }}>
                                <Image
                                    style={styles.profileBackBtn}
                                    source={require('../assets/Images/icon/Arrow.png')}
                                />
                            </View>
                        </TouchableOpacity>
                    </View>
                    : null
            }
            {
                errorMsg.length != 0 ?
                    <View style={defaultStyle.errorBoxOutside} >
                        <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                    </View>
                    : null
            }
            {
                hasStory ?
                    <Stories dataStories={storyList} onStoryClose={closeButton} onClose={closeButton} />
                    : null


            }
            {
                showLoading ?
                    <View style={defaultStyle.errorBoxOutside} >
                        <ActivityIndicator size={'large'} />
                    </View>
                    : null
            }

        </View>
    )
}

export default SingleStoryStatusScreen

const styles = StyleSheet.create({
    profileBakBtnBox: {
        position: 'absolute',
        left: 15,
        top: 20, zIndex: 3,
    },
    profileBackBtn: {
        width: 24,
        height: 24,

    },
})
