import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import Dimensions from '../../constants/Dimensions'
import Video from 'react-native-video'

const RItemComponent = ({ data,
    isActive, }) => {


    return (
        <View style={{ width: Dimensions.screenWidth, height: Dimensions.screenHeight }}>
            <Video
                source={{ uri: data.media_file }}
                style={styles.video}
                resizeMode="cover"
                paused={!isActive}
                repeat
            />

        </View>
    )
}

export default RItemComponent

const styles = StyleSheet.create({
    video: {
        position: 'absolute',
        width: '100%',
        height: '100%',
    },
})