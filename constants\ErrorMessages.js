export default {
    oldPasswordErr: "Hey hey! This is required!",
    newPasswordErr: "Hey hey! This is required",
    confirmPasswordErr: "Oh no! This password doesn't match. Try Again?",
    delAccResasonErr: "Oh no! Please tell us why, select a reason!",
    delAccCommentErr: "Oh no! Please tell us more so we can be better! ",
    delCmntConfirmMsg: "Oh no! Are you sure you want to delete the comment?",
    delPostConfirmMsg: "Oh no! Are you sure you want to delete the post?",
    accNotFoundForPayMsg: "Add your location to view this exclusive content!",

    blockConfirmMsg: "Oh no! Are you sure you want to block this profile?",
    restrictConfirmMsg: "Oh no! Are you sure you want to restrict this profile?",

    youBlockProfileMsg: "You have blocked this profile!",
    profileBlockYouMsg: "The profile you are trying to view has blocked you",

    helpReasonErr: "Oh no! Please tell us why, select a reason!",
    helpCommentErr: "Oh no! Please tell us more so we can help !",
    helpExistLinkErr: "Please copy the link available on the top right to help us, help you!",
    helpStolenLinkErr: "Please copy the stolen content link to help us, help you!",
    helpStolenImageErr: "Hey hey! Almost done, just attach a screenshot of where  the stolen content has been posted!",
    storyDeleteConfirmMsg: "Oh no! Are you sure you want to delete the story?",

    discardChangesTitle: "Discard changes?",
    discardChangesMsg: "Oh hey! You have unsaved changes. Are you sure to discard them and leave the screen?",

    selFreeToPaidConfirmMsg: "Are you sure you want to switch Your SoTrue? Check out our FAQ's for more details!",
    selPaidToFreeConfirmMsg: "Hey hey! You will lose all your subscribers by switching your account, would you like to proceed?",

    panGstMisMatchErr: "Oh no! Your Pan and GST don’t match. Please try again?",
    countryNameErr: "Hey hey! We need the name of your country for tax purposes. It’s a secret between us!",
    stateNameErr: "Hey hey! We need the name of your state for tax purposes. It’s a secret between us!",
    accountNoErr: "Please enter a valid account number, we want to credit all earnings only to you! Please try again?",
    ifscCodeErr: "Almost there! Please enter a valid IFSC code!",
    accountNameErr: "So close! Please enter your bank account name correctly!",
    accountTypeErr: "Wohoo done! Just select the type of account, so we can credit your entire earning amount!",
    accVerifiedFlushMsg: "Submit your profile verification to earn through your content! Go to your settings!",
    accMobileNoErr: "Hey hey! Mobile Number is required!",

    addPostCaptionErr: "Add a caption to upload your content successfully! Try again now?",
    addPostNoMediaErr: "Hey hey! Please select some content so we can post it!",
    addPostMediaSizeErr: "Your file is too large! Check out our FAQ's for more information!",
    addPostMediaInvalidErr: "Oh no! This kind of media file is not supported…yet. Please try posting something else?",
    paidPostBankDetailsConfirmMsg: "Hey hey! To upload a paid post, we need your bank details to send your earnings. Let’s add them now?",

    addStoryCaptionErr: "Add a caption to upload your content successfully! Try again now?",
    addStoryMediaErr: "Hey hey! Please upload some content so we can post your story!",
    addStoryMediaSizeErr: "Your file is too large! Check out our FAQ's for more information!",
    addStoryMediaInvalidErr: "Oh no! This kind of media file is not supported…for now. Please try posting something else?",

    noOtpErr: "Oh hey! This is mandatory, try again?",

    loginUserNameErr: "Hey hey! Use your email Id to login. Please try again?",
    loginUserPasswordErr: "This is required also! Please try again?",

    referralBankCofirmMsg: "Add your bank account details to earn via referrals!",

    logoutConfirmMsg: "Oh no! Are you sure you want to logout? Please stay!",

    signupEmailIdErr: "Hey hey! Use your email Id to sign up. Please try again?",
    signupPasswordErr: "Almost there! Please create memorable password?",
    signupFullNameErr: "Wohoo finally! Just tell us your name, and SoTrue will guide you to money and fame!",

    veriSelfieImageErr: "Oh hey! We need a selfie to verify you!!",
    veriIdImageErr: "Almost there! We just need a government authorised ID card image here!",
    veriIdIErr: "Almost there! We just need a government authorised ID card Type here!",
    veriSelfieIdImageErr: "Wohoo finally! Just take a selfie holding the same ID as before!",

    veriNotSelectTypeErr: "Please select a type",
    bookmarkPostExpiryHint: "Hey Hey! This creators post is set to expire and won’t be available in your bookmarks soon. Enjoy it while it lasts wohoo!",

    imageNeedForTagErr: "Media needed for tag",

    categoryRequiredErr: "Please choose a suitable category to create Your SoTrue. Try again now?",
    duplicateHandleInsertErr: "Oh no! The user is already tagged. Try another one!",
    downloadSuccessMsg: "File Downloaded Successfully.",
    otherReasonErr: "Oh no! Please tell us why, enter a reason!",
    editProfileMediaSizeErr: "Oh no! Your content exceeds the limit. Please try posting something else?",

    permissionStorageHeaderMsg: "Permission to use Gallery",
    permissionStorageMsg: "We are requesting access to save photos so that the pictures and videos you take using the app to share on the platform will also be saved in your device for your reference.",
    permissionCameraHeaderMsg: "Permission to use camera",
    permissionCameraMsg: "We are requesting Camera Access to allow you to take pictures and videos so that you may if you wish share them with other users on the platform.",
    permissionLimitedStorageTitle: '"Sotrue" Would Like to Access Your Photos ',
    permissionLimitedStorageMsg: "We are requesting access to save photos so that the pictures and videos you take using the app to share on the platform will also be saved in your device for your reference.",

    permissionLocationHeaderMsg: "Location Permission",
    permissionLocationMsg: "App needs access to your location.",

    permissionAudioHeaderMsg: "Permission to use audio recording",
    permissionAudioMsg: "We are requesting permission to use your microphone so that while recording content to share on the platform we are able to capture the audio as well.",

    unlockPostBody1: "Love this post?",
    unlockPostBody2: "Share it across all platforms and make this creator famous",

    likePostBody1: "Love this post?",
    likePostBody2: "Be the first to share it far and wide and support this creator!",
    followBody1: "Love this creator?",
    followBody2: "Help them grow their fanbase by cross-promoting all over your social media!",
    verifyProfileBody1: "Congratulations on being SoTruely verified!",
    verifyProfileBody2: "Spread the word everywhere, get paid and go viral on SoTrue now!",
    bookmarkPostBody1: "Love this content?",
    bookmarkPostBody2: "Share this post, spread the word everywhere, get paid and go viral on SoTrue now!",
    imagePostBody1: "Love your content?",
    imagePostBody2: "Love your content? Share this post, spread the word everywhere, get paid and go viral on SoTrue now!",
    videoPostBody1: "Love your content?",
    videoPostBody2: "Love your content? Share this post, spread the word everywhere, get paid and go viral on SoTrue now!",


    addVerificationMediaSizeErr: "Oh no! Your content exceeds the temporary 15MB limit. Please try something else?",

    loginMobileNoErr: "Please enter mobile number",
    loginMobileNoInvalidErr: "Please enter a valid mobile number",
    emailOTPRequired: "Please enter the OTP",
    addPostInterestErr: "Please select a minimum of 3 Topics of your interests",
    addPostNoInterestErr: "Select 3 topics that best represent your content!",
    addPostReactionsErr: "Select the interactions you want enabled for your content!",
    heicFileErr: "Please ensure that the media format is in “most compatible” mode, in the camera section of your phone settings!",

    alreadyEmailVerifiedMsg: "This email id is already verified.",
    changeVerifiedEmailConfirmMsg: "Your email id is already verified, do you want to change it?",
    alreadyUsernameVerifiedMsg: "This Username is already verified.",
    changeVerifiedUsernameConfirmMsg: "Your Username is already verified, do you want to change it?"

}