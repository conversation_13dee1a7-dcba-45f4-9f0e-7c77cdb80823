import React, { useEffect, useState } from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { Button, Dialog, Portal } from 'react-native-paper'
import EntutoTextView from './EntutoTextView'
import useSTheme from '../../theme/useSTheme'

const ConfirmationPopup = ({ backdrop = false, isOkBtnPopup = false, ...props }) => {
    const [visiblePopup, setvisiblePopup] = useState(props.visiblePopup);
    const title = props.title;
    const messagebody = props.messagebody;
    const positiveButton = props.positiveButton ? props.positiveButton : "Confirm";
    const negativeButton = props.negativeButton ? props.negativeButton : "Cancel";
    const data = props.data;
    const theme = useSTheme();
    useEffect(() => {
        setvisiblePopup(true);
        return () => { setvisiblePopup(false) };
    }, [props.visiblePopupKey]);

    const dialogBtnPress = (type) => {
        if (type == "cancel") {
            if (!backdrop) {
                setvisiblePopup(false);
                props.popupClick(type, data);
            }

        }
        else if (type == "negative") {
            setvisiblePopup(false);
            props.popupClick(type, data);
        }
        else if (type == "positive") {
            setvisiblePopup(false);
            props.popupClick(type, data);
        }

    }
    return (
        <Portal>
            <Dialog visible={visiblePopup} dismissable={!backdrop} onDismiss={() => dialogBtnPress("cancel")}>
                <Dialog.Title>{title}</Dialog.Title>
                <Dialog.Content>
                    <EntutoTextView style={{ fontSize: theme.calculateFontSize(theme.dimensions.confirmDialogMsgText) }}>{messagebody}</EntutoTextView>
                </Dialog.Content>
                <Dialog.Actions style={{ justifyContent: isOkBtnPopup ? 'center' : 'flex-end' }}>
                    {
                        !isOkBtnPopup ?
                            <Button onPress={() => dialogBtnPress("negative")}>{negativeButton}</Button>
                            : null
                    }

                    <Button onPress={() => dialogBtnPress("positive")}>{positiveButton}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    )
}

export default ConfirmationPopup

const styles = StyleSheet.create({})
