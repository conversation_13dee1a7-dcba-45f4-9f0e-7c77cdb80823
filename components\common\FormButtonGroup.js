import { Pressable, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';

const FormButtonGroup = ({
    selectedValue = "",
    onChange,
    dataList = [],
    disabled = false
}) => {
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    return (
        <View style={style.groupContainer}>
            {
                dataList.map((item, index) => {
                    return (
                        <Pressable disabled={disabled} key={index} onPress={() => onChange(item.value)} style={[style.itemContainer,
                        selectedValue === item.value ? style.selectedItemContainer : null
                        ]}>
                            <View >
                                <Text allowFontScaling={false} style={{
                                    ...style.itemText,
                                    color: selectedValue === item.value ? theme.colors.buttonGroupActiveTextColor : theme.colors.buttonGroupInActiveTextColor
                                }}>{item.label}</Text>
                            </View>
                        </Pressable>
                    )
                })
            }


        </View>
    )
}

export default FormButtonGroup

const styles = theme => StyleSheet.create({
    groupContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 25,
        height: 50,
        backgroundColor: theme.colors.buttonGroupBackColor,
        marginBottom: 10,
        padding: 4,
        borderWidth: 1,
        borderColor: theme.colors.buttonGroupBorderColor
    },
    itemContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 25,
        height: 42,
    },
    selectedItemContainer: {
        backgroundColor: theme.colors.primaryColor,
    },
    itemText: {
        fontSize: theme.calculateFontSize(theme.dimensions.buttonGroupItemText)
    }
})