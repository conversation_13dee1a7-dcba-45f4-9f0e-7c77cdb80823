import React, { useState } from 'react'
import { StyleSheet, View, Image, ScrollView, TouchableOpacity } from 'react-native'
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import EntutoEditText from '../components/common/EntutoEditText';
import PrimaryButton from '../components/common/PrimaryButton';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import TopNavigationBar from '../components/TopNavigationBar';
import ServerConnector from '../utils/ServerConnector';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import ErrorMessages from '../constants/ErrorMessages';
import CustomStatusBar from '../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import EntutoTextView from '../components/common/EntutoTextView';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import OldLoginBackComponent from '../components/Login/OldLoginBackComponent';
const ForgotVerifyEmailScreen = ({ route, navigation }) => {
    const [otp, setotp] = useState("");
    const [otpErr, setotpErr] = useState("");
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const [refreshKey, setrefreshKey] = useState(Math.random())
    const { _data, _emailId, _user_seq } = route.params;
    const theme = useSTheme();
    const loginTxtClick = () => {
        navigation.replace("LoginScreen", {
            ErrorMsg: "",
        });
    }
    const { defaultStyle } = useDefaultStyle();
    const verifyButtonClick = () => {
        var isValid = true;
        setotpErr("");
        if (otp.length === 0) {
            setotpErr(ErrorMessages.noOtpErr);
            isValid = false;
        }
        if (isValid) {
            setShowLoading(true);
            appServiceCall();
        }
    }
    function appServiceCall() {
        let hashMap = {
            _action_code: "11:SUBMIT_PASSWORD_OTP",
            otp: otp,
            user_seq: _user_seq,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            seterrorMsg("");
            navigation.navigate("ForgotResetPassScreen", {
                _data: data,
                _emailId: _emailId,
                _user_seq: _user_seq,
                _otp: otp
            })
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_seq) {
                        seterrorMsg(data.data.user_seq);
                        setrefreshKey(Math.random());
                        fieldErrorShown = true;
                    }
                    if (data.data.otp) {
                        setotpErr(data.data.otp);
                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }

    return (
        <>
            <CustomStatusBar translucent={true} hidden={false} />

            <View style={{ flex: 1, position: 'relative', backgroundColor: theme.colors.oldLoginBackground }}>

                <CustomProgressDialog
                    showLoading={showLoading}
                />
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View>
                        <View style={defaultStyle.signupTextBox}>
                            <OldLoginBackComponent navigation={navigation} />
                            <EntutoTextView style={defaultStyle.signupText}>Verify Your Email</EntutoTextView>
                            <EntutoTextView style={defaultStyle.signupSmallText}>{_data.msg}</EntutoTextView>
                        </View>
                        <View style={{ ...defaultStyle.signUpFormBox, marginTop: 80 }}>
                            <EntutoEditText
                                mode={"flat"}
                                backgroundColor={{ backgroundColor: theme.colors.editTextBackgroundColor }}
                                activeUnderlineColor={true}
                                textColor={"#111111"}
                                placeholderTxt="Otp"
                                value={otp}
                                keyboardType='numeric'
                                onChangeText={text => setotp(text)}
                                showErrorField={otpErr.length}
                                errorMsg={otpErr}
                            />
                        </View>
                        <View style={{ ...defaultStyle.signUpBtnBox, marginTop: 54 }}>
                            <TouchableOpacity style={{ ...defaultStyle.signUpBtn, backgroundColor: '#111111' }}
                                onPress={() => verifyButtonClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Verify</EntutoTextView>
                            </TouchableOpacity>
                            <View style={{ flex: 1 }} />
                            {/* <TouchableOpacity style={defaultStyle.signUpBtn}
                                onPress={() => loginTxtClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Login</EntutoTextView>
                            </TouchableOpacity> */}
                        </View>
                    </View>
                </ScrollView>
            </View>
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                    : null
            }
        </>
    )
}

export default ForgotVerifyEmailScreen;

const styles = StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: Colors.bodyTextColor
    },
    signupTxtVal: {
        color: Colors.primaryColor,
    },
    forgetPassLogo: {
        height: 200,
        width: "auto",
        resizeMode: "contain",
        marginTop: 10,
    },
    loginHeadTxt: {
        marginTop: 40,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },
    resendTxtVal: {
        color: Colors.primaryColor,
        marginBottom: 18,
    },

})
