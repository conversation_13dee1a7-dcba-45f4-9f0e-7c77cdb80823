import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useDefaultStyle from '../../theme/useDefaultStyle';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../../components/common/LoginSignUpLinearGrad';
import {ScrollView} from 'react-native';
import ModuleAppBar from '../../components/loginModule/ModuleAppBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoTextView from '../../components/common/EntutoTextView';
import ModuleHeaderText from '../../components/loginModule/ModuleHeaderText';
import LoginModuleTitle from '../../components/loginModule/LoginModuleTitle';
import EntutoEditText from '../../components/common/EntutoEditText';
import PrimaryButton from '../../components/common/PrimaryButton';
import {checkValueLength} from '../../utils/Utils';
import ErrorMessages from '../../constants/ErrorMessages';
import ServerConnector from '../../utils/ServerConnector';
import SuccessFailureMsgBox from '../../components/common/SuccessFailureMsgBox';
import {AppStateContext} from '../..';
import appData from '../../data/Data';
import {_clearAllData} from '../../utils/AuthLogin';

const QuickSignInScreen = ({navigation}) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();
  const [errorMsg, setErrorMsg] = useState('');
  const [refreshKey, setRefreshKey] = useState(Math.random());
  const [showLoading, setShowLoading] = useState(false);

  const [mobileNumber, setMobileNumber] = useState('');
  const [mobileNumberErr, setMobileNumberErr] = useState('');

  const {changeUserDetails} = useContext(AppStateContext);

  useEffect(() => {
    appData._copyLinkPostSeq = '';
    appData._copyLinkProfileSeq = '';
    _clearAllData();
    appData._userDetails = null;
  }, []);

  const getOtpBtnPress = () => {
    if (formValid()) {
      setShowLoading(true);
      generateOtpService();
    }
  };
  const formValid = () => {
    let formValid = true;
    if (!checkValueLength(mobileNumber)) {
      setMobileNumberErr(ErrorMessages.loginMobileNoErr);
      formValid = false;
    }
    if (checkValueLength(mobileNumber)) {
      if (mobileNumber.length < 10) {
        setMobileNumberErr(ErrorMessages.loginMobileNoInvalidErr);
        formValid = false;
      }
    }
    return formValid;
  };
  const oldAccountBtnClick = () => {
    navigation.navigate('LoginOldFlowScreen', {ErrorMsg: ''});
  };
  const onMobileNumberChange = text => {
    setMobileNumber(text);
    setMobileNumberErr('');
  };
  const generateOtpService = () => {
    let hashMap = {
      _action_code: '11:GENERATE_OTP',
      mobile: mobileNumber,
    };
    // setShowLoading(false);
    // navigation.navigate("QuickSignInOTPScreen", {
    //     _data: {
    //         data:{active_accs: [
    //             { value: 1, label: "Email" },
    //             { value: 2, label: "Mobile" },
    //         ]},
    //         msg: "Please enter the OTP sent to your mobile number!",
    //     },
    //     _mobile: mobileNumber,
    //     _access_key: "",
    // });
    // return;
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        setErrorMsg('');
        navigation.navigate('QuickSignInOTPScreen', {
          _data: data,
          _mobile: mobileNumber,
          _access_key: data._access_key,
        });
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        var fieldErrorShown = false;
        if (errorCode === 'E006') {
          if (data && data != null && data.data) {
            if (data.data.mobile) {
              setMobileNumberErr(data.data.mobile);
              fieldErrorShown = true;
            }
          }
        }
        if (!fieldErrorShown) {
          setErrorMsg(errorMessage);
          setRefreshKey(Math.random());
        }
      },
    );
    // navigation.navigate('QuickSignInOTPScreen', {
    //   _data: {
    //     data: {
    //       active_accs: [],
    //     },
    //     msg: 'Please enter the OTP sent to your mobile number!',
    //   },
    //   _mobile: mobileNumber,
    //   _access_key: '',
    // });
    // setShowLoading(false);
    // setErrorMsg('');
  };

  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <CustomProgressDialog showLoading={showLoading} />
      <View style={{flex: 1, position: 'relative'}}>
        <LoginSignUpLinearGrad />
        <ModuleAppBar navigation={navigation} />
        <ScrollView keyboardShouldPersistTaps="handled">
          <View style={defaultStyle.loginModuleContainer}>
            <ModuleHeaderText
              text="Welcome to Sotrue"
              style={{marginTop: theme.dimensions.loginModuleHeaderTextMT}}
              // secondText="Exclusive Content, Tailored Just For You"
            />

            <LoginModuleTitle
              firstTitleText="Quick Sign-in,"
              secondTitleText="Use Your Digits"
              style={{marginTop: theme.dimensions.loginModuleTitleMT}}
            />
            <View style={defaultStyle.loginModuleFormContainer}>
              <View>
                <EntutoEditText
                  label="Mobile Number"
                  keyboardType="numeric"
                  value={mobileNumber}
                  onChangeText={onMobileNumberChange}
                  placeholderTxt="Mobile Number"
                  showErrorField={mobileNumberErr.length}
                  errorMsg={mobileNumberErr}
                  maxLength={12}
                  returnKeyType="go"
                  onSubmitEditing={() => getOtpBtnPress()}
                />
              </View>
              <View style={{marginTop: theme.dimensions.loginModuleButtonMT}}>
                <PrimaryButton
                  label="Get OTP"
                  style={{}}
                  uppercase={false}
                  onPress={() => getOtpBtnPress()}
                />
              </View>
            </View>
            {/* <View 
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 20,
              }}>
              <TouchableOpacity onPress={() => oldAccountBtnClick()}>
                <EntutoTextView>For older accounts, click here.</EntutoTextView>
              </TouchableOpacity>
            </View> */}
          </View>
        </ScrollView>
      </View>
      {errorMsg.length != 0 ? (
        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
      ) : null}
    </>
  );
};

export default QuickSignInScreen;

const styles = theme => StyleSheet.create({});
