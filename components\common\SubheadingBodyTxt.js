import React from 'react'
import { StyleSheet, } from 'react-native'
import Dimensions from '../../constants/Dimensions';
import EntutoTextView from './EntutoTextView';
import useSTheme from '../../theme/useSTheme';

const SubheadingBodyTxt = props => {
    const theme = useSTheme();
    return <EntutoTextView style={{
        ...styles.default,
        color: theme.colors.mainHeadingColor,
        fontSize: theme.calculateFontSize(theme.dimensions.SubheadingBodyTxt),
        ...props.style
    }}>{props.children}</EntutoTextView>
}

export default SubheadingBodyTxt;

const styles = StyleSheet.create({
    default: {
        fontSize: Dimensions.SubheadingBodyTxt,
    }
})
