import { Dimensions, PixelRatio } from 'react-native';

const { width, height } = Dimensions.get('window');

const guidelineBaseWidth = 390;
const guidelineBaseHeight = 780;

const [shortDimension, longDimension] = width < height ? [width, height] : [height, width];

const horizontalScale = (size) => {
    return (shortDimension / guidelineBaseWidth) * size
};
const verticalScale = (size) => {
    return (longDimension / guidelineBaseHeight) * size;
};
const moderateScaleCustom = (size, factor = 0.5) => {
    return size + (horizontalScale(size) - size) * factor;
};

const moderateScalePixel = (size) => {
    const screenWidthDP = width / 390;
    const designPX = PixelRatio.roundToNearestPixel(size);
    return designPX * screenWidthDP;
};

export { horizontalScale, verticalScale, moderateScaleCustom, moderateScalePixel };