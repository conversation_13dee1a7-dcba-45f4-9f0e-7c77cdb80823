import React from 'react'
import { StyleSheet, View, Animated } from 'react-native';
import FastImage from 'react-native-fast-image';

const ProgressiveImage = ({ defaultImageSource = require('../../assets/Images/default_image.jpg'),
    source, style, ...props }) => {
    const defaultImageAnimated = new Animated.Value(0);
    const imageAnimated = new Animated.Value(0);
    const [imageLoading, setimageLoading] = React.useState(true);

    const handleDefaultImageLoad = () => {
        Animated.timing(defaultImageAnimated, {
            toValue: 1,
            useNativeDriver: true,
        }).start();
    }
    const handleImageLoad = () => {
        Animated.timing(imageAnimated, {
            toValue: 1,
            useNativeDriver: true,
        }).start();
    }
    let sourseImage = source == null ? defaultImageSource : source;
    return (
        <View style={{ ...styles.defaultContainer, ...style, }}>
            <FastImage

                {...props}
                style={{ ...style, ...styles.imageOverlay }}
                source={sourseImage}
            />
        </View>
    )
    // return (
    //     <View style={{ ...styles.defaultContainer, ...style }}>
    //         <Animated.Image
    //             {...props}
    //             source={defaultImageSource}
    //             style={{ ...style, opacity: defaultImageAnimated, }}
    //             onLoad={handleDefaultImageLoad}
    //             blurRadius={1}
    //         />

    //         <Animated.Image
    //             {...props}
    //             source={source}
    //             style={{ ...style, opacity: imageAnimated, ...styles.imageOverlay }}
    //             onLoad={handleImageLoad}
    //         />

    //     </View>
    // )
}

export default ProgressiveImage

const styles = StyleSheet.create({
    defaultContainer: {

    },
    imageOverlay: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        width: '100%',
        height: '100%',
    }
})
