import React, { useContext, useEffect } from 'react';
import { Image, ScrollView, StyleSheet, Text, View } from 'react-native';
import TopNavigationBar from '../components/TopNavigationBar';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import PrimaryButton from '../components/common/PrimaryButton';
import CustomStatusBar from '../components/common/CustomStatusBar';
import appData from '../data/Data';
import { creationOfCopyLink } from '../utils/Utils';
import Share from 'react-native-share';
import { AppStateContext } from '..';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';

const SuccessfullVerificationScreen = ({ navigation, route }) => {
    const { fullUserDetails } = useContext(AppStateContext);
    const { successMsg } = route.params;
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    useEffect(() => {
        appData.__isPageRefresh = Math.random();
    }, []);

    const goToHomeScreen = () => {
        navigation.reset({
            index: 0,
            routes: [{ name: 'HomeScreen', params: { ErrorMsg: "", } }],
        });
    }
    const shareProfileBtnPress = async () => {
        let copyLinkText = creationOfCopyLink("PROFILE", __ProfileSeq);
        const shareOptions = {
            message: "Exclusive content on SoTrue\n",
            url: copyLinkText
        }
        try {
            const shareResponse = await Share.open(shareOptions);
        } catch (error) {
            // console.log(error.message);
        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />

            <ScrollView
                keyboardShouldPersistTaps="handled"
                style={{ backgroundColor: theme.colors.backgroundColor }}>

                <View style={defaultStyle.container}>
                    <TopNavigationBar navigation={navigation} />
                    <View style={{ ...defaultStyle.withoutPapercontainer, flexDirection: 'column' }}>

                        <View style={{ flex: 1, marginTop: 40, }}>
                            <HeadLineTxt style={style.loginHeadTxt}>Awesome</HeadLineTxt>
                            <HeadLineDownTxt style={style.headBodyTxt}>
                                {/* You have successfully uploaded your documents.
                                Our support team will verify and get back to you. */}
                                {successMsg}
                            </HeadLineDownTxt>
                            <PrimaryButton
                                label="Continue to Explore"
                                style={{ marginVertical: 20, }} uppercase={false}
                                onPress={() => goToHomeScreen()} />
                            {/* <PrimaryButton
                                label="Share Profile"
                                labelStyle={style.labelStyle}
                                style={{ marginVertical: 20, backgroundColor: '#FFF', color: '#000' }} uppercase={false}
                                onPress={() => shareProfileBtnPress()} /> */}
                        </View>

                    </View>
                </View>
            </ScrollView>
        </>
    )
}

export default SuccessfullVerificationScreen;

const styles = theme => StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: theme.colors.bodyTextColor
    },
    signupTxtVal: {
        color: theme.colors.primaryColor,
    },
    forgetPassLogo: {
        height: 200,
        width: "auto",
        resizeMode: "contain",
        marginTop: 10,
    },
    loginHeadTxt: {
        marginTop: 40,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },
    resendTxtVal: {
        color: theme.colors.primaryColor,
        marginBottom: 18,
    },
    labelStyle: {
        color: '#000'
    }
})
