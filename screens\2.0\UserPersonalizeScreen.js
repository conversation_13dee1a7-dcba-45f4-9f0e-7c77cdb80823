import { Image, Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useDefaultStyle from '../../theme/useDefaultStyle';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../../components/common/LoginSignUpLinearGrad';
import { ScrollView } from 'react-native';
import ModuleAppBar from '../../components/loginModule/ModuleAppBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoTextView from '../../components/common/EntutoTextView';
import ModuleHeaderText from '../../components/loginModule/ModuleHeaderText';
import LoginModuleProgress from '../../components/loginModule/LoginModuleProgress';
import LoginModuleTitle from '../../components/loginModule/LoginModuleTitle';
import EntutoEditText from '../../components/common/EntutoEditText';
import PrimaryButton from '../../components/common/PrimaryButton';
import ThemeColorComponent from '../../components/ThemeColorComponent';
import SearchIcon from "../../assets/Images/icon/search_icon.png"
import SelectBoxComponent from '../../components/common/SelectBoxComponent';
import { CommonActions } from '@react-navigation/native';
import ServerConnector from '../../utils/ServerConnector';
import SuccessFailureMsgBox from '../../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../../components/HomeTopNavigationBar';
import OptionSelectionItem from '../../components/common/OptionSelectionItem';
import DarkModeChangeComponent from '../../components/settings/DarkModeChangeComponent';

const UserPersonalizeScreen = ({ navigation }) => {
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const { defaultStyle } = useDefaultStyle();
    const [showLoading, setShowLoading] = useState(false);
    const [errorMsg, setErrorMsg] = useState("");
    const [errorMsgType, setErrorMsgType] = useState("FAILED");
    const [refreshKey, setRefreshKey] = useState(Math.random());

    const [topicList, setTopicList] = useState([]);
    const [selectedInterests, setSelectedInterests] = useState([]);
    const [displayTopicList, setDisplayTopicList] = useState([]);
    const [backupList, setBackupList] = useState([]);
    const [selectedUIColor, setSelectedUIColor] = useState("COLOR_1");
    useEffect(() => {
        setSelectedUIColor(theme.selectedColorType);
    }, [theme.selectedColorType]);
    useEffect(() => {
        setShowLoading(true);
        getInterestListService()

    }, [])
    const getInterestListService = () => {
        let hashMap = {
            _action_code: "11:GET_INTEREST_CATEGORIES",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let tempList = [];
            data.data.map(item => {
                tempList.push({
                    label: item.category,
                    value: item.category,
                })
            });
            setTopicList(tempList);
            getUserInterestListService(tempList, data.random)
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            setTopicList([]);
        });
    }
    const getUserInterestListService = (tempList, randomList) => {
        let hashMap = {
            _action_code: "11:GET_USER_INTERESTS",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let selectedInterestList = []
            data.data.map(item => {
                selectedInterestList.push(item.interest_name)
            })
            setSelectedInterests(selectedInterestList)
            setBackupList([]);
            createUpdatedList(selectedInterestList, tempList, [])
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            setBackupList(randomList);
            createUpdatedList([], tempList, randomList)
        });
    }

    const saveThemeBtnClick = () => {
        if (formValid()) {
            setShowLoading(true);
            updateUserInterestService()
        }
    }
    const formValid = () => {
        let isValid = true;
        if (selectedInterests.length < 6) {
            setErrorMsg("Please select minimum of 6 interesting topics, for the best experience.")
            setRefreshKey(Math.random());
            isValid = false;
        }
        if (selectedUIColor.length == 0) {
            setErrorMsg("Please select the color!")
            setRefreshKey(Math.random());
            isValid = false;
        }
        return isValid;
    }
    const themeColorChange = (themeValue) => {
        setSelectedUIColor(themeValue);
    }
    const [openInterestList, setOpenInterestList] = useState(false);
    const selectInterestBoxClick = (clickID, obj) => {
        setOpenInterestList(false);
        if (clickID == "DONE") {
            setSelectedInterests([...[], ...obj.selectedItem])
            createUpdatedList(obj.selectedItem, topicList, backupList);
        }
    }
    const createUpdatedList = (selectedItems, tempList, tempBackUplList) => {
        let tempTopicList = JSON.parse(JSON.stringify(tempList));
        let list = []
        tempTopicList.map((item, index) => {
            if (selectedItems.includes(item.value)) {
                item.isChecked = true;
                list.push(item)
            } else {
                if (tempBackUplList.includes(item.value)) {
                    list.push(item)
                }
                item.isChecked = false;
            }
        });
        setDisplayTopicList(list);
    }
    const onInterestItemPress = (clickID, obj) => {
        if (clickID == "ITEM_CLICK") {
            let tempTopicList = JSON.parse(JSON.stringify(displayTopicList));
            let list = [...tempTopicList];
            list[obj.index].isChecked = !list[obj.index].isChecked;
            if (list[obj.index].isChecked) {
                setSelectedInterests([...selectedInterests, obj.value])
            } else {
                setSelectedInterests(selectedInterests.filter((item) => item !== obj.value))
            }
            setDisplayTopicList(list);
        }
    }
    const updateUserInterestService = () => {
        let hashMap = {
            _action_code: "11:UPDATE_USER_INTERESTS",
            interests: JSON.stringify(selectedInterests),
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(true);
            updateUserColorService()
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {
                if (data && data != null && data.data) {
                    if (data.data.interests) {
                        setErrorMsg(data.data.interests);
                        setErrorMsgType("FAILED");
                        setRefreshKey(Math.random())

                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                setErrorMsg(errorMessage);
                setErrorMsgType("FAILED");
                setRefreshKey(Math.random())
            }
        });
    }
    const updateUserColorService = () => {
        let hashMap = {
            _action_code: "11:UPDATE_USER_UI_COLOUR",
            ui_colour: selectedUIColor,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            theme.changeThemeColor(selectedUIColor);
            setShowLoading(false);
            setErrorMsg(data.msg);
            setErrorMsgType("SUCCESS");
            setRefreshKey(Math.random());
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {
                if (data && data != null && data.data) {
                    if (data.data.ui_colour) {
                        setErrorMsg(data.data.ui_colour);
                        setErrorMsgType("FAILED");
                        setRefreshKey(Math.random())

                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                setErrorMsg(errorMessage);
                setErrorMsgType("FAILED");
                setRefreshKey(Math.random())
            }
        });
    }
    const searchTopicBtnPress = () => {
        setOpenInterestList(true);
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <View style={{ flex: 1, position: 'relative', backgroundColor: theme.colors.backgroundColor }}>
                <HomeTopNavigationBar showBackBtn={true} showBorderBottom={false} title="Update Theme" navigation={navigation} />

                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View style={{ ...defaultStyle.loginModuleContainer, }}>
                        <View style={{ ...defaultStyle.loginModuleFormContainer, marginTop: 8 }}>
                            <View style={{ marginTop: 10 }}>
                                <EntutoTextView style={style.labelText}>
                                    Select The Theme
                                </EntutoTextView>
                            </View>
                            <View style={{ marginTop: theme.dimensions.loginModuleInputMT }}>
                                <DarkModeChangeComponent />
                            </View>
                            <View style={{ marginTop: 32 }}>
                                <EntutoTextView style={style.labelText}>
                                    Select The Colour That Represents You The Best
                                </EntutoTextView>
                            </View>
                            <View style={{ marginTop: theme.dimensions.loginModuleInputMT }}>
                                <ThemeColorComponent themeColorChange={themeColorChange} selectedUIColor={selectedUIColor} />
                            </View>


                            <View style={{ marginTop: 32 }}>
                                <EntutoTextView style={style.labelText}>
                                    Update Interests
                                </EntutoTextView>
                            </View>
                            {
                                selectedInterests.length == 0 ?
                                    <EntutoTextView style={style.warringInterestText}>
                                        Please select minimum of 6 interesting topics, for the best experience.
                                    </EntutoTextView>
                                    : null
                            }

                            <View style={{
                                marginTop: theme.dimensions.loginModuleInputMT,
                                flexDirection: 'row', flexWrap: 'wrap'
                            }}>{
                                    displayTopicList.map((obj, i) => {
                                        return <OptionSelectionItem key={i}
                                            index={i}
                                            label={obj.label}
                                            isChecked={obj.isChecked}
                                            value={obj.value}
                                            onItemSelected={onInterestItemPress} />
                                    })
                                }
                                <OptionSelectionItem isSearch={true} onItemSelected={searchTopicBtnPress} />


                            </View>
                            <View style={{ marginTop: 54, }}>
                                <PrimaryButton label="Save"
                                    style={{}} uppercase={false}
                                    onPress={() => saveThemeBtnClick()} />
                            </View>
                        </View>
                    </View>
                </ScrollView>
            </View>
            <Modal
                animationType="fade"
                visible={openInterestList}
                style={{ margin: 0, flex: 1 }}>
                <SelectBoxComponent
                    selectBoxClick={selectInterestBoxClick}
                    list={JSON.parse(JSON.stringify(topicList))}
                    selectedValue={selectedInterests}
                    title="Select Interest"
                    maxSelectedValue={10}
                    multiSelect={true}
                    labelField="label"
                    valueField="value"
                />
            </Modal>
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} alertType={errorMsgType} />
                    : null
            }
        </>
    )
}
const InterestListItem = ({ label = "", value = "", index, onInterestItemPress = null, isChecked = false, }) => {
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    return (
        <View style={[style.itemContainer, {
            borderColor: isChecked ? theme.colors.primaryColor : "#707070",

        }]}>
            <TouchableOpacity onPress={() => onInterestItemPress(index, value, isChecked)} style={{ width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center' }}>
                <EntutoTextView style={style.itemContainerText}>{label}</EntutoTextView>
            </TouchableOpacity>
        </View>
    )

}
const InterestSearchListItem = ({ onPress }) => {
    const style = useSThemedStyles(styles);
    return (
        <View style={[style.itemContainer, {
            borderColor: "#707070", minWidth: 104,

        }]}>
            <TouchableOpacity onPress={onPress} style={{ width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center' }}>
                <Image source={SearchIcon} style={style.searchIcon} />
            </TouchableOpacity>
        </View>
    )

}

export default UserPersonalizeScreen

const styles = theme => StyleSheet.create({
    labelText: {
        fontSize: theme.calculateFontSize(theme.dimensions.personalizeLabelText)
    },
    itemContainer: {
        padding: theme.dimensions.loginModuleInputPadding,
        borderWidth: 1,
        justifyContent: "center",
        alignItems: "center",
        height: 36,
        // borderRadius: 36 / 2,
        marginEnd: 8,
        marginBottom: 8,
        minWidth: 90,

    },
    itemContainerText: {
        paddingHorizontal: 25,
    },
    searchIcon: {
        width: 17,
        height: 17,
        resizeMode: 'contain',
        tintColor: "#707070",
        paddingHorizontal: 25,

    },
    warringInterestText: {
        color: "#FF0000",
        marginTop: 10,
    }
})