{"v": "5.9.0", "fr": 24, "ip": 0, "op": 72, "w": 500, "h": 500, "nm": "happy after 2", "ddd": 0, "assets": [{"id": "image_0", "w": 1, "h": 1, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAADElEQVQImWNgoBwAAABEAAGC/mVLAAAAAElFTkSuQmCC", "e": 1}, {"id": "image_1", "w": 247, "h": 148, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_2", "w": 63, "h": 38, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAAmCAYAAABzhkOMAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAADNElEQVRoge2Yv3LTQBDGv5VsEayQXGYijahwOjpc0qGWClPSKR2lHyGPEN5A6ehwupRJR4fd0ZF0idTIM/hGsWwtBTajGGxLluTYHn6ldP++3b3buyUUhGEYDQANIqoTsw0AMdAhogBAh5mvfd/vFDVfEVCeztbhoR0TOWC2QfQiRZcemC+ZqB1FUTsIgiDP/HnJLF4IIZ5UKg4DrZSCZ8N8DkVxPc9r5xpnSVKLF0IITdNaYG4B2C90Fcw3BJzeD4fuKqMhlXjr8NCOATe3pxfTA9HpYDA4XYUR1EUNDMNwQPQFRKLsxQDYAWCrivKhpuuBlLLUA3Ku5y3Lqsej0Y8yFzAPAq5ioFVWllDm/h0O62VMmhYG3hDwzTTNkzLGn+t5IYTQKpXOCvZ6GroMOEVGwdw9H4ZhWNU0t6IodyB6DsAqauIlsAj4uFur9fpSfi1iwEx5fpzubAANYrYZeFPEIjLDfD4YDp28GSHXDQ8Yp0FFscHcBPAq73gZ6Cqq2ry9vb1edoDc4pMIIUS1Wm0SYANooujL0N/0GLCXPQcKFT+NaZrNcUSUaYge/06HbtaOpYpPYhiGQ8xNEL0rY3wGjrMaYGXiJ1iWVY/j2CnjjZDVACsXn2QcDSdF3iOyGOBRxU8o3AhE79M8k9dC/IQCjZAqC6yV+AmmaZ4UcCb0BlFUn3cRWkvxwJ93hZszO3Q932/M+rm24icUUEg583zf+dePhcWMx+anlNdVTXMVVX1KwOslhmjou7vdfr//ffrH2ns+yfjG6CL7WdBTVLUx/Q6YX8xYMzzPaw+iqE7AVcau+zwaudMf1z7spwnDMOxL6dZ0/SDjNqjXdP0mWRfcqLCfxjAMh4BTpN8GD9Lfxnk+iZSyU9P1C2J+m7K6vKNWKvf9fv8S2HDPTxBCCK1avUTKYsogig6CIAg26sCbRRAEwSCKbABnadqPS3GbHfZJxgdhW9f1IwAzb3UAwMCdlPJiKzyfxPN9h4FP89ooY+NsnXgA8H2/xcDxonZbKR4AfN93Fxlga8UDDwzQS36PgQ6wRQfeLKSUnWd7e585jo9A9BJAN4oiJwzD8LHX9p/H4hfIG1Ff2LpvEQAAAABJRU5ErkJggg==", "e": 1}, {"id": "image_3", "w": 65, "h": 39, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAAnCAYAAABQWiUCAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAC4klEQVRoge2aS3abMBSG/ytCOqwGwGGYHcQ7KEugK6i7gyyB7sDZgbOCkBWE7MDegT1zkQdm1hNsbgcmOU6TGFkIv06/qcWV9HN1H8KEI8T3/R4RXQHoAQAxR0yUUVUtCBjN5vPM5nxk05gpYRhe8XIZMxCBKALwtfEh5gcmSsuyTBeLxaLN/AcTQUopXdeNCbgBcN3CVAEgFY6TzGaziYmBvYsQhuFVtVolAGLovPHduDMRY28ihJ4XMVHCwLfOJyP6led5oj28w6UA2PPm3zJ+LstIJ150JsKG2//oag4NtIQQXcwcBEFSrVYjHFYAALh2XTduGnRhc8bQ86KKaADmNtHeKg7zpGmMNU/wfX9QET2iXbqzyRhE33UKq9Yxwff9HgFDHHbzYwAjEE1EVWV/lsvRLgVUKxF83+8TMID9fL8d5gcIMRJVldkooY1FCHx/iH0FPuYpE6UOc2q7bwAMRJBSyi+um3ae95mnEGIohBialsO67JQdpJTy0nUz7u78FwBSBgZqPh91NMc7tD2hDoAZujj/a3dPbHSEJmh5QlcCEPDERINcqdSmXYN1bKcLAQh4IuakiyBnwlZPkFLKugawIwDzVAD9Y9n8C1tFuHTdDHaCYEHMye/5fGDBlnU+FaGuA2wIcPdcljeHCHi6fBgTgiCIwXzfyvKRuv5HvBOhrgUmaBEHGLgtyzI55re/ybvjcHlxMYS5AAWI+irPD5ryduWNJ9T3AY+GtrSvs46NN57ARImJEQZulVI3VlZ0AF5FCD0vqgyaIgZ+KqWGVle1Z15FqIj6Oz5bMBAppfbW6HTF5vVa44XkBmcjAFCLEHpeBP2MMBaO0zsXAYD6OKyIYs2eep0BlDq5DLANAQAERBpjTzYFNvESE5p6hEI4TnyOAgB63x0KBqKu7/kOSZMIZ5UFPmMtAvP0g9/WfcCZCwC8iCDEvyXvmIEoP7FGyJTXzFj/WSq29VXnPyfGXyDNRLTEjT18AAAAAElFTkSuQmCC", "e": 1}, {"id": "image_4", "w": 65, "h": 25, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAAZCAYAAABuKkPfAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAClklEQVRYhe2XP3LaQBTGv7cCJjPxTLZgibqoTDrlBFa6lOQEUeeW3MA3COnSBW6Au5TyCSLfwO4wi2dWhT2MJOulMDhkgoQEwmM7/CrE231/vn08LYQHwG63vVsih4gcZpYCcIvWM7OBECFlmSEgRKNxPh6Pz3eVH9Xt0LZth9O0y0QumD0QvanJdQTmgIAgIwq01mFNfusRQSnlAvCJuVtj0cUwXzDRyLKs/rZdsrEIUkrZbDa7xHz8YIXnQMApMR+Pp9Ngw/3VkFLKVqvVA3MPwKtNgu6KTcWoJIJSyn8MJ1+CYZwkPWOMKbO4lAi2bTt8eztg4HC73BAREC6m/z9WZocABwC2jsV8IQC/TFesFaHT6XTBPMBmrX8GopHIsmCWpmHZk1kgpZQvGg2XAZcBD0Re1TyI+cvldNovXFNkfN1u95joa6WgwGkGDJIkGVUtugx2u+1lRD6ALsoLMpxo7ecZc0XoKDUA8LlkkIiBQR2vqypUnFG5QqwUoYIAEYj6cRz3d3HqZVFK+QT0sb4zVgphrXDYJ+CoROxhnCQfr66ufs5ms1m5dHfDzc1N2Gy1vltCvAPR24Kl7suDA7q+vg6Wv/yrE+aK/lgT80ww9za9mOyaMjUw8H752n0vglLKJeDXms3ftNa97VPdLfNaAuT8PAg4vdTaWzyLewPzqMBvBKJPT0EAANBah8KyXABnq+wMHNq27SyeBXDXQgUTNmLAm0wmRSI9Osbj8XmcJB5yhECaOouPAgCIuZvjK2LAq/Nv60NijDFzIYZF6+5EIJIrbE9agAXGGDPR2hfMH8B8AtzNhFma3tfVyNn7LARYZv42C1bZBAAw0Z+7NfNJnCTOcxKgNLZtO8sTc8+ePXv+V34DyX0zMWf5A7gAAAAASUVORK5CYII=", "e": 1}, {"id": "image_5", "w": 65, "h": 24, "u": "", "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAAYCAYAAACldpB6AAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAChklEQVRYhe2Xz3WbQBDGv1kZXbMHVt6b6SB0YDqIXEFIBVEqCK4gSgdyB6QDXEFQB/ZNZnVAtzwjmBz050lKBAghy37PvwMI3uzw8b1hZkU4IVprB/O5kxM5ROQAAIrCJSK5G8vMKYSIAUAURZQTpcaY+JT6VlBbiaSUstvteszsCcBl4Lql1GMAMQNRp9OJJpPJQ0t51xxlgpRSWpbVJ+Y+iD61JaqCMTGP6OIibMuQRiZorZ0izwMAn9sQ0RQC7gtgZIwZHZmnPlprh/N81GKptwPzIxMFTc2oZcKy7AMCvjZ5yAsyFsyDyXQaHbKo0gRt214BjEB01VjaghkBMbA9CZhZCsAFAF6cPxz5HDDwM8uyIE3TtE58qQm9Xi8A8/dmSvgXhIhFUUR/5vO4riBgYXy+GKkuAR6Ajw0UjEWn06/TPP9rgpRSdi1riMMa3wxACKIwSZLwgHWVrMYvmPsA+qhfLTMGvKr9xj8mLA2IUN/9MQPDYzt0XdZjGRigpkYGvpTp2zLhEAMIuCfm4NAm1Cbatr2CaIg6ZhDd7KvQLRMulYoqxx/zowD8c778Lpe2PWCiAOWfyd5PY22CUmpYOQKJbpMkCRpqPSnLDVyI8qqYPWeZs9ukCQCUUi4Bv0sWjxnwX+oPTVPqNHQC7p+M8TbvieXZL8l995xllR32NZCmaZoY4wO42xfDwLVSyt+8J5YHd8+au8QY/5AZ/xqoMoKYg81rsScOxPxtmexNUmoE0ZXW2lldCgBg4GEdwPwIopun6XR4SpEvQakR87mz+nkBAM9ZNrAsKyKiNDGm1d3euUmM8S9tO2aiH+fWcnaUUm7PtsOeUqlS6s1X+TvvvHM6/gLXWRn/2aLeeAAAAABJRU5ErkJggg==", "e": 1}, {"id": "image_6", "w": 341, "h": 341, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "guide", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [0]}, {"t": 47, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 24, "s": [0, 500, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 47, "s": [0, 500, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"t": 47, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 72, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "smile", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [0]}, {"t": 47, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [250.865, 309.571, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 11, "s": [250.865, 332.571, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [250.865, 332.571, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 24, "s": [250.865, 309.571, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 47, "s": [250.865, 309.571, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [123.491, 73.887, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [76, 76, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"t": 47, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 72, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "right eyebrow", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [0]}, {"t": 47, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 24, "s": [312.52, 167.677, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 47, "s": [312.52, 167.677, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [37.367, 13.275, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 38, "s": [120, 120, 100]}, {"t": 47, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 72, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "left eyebrow", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [0]}, {"t": 47, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 24, "s": [184.128, 168.573, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 47, "s": [184.128, 168.573, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [25.157, 14.901, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 38, "s": [120, 120, 100]}, {"t": 47, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 72, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "Right eye", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [0]}, {"t": 47, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [297.36, 219.033, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [297.36, 219.033, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [297.36, 205.033, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 47, "s": [297.36, 219.033, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [32.664, 3.563, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11, "s": [115, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"t": 47, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 72, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "Left eye", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [0]}, {"t": 47, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [204.773, 219.136, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [204.773, 219.136, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [204.773, 205.136, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 47, "s": [204.773, 219.136, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [32.648, 3.783, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11, "s": [115, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"t": 47, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 72, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "Base", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 24, "s": [0]}, {"t": 47, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 24, "s": [248.359, 249.878, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 47, "s": [248.359, 249.878, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [170.453, 170.453, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 24, "s": [100, 100, 100]}, {"t": 47, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 72, "st": 0, "bm": 0}], "markers": []}