import { StyleSheet, Text, View } from 'react-native'
import React, { useState } from 'react'
import GestureRecognizer from 'react-native-swipe-gestures'
import Stories from './Stories';

const MultipleStoryPlayComponent = ({ navigation, storyList, getCurrentIndex }) => {

    const [currentIndex, setCurrentIndex] = useState(0);

    const storyData = storyList.length ? storyList[currentIndex] : {};

    const config = {
        velocityThreshold: 0.3,
        directionalOffsetThreshold: 80,
    };
    const onSwipeLeft = () => {
        if (storyList.length - 1 > currentIndex) {
            getCurrentIndex(currentIndex + 1)
            setCurrentIndex(currentIndex + 1);

        } else {
            getCurrentIndex(currentIndex + 1)
            // closeButton();
        }
    }
    const onSwipeRight = () => {
        if (currentIndex > 0 && storyList.length) {
            getCurrentIndex(currentIndex - 1)
            setCurrentIndex(currentIndex - 1);

        } else {
            getCurrentIndex(0)
            setCurrentIndex(0);
        }
    }
    const closeButton = () => {
        navigation.goBack();
    }
    const onSwipeDown = () => {
        closeButton();
    };

    const onSwipeUp = () => {
        closeButton();
    };

    return (
        <>
            <GestureRecognizer
                config={config}
                style={styles.container}
                onSwipeDown={onSwipeDown}
                onSwipeUp={onSwipeUp}
                onSwipeLeft={onSwipeLeft}
                onSwipeRight={onSwipeRight}>
                <Stories key={currentIndex} dataStories={storyData} onStoryClosePress={closeButton} onClose={onSwipeLeft} />
            </GestureRecognizer>
        </>
    )
}

export default MultipleStoryPlayComponent

const styles = StyleSheet.create({
    container: {
        flex: 1,
        width: "100%",
        justifyContent: "flex-start",
        alignItems: "center",
        // paddingTop: 30,
    },
})