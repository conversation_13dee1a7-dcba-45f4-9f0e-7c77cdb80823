import React, { useContext, useState } from 'react'
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native'
import { AppStateContext } from '../..'
import ErrorMessages from '../../constants/ErrorMessages'
import { _RedirectionErrorList } from '../../utils/Appconfig'
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl'
import ServerConnector from '../../utils/ServerConnector'
import BottomSheetLoader from '../common/BottomSheetLoader'
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg'
import EntutoEditText from '../common/EntutoEditText'
import EntutoTextView from '../common/EntutoTextView'
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox'
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSTheme from '../../theme/useSTheme'
import { PopupNegativeButton, PopupPositiveButton } from '../common/PopupButton'

const ChangePassword = ({ refVal, ...props }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [oldPassword, setoldPassword] = useState("");
    const [oldPasswordErr, setoldPasswordErr] = useState("");
    const [newPassword, setnewPassword] = useState("");
    const [newPasswordErr, setnewPasswordErr] = useState("");
    const [confirmPassword, setconfirmPassword] = useState("");
    const [confirmPasswordErr, setconfirmPasswordErr] = useState("");

    const [showLoading, setshowLoading] = useState(false);
    const [showSuccessMsg, setshowSuccessMsg] = useState(false);
    const [successMsg, setsuccessMsg] = useState("");

    const [errorMsg, seterrorMsg] = useState("");
    const [refresfErrorMsg, setrefresfErrorMsg] = useState(Math.random());

    const { changeUserDetails } = useContext(AppStateContext)

    const cancelBtnPress = () => {
        props.changePasswordActionClick("negetive", {})
    }
    const closeBtnClick = () => {
        props.changePasswordActionClick("negetive", {})
    }
    const oldPasswordChangeHandler = (text) => {
        setoldPassword(text);
        setoldPasswordErr("");
    }
    const newPasswordChangeHandler = (text) => {
        setnewPassword(text);
        setnewPasswordErr("");
    }
    const confirmPasswordChangeHandler = (text) => {
        setconfirmPassword(text);
        setconfirmPasswordErr("");
    }
    const changePassword = () => {
        seterrorMsg("");
        let isFormValid = true;
        if (oldPassword.length === 0) {
            setoldPasswordErr(ErrorMessages.oldPasswordErr);
            isFormValid = false;
        }
        if (newPassword.length === 0) {
            setnewPasswordErr(ErrorMessages.oldPasswordErr);
            isFormValid = false;
        }
        if (newPassword !== confirmPassword) {
            setconfirmPasswordErr(ErrorMessages.confirmPasswordErr);
            isFormValid = false;
        }
        if (isFormValid) {
            setshowLoading(true);
            changePasswordService();
        }
    }
    function changePasswordService() {
        let hashMap = {
            _action_code: "11:CHANGE_PASSWORD",
            old_password: oldPassword,
            new_password: newPassword,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setshowLoading(false);
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true);
            let userDeatails = {
                _username: data.data.uid,
                _password: data.data.pwd,
            }
            changeUserDetails(userDeatails);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.new_password) {
                            setnewPasswordErr(data.data.new_password);
                            fieldErrorShown = true;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                    setrefresfErrorMsg(Math.random())
                }
            }
        });
    }
    return (
        <View>
            <View style={defaultStyle.popupBox}>
                <EntutoTextView style={defaultStyle.popupHeadTxt}>Change Password</EntutoTextView>
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                {
                    showSuccessMsg ?
                        <BottomSheetSuccessMsg successMsg={successMsg} cancelBtnClick={() => closeBtnClick()} />
                        : null
                }

                {
                    errorMsg.length != 0 ?
                        // <View >
                        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refresfErrorMsg} />
                        // </View>
                        : null
                }
                <EntutoEditText
                    labelTxt="Old Password"
                    placeholderTxt="Old Password"
                    secureEntryTxt={true}
                    showPasswordIcon={true}
                    value={oldPassword}
                    onChangeText={(text) => oldPasswordChangeHandler(text)}
                    showErrorField={oldPasswordErr.length}
                    errorMsg={oldPasswordErr}
                />
                <EntutoEditText
                    labelTxt="New Password"
                    placeholderTxt="New Password"
                    secureEntryTxt={true}
                    showPasswordIcon={true}
                    value={newPassword}
                    onChangeText={(text) => newPasswordChangeHandler(text)}
                    showErrorField={newPasswordErr.length}
                    errorMsg={newPasswordErr}

                />
                <EntutoEditText
                    labelTxt="Confirm Password"
                    placeholderTxt="Confirm Password"
                    secureEntryTxt={true}
                    showPasswordIcon={true}
                    value={confirmPassword}
                    onChangeText={(text) => confirmPasswordChangeHandler(text)}
                    showErrorField={confirmPasswordErr.length}
                    errorMsg={confirmPasswordErr}

                />
                <View style={{ flexDirection: 'row', flex: 1, marginTop: 16, marginBottom: 16, }}>
                    <View style={{ flex: 1 }}>
                        <PopupNegativeButton
                            onPress={() => cancelBtnPress()}
                            btnText='No'
                            style={{ marginEnd: theme.dimensions.popupBtnGap }} />
                    </View>
                    <View style={{ flex: 1 }}>
                        <PopupPositiveButton
                            onPress={() => changePassword()}
                            btnText={"Change"} />
                    </View>
                </View>
            </View>
        </View>
    )
}

export default ChangePassword

const styles = StyleSheet.create({})
