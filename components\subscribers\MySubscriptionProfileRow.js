import React, { useContext } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import Colors from '../../constants/Colors';
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import DateDividerIcon from '../../assets/Images/icon/date_divider.png';
import { hasImageUrlExist } from '../../utils/Utils';
import ProfileImagePlaceholder from '../../assets/Images/full_user_image_place_holder.png'
import { CurrencySymbol, UserHandlePrefix } from '../../utils/Appconfig';
import Dimensions from '../../constants/Dimensions';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSThemedStyles from '../../theme/useSThemedStyles';
import { AppStateContext } from '../..';
const MySubscriptionProfileRow = ({ navigation, data }) => {
    const { fullUserDetails } = useContext(AppStateContext);
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const goToProfile = (profileSeq) => {
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }
    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
            <View>
                <TouchableOpacity onPress={() => goToProfile(data.subscribed_to_seq)}>
                    <ProgressiveImage
                        style={style.subsProfileBox}
                        source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                        defaultImageSource={ProfileImagePlaceholder}
                        resizeMode={'contain'}
                    />
                </TouchableOpacity>

            </View>
            <View style={{ marginStart: 15 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <TouchableOpacity onPress={() => goToProfile(data.subscribed_to_seq)}>
                        <EntutoTextView style={style.profileName}>{data.display_name}</EntutoTextView>
                    </TouchableOpacity>
                    {
                        data.is_verified == "YES" ?
                            <Image
                                style={style.verifiedIcon}
                                source={require('../../assets/Images/icon/verifiedicon.png')}
                                resizeMode={'contain'}
                            />
                            : null
                    }
                </View>
                <View >
                    <View>
                        <EntutoTextView style={style.profileHandle}>{UserHandlePrefix}{data.user_handle}</EntutoTextView>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                        <View>
                            <EntutoTextView style={style.profileHandle}>{data.from_date}</EntutoTextView>
                        </View>
                        <View>
                            <Image source={DateDividerIcon} style={style.dateValDivider} resizeMode='stretch' />
                        </View>
                        <View>
                            <EntutoTextView style={style.profileHandle}>{data.to_date}</EntutoTextView>
                        </View>
                    </View>


                </View>
            </View>
            {/* <View style={style.postRightBox}>
                <EntutoTextView style={style.priceVal}>{CurrencySymbol}{data.paid_amount}</EntutoTextView>

                <View style={{ ...style.subsStatusBox, backgroundColor: data.status == "ACTIVE" ? "#34A85320" : "#FC656820" }}>
                    <EntutoTextView style={{ ...style.subsStatusTxt, color: data.status == "ACTIVE" ? "#34A853" : "#FC6568" }}>{data.status}</EntutoTextView>
                </View>
            </View> */}
        </View>
    )
}

export default MySubscriptionProfileRow;

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    subsProfileBox: {
        width: 50,
        height: 50,
        borderRadius: 50,
    },
    verifiedIcon: {
        width: 16,
        height: 15,
        marginLeft: theme.dimensions.veritextLeftmargin,
        // position: 'absolute',
        // right: -6,
        // top: 20,
    },
    subsPostProfile: {
        width: 20,
        height: 20,
        borderRadius: 20,
    },

    profileName: {
        color: theme.colors.primaryTextColor,
        fontWeight: '700',
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileNameText),
    },
    profileHandle: {
        color: theme.colors.primaryTextColor,
        fontWeight: '400',
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileIDText),
        marginTop: 4,
    },
    postRightBox: {
        marginLeft: 'auto',
        alignItems: 'flex-end',
    },
    priceVal: {
        color: theme.colors.primaryColor,
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfilePriceText),
        fontWeight: '700',
    },
    dateValDivider: {
        height: 8,
        width: 20,
        marginTop: 6,
        marginHorizontal: 5,
        // tintColor: "#FFFFFF"
    },
    subsStatusBox: {
        borderRadius: 3,
        marginTop: 2
    },
    subsStatusTxt: {
        paddingVertical: 4,
        paddingHorizontal: 10,
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileStatusText),
        fontWeight: '600'
    }

})
