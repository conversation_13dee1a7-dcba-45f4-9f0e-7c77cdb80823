import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useRef } from 'react'
import EntutoTextView from '../common/EntutoTextView';
import ActionSheet from 'react-native-actions-sheet';
import DeleteStoryActionView from './DeleteStoryActionView';
import HelpSetting from '../settings/HelpSetting';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';

const ThreeDotMenuStoryActionView = ({ profileSeq, currentProfileSeq, ThreeDotMenuPress, storySeq }) => {
    const deleteBtnRef = useRef(null);
    const postBtnRef = useRef(null);
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    const deleteStoryBtnPress = () => {
        deleteBtnRef.current?.show();
    }
    const deletePostActionPress = (clickId, obj) => {
        if (clickId == "negetive") {
            deleteBtnRef.current?.hide();
        }
        if (clickId == "close") {
            deleteBtnRef.current?.hide();
            ThreeDotMenuPress("delete_story", { msg: obj.erMsg });

        }
    }
    const postBtnActionClick = (clickId, obj) => {
        if (clickId == "negetive") {
            postBtnRef.current?.hide();
        }
        if (clickId == "close") {
            postBtnRef.current?.hide();
            ThreeDotMenuPress("blockPost", { msg: obj.erMsg });

        }
    }
    const reportStoryBtnPress = () => {
        postBtnRef.current?.show();
    }

    return (
        <View>
            <View style={style.popupBox}>
                {
                    currentProfileSeq == profileSeq ?
                        <View style={style.popupListRowBox}>
                            <TouchableOpacity style={style.linkBox}
                                onPress={() => deleteStoryBtnPress()}>
                                <EntutoTextView style={style.linkTxt}>Delete Story</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                        : null
                }

                <View style={style.popupListRowBox}>
                    <TouchableOpacity style={style.linkBox}
                        onPress={() => reportStoryBtnPress()}>
                        <EntutoTextView style={style.linkTxt}>Report Story</EntutoTextView>
                    </TouchableOpacity>
                </View>
            </View>
            <ActionSheet ref={deleteBtnRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                closeOnTouchBackdrop={true}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        deleteBtnRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <DeleteStoryActionView profileSeq={profileSeq} storySeq={storySeq}
                        deletePostActionPress={(clickId, obj) => deletePostActionPress(clickId, obj)}
                    />
                </ScrollView>
            </ActionSheet >
            <ActionSheet ref={postBtnRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                closeOnTouchBackdrop={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        postBtnRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <HelpSetting refVal={postBtnRef} settingsType="STORY" postSeq={storySeq}
                        helpBtnActionClick={(clickId, obj) => postBtnActionClick(clickId, obj)} />
                </ScrollView>
            </ActionSheet >
        </View>
    )
}

export default ThreeDotMenuStoryActionView;

const styles = theme => StyleSheet.create({
    popupBox: {
        paddingHorizontal: 16,
        paddingBottom: 16,
    },
    linkBox: {
        paddingVertical: 15,
    },
    linkTxt: {
        color: theme.colors.threeDotMenuText,
        fontSize: 14,

    },
})