import { FlatList, Image, Pressable, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import CustomStatusBar from '../common/CustomStatusBar'
import HeadingTxt from '../common/HeadingTxt'
import CustomActivityIndicator from '../common/CustomActivityIndicator'
import { ActivityIndicator } from 'react-native-paper'
import TagPeopleProfile from './TagPeopleProfile'
import { getSecondsBetweenDates, hasImageUrlExist } from '../../utils/Utils'
import ServerConnector from '../../utils/ServerConnector'
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl'
import { MaxStoryTxtLimit, _RedirectionErrorList } from '../../utils/Appconfig'
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox'
import EntutoTextView from '../common/EntutoTextView'
import ProgressiveImage from '../common/ProgressiveImage';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSTheme from '../../theme/useSTheme'
import useSThemedStyles from '../../theme/useSThemedStyles'

const TagPeopleSearchComponent = ({ tagPeopleSearchPress, selectedPList, navigation }) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [isTyped, setisTyped] = useState(false);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [errorMsg, seterrorMsg] = useState("");
    const [errorMsgKey, seterrorMsgKey] = useState(Math.random());
    const [showLoading, setshowLoading] = useState(false);
    const [searchList, setsearchList] = useState([]);
    const [failedCount, setfailedCount] = useState(0);

    const RowsPerPage = 10;
    const [startRecord, setstartRecord] = useState(0);
    const [bottomLoading, setbottomLoading] = useState(false);
    const [isNoDataFound, setisNoDataFound] = useState(false);
    const [bottomReachTime, setbottomReachTime] = useState(new Date());
    const [selectedTab, setselectedTab] = useState("ACCOUNT");

    const [selectedProfileSeqs, setselectedProfileSeqs] = useState([]);
    const [selectedProfileList, setselectedProfileList] = useState([]);

    const [showTabView, setShowTabView] = useState(false)

    useEffect(() => {
        getSearchResult("", 0, RowsPerPage, true);
    }, [])


    useEffect(() => {
        delay2 = setTimeout(() => {
            if (!isNoDataFound) {
                if (searchQuery.length != 0) {
                    let stRec = startRecord + RowsPerPage;
                    if (searchList.length < stRec) {
                        getSearchResult(searchQuery, stRec, RowsPerPage);
                    }
                }
            }
        }, 500)
        return () => clearTimeout(delay2)
    }, [searchList]);

    useEffect(() => {
        delay = setTimeout(() => {
            if (searchQuery) {
                if (searchQuery.length >= 2) {
                    setshowLoading(true);
                    getSearchResult(searchQuery, 0, RowsPerPage);
                }
            }
        }, 500)
        return () => clearTimeout(delay)
    }, [searchQuery])

    const onChangeSearch = query => {
        if (query.length != 0) {
            setisTyped(true);
        }
        else {
            setisTyped(false);
        }
        setSearchQuery(query);
    }
    const cancelBtnClick = () => {
        tagPeopleSearchPress("CANCEL", {})
    }
    const peopleRowPress = (profileSeq) => {
        let sList = searchList;
        sList.map(obj => {
            if (obj.profile_seq == profileSeq) {
                if (!obj.isChecked) {
                    obj.isChecked = true;
                    addValueToArray(profileSeq, obj);
                }
                else {
                    removeValueFromArray(profileSeq);
                    obj.isChecked = false;
                }
            }
        });
    }
    const renderSearchRow = ({ item }) => {
        return (
            <TagPeopleProfile data={item} isChecked={item.isChecked} navigation={navigation}
                peopleRowPress={peopleRowPress} />
        );
    };
    function getSearchResult(query, stRecord, rowsPerPage, firstTime = false) {
        setstartRecord(stRecord)
        let hashMap = {
            _action_code: "11:GET_USER_TO_TAG",
            _start_row: stRecord,
            _rows_page: rowsPerPage,
        }
        if (query.length != 0) {
            hashMap.search_str = query;
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false);
            setbottomLoading(false);
            data.data.map(item => item.isChecked = false);
            let list = searchList;
            if (parseInt(stRecord) == 0) {
                list = data.data;
                // console.log("IF", list);
            }
            else {
                list = list.concat(data.data);
                // console.log("ELSE", list);
            }
            let filterList = [];
            list.map(obj => {
                if (!selectedPList.includes(obj.profile_seq)) {
                    filterList.push(obj);
                }
            });
            setShowTabView(true);
            seterrorMsg("");
            seterrorMsgKey(Math.random())
            setsearchList(filterList);
            if (filterList.length == 0) {

                seterrorMsg("We don’t have this.");
                seterrorMsgKey(Math.random())
                setShowTabView(false);
            }
            // console.log("filterList", filterList.length);
            // console.log("startRecord", stRecord);
            // console.log("RowsPerPage", rowsPerPage);

            // if (filterList.length < RowsPerPage) {
            //     if (searchQuery === query) {
            //         let stRec = stRecord + RowsPerPage;
            //         setTimeout(() => {
            //             getSearchResult(query, stRec, RowsPerPage);
            //         }, 500);
            //     }
            // }

            setisNoDataFound(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                setbottomLoading(false);
                setisNoDataFound(true);
                if (parseInt(stRecord) == 0) {
                    if (firstTime) {
                        seterrorMsg("We don’t have this.");
                        seterrorMsgKey(Math.random())
                    }
                    else {
                        setShowTabView(false);
                        setsearchList([]);
                        let errrMsg2 = "";
                        let failedCountV = failedCount + 1;
                        if (failedCount === 0) {
                            errrMsg2 = "Are you sure?";
                        }
                        else if (failedCount === 1) {
                            errrMsg2 = "We don’t have this.";
                        }
                        else {
                            failedCountV = 0;
                            errrMsg2 = "Ask again ";
                        }
                        setfailedCount(failedCountV);
                        seterrorMsg(errrMsg2);
                        seterrorMsgKey(Math.random());
                    }

                }
                else {
                    setShowTabView(true);
                }
            }
        });
    }
    const handleEndRefresh = () => {
        if (!isNoDataFound) {
            let currentTime = new Date();
            let diffTime = getSecondsBetweenDates(bottomReachTime, currentTime);
            if (diffTime > 4) {
                let startRec = startRecord + RowsPerPage;
                setbottomLoading(true);
                getSearchResult(searchQuery, startRec, RowsPerPage);
                setbottomReachTime(new Date());
            }

        }
    }

    const onTabChange = (item) => {
        setselectedTab(item);
    }
    const addValueToArray = (value, object) => {
        let selProSeqs = selectedProfileSeqs;
        let selProList = selectedProfileList;
        selProSeqs.push(value);
        selProList.push(object);
        setselectedProfileSeqs(selProSeqs);
        setselectedProfileList([...[], ...selProList]);
        refreshSearchListAfterAddRemove(selProSeqs);

    }
    const removeValueFromArray = (value) => {
        const selProSeqs = [...selectedProfileSeqs]
        const selProList = [...selectedProfileList]
        const seqsIndex = selectedProfileSeqs.findIndex((pSeq) => pSeq === value);
        const listIndex = selectedProfileList.findIndex((obj2) => obj2.profile_seq === value);
        selProSeqs.splice(seqsIndex, 1);
        selProList.splice(listIndex, 1);
        setselectedProfileSeqs(selProSeqs);
        setselectedProfileList([...[], ...selProList]);
        refreshSearchListAfterAddRemove(selProSeqs);
    }
    const refreshSearchListAfterAddRemove = (selProSeqs) => {
        let sList = searchList;
        sList.map(objV => {
            if (selProSeqs.includes(objV.profile_seq)) {
                objV.isChecked = true;
            }
            else {
                objV.isChecked = false;
            }
        })
    }
    const removeSelectPeople = (pSeq) => {
        removeValueFromArray(pSeq);
    }
    const renderSelectedItem = ({ item }) => {
        return (
            <View style={style.selFullTagBox}>
                <View style={{ ...style.selTagBox, }}>
                    <ProgressiveImage
                        style={style.selTagBoxImage}
                        source={hasImageUrlExist(item.profile_picture) ? { uri: item.profile_picture } : null}
                        defaultImageSource={require('../../assets/Images/full_user_image_place_holder.png')}
                        resizeMode={'cover'}
                    />
                </View>
                <EntutoTextView style={style.selTagBoxName}>
                    {((item.display_name).length > MaxStoryTxtLimit) ?
                        (((item.display_name).substring(0, MaxStoryTxtLimit - 3)) + '...') :
                        item.display_name}

                </EntutoTextView>
                <View style={style.crossIconBox}>
                    <TouchableOpacity
                        onPress={() => removeSelectPeople(item.profile_seq)}>
                        <Image
                            source={require('../../assets/Images/icon/close_icon.png')}
                            style={style.crossIconBoxIcon}
                        />
                    </TouchableOpacity>

                </View>
            </View>
        );
    }
    const saveBtnPress = () => {
        tagPeopleSearchPress("SELECTED", { profileSeqs: selectedProfileSeqs, listData: selectedProfileList })
    }
    const clearSearchTxt = () => {
        setSearchQuery("");
        setisTyped(false);
        setsearchList([]);
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={defaultStyle.container}>
                <View style={style.searchBarBox}>
                    <View
                        style={style.searchBar}>
                        <Image
                            source={require('../../assets/Images/icon/search_icon.png')}
                            style={style.searchIcon}
                        />
                        <TextInput
                            numberOfLines={1}
                            style={style.input}
                            placeholder="Search by name, username or category!"
                            placeholderTextColor={theme.colors.inputPlaceholderColor}
                            value={searchQuery}
                            autoCorrect={false}
                            autoFocus={true}
                            onChangeText={text => onChangeSearch(text)}
                            selectionColor={theme.colors.primaryColor}
                        />
                        {isTyped && (
                            <TouchableOpacity onPress={() => clearSearchTxt()}>
                                <Image
                                    source={require('../../assets/Images/icon/close_icon.png')}
                                    style={style.searchCrossIcon}
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                    <TouchableOpacity onPress={() => cancelBtnClick()}>
                        <HeadingTxt style={{ marginLeft: 13 }}>CANCEL</HeadingTxt>
                    </TouchableOpacity>
                </View>
            </View>
            {
                selectedProfileList.length != 0 ?
                    <View style={{ flexDirection: 'row', }}>
                        <FlatList
                            style={{ marginRight: 6, zIndex: 1000 }}
                            data={selectedProfileList}
                            renderItem={renderSelectedItem}
                            keyExtractor={(item, index) => `${index}`}
                            horizontal
                            showsHorizontalScrollIndicator={false} />
                        <Pressable
                            onPress={() => saveBtnPress()}
                            android_ripple={{ color: theme.colors.pressableRippleColor, borderless: false }}>
                            <View style={style.saveIconBox}>
                                <MaterialIcons name="save" size={30} style={style.saveIcon} />
                            </View>
                        </Pressable>

                    </View>
                    : null
            }

            {
                showTabView ?
                    <View style={{ flexDirection: 'row', marginBottom: 10 }}>
                        <TouchableOpacity
                            style={[{ paddingHorizontal: 12, }, selectedTab === "ACCOUNT" ? {
                                borderBottomWidth: theme.dimensions.tabBorderBottomWidth,
                                borderBottomColor: theme.colors.tabActiveBootomBorderColor,
                            } : null]}
                            onPress={() => onTabChange("ACCOUNT")}>
                            <Text allowFontScaling={false} style={[defaultStyle.tabBarLabelTxt, {
                                color: theme.colors.primaryTextColor,
                                paddingBottom: 10
                            }, selectedTab === "ACCOUNT" ? {
                                color: theme.colors.tabActiveColor
                            } : null]}>
                                Accounts
                            </Text>
                        </TouchableOpacity>
                    </View>
                    : null
            }

            <View>
                {
                    showLoading ?
                        <CustomActivityIndicator progress={showLoading} />
                        : null
                }
                {
                    errorMsg.length != 0 ?
                        <View style={{ ...defaultStyle.errorBoxOutside, marginTop: 10, minHeight: 30 }} >
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsgKey} />
                        </View>
                        : null
                }
            </View>

            <FlatList
                contentContainerStyle={{ paddingBottom: 20, zIndex: 1000 }}
                data={searchList}
                renderItem={renderSearchRow}
                keyExtractor={(item, index) => `${index}`}
                onEndReached={handleEndRefresh}
                initialNumToRender={10}

            />
            {
                bottomLoading ?
                    <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 15 }}>
                        <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={'large'} />
                    </View>
                    : null
            }
        </>
    )
}

export default TagPeopleSearchComponent

const styles = theme => StyleSheet.create({
    searchBarBox: {
        alignItems: "center",
        flexDirection: "row",
        marginTop: 8,
    },
    searchBar: {
        flex: 1,
        flexDirection: "row",
        backgroundColor: theme.colors.searchBarBackground,
        borderRadius: theme.dimensions.searchBarRadius,
        alignItems: "center",
    },
    searchIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        marginLeft: theme.dimensions.searchInputIconMH,
    },
    input: {
        fontSize: theme.calculateFontSize(theme.dimensions.searchTextInputSize),
        marginHorizontal: 8,
        flex: 1,
        color: theme.colors.inputTextColor,
        minHeight: 56,
    },
    searchCrossIcon: {
        height: 15,
        width: 15,
        marginRight: 8,
    },
    selFullTagBox: {
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 10,
    },
    selTagBox: {
        position: 'relative',
        width: 56,
        height: 56,
        borderRadius: 15,
        marginRight: 5,
        // overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center'
    },
    selTagBoxImage: {
        width: 52,
        height: 52,
        borderRadius: 15,
        borderColor: '#FFFFFF',
        borderWidth: 2,
    },
    selTagBoxName: {
        fontSize: theme.calculateFontSize(theme.dimensions.selectedTagNameText),
        color: theme.colors.searchBarText,
        marginTop: 6,

    },
    crossIconBox: {
        position: 'absolute',
        bottom: 20,
        right: 6,
        backgroundColor: '#CCC',
        borderRadius: 20,
        padding: 6
    },
    crossIconBoxIcon: {
        height: 15,
        width: 15,
    },
    saveIconBox: {
        borderWidth: 1,
        flex: 1,
        paddingHorizontal: 10,
        justifyContent: 'center',
        // borderLeftWidth: 1,
        borderColor: theme.colors.borderBottomColor
    },
    saveIcon: {
        color: theme.colors.primaryColor
    }
})