// HomePlaylistCard.js
import {
  Dimensions,
  Image,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useRef, useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
} from 'react-native-reanimated';
import EntutoTextView from '../common/EntutoTextView';
import {checkValueLength, onMaxTextLengthReached} from '../../utils/Utils';
import {MaxPlaylistGridTxtLimit} from '../../utils/Appconfig';
import LinearGradient from 'react-native-linear-gradient';
import HomeCarousel from './HomeCarousel';
import AnimatedPlaylistItem from './AnimatedPlaylistItem'; // new component we'll create

const screenWidth = Dimensions.get('screen').width;
const ITEM_SIZE = 184;
const EMPTY_ITEM_SIZE = (screenWidth - ITEM_SIZE) / 2;
const HOME_IMAGE_HEIGHT = 326;

const HomePlaylistCard = ({itemData, homPlaylistClick}) => {
  const flatListRef = useRef(null);
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const [playListData] = useState(itemData.show_list);
  const [newData] = useState([
    {key: 'spacer-left'},
    ...playListData,
    {key: 'spacer-right'},
  ]);

  const scrollX = useSharedValue(0);

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      scrollX.value = event.contentOffset.x;
    },
  });

  return (
    <View>
      <HomeCarousel
        itemData={itemData.show_list}
        homPlaylistClick={homPlaylistClick}
      />

      {/* <View style={style.container}>
        <Animated.FlatList
          ref={flatListRef}
          keyboardShouldPersistTaps={'handled'}
          showsHorizontalScrollIndicator={false}
          data={newData}
          keyExtractor={(item, index) => `${index}`}
          horizontal
          bounces={false}
          pagingEnabled
          decelerationRate={'fast'}
          renderToHardwareTextureAndroid
          contentContainerStyle={{alignItems: 'center'}}
          snapToInterval={ITEM_SIZE}
          snapToAlignment="center"
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          renderItem={({item, index}) => (
            <AnimatedPlaylistItem
              item={item}
              index={index}
              scrollX={scrollX}
              ITEM_SIZE={ITEM_SIZE}
              EMPTY_ITEM_SIZE={EMPTY_ITEM_SIZE}
              onPress={homPlaylistClick}
            />
          )}
        />
      </View> */}
    </View>
  );
};

export default HomePlaylistCard;

const styles = theme =>
  StyleSheet.create({
    container: {
      height: HOME_IMAGE_HEIGHT + 60,
      backgroundColor: theme.colors.backgroundColor,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    posterImage: {
      width: 184,
      height: 326,
      resizeMode: 'cover',
      borderRadius: 7,
      marginBottom: 10,
    },
    textBox: {
      position: 'absolute',
      bottom: 8,
      left: 8,
      right: 8,
      alignItems: 'center',
    },
    textBoxValue: {
      color: '#FFFFFF',
      fontSize: theme.calculateFontSize(12),
      textAlign: 'center',
      paddingHorizontal: 10,
      textShadowColor: 'rgba(0, 0, 0, 0.75)',
      textShadowOffset: {width: -1, height: 1},
      textShadowRadius: 2,
    },
    linearGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
  });
