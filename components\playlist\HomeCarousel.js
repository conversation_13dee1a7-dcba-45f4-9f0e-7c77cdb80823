import {
  Dimensions,
  ImageBackground,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import React, {useCallback} from 'react';
import Carousel from 'react-native-reanimated-carousel';
import {parallaxLayout} from './parallax';
import {checkValueLength, onMaxTextLengthReached} from '../../utils/Utils';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import EntutoTextView from '../common/EntutoTextView';
import {MaxPlaylistGridTxtLimit} from '../../utils/Appconfig';
import {useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import PlayBtnIcon from '../../assets/Images/icon/Play_Icon_Filled.png';

const {width: screenWidth} = Dimensions.get('window');
// Modified dimensions for horizontal cards
const HOME_IMAGE_HEIGHT = 184;
const HOME_IMAGE_WIDTH = 326;

const HomeCarousel = ({itemData}) => {
  const navigation = useNavigation();
  const renderItem = useCallback(({item, index}) => {
    return <CardItem index={index} item={item} navigation={navigation} />;
  }, []);
  console.log('itemData', itemData.length);
  return (
    <View>
      <Carousel
        keyboardShouldPersistTaps={'handled'}
        useNativeDriver={false}
        width={screenWidth}
        height={HOME_IMAGE_HEIGHT + 30}
        enableMomentum={false}
        panGestureHandlerProps={{
          activeOffsetX: [-10, 10],
        }}
        data={itemData}
        mode="parallax"
        vertical={false}
        snapEnabled={true}
        autoPlayInterval={1500}
        autoPlay={false}
        loop
        customAnimation={parallaxLayout(
          {
            size: screenWidth,
            vertical: false,
          },
          {
            parallaxScrollingScale: 1,
            parallaxAdjacentItemScale: 0.8,
            parallaxScrollingOffset: HOME_IMAGE_WIDTH / 3, // Adjusted offset for horizontal layout
          },
        )}
        renderItem={renderItem}
      />
    </View>
  );
};

const CardItem = ({index, item, navigation}) => {
  const style = useSThemedStyles(styles);
  const titleVal = checkValueLength(item.grid_title)
    ? item.grid_title
    : item.title;

  const cardItemClick = showSeq => {
    navigation.navigate('PlaylistSeasonScreen', {
      showSeq: showSeq,
    });
  };

  return (
    <View
      style={{
        height: HOME_IMAGE_HEIGHT + 16,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <Pressable onPress={() => cardItemClick(item.show_seq)}>
        <View style={style.imageContainer}>
          <ImageBackground
            source={{
              uri: checkValueLength(item.home_file)
                ? item.home_file
                : item.thumb_file,
            }}
            style={{
              width: HOME_IMAGE_WIDTH,
              height: HOME_IMAGE_HEIGHT,
              resizeMode: 'cover',
            }}>
            {/* Centered Play Button Overlay */}
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                justifyContent: 'center',
                alignItems: 'center',
                zIndex: 2,
              }}>
              <View
                style={{
                  height: 63,
                  width: 63,
                  // backgroundColor: '#00000080',
                  borderRadius: 15,
                  justifyContent: 'center',
                  alignItems: 'center',
                  //   shadowColor: '#000',
                  //   shadowOffset: {width: 0, height: 1},
                  //   shadowOpacity: 0.5,
                  //   shadowRadius: 1.41,
                }}>
                <Image
                  source={PlayBtnIcon}
                  style={{
                    width: '100%',
                    height: '100%',
                    backgroundColor: 'transparent',
                  }}
                  resizeMode="cover"
                />
              </View>
            </View>
            <LinearGradient
              colors={['#11111100', '#11111100', '#11111100', '#111111']}
              style={style.linearGradient}
            />
            <View style={style.textBox}>
              <EntutoTextView style={style.textBoxValue}>
                {onMaxTextLengthReached(titleVal, MaxPlaylistGridTxtLimit, '')}
              </EntutoTextView>
            </View>
          </ImageBackground>
        </View>
      </Pressable>
    </View>
  );
};

export default HomeCarousel;

const styles = theme =>
  StyleSheet.create({
    imageContainer: {
      overflow: 'hidden',
      position: 'relative',
      borderRadius: 8, // Optional: added rounded corners for a modern look
    },
    textBox: {
      position: 'absolute',
      bottom: 10,
      left: 12,
      right: 12,
      alignItems: 'flex-start', // Changed to align text to the left for horizontal cards
    },
    textBoxValue: {
      color: theme.colors.homeShowCardText,
      fontSize: theme.calculateFontSize(14), // Slightly increased font size
      textAlign: 'left', // Left-aligned text for horizontal cards
      paddingHorizontal: 8,
      textShadowColor: theme.colors.homeShowCardTextShadow,
      textShadowOffset: {width: -1, height: 1},
      textShadowRadius: 2,
    },
    linearGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
  });
