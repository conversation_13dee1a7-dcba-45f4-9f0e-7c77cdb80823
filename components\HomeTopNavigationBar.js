import React from 'react'
import { StyleSheet, Text, View, Image, TouchableOpacity, Pressable } from 'react-native'
import HeadingTxt from './common/HeadingTxt';
import useSThemedStyles from '../theme/useSThemedStyles';
import useSTheme from '../theme/useSTheme';

const HomeTopNavigationBar = ({ title = "", showBackBtn = true, extrabackBtn = false, extrabackBtnPress = null,
    showTopButton = false, buttonComponent = null, showBorderBottom = false, navigation, ...props }) => {
    const style = useSThemedStyles(styles);
    const goBackPrevious = () => {
        if (!extrabackBtn) {
            if (navigation.canGoBack()) {
                navigation.goBack(null);
            }
            else {
                navigation.replace("HomeScreen");
            }

        }
        else {
            extrabackBtnPress();
        }

    }
    const theme = useSTheme();
    return (
        <View style={[style.appBar, showBorderBottom && style.borderBottom]} >
            <View style={{ height: 48, justifyContent: 'center' }}>
                {
                    showBackBtn ?
                        <View style={{ zIndex: 1000, marginStart: 10, marginTop: 10, }}>
                            <Pressable onPress={() => goBackPrevious()}
                                android_ripple={{
                                    color: theme.colors.pressableRippleColor, borderless: true,
                                    radius: 30,
                                }}
                                style={{}}>
                                <View style={{ paddingHorizontal: 12, }}>
                                    <Image style={style.arrowIcon}
                                        resizeMode="cover"
                                        source={require('../assets/Images/icon/Arrow.png')} />
                                </View>
                            </Pressable>
                        </View>


                        : null
                }
            </View>
            <View style={style.titleContainer} >
                <View>
                    <HeadingTxt style={{ color: theme.colors.topHeaderColor, fontWeight: 'bold' }}>{title}</HeadingTxt>
                </View>
                {
                    showTopButton ?
                        <View style={{ marginLeft: 'auto', }}>
                            {buttonComponent}
                        </View>
                        : null
                }
            </View>
        </View >
    )
}

export default HomeTopNavigationBar

const styles = theme => StyleSheet.create({
    appBar: {
        flexDirection: "column",
        alignItems: "flex-start",
        paddingRight: 8,
        backgroundColor: theme.colors.appBarBackgroundColor,

    },
    borderBottom: {
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.topHeaderBottomColor,
    },
    arrowIcon: {
        height: 20,
        width: 12,
        tintColor: theme.colors.topHeaderColor

    },
    titleContainer: {
        height: 56,
        width: '100%',
        flexDirection: "row",
        alignItems: 'center',
        paddingStart: 15,
        marginBottom: 12,
    }
})
