import React, { useContext, useEffect, useRef, useState } from 'react'
import { Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import EntutoEditText from '../common/EntutoEditText';
import EntutoTextView from '../common/EntutoTextView';
import { Dropdown } from 'react-native-element-dropdown';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import BottomSheetLoader from '../common/BottomSheetLoader';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import appData from '../../data/Data';
import { CountryList } from '../../constants/Constants';
import SelectBoxComponent from '../common/SelectBoxComponent';
import { AppStateContext } from '../..';
import ErrorMessages from '../../constants/ErrorMessages';
import CustomSnackbar from '../common/CustomSnackbar';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import { PopupNegativeButton, PopupPositiveButton } from '../common/PopupButton';

const UpdateUserLocationComponent = ({ navigation, ...props }) => {
    const { changeUserDetails } = useContext(AppStateContext);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [countryVal, setcountryVal] = useState("");
    const [countryData, setcountryData] = useState(CountryList);

    const [stateVal, setstateVal] = useState("");
    const [stateData, setstateData] = useState([]);
    const [showStateField, setshowStateField] = useState(false);


    const [popupHeading, setpopupHeading] = useState("Update User Location");
    const [submitBtnTxt, setsubmitBtnTxt] = useState("Update");

    const [showLoading, setshowLoading] = useState(false);
    const [showSuccessMsg, setshowSuccessMsg] = useState(false);
    const [successMsg, setsuccessMsg] = useState("");

    const [errorMsg, seterrorMsg] = useState("");
    const [errorMsgRef, seterrorMsgRef] = useState(Math.random());

    const modalFlashRef = useRef(null);
    function getStateListService(countryCodeType) {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: countryCodeType,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

            setstateData(data.data);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setstateData([]);
            }
        });
    }
    const cancelBtnPress = () => {
        props.updateUserLocationPopupPress("negetive", {})
    }
    const submitBtnPress = () => {
        let isFormValid = true;
        if (countryVal === null || countryVal.length === 0) {
            seterrorMsg(ErrorMessages.countryNameErr);
            seterrorMsgRef(Math.random())
            isFormValid = false;
        }
        if (stateVal === null || stateVal.length === 0) {
            seterrorMsg(ErrorMessages.stateNameErr);
            seterrorMsgRef(Math.random())
            isFormValid = false;
        }
        if (isFormValid) {
            setshowLoading(true);
            updateUserLocationService();
        }
    }
    function updateUserLocationService() {
        let hashMap = {
            _action_code: "11:UPDATE_USER_LOCATION",
            country: countryVal,
            state: stateVal,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            let hasStateCity = "NO";
            if (stateVal !== null) {
                if (stateVal.length !== 0) {
                    hasStateCity = "YES";
                }
            }
            let userDetails = {
                _has_state_city: hasStateCity,
            }
            changeUserDetails(userDetails);
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true)
            setshowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.country) {
                            seterrorMsg(data.data.country);
                            seterrorMsgRef(Math.random())
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.state) {
                            seterrorMsg(data.data.state);
                            seterrorMsgRef(Math.random())
                            fieldErrorShown = true;
                            return;
                        }

                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage);
                    seterrorMsgRef(Math.random())
                }

            }
        });
    }
    const closeBtnClick = () => {
        props.updateUserLocationPopupPress("close", { erMsg: successMsg })
    }
    const [countryModalVisible, setcountryModalVisible] = useState(false);
    const selectCountryBoxClick = (clickID, obj) => {
        setcountryModalVisible(false);
        if (clickID == "DONE") {
            if (obj.selectedItem == "INDIA") {
                getStateListService("STATE_INDIA");
                setshowStateField(true);
            }
            else {
                setshowStateField(false);
            }
            setcountryVal(obj.selectedItem);
        }
    }
    const [stateModalVisible, setstateModalVisible] = useState(false);
    const selectStateBoxClick = (clickID, obj) => {
        setstateModalVisible(false);
        if (clickID == "DONE") {
            setstateVal(obj.selectedItem);
        }
    }

    return (
        <View>
            <View style={{ ...defaultStyle.popupBox, }}>

                <EntutoTextView style={{ ...defaultStyle.popupHeadTxt, marginBottom: 20 }}>{popupHeading}</EntutoTextView>
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                {
                    showSuccessMsg ?
                        <BottomSheetSuccessMsg successMsg={successMsg} cancelBtnClick={() => closeBtnClick()} />
                        : null
                }
                {/* {
                    errorMsg.length != 0 ?
                        // <View style={defaultStyle.errorBoxOutside}>
                        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                        // </View>
                        : null
                } */}
                <CustomSnackbar snackMsg={errorMsg} displaySnackbar={errorMsg.length != 0}
                    refreshSnack={errorMsgRef} showInsideFlashRef={true} insideFlashRef={modalFlashRef} />
                <Modal
                    animationType="fade"
                    visible={countryModalVisible}
                    style={{ margin: 0, flex: 1 }}>
                    <SelectBoxComponent
                        selectBoxClick={selectCountryBoxClick}
                        list={countryData}
                        selectedValue={countryVal}
                        title="Select Country"
                        maxSelectedValue={1}
                        multiSelect={false}
                        labelField="label"
                        valueField="value"
                    />
                </Modal>
                <TouchableOpacity onPress={() => setcountryModalVisible(true)} >
                    <View style={defaultStyle.customDropdown}>

                        <View style={defaultStyle.customMultiSelectBox}>
                            {
                                countryVal.length != 0 ?
                                    <EntutoTextView style={defaultStyle.customMultiSelectSingleTxt}>{countryVal}</EntutoTextView>
                                    :
                                    <EntutoTextView style={defaultStyle.customMultiSelectSinglePH}>Select Country</EntutoTextView>
                            }
                        </View>
                        <MaterialIcons style={defaultStyle.dropdownIcon}
                            color={theme.colors.dropdownInActiveColor}
                            name="keyboard-arrow-right" size={theme.dimensions.dropdownRightIcon} />

                    </View>
                </TouchableOpacity>
                <View style={{ ...defaultStyle.inputUnderLineView, marginTop: 0, marginBottom: 20 }}>
                    <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                        Where are you? A secret only we know!
                    </EntutoTextView>
                </View>
                {
                    showStateField ?
                        <>
                            <Modal
                                animationType="fade"
                                visible={stateModalVisible}
                                style={{ margin: 0, flex: 1 }}>
                                <SelectBoxComponent
                                    selectBoxClick={selectStateBoxClick}
                                    list={stateData}
                                    selectedValue={stateVal}
                                    title="Select State"
                                    maxSelectedValue={1}
                                    multiSelect={false}
                                    labelField="display_value"
                                    valueField="config_key"
                                />
                            </Modal>
                            <TouchableOpacity onPress={() => setstateModalVisible(true)}>
                                <View style={defaultStyle.customDropdown}>

                                    <View style={defaultStyle.customMultiSelectBox}>
                                        {
                                            stateVal.length != 0 ?
                                                <EntutoTextView style={defaultStyle.customMultiSelectSingleTxt}>{stateVal}</EntutoTextView>
                                                :
                                                <EntutoTextView style={defaultStyle.customMultiSelectSinglePH}>Select State</EntutoTextView>
                                        }
                                    </View>
                                    <MaterialIcons style={defaultStyle.dropdownIcon}
                                        color={theme.colors.dropdownInActiveColor}
                                        name="keyboard-arrow-right" size={theme.dimensions.dropdownRightIcon} />

                                </View>
                            </TouchableOpacity>
                            <View style={{ ...defaultStyle.inputUnderLineView, marginBottom: 20 }}>
                                <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                    {`Don’t wait it, State it!`}
                                </EntutoTextView>
                            </View>
                        </>
                        : null
                }

                <View style={{ flexDirection: 'row', flex: 1, marginTop: 16, marginBottom: 15, }}>
                    <View style={{ flex: 1 }}>
                        <PopupNegativeButton
                            onPress={() => cancelBtnPress()}
                            btnText='No'
                            style={{ marginEnd: theme.dimensions.popupBtnGap }} />

                    </View>
                    <View style={{ flex: 1 }}>
                        <PopupPositiveButton
                            onPress={() => submitBtnPress()}
                            btnText={submitBtnTxt} />

                    </View>
                </View>
            </View>

        </View>
    )
}

export default UpdateUserLocationComponent;

const styles = StyleSheet.create({})
