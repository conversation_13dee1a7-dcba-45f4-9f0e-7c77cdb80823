import React, { useRef } from 'react'
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import ActionSheet from 'react-native-actions-sheet';
import EntutoTextView from '../common/EntutoTextView';
import CopyLinkActionView from '../post/CopyLinkActionView';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import useDefaultStyle from '../../theme/useDefaultStyle';


const ThreeDotMenuProfileActionView = ({ isMyProfile, profileSeq, isBlock, isRestrict, showRestrictAccount, ThreeDotMenuPress }) => {
    const copyLinkBtnRef = useRef(null);
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    const copyLinkBtnPress = () => {
        copyLinkBtnRef.current?.show();
    }
    const restrictBtnPress = () => {
        if (isRestrict) {
            ThreeDotMenuPress("UN_RESTRICT", {});
        }
        else {
            ThreeDotMenuPress("RESTRICT", {});
        }
    }
    const blockBtnPress = () => {
        if (isBlock) {
            ThreeDotMenuPress("UN_BLOCK", {});
        }
        else {
            ThreeDotMenuPress("BLOCK", {});
        }
    }
    const shareLinkBtnPress = () => {
        ThreeDotMenuPress("SHARE", {});
    }

    return (
        <>
            <View style={{ ...defaultStyle.popupBox, }}>
                <View style={style.popupListRowBox}>
                    <TouchableOpacity style={style.linkBox}
                        onPress={() => copyLinkBtnPress()}>
                        <EntutoTextView style={style.linkTxt}>Copy link</EntutoTextView>
                    </TouchableOpacity>
                </View>
                <View style={style.popupDivider} />
                <View style={style.popupListRowBox}>
                    <TouchableOpacity style={style.linkBox}
                        onPress={() => shareLinkBtnPress()}>
                        <EntutoTextView style={style.linkTxt}>Share</EntutoTextView>
                    </TouchableOpacity>
                </View>
                <View style={style.popupDivider} />
                {
                    !isMyProfile ?
                        <>
                            {/* {
                                showRestrictAccount ?
                                    <View style={style.popupListRowBox}>
                                        <TouchableOpacity style={style.linkBox}
                                            onPress={() => restrictBtnPress()}>
                                            <EntutoTextView style={style.linkTxt}>
                                                {
                                                    isRestrict ?
                                                        "Un-Restrict Account" : "Restrict Account"
                                                }
                                            </EntutoTextView>
                                        </TouchableOpacity>
                                    </View>
                                    : null
                            } */}


                            <View style={style.popupDivider} />
                            <View style={style.popupListRowBox}>
                                <TouchableOpacity style={style.linkBox}
                                    onPress={() => blockBtnPress()}>
                                    <EntutoTextView style={style.linkTxt}>
                                        {
                                            isBlock ?
                                                "Un-Block Account" : "Block Account"
                                        }
                                    </EntutoTextView>
                                </TouchableOpacity>
                            </View>
                            <View style={style.popupDivider} />
                        </>
                        : null
                }

            </View>
            <ActionSheet ref={copyLinkBtnRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                closeOnTouchBackdrop={true}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        copyLinkBtnRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <CopyLinkActionView copyLinkID={profileSeq} copyLinkType="PROFILE" />
                </ScrollView>
            </ActionSheet >

        </>
    )
}

export default ThreeDotMenuProfileActionView

const styles = theme => StyleSheet.create({
    popupBox: {
        paddingHorizontal: 16,
        paddingBottom: 16,
        backgroundColor: theme.colors.backgroundColor,
    },
    popupDivider: {
        height: 0.5,
        backgroundColor: theme.colors.threeDotMenuDivider,
        opacity: 0.5,
    },
    popupListRowBox: {

    },
    linkBox: {
        paddingVertical: 15,
    },
    linkTxt: {
        color: theme.colors.threeDotMenuText,
        fontSize: theme.calculateFontSize(theme.dimensions.threeDotMenuText),

    },
})
