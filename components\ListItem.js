import React from 'react'
import { Image, StyleSheet, View, TouchableOpacity } from 'react-native'
import EntutoTextView from './common/EntutoTextView'
import useDefaultStyle from '../theme/useDefaultStyle'
import useSTheme from '../theme/useSTheme'
import useSThemedStyles from '../theme/useSThemedStyles'

const ListItem = ({ label, showIcon = false, icon = "",
    showBottomtxt = false, bottomTxt = "", textstyle = {}, ...props }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    return (
        <View style={{ flexDirection: 'column', }}>
            <View style={{
                ...style.listRow,
                paddingBottom: showBottomtxt ? 0 : 10
            }}>
                {/* {
                    showIcon ?
                        <Image
                            style={style.listRowIcon}
                            source={icon}
                            resizeMode='contain'

                            {...props}
                        />
                        : null
                } */}

                <EntutoTextView style={{ ...style.listRowTxt, ...textstyle }}>{label}</EntutoTextView>
            </View>
            {
                showBottomtxt ?
                    <View style={{ ...defaultStyle.underInputTxtBox, ...style.extraPad }}>
                        <EntutoTextView style={defaultStyle.underInputTxt}>
                            {bottomTxt}
                        </EntutoTextView>
                    </View>
                    : null
            }

            {/* <View style={{ backgroundColor: theme.colors.borderBottomColor, height: 1, }} /> */}
        </View>
    )
}

export default ListItem

const styles = theme => StyleSheet.create({
    listRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 20
    },
    listRowTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.listItemText),
        color: theme.colors.listRowTextColor,
        fontWeight: '700',
    },
    listRowIcon: {
        width: 20,
        height: 20,
        marginEnd: 15,
        tintColor: theme.colors.listRowIconTintColor,
    },
    extraPad: {
        paddingBottom: 15,
    }
})
