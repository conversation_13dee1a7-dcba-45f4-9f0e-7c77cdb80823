import React from 'react'
import { StyleSheet, } from 'react-native'
import EntutoTextView from './EntutoTextView';
import useSTheme from '../../theme/useSTheme';

const HeadLineDownTxt = props => {
    const theme = useSTheme();
    return <EntutoTextView style={{
        ...styles.default,
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.HeadingLineTxt),
        ...props.style
    }}>{props.children}</EntutoTextView>
}

export default HeadLineDownTxt;

const styles = StyleSheet.create({
    default: {

    }
})
