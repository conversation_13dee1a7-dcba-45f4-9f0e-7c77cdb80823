import { StyleSheet, Text, View } from 'react-native'
import React, { useState } from 'react'
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import { Dropdown } from 'react-native-element-dropdown';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { checkValueLength } from '../../utils/Utils';

const EntutoDropdown = ({
    label = "",
    value = "",
    labelField = "label",
    valueField = "value",
    onOptionChange = null,
    options = [], placeholder = "",
    errorMsg = ""
}) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [dropdownFocus, setDropdownFocus] = useState(false);

    const dropdownLabelBox = (placeholderTxt, dropValue, valueFocus) => {
        if (dropValue || valueFocus) {
            return (
                <Text
                    allowFontScaling={false}
                    style={[defaultStyle.dropdownLabel,
                    valueFocus && { color: theme.colors.primaryColor }]}>
                    {placeholderTxt}
                </Text>
            );
        }
        return null;
    };
    const handleChange = (selectedVal) => {
        onOptionChange && onOptionChange(selectedVal)
    }
    const renderItem = item => {
        return (
            <View style={styles.dropdownItem}>
                <Text allowFontScaling={false} style={{ ...styles.dropdownItemText, fontSize: theme.calculateFontSize(theme.dimensions.dropdownFontText), color: theme.colors.dropdownTextColor }}>{item[labelField]}</Text>
                {
                    item[valueField] === value && <MaterialIcons
                        name="check-circle"
                        size={20}
                        color={theme.colors.primaryColor}
                        style={styles.icon} />
                }


            </View>
        );
    };
    return (
        <View style={{ ...defaultStyle.dropdownContainer, flexGrow: 1 }}>
            {dropdownLabelBox(label, value, dropdownFocus)}
            <Dropdown
                style={[defaultStyle.dropdownMain, dropdownFocus && {
                    borderColor: theme.colors.dropdownActiveColor,
                    borderWidth: theme.dimensions.dropdownActiveBorder
                }]}
                placeholderStyle={defaultStyle.dropdownPlaceholderStyle}

                inputSearchStyle={defaultStyle.dropdownInputSearchStyle}
                selectedTextStyle={{ ...defaultStyle.dropdownSelectedTextStyle, backgroundColor: theme.colors.backgroundColor }}
                containerStyle={{ backgroundColor: theme.colors.backgroundColor, borderWidth: 1, borderColor: theme.colors.primaryColor }}
                itemContainerStyle={{ backgroundColor: theme.colors.backgroundColor, borderWidth: 1, borderColor: theme.colors.primaryColor }}
                itemTextStyle={{ backgroundColor: theme.colors.backgroundColor, }}
                activeColor={theme.colors.backgroundColor}
                data={options}
                maxHeight={200}
                labelField={labelField}
                valueField={valueField}
                placeholder={placeholder}
                value={value}
                onFocus={() => setDropdownFocus(true)}
                onBlur={() => setDropdownFocus(false)}
                onChange={item => {
                    handleChange(item);
                    setDropdownFocus(false);
                }}
                renderRightIcon={() => (
                    <MaterialIcons style={defaultStyle.dropdownIcon} color={dropdownFocus
                        ? theme.colors.dropdownActiveColor : theme.colors.dropdownInActiveColor}
                        name="keyboard-arrow-down" size={theme.dimensions.dropdownRightIcon} />
                )}
                renderItem={renderItem}

            />
            {
                checkValueLength(errorMsg) ?
                    <View style={{ marginTop: 8 }}>
                        <Text allowFontScaling={false} style={{ color: theme.colors.errorColor }}>{errorMsg}</Text>
                    </View>
                    : null
            }

        </View>
    )
}

export default EntutoDropdown

const styles = StyleSheet.create({
    dropdownItem: {
        padding: 14,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottomColor: "#CCC",
        borderBottomWidth: 1,

    },
    dropdownItemText: {
    }
})