export const DEFAULT_THEME_COLOR = {
    primaryColor: '#FC6767',
    accentColor: "#FC6767",//#74329C
    primaryTextColor: '#000000',

    loginModuleBackBtnTint: "#AAB2B7",

    textInputBackgroundColor: '#FFFFFF',

    oldLoginBackground: "#FC6767",

    // Check Color
    darkGray: "#646D7E",

    backgroundColor: '#E5E5E5',
    statusBarColor: '#E5E5E5',

    editTextBackgroundColor: 'transparent',

    appBarBackgroundColor: "transparent",
    topHeaderBottomColor: '#CCC',
    topHeaderColor: "#FC6767",

    mainHeadingColor: '#FC6767',

    extraBackgroundColor: '#F2EBE9',

    successColor: '#52c41a',
    errorColor: '#ff3232', //ff190c ff603d
    warningColor: '#ffbb33',
    //#bd6a4f
    infoColor: '#33b5e5',

    //Text Input color
    // inputPlaceholderColor: "rgba(60, 60, 67, 0.3)",
    inputPlaceholderColor: "#171F24",
    inputTextColor: "#000000",//"#FC6767"
    inputDisabledTextColor: "#00000023",
    inputPrimaryColor: "#FC6767",//#FC6767
    inputOutlineColor: "rgba(60, 60, 67, 0.3)",
    inputLabelColor: "#000000",

    formInputTextColor: "#000000",
    formBackgroundColor: "#FFFFFF",

    buttonBackColor: 'transparent',
    buttonTextColor: '#FC6767',
    buttonBorderColor: '#FC6767',

    darkGray: "#646D7E",

    rejectBoxColor: '#ff603d',

    iconTintColorDark: "#7F8A96",

    /////////////////////////////////////
    transparentColor: "transparent",
    placeholderBackgroundColor: '#ccc',
    placeholderForegroundColor: '#999',

    ///////////////////////////////////////
    tabActiveBootomBorderColor: '#FC6767',
    tabActiveColor: '#FC6767',
    tabInActiveColor: '#C5C5C7',

    dropdownActiveColor: '#FC6767',
    dropdownActiveBorder: '#FC6767',
    dropdownInActiveColor: 'grey',

    bottomSheetColor: '#FFFFFF',



    actionSPBackColor: "#FC6767",
    actionSPBackDisableColor: "#FC676740",
    actionSPColor: "#FFFFFF",

    actionSSBackColor: "#F2EBE9",
    actionSSBackDisableColor: "#F2EBE940",
    actionSSColor: "#FC6767",

    veriGradient1stColor: "#FDAA6A99",
    veriGradient2ndColor: "#FC6568",

    pressableColor: "#FC676750",
    bodyTextColor: '#000000',

    borderBottomColor: "#CCC",

    pressableRippleColor: "#FC676740",


    profileCatBorderColor: '#E59D80',
    profileCatActiveColor: '#E59D80',

    profileCatInActiveColor: '#C1C0C8',

    /////////////////////////
    listRowTextColor: '#FC6767',
    listRowIconTintColor: '#FC6767',

    cancelBtnBackground: "#F2EBE9",
    cancelBtnText: "#FC6767",
    cancelBtnBorder: "#F2EBE9",
    submitBtnBackground: '#FC6767',
    submitBtnBorder: '#FC6767',
    disableSubmitBtnBackground: '#FC676740',
    submitBtnText: '#FFFFFF',


    bottomSheetBtnBackground: '#FC6767',
    bottomSheetBtnText: '#FFFFFF',

    spinnerBackground: "#000",
    spinnerText: '#FFF',
    snackbarSuccessBackground: "#55e200",
    snackbarSuccessText: "#000000",
    snackbarFailureBackground: "#ff3232",
    snackbarFailureText: "#FFFFFF",

    /////////////
    googleSignBtnBackground: "#F2EBE9",
    googleSignBtnText: "#FC6767",

    ///////////////////
    loginSignupGradient1: '#E59D80',
    loginSignupGradient2: '#d89d89',
    loginSignupGradient3: '#759fd2',

    /////////////////
    notiHeaderText: "#171F24",
    notiHeaderViewAllText: "#AAB2B7",

    primaryButtonText: "#FFFFFF",
    //////////
    searchBarBackground: '#F2EBE9',
    searchBarText: '#FC6767',
    /////////////////
    modalBackground: "rgba(255, 255, 255, 0.1)",
    modalContainerBackground: "#FFFFFF",
    awesomeText: "#FC6767",
    awesomeBodyText1: "#000000",
    awesomeBodyText2: "#FC6767",
    shareItNowText: "#000000",
    shareSocialTintIcon: "#0000004D",

    //////////////
    errorBoxBackground: "#F2EBE9",
    errorBoxText: "#C5C5C7",

    /////////////
    followedBoxBackground: "#F2EBE9",
    followedBoxText: "#FC6767",
    introPopupIconTint: '#FFFFFF',

    notiProfileImageBoxBackground: "#DDDDDD",

    commentFooterContainerBackground: "#FFFFFF",
    commentInputBoxBorderColor: "#000000",
    postBtnColor: "#FC6767",
    commentsText: "#000000",

    threeDotMenuDivider: "#CCC",
    threeDotMenuText: "#FC6767",

    /////////////
    unlockPopupHeadText: '#FC6767',
    unlockPopupBodyTxt: '#000000',
    unlockPopupWarringTxt: '#52c41a',
    unlockPopupBtnBackground: "#F2EBE9",
    unlockPopupBtnText: "#FC6767",
    unlockPopupRefreshBtnText: "#FC6767",

    signUpBtnBackground: "#E59D80",

    postProgressBarBackground: "#030001",
    postTypeButtonBackground: "#FFFFFF",

    switchTextColor: "#000000",

    homeBottomTabActiveColor: "#FC6767",
    homeBottomTabInActiveColor: "#FFFFFF",

    postActiveIconColor: "#FC6767",
    postInActiveIconColor: "#FFFFFF",

    bottomTabBackground: '#000000',
    fullScreenIconTintColor: "#FFFFFF",
    dropdownTextColor: "#FFFFFF",
    dropdownBorderColor: "#FFFFFF",
    dropdownSelectedTextColor: "#FFFFFF",
    dropdownPlaceHolderColor: "#CCC",
    dropdownLabelColor: "#FFFFFF",
    copyTextBackground: "#373737",
    copyTextBtnColor: "#FFFFFF",

    playlistBannerTitleColor: "#FFFFFF",
    playlistBannerYearCountColor: "#FFFFFF",
    playlistBannerDescColor: "#FFFFFF",

    buttonGroupBackColor: "#373737",
    buttonGroupBorderColor: "#373737",
    buttonGroupInActiveTextColor: "#FFFFFF",
    buttonGroupActiveTextColor: "#FFFFFF",

    switchInActiveC: "#383b37",
    switchInActiveThumbC: "#50524e",

    totalEaringsVal: "#FFFFFF",
    totalEaringsValTxt: "#FFFFFF",
    dropIconTintColor: "#FFFFFF",
    listItemIcon: "#111111",
    accVerifiedFlushMsg: "#111111",
    progressBarItemIcon: "#111111",

    fileInputBoxBorderC: "#FFFFFF",
    fileInputBoxActionIconTintColorC: "#FFFFFF",

    profileImageBoxGradientOne: "#00000050",
    profileImageBoxGradientTwo: "#00000050",

    profileCountLabelC: "#FFFFFF",
    profileCountValueC: "#FFFFFF",
    profileBoxHeaderOptionIconC: "#FFFFFF",
    profileBoxNameC: "#FFFFFF",
    profileBoxIdC: "#FFFFFF",
    profileBoxBioC: "#FFFFFF",
    PItitleC: "#FFFFFF",

    playlistLCOne: "#111111",
    playlistLCTwo: "#00000070",
    playlistLCThree: "#11111170",
    playlistLCFour: "#000",

    homeShowCardText: "#FFFFFF",
    homeShowCardTextShadow: 'rgba(0, 0, 0, 0.75)',

    optionSelectionItemColor: "#768390",
    selectBoxSearchIconTintColor: "#CCCCCC",
    searchBoxBorder: "#707070",
    dateTextColor: "#FFFFFF",

    shareSocialIconTintColor: "#FFFFFF",
    shareSocialTintIconTintColor: "#FFFFFF4D",

    errorPopupMsgColor: "#707070"

}
export default Colors = DEFAULT_THEME_COLOR;