import React, { useState } from 'react'
import { StyleSheet, Text, View, Image, ScrollView, TouchableOpacity } from 'react-native'
import PrimaryButton from '../components/common/PrimaryButton';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import TopNavigationBar from '../components/TopNavigationBar';
import CustomStatusBar from '../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
const ForgotResetPassSuccessScreen = ({ route, navigation }) => {
    const gotToLoginButton = () => {
        navigation.reset({
            index: 0,
            routes: [{ name: 'SignInNScreen', params: { ErrorMsg: "", } }],
        });
    }
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={{ flex: 1, position: 'relative', backgroundColor: theme.colors.oldLoginBackground }}>
                <ScrollView
                    keyboardShouldPersistTaps="handled">

                    <View style={defaultStyle.container}>
                        {/* <TopNavigationBar navigation={navigation} /> */}
                        <View style={{
                            ...defaultStyle.withoutPapercontainer,
                            flexDirection: 'column', marginTop: 80
                        }}>
                            <View style={{ flex: 1, marginTop: 40, }}>
                                <HeadLineTxt style={style.loginHeadTxt}>Password Reset</HeadLineTxt>
                                <HeadLineDownTxt style={style.headBodyTxt}>Your password has been reset successfully.</HeadLineDownTxt>



                                <PrimaryButton label="Continue to Login" style={{ marginVertical: 20, backgroundColor: "#111111" }} uppercase={false}
                                    onPress={() => gotToLoginButton()} />
                            </View>

                        </View>
                        {/* <View style={{ marginTop: 10, }}>
                    <View style={style.signupBox}>
                        <EntutoTextView style={style.signupTxt}>Already have an account?</EntutoTextView>
                        <TouchableOpacity onPress={(e) => loginTxtClick()}>
                            <EntutoTextView style={style.signupTxtVal}>Back to Login</EntutoTextView>
                        </TouchableOpacity>
                    </View>
                </View> */}
                    </View>
                </ScrollView>
            </View>
        </>
    )
}

export default ForgotResetPassSuccessScreen;

const styles = theme => StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: theme.colors.bodyTextColor
    },
    signupTxtVal: {
        color: theme.colors.primaryColor,
    },
    forgetPassLogo: {
        height: 200,
        width: "auto",
        resizeMode: "contain",
        marginTop: 10,
    },
    loginHeadTxt: {
        marginTop: 40,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },
    resendTxtVal: {
        color: theme.colors.primaryColor,
        marginBottom: 18,
    },

})
