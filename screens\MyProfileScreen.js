import React, { useContext, useState } from 'react'
import { AppStateContext } from '..';
import CustomStatusBar from '../components/common/CustomStatusBar';
import ProfileComponent from '../components/profile/ProfileComponent';
import ProfileComponentNew from '../components/profile/ProfileComponentNew';

const MyProfileScreen = ({ route, navigation }) => {
    const { fullUserDetails } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    return (
        <>
            <CustomStatusBar translucent={true} hidden={false} />
            {/* <ProfileComponentNew navigation={navigation} profileSeq={__ProfileSeq} isOthersProfile={false} /> */}
            <ProfileComponent navigation={navigation} profileSeq={__ProfileSeq} isOthersProfile={false} />
        </>

    )
}

export default MyProfileScreen;