import {
  Alert,
  Image,
  ImageBackground,
  Keyboard,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useDefaultStyle from '../../theme/useDefaultStyle';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../../components/common/LoginSignUpLinearGrad';
import ModuleAppBar from '../../components/loginModule/ModuleAppBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoTextView from '../../components/common/EntutoTextView';
import ModuleHeaderText from '../../components/loginModule/ModuleHeaderText';
import LoginModuleTitle from '../../components/loginModule/LoginModuleTitle';
import EntutoEditText from '../../components/common/EntutoEditText';
import PrimaryButton from '../../components/common/PrimaryButton';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {ActivityIndicator} from 'react-native-paper';
import SuccessFailureMsgBox from '../../components/common/SuccessFailureMsgBox';
import ServerConnector from '../../utils/ServerConnector';
import {AppStateContext} from '../..';
import ErrorMessages from '../../constants/ErrorMessages';

// Real backend implementation is used instead of the simulated backend

const QuickSignUpScreen = ({route, navigation}) => {
  const {_data, _mobile} = route.params || {_data: {}, _mobile: '1234567890'};
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();
  const [showLoading, setShowLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [errorMsgType, setErrorMsgType] = useState('FAILED');
  const [refreshKey, setRefreshKey] = useState(Math.random());

  // Removed email verification states and ref since it's no longer needed.
  // Commented out username-related states
  // const [verifiedUserName, setVerifiedUserName] = useState('');
  // const [validUserName, setValidUserName] = useState(false);
  // const [isUsernameVerifying, setIsUsernameVerifying] = useState(false);

  const {acceptTerms, changeAcceptTerms, changeUserDetails} =
    useContext(AppStateContext);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      if (acceptTerms) {
        // Automatically go to registration if terms were already accepted
        setTimeout(() => {
          nextBtnPress();
        }, 500);
      }
    });
    return unsubscribe;
  }, [navigation, acceptTerms]);

  const [inputValues, setInputValues] = useState({
    fullName: '',
    emailID: '',
    // userName: '', // Username functionality commented out

    fullNameErr: '',
    emailIDErr: '',
    // userNameErr: '', // Username functionality commented out
  });

  const handleInputChange = (typeName, input) => {
    setInputValues(prevState => {
      // Create new state object
      const newState = {
        ...prevState,
        [typeName]: input,
        [typeName + 'Err']: '', // Clear errors when user starts typing
      };

      // If fullName is being changed, update email
      if (typeName === 'fullName') {
        // Convert full name to lowercase and remove spaces
        const emailPrefix = input.toLowerCase().replace(/\s+/g, '');
        newState.emailID = emailPrefix;
        newState.emailIDErr = ''; // Clear any email errors
      }

      return newState;
    });
    // Commented out username verification reset code
    // if (typeName === 'userName' && validUserName) {
    //   setValidUserName(false);
    //   setVerifiedUserName('');
    // }
  };

  // Email chip data
  const emailChips = ['@gmail.com', '@yahoo.com'];

  const handleEmailChipPress = chipText => {
    const currentEmail = inputValues.emailID;
    const strippedEmail = currentEmail.replace(/@.*$/, '');
    if (!strippedEmail.includes(chipText)) {
      const newEmailValue = strippedEmail + chipText;
      handleInputChange('emailID', newEmailValue);
    }
  };

  const nextBtnPress = () => {
    if (formValid()) {
      setShowLoading(true);
      registerUserService();
    }
  };

  const formValid = () => {
    let isValid = true;
    let err = {
      fullNameErr: '',
      emailIDErr: '',
      // userNameErr: '', // Removed username validation
    };

    if (inputValues.fullName.length === 0) {
      err.fullNameErr = 'Please enter your full name';
      isValid = false;
    }

    if (inputValues.emailID.length === 0) {
      err.emailIDErr = 'Please enter your email ID';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(inputValues.emailID)) {
      err.emailIDErr = 'Please enter a valid email address';
      isValid = false;
    }
    setInputValues(prevState => ({
      ...prevState,
      ...err,
    }));

    return isValid;
  };

  // --- Username Verification Functions (Commented Out) ---
  // const checkUsernameBtn = () => {
  //   if (inputValues.userName.length > 3) {
  //     Keyboard.dismiss();
  //     setIsUsernameVerifying(true);
  //     checkUsernameService();
  //   } else {
  //     setInputValues(prevState => ({
  //       ...prevState,
  //       userNameErr: 'Username must be at least 4 characters long!',
  //     }));
  //     setIsUsernameVerifying(false);
  //   }
  // };

  // const editUsernameBtn = () => {
  //   Alert.alert(
  //     'Confirmation',
  //     ErrorMessages.changeVerifiedUsernameConfirmMsg ||
  //       'Are you sure you want to change your username?',
  //     [
  //       {
  //         text: 'Yes',
  //         onPress: () => {
  //           setValidUserName(false);
  //           setVerifiedUserName('');
  //         },
  //       },
  //       {
  //         text: 'No',
  //         style: 'cancel',
  //       },
  //     ],
  //   );
  // };

  // const checkUsernameService = () => {
  //   SimulatedBackend.verifyUsername(inputValues.userName, _mobile, {
  //     success: data => {
  //       setIsUsernameVerifying(false);
  //       setVerifiedUserName(inputValues.userName);
  //       setValidUserName(true);
  //       setInputValues(prevState => ({...prevState, userNameErr: ''}));
  //     },
  //     failure: (errorCode, errorMessage, data) => {
  //       setIsUsernameVerifying(false);
  //       setValidUserName(false);
  //       var fieldErrorShown = false;
  //       if (errorCode === 'E006' && data?.data?.user_name) {
  //         setInputValues(prevState => ({
  //           ...prevState,
  //           userNameErr: data.data.user_name,
  //         }));
  //         fieldErrorShown = true;
  //       }
  //       if (!fieldErrorShown) {
  //         setInputValues(prevState => ({
  //           ...prevState,
  //           userNameErr: errorMessage || 'Failed to verify username',
  //         }));
  //       }
  //     },
  //   });
  // };

  // --- Registration ---
  const registerUserService = () => {
    console.log('registerUserService');
    let connector = new ServerConnector();
    connector.postData(
      {
        _action_code: '11:REGISTER_USER',
        mobile: _mobile,
        email: inputValues.emailID,
        full_name: inputValues.fullName,
        user_name: Math.random().toString(36).substring(2, 6),
      },
      data => {
        // success callback
        setShowLoading(false);
        let userDetails = {
          _username: data.data.uid,
          _password: data.data.pwd,
          _profile_seq: data.data.profile_seq,
          _user_seq: data.data.user_seq,
          _user_handle: data.data.user_handle,
          _user_account_type: data.data.account_type,
          _user_display_name: '',
          _has_bank_details: 'NO',
          _is_profile_verified: 'NO',
          _is_gmail_login: 'NO',
          _max_file_size: data.data.max_file_size,
        };
        changeUserDetails(userDetails);
        changeAcceptTerms(false);
        navigation.replace('QuickSignUpPersonalizeScreen', {
          mobile: _mobile,
          userData: userDetails,
        });
      },
      (errorCode, errorMessage, data) => {
        // failure callback
        setShowLoading(false);
        var fieldErrorShown = false;
        if (errorCode === 'E006' && data?.data) {
          if (data.data.user_name) {
            setErrorMsg(data.data.user_name);
            setErrorMsgType('FAILED');
            setRefreshKey(Math.random());
            fieldErrorShown = true;
          }
          if (data.data.email) {
            setInputValues(prevState => ({
              ...prevState,
              emailIDErr: data.data.email,
            }));
            fieldErrorShown = true;
          }
          if (data.data.full_name) {
            setInputValues(prevState => ({
              ...prevState,
              fullNameErr: data.data.full_name,
            }));
            fieldErrorShown = true;
          }
        }
        if (!fieldErrorShown) {
          setErrorMsg(errorMessage);
          setErrorMsgType('FAILED');
          setRefreshKey(Math.random());
        }
      },
    );
  };

  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <CustomProgressDialog showLoading={showLoading} />

      <View style={{flex: 1, position: 'relative'}}>
        <LoginSignUpLinearGrad />
        <ModuleAppBar navigation={navigation} />
        <ScrollView keyboardShouldPersistTaps="handled">
          <View style={defaultStyle.loginModuleContainer}>
            {/* <ModuleHeaderText
              text="Welcome to Sotrue"
              style={{marginTop: theme.dimensions.loginModuleHeaderTextMT}}
              secondText="Exclusive Content, Tailored Just For You"
            /> */}
            <LoginModuleTitle
              firstTitleText="Time to make"
              secondTitleText="it official"
              style={{marginTop: theme.dimensions.loginModuleTitleMT}}
            />
            <View style={defaultStyle.loginModuleFormContainer}>
              {/* Full Name */}
              <View>
                <EntutoEditText
                  labelTxt="Full Name"
                  placeholderTxt="Full Name"
                  value={inputValues.fullName}
                  showErrorField={
                    // shouldShowError(inputValues.fullNameErr) &&
                    inputValues.fullNameErr.length > 0
                  }
                  errorMsg={inputValues.fullNameErr}
                  onChangeText={text => handleInputChange('fullName', text)}
                />
              </View>

              {/* Username Functionality Commented Out */}
              {/*
              <View style={{marginTop: theme.dimensions.loginModuleInputMT}}>
                <EntutoEditText
                  labelTxt="Username"
                  placeholderTxt="Username (min 4 characters)"
                  value={inputValues.userName}
                  showErrorField={
                    shouldShowError(inputValues.userNameErr) &&
                    inputValues.userNameErr.length > 0
                  }
                  errorMsg={inputValues.userNameErr}
                  onChangeText={text => handleInputChange('userName', text)}
                />
                <View style={style.verifyButtonContainer}>
                  {validUserName ? (
                    <View style={style.verifiedContainer}>
                      <View style={style.verifiedTextContainer}>
                        <Text style={style.verifiedText}>
                          Username Verified
                        </Text>
                        <View style={style.verifiedIconWrapper}>
                          <ImageBackground
                            style={style.verifiedIconBackground}
                            source={require('../../assets/Images/icon/round_circle.png')}>
                            <Image
                              style={style.verifiedIconTick}
                              source={require('../../assets/Images/icon/tick_white.png')}
                              resizeMode={'contain'}
                            />
                          </ImageBackground>
                        </View>
                      </View>
                      <TouchableOpacity
                        onPress={editUsernameBtn}
                        style={style.editButton}>
                        <Text style={style.editButtonText}>Edit</Text>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <PrimaryButton
                      label="Verify Username"
                      onPress={checkUsernameBtn}
                      disabled={isUsernameVerifying}
                      loading={isUsernameVerifying}
                      uppercase={false}
                    />
                  )}
                </View>
              </View>
              */}

              {/* Email ID */}
              <View style={{marginTop: theme.dimensions.loginModuleInputMT}}>
                <EntutoEditText
                  labelTxt="Email ID"
                  placeholderTxt="Email ID"
                  value={inputValues.emailID}
                  showErrorField={
                    // shouldShowError(inputValues.emailIDErr) &&
                    inputValues.emailIDErr.length > 0
                  }
                  errorMsg={inputValues.emailIDErr}
                  onChangeText={text => handleInputChange('emailID', text)}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  showChips={true}
                  chipData={emailChips}
                  onChipPress={handleEmailChipPress}
                />
                {/* Only one "Next" button */}
                <View style={style.verifyButtonContainer}>
                  <PrimaryButton
                    label="Submit"
                    onPress={nextBtnPress}
                    disabled={
                      inputValues.emailID.length === 0 ||
                      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(inputValues.emailID)
                    }
                    uppercase={false}
                  />
                </View>
              </View>

              {/* Removed separate Next Button Block */}
            </View>
          </View>
        </ScrollView>
      </View>

      {/* Success/Failure Snackbar */}
      {errorMsg.length !== 0 ? (
        <SuccessFailureMsgBox
          alertMsg={errorMsg}
          alertKey={refreshKey}
          alertType={errorMsgType}
        />
      ) : null}
    </>
  );
};

export default QuickSignUpScreen;

// We assume styles are defined elsewhere and imported
// const styles = StyleSheet.create({...});// Add styles for the new elements
const styles = theme =>
  StyleSheet.create({
    verifyButtonContainer: {
      marginTop: 30,
      width: '100%',
    },
    verifiedContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 10,
      paddingHorizontal: 10,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.dimensions.buttonBorderRadius || 8,
      borderWidth: 1,
      borderColor: theme.colors.primaryColor,
    },
    verifiedTextContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    verifiedText: {
      color: theme.colors.primaryColor,
      fontWeight: 'bold',
      fontSize: 14,
      marginRight: 8,
    },
    verifiedIconWrapper: {},
    verifiedIconBackground: {
      height: 20,
      width: 20,
      alignItems: 'center',
      justifyContent: 'center',
    },
    verifiedIconTick: {
      height: 8,
      width: 10,
    },
    editButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 4,
      borderRadius: 6,
    },
    editButtonText: {
      color: theme.colors.primaryColor,
      marginLeft: 4,
      fontSize: 14,
      fontWeight: '500',
    },
  });
