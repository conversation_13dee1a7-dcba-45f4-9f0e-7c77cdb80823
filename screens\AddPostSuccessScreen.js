import { <PERSON><PERSON>, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import CustomStatusBar from '../components/common/CustomStatusBar'
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import Colors from '../constants/Colors';
import { checkValueLength, creationOfCopyLink, hasImageUrlExist } from '../utils/Utils';
import ProgressiveImage from '../components/common/ProgressiveImage';
import ProfileImagePlaceholder from '../assets/Images/full_user_image_place_holder.png';
import EntutoTextView from '../components/common/EntutoTextView';
import { Image } from 'react-native';
import Dimensions from '../constants/Dimensions';
import VerifiedIcon from '../assets/Images/icon/verifiedicon.png';
import LinearGradient from 'react-native-linear-gradient';
import TwitterIcon from '../assets/Images/social_icon/twitter.png';
import SnapchatIcon from '../assets/Images/social_icon/snapchat.png';
import WhatsappIcon from '../assets/Images/social_icon/whatsapp.png';
import FacebookIcon from '../assets/Images/social_icon/facebook.png';
import LinkedinIcon from '../assets/Images/social_icon/linkedin.png';
import Share from 'react-native-share';
import Clipboard from '@react-native-clipboard/clipboard';
import { _RedirectionErrorList, _UnauthErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import { AuthValidation } from '../utils/AuthValidation';
import ServerConnector from '../utils/ServerConnector';
import PlayBtnIcon from '../assets/Images/icon/play_btn.png';
import CloseIcon from '../assets/Images/icon/close_icon.png';
import LottieView from 'lottie-react-native';
import ErrorMessages from '../constants/ErrorMessages';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSThemedStyles from '../theme/useSThemedStyles';
import useSTheme from '../theme/useSTheme';
import TempData from '../data/TempData';

const IMAGE_HEIGHT = Dimensions.screenWidth - 40;
const AddPostSuccessScreen = ({ route, navigation }) => {
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    const { postSeq } = route.params;
    const [showLoading, setShowLoading] = useState(true);
    const [isCopied, setIsCopied] = useState(false);
    const [bodyMsg1, setBodyMsg1] = useState("");
    const [bodyMsg2, setBodyMsg2] = useState("");
    const [postData, setPostData] = useState({
        profile_picture: null,
        display_name: "",
        is_verified: "NO",
        media_type: "IMAGE",
        media_file: null,
        media_cover: null,
        user_handle: ""
    });

    useEffect(() => {
        getPostUserDataService();
    }, [])
    const ImageOverlayNew = () => {
        return <LinearGradient
            colors={[
                '#000000',
                'transparent',
                'transparent',
                '#000000'
            ]}
            locations={[0, 0.18, 0.8, 0.97]}
            style={style.NSSLinearGradient}
        />
    }
    const shareBtnPress = (shareType) => {
        onShare(shareType);
    }


    const onShare = async (shareType) => {
        let copyLinkText = creationOfCopyLink("POST", postSeq);
        let social = Share.Social.TWITTER;
        if (shareType == "SNAPCHAT") {
            social = Share.Social.SNAPCHAT;
        }
        else if (shareType == "WHATSAPP") {
            social = Share.Social.WHATSAPP;
        }
        else if (shareType == "FACEBOOK") {
            social = Share.Social.FACEBOOK;
        }
        else if (shareType == "LINKEDIN") {
            social = Share.Social.LINKEDIN;
        }
        submitPostShareCountService();
        const shareOptions = {
            message: "Exclusive content on SoTrue\n",
            url: copyLinkText,
            social: social,
            backgroundBottomColor: '#fefefe',
            backgroundTopColor: '#906df4',
        }
        try {
            await Share.shareSingle(shareOptions);
        } catch (error) {
            // console.log(error);
            await Share.open(shareOptions);
            // Alert.alert("Error", "No Activity found to handle Intent");
        }
    };
    const copyBtnPress = async () => {
        let copyLinkText = creationOfCopyLink("POST", postSeq);
        setIsCopied(true);
        Clipboard.setString(copyLinkText);
        setTimeout(() => {
            setIsCopied(false);
        }, 2000);
    }
    function getPostUserDataService() {
        let hashMap = {
            _action_code: "11:GET_USER_POST",
            post_seq: postSeq
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let dataObj = data.data[0];
            if (!checkValueLength(dataObj.media_file)) {
                dataObj.media_file = TempData.backupMediaData;
                if (dataObj.media_type == "VIDEO") {
                    dataObj.media_cover = TempData.backupMediaData;
                }
            }
            setPostData(dataObj);
            setShowLoading(false);
            if (dataObj.media_type == "IMAGE") {
                setBodyMsg1(ErrorMessages.imagePostBody1);
                setBodyMsg2(ErrorMessages.imagePostBody2);
            }
            if (dataObj.media_type == "VIDEO") {
                setBodyMsg1(ErrorMessages.videoPostBody1);
                setBodyMsg2(ErrorMessages.videoPostBody2);
            }

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            setShowLoading(false);
        });
    }
    const imageBtnPress = () => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: postData.media_file
        })
    }
    const videoBtnPress = () => {
        navigation.navigate("VideoDisplayScreen", {
            mediaUri: postData.media_file,
            thumbnailUri: postData.media_cover
        })
    }
    const closeBtnPress = () => {
        if (navigation.canGoBack()) {
            navigation.goBack(null);
        }
        else {
            navigation.replace("HomeScreen");
        }
    }
    function submitPostShareCountService() {
        let hashMap = {
            _action_code: "11:UPDATE_SHARE_COUNT",
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
        }, (errorCode, errorMessage, data) => { // failure method

        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            {/* <HomeTopNavigationBar title="" navigation={navigation} /> */}
            <View style={style.closeIconBox}>
                <TouchableOpacity onPress={() => closeBtnPress()}>
                    <Image source={CloseIcon} style={style.closeIcon} resizeMode='contain' />
                </TouchableOpacity>

            </View>
            <ScrollView
                style={{ zIndex: 100, backgroundColor: theme.colors.backgroundColor }}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}>
                <View >
                    <View style={style.congratulateBox}>
                        <EntutoTextView style={style.congratulateBoxTxt}>Congratulations!</EntutoTextView>
                    </View>
                    <View style={{ ...style.bodyBox }}>
                        <EntutoTextView style={style.bodyBoxText}>{bodyMsg2}</EntutoTextView>
                    </View>
                    {/* <View style={style.bodyBox2}>
                        <EntutoTextView style={style.bodyBox2Text}>{bodyMsg2}</EntutoTextView>
                    </View> */}
                    <View style={{ ...style.postCard, ...defaultStyle.ListCardStyle, marginBottom: 32, borderRadius: 1 }}>
                        <View style={{ ...style.postCardHeader, ...style.newPostHeader }}>
                            <View style={style.headerImageBox}>
                                <ProgressiveImage
                                    style={style.headerImage}
                                    source={hasImageUrlExist(postData.profile_picture) ? { uri: postData.profile_picture } : null}
                                    defaultImageSource={ProfileImagePlaceholder}

                                />

                            </View>
                            <View style={style.headerTextBox}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <EntutoTextView style={style.headerDisplayName}>{postData.display_name}</EntutoTextView>
                                    {
                                        postData.is_verified == "YES" ?
                                            <Image
                                                style={style.verifiedIcon}
                                                source={VerifiedIcon}
                                                resizeMode={'contain'}
                                            />
                                            :
                                            null
                                    }
                                </View>
                                <EntutoTextView style={style.headerDate}>{postData.user_handle}</EntutoTextView>
                            </View>
                        </View>
                        <View style={style.postImageBox}>
                            <View style={{ flex: 1, flexDirection: 'column', width: '100%', }}>
                                {
                                    postData.media_type == "IMAGE" ?
                                        <View style={{ ...style.postImageContainer, borderRadius: 1 }}>
                                            <TouchableOpacity onPress={() => imageBtnPress()}>
                                                <ImageBackground
                                                    imageStyle={{ borderRadius: 1, }}
                                                    source={hasImageUrlExist(postData.media_file) ? { uri: postData.media_file } : null}
                                                    style={style.postImageContainerImg} >
                                                    <ImageOverlayNew />
                                                </ImageBackground>
                                            </TouchableOpacity>
                                        </View>
                                        : null
                                }
                                {
                                    postData.media_type == "VIDEO" ?
                                        <View style={{ ...style.videoBox, borderRadius: 1 }}>
                                            <TouchableOpacity onPress={() => videoBtnPress()}>
                                                <ImageBackground
                                                    imageStyle={{ borderRadius: 1 }}
                                                    source={hasImageUrlExist(postData.media_cover) ? { uri: postData.media_cover } : null}
                                                    style={style.videPostImage} >
                                                    <ImageOverlayNew />
                                                    <View style={style.playBtnBox}>
                                                        <Image
                                                            style={style.playBtnBoxIcon}
                                                            source={PlayBtnIcon}
                                                            resizeMode="cover"
                                                        />
                                                    </View>

                                                </ImageBackground>
                                            </TouchableOpacity>
                                        </View>
                                        : null
                                }

                            </View>
                        </View>

                        <View style={{ ...style.postActionIconBox, zIndex: 1000, backgroundColor: isCopied ? '#707070' : theme.colors.primaryColor }}>
                            <TouchableOpacity disabled={isCopied} onPress={() => copyBtnPress()}>
                                <EntutoTextView style={style.copyLinkText}>
                                    {isCopied ? "Copied" : "Copy Link"}
                                </EntutoTextView>
                            </TouchableOpacity>
                        </View>

                    </View>
                    <View style={{ marginTop: 12, }}>
                        <EntutoTextView style={style.shareItNowText}>Share it now</EntutoTextView>
                    </View>
                    <View style={style.shareSocialBox}>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("TWITTER")}>
                                <Image source={TwitterIcon} resizeMode={'contain'} style={style.shareSocialIcon} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("SNAPCHAT")}>
                                <Image source={SnapchatIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("WHATSAPP")}>
                                <Image source={WhatsappIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("FACEBOOK")}>
                                <Image source={FacebookIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                        <View>
                            <TouchableOpacity onPress={() => shareBtnPress("LINKEDIN")}>
                                <Image source={LinkedinIcon} resizeMode={'contain'}
                                    style={{ ...style.shareSocialIcon, ...style.shareSocialTintIcon }} />
                            </TouchableOpacity>
                        </View>
                    </View>
                    {/* <LottieView source={require('../assets/jsonfile/Celebration.json')} autoPlay loop
                        resizeMode='cover' style={style.lottie} /> */}
                </View>

            </ScrollView>

        </>
    )
}

export default AddPostSuccessScreen

const styles = theme => StyleSheet.create({
    congratulateBox: {
        marginBottom: 15,
    },
    congratulateBoxTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.addPostSuccessCongratsText),
        fontWeight: 'bold',
        color: theme.colors.primaryColor,
        alignSelf: 'center'
    },
    bodyBox: {
        marginBottom: 8,
    },
    bodyBoxText: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.addPostSuccessBodyText),
        alignSelf: 'center',
        textAlign: 'center',
        marginHorizontal: 16
    },
    bodyBox2: {
        marginBottom: 8,
    },
    bodyBox2Text: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.addPostSuccessBodyText),
        alignSelf: 'center',
        textAlign: 'center',
        marginHorizontal: 16
    },
    postCard: {
        flexDirection: "column",
        padding: 2,
        position: 'relative',
    },
    postCardHeader: {
        flexDirection: "row",
        flex: 1,
        alignItems: 'center',
        paddingHorizontal: 4,
    },
    newPostHeader: {
        position: 'absolute',
        top: 15,
        left: 0,
        right: 0,
        zIndex: 9,
        backgroundColor: 'transparent',
        height: 56,
    },
    headerImageBox: {
        height: 31.5,
        width: 31.5,
        marginRight: 10,
        borderRadius: 31.5,
        marginLeft: 16,
    },
    headerImage: {
        height: 31.5,
        width: 31.5,
        borderRadius: 31.5
    },
    headerTextBox: {
        flexDirection: 'column',
        alignContent: 'center'
    },
    headerDisplayName: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.postProfileNameText),
        color: "#FFFFFF",//#43180B
        fontWeight: 'bold',
        flexDirection: 'row',
    },
    verifiedIcon: {
        width: 13,
        height: 13,
        marginLeft: 8,
    },
    headerDate: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.postDateText),
        color: "#FFFFFF",
        fontWeight: '400',
    },
    postImageBox: {
        marginVertical: 13,
        flex: 1,
        position: 'relative',

    },
    postImageContainer: {
        width: '100%',
        minHeight: 300,
        maxHeight: '100%',
        resizeMode: 'cover',
        borderRadius: 1,
        position: 'relative',
        backgroundColor: '#CCC',
        // paddingVertical: 1
    },
    postImageContainerImg: {
        width: '100%',
        height: '100%',
        minHeight: IMAGE_HEIGHT,
        // resizeMode: 'cover',
        borderRadius: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    NSSLinearGradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        borderRadius: 1,
    },
    postActionIconBox: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        position: 'absolute',
        backgroundColor: theme.colors.primaryColor,
        justifyContent: 'center',
        bottom: -8,
        paddingVertical: 10,
        left: 16,
        right: 16,
        borderRadius: 1,
    },
    videoBox: {
        width: '100%',
        minHeight: IMAGE_HEIGHT,
        maxHeight: '100%',
        resizeMode: 'cover',
        borderRadius: 15,
        position: 'relative',
        backgroundColor: '#000',
    },
    videPostImage: {
        width: '100%',
        height: "100%", //400
        minHeight: IMAGE_HEIGHT,
        // resizeMode: 'cover',
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center'
    },
    playBtnBox: {
        height: 63,
        width: 63,
        position: 'absolute',
        top: '43%',
        left: '43%',
        backgroundColor: '#00000080',
        borderRadius: 15,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.50,
        shadowRadius: 1.41,


        // elevation: 2,
    },
    playBtnBoxIcon: {
        width: 34,
        height: 40,
        // tintColor: '#000',
        backgroundColor: 'transparent'
    },
    copyLinkText: {
        fontSize: theme.calculateFontSize(theme.dimensions.copyLinkText),
        color: theme.colors.primaryTextColor,
    },
    shareItNowText: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.shareItNowText),
        alignSelf: 'center',
    },
    shareSocialBox: {
        marginTop: 25,
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        marginHorizontal: 15,
        marginBottom: 50,
        zIndex: 1000
    },
    shareSocialIcon: {
        width: 32,
        height: 32,
        tintColor: theme.colors.shareSocialIconTintColor
    },
    shareSocialTintIcon: {
        tintColor: theme.colors.shareSocialTintIconTintColor
    },
    closeIcon: {
        width: 24,
        height: 24,
    },
    closeIconBox: {
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
        paddingTop: 10,
        paddingEnd: 10, zIndex: 1000,
        backgroundColor: theme.colors.backgroundColor
    },
    lottie: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 99,
    },
})