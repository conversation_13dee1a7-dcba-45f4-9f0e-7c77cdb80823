import React, { useContext, useEffect, useState } from 'react'
import { Image, ImageBackground, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import CustomStatusBar from '../components/common/CustomStatusBar';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import EntutoTextView from '../components/common/EntutoTextView';
import PrimaryButton from '../components/common/PrimaryButton';
import ListItem from '../components/ListItem';
import Share from 'react-native-share';
import Clipboard from '@react-native-clipboard/clipboard';
import { AppStateContext } from '..';
import ConfirmationPopup from '../components/common/ConfirmationPopup';
import ServerConnector from '../utils/ServerConnector';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import ErrorMessages from '../constants/ErrorMessages';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import { creationOfCopyLink } from '../utils/Utils';

const ShareYourProfileScreen = ({ navigation }) => {
    const [isCopied, setIsCopied] = useState(false);
    const [profileLink, setProfileLink] = useState("");

    const [errorMsg, setErrorMsg] = useState("");
    const [errorMsgKey, setErrorMsgKey] = useState(Math.random());


    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    const { fullUserDetails } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    useEffect(() => {
        let copyLinkText = creationOfCopyLink("PROFILE", __ProfileSeq);
        setProfileLink(copyLinkText)
    }, [__ProfileSeq])

    const shareInviiteCodePress = () => {
        onShare()
    }
    const onShare = async () => {
        const shareOptions = {
            message: "Exclusive content on SoTrue\n",
            url: profileLink
        }
        try {
            const shareResponse = await Share.open(shareOptions);
        } catch (error) {
            // console.log(error.message);
        }
    };
    const copyBtnPress = () => {
        setIsCopied(true);
        setErrorMsg("Link copied successfully!");
        setErrorMsgKey(Math.random())
        Clipboard.setString(profileLink);
        setTimeout(() => {
            setErrorMsg("")
            setIsCopied(false);
            setErrorMsgKey(Math.random())
        }, 3000);
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />

            <HomeTopNavigationBar title="Share Your Profile" showBackBtn={true} navigation={navigation}
                showBorderBottom={false} />

            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                <View style={style.referTopBox}>
                    <View style={defaultStyle.container}>
                        <View style={style.referBoxStart}>
                            {/* <HeadLineTxt >Refer and Earn</HeadLineTxt> */}
                            <HeadLineDownTxt style={{ marginTop: 15, }}>
                                {/* Refer a friend  to Sotrue and get  10% cashback.
                                    Copy your code, share it with your friends. */}
                                To share your account across other platforms, simply use the link below:
                                This will help you expand your network and connect with others more easily.

                            </HeadLineDownTxt>
                        </View>
                        <View style={style.referCodeBox}>
                            <EntutoTextView style={style.referTxt}>{profileLink}</EntutoTextView>

                            <View style={{ ...style.copyBtn, marginLeft: 'auto', marginRight: 8, }}>
                                <TouchableOpacity onPress={() => copyBtnPress()} style={{ paddingEnd: 10 }}>
                                    <Image source={require('../assets/Images/icon/new_copy_icon.png')}
                                        style={style.copyBtnIcon} />
                                    {/* <EntutoTextView style={style.copyBtnTxt}>
                                        {isCopied ? "Copied" : "Copy"}
                                    </EntutoTextView> */}
                                </TouchableOpacity>
                            </View>
                        </View>
                        <PrimaryButton
                            label="Share"
                            style={{ marginVertical: 20, marginTop: 40 }}
                            uppercase={false}
                            onPress={() => shareInviiteCodePress()} />
                    </View>


                </View>

            </View>
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertType='SUCCESS' alertKey={errorMsgKey} />
                    : null
            }

        </>
    )
}

export default ShareYourProfileScreen;

const styles = theme => StyleSheet.create({
    referTopBox: {
        backgroundColor: theme.colors.backgroundColor,//'#F2EBE9'
        // height: 427,
    },
    vectorIcon: {
        width: 213,
        height: 427,
        marginLeft: 'auto',
        position: 'absolute',
        right: 0,
        top: 0,
    },
    backBtnBox: {
        marginTop: 56,
        paddingStart: 11,
    },
    referBackBtn: {
        width: 22,
        height: 22,
        tintColor: theme.colors.primaryColor
    },
    referBoxStart: {
        marginTop: 20,
    },
    referCodeBox: {
        backgroundColor: theme.colors.copyTextBackground,
        // borderColor: theme.colors.primaryColor,
        // borderWidth: 1,
        // borderStyle: 'dashed',
        // borderRadius: 24.5,
        marginTop: 20,
        minHeight: 54,
        alignItems: 'center',
        flexDirection: 'row',

    },
    referTxt: {
        color: theme.colors.inputTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.copyLinkText),
        fontWeight: '600',
        paddingLeft: 20,
        width: 240,
    },
    copyBtn: {
        // backgroundColor: theme.colors.primaryColor,
        borderRadius: 1,
    },
    copyBtnIcon: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
        paddingEnd: 10
    },
    copyBtnTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.copyLinkBtnText),
        color: theme.colors.copyTextBtnColor,
        fontWeight: 'bold',
        paddingVertical: 6,
        paddingHorizontal: 16,

    },
    orTxtLine: {
        height: 0.5,
        flex: 1,
        backgroundColor: '#00000020',
    },
    orTxtBox: {
        flexDirection: 'row',
        alignItems: 'center',
    }
})
