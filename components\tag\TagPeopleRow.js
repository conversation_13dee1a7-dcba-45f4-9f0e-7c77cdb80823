import { Image, Pressable, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import SubheadingBodyTxt from '../common/SubheadingBodyTxt'
import { UserHandlePrefix } from '../../utils/Appconfig'
import SubheadingTxt from '../common/SubheadingTxt'
import ProgressiveImage from '../common/ProgressiveImage'
import Dimensions from '../../constants/Dimensions'
import { hasImageUrlExist } from '../../utils/Utils'
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSTheme from '../../theme/useSTheme'
import useSThemedStyles from '../../theme/useSThemedStyles'

const TagPeopleRow = ({ data, tagItemPress }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    return (
        <>
            <Pressable
                onPress={() => tagItemPress(data.user_handle)}
                android_ripple={{ color: theme.colors.pressableRippleColor, borderless: false }}>
                <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>

                    <View style={style.profileImageBox}>
                        <ProgressiveImage
                            style={style.profileImage}
                            source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                            defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                            resizeMode={'cover'}
                        />
                    </View>
                    <View style={style.profileNameBox}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <SubheadingTxt>{data.display_name}</SubheadingTxt>
                            {
                                data.is_verified == "YES" ?
                                    <Image
                                        style={style.verifiedIcon}
                                        source={require('../../assets/Images/icon/verifiedicon.png')}
                                        resizeMode={'contain'}
                                    />
                                    : null
                            }
                        </View>

                        <SubheadingBodyTxt>{UserHandlePrefix}{data.user_handle}</SubheadingBodyTxt>

                    </View>


                </View>
            </Pressable>
        </>
    )
}

export default TagPeopleRow

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: Dimensions.sugProfileImgH,
        width: Dimensions.sugProfileImgW,
        borderRadius: Dimensions.sugProfileImgR,
    },
    profileNameBox: {
        flexDirection: 'column',
        marginLeft: Dimensions.sugProfileImgGapTxt,
    },
    verifiedIcon: {
        width: Dimensions.sugVerifiedIconW,
        height: Dimensions.sugVerifiedIconH,
        marginLeft: Dimensions.veritextLeftmargin,
    },
})