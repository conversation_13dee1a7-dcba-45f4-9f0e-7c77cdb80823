import React, { useContext, useEffect, useRef, useState } from 'react'
import { Image, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View, BackHandler, KeyboardAvoidingView, Keyboard } from 'react-native'
import CustomStatusBar from '../components/common/CustomStatusBar';
import EntutoTextView from '../components/common/EntutoTextView';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import CameraIcon from '../assets/Images/icon/addcamera.png'
import GalleryIcon from '../assets/Images/icon/verification_gallery.png'
import CROSS_ICON from '../assets/Images/icon/close_icon.png'
import LinearGradient from 'react-native-linear-gradient';
import { AppStateContext } from '..';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import ServerConnector from '../utils/ServerConnector';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import { _RedirectionErrorList, _UnauthErrorList } from '../utils/Appconfig';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import MimeTypeList from '../utils/MimeTypeList';
import ErrorMessages from '../constants/ErrorMessages';

import INDIVIDUAL_ICON from '../assets/Images/icon/individual.png'
import GROUP_ICON from '../assets/Images/icon/group.png'
import FIRM_ICON from '../assets/Images/icon/firm.png'
import { _getPanNoFromGst, checkValueLength } from '../utils/Utils';
import EntutoEditText from '../components/common/EntutoEditText';
import { AuthValidation } from '../utils/AuthValidation';
import { requestStoragePermission } from '../utils/PermissionManager';
import { launchImageLibrary } from 'react-native-image-picker';
import ActionSheet from 'react-native-actions-sheet';
import MediaMenuActionView from '../components/profile/MediaMenuActionView';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import FormButtonGroup from '../components/common/FormButtonGroup';
import EntutoDropdown from '../components/common/EntutoDropdown';
import Fontisto from 'react-native-vector-icons/Fontisto';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';



const VerificationsScreen = ({ navigation }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [takeSelfieData, settakeSelfieData] = useState(null);
    const [takeSelfieHead, settakeSelfieHead] = useState("");
    const [takeSelfieDown, settakeSelfieDown] = useState("");

    const [addIdData, setaddIdData] = useState(null);
    const [addIdHead, setaddIdHead] = useState("");
    const [addIdDown, setaddIdDown] = useState(null);

    const [selfieIdData, setselfieIdData] = useState(null);
    const [selfieIdHead, setselfieIdHead] = useState("");
    const [selfieIdDown, setselfieIdDown] = useState("");

    const mediaActionRef = useRef(null);

    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(true);
    const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);

    const [showVerificationBox, setshowVerificationBox] = useState(false);

    const { fullUserDetails,
        captureSelfie,
        captureImageId,
        captureSelfieId,
        changeVeriSelfieMedia,
        changeVeriImageID,
        changeVeriSelfieId, } = useContext(AppStateContext);

    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;

    const [showRejectionMsg, setshowRejectionMsg] = useState(false);
    const [rejectionMsg, setrejectionMsg] = useState("");
    const [isVerified, setisVerified] = useState(false);
    const [disableAllField, setdisableAllField] = useState(true);

    const [verificationPage, setverificationPage] = useState("TYPE"); // FORM TYPE

    const [verificationPageType, setverificationPageType] = useState("INDIVIDUAL");
    const [veriTempPageType, setveriTempPageType] = useState("INDIVIDUAL");

    const [btnTxt, setbtnTxt] = useState("NEXT");

    const [gstValue, setgstValue] = useState("");
    const [gstValueErr, setgstValueErr] = useState("");

    const [panNo, setpanNo] = useState("");
    const [panNoErr, setpanNoErr] = useState("");

    const [isGalleryBtnPress, setisGalleryBtnPress] = useState(false);
    const [profileUserHandle, setProfileUserHandle] = useState("");
    const [docTypeList, setDocTypeList] = useState([]);
    const [selectedDocType, setSelectedDocType] = useState("");
    const [selectedDocTypeErr, setSelectedDocTypeErr] = useState("");
    const [uploadDocName, setUploadDocName] = useState("");
    const [uploadDocNameErr, setUploadDocNameErr] = useState("");

    const [selfieImageName, setSelfieImageName] = useState("");
    const [joinPlaylist, setJoinPlaylist] = useState(false);

    function handleBackButtonClick() {
        // console.log("Back Press")
        backBtnhandler()
        return true;
    }
    function extrabackBtnPress() {
        backBtnhandler()
    }
    function backBtnhandler() {
        if (verificationPage == "TYPE") {
            navigation.goBack();
        }
        if (verificationPage == "FORM") {
            setverificationPage("TYPE")
            setbtnTxt("NEXT")
            setdisableUpdateBtn(false)
        }

    }
    useEffect(() => {
        BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
        return () => {
            BackHandler.removeEventListener('hardwareBackPress', handleBackButtonClick);
        };
    }, []);

    useEffect(() => {
        if (captureSelfie != null) {
            setdisableUpdateBtn(false);
        }
        settakeSelfieData(captureSelfie);
    }, [captureSelfie]);
    useEffect(() => {
        if (captureImageId != null) {
            setdisableUpdateBtn(false);
        }
        setaddIdData(captureImageId)
    }, [captureImageId]);
    useEffect(() => {
        if (captureSelfieId != null) {
            setdisableUpdateBtn(false);
        }
        setselfieIdData(captureSelfieId);
    }, [captureSelfieId]);

    useEffect(() => {
        getUserProfileService();
        getDocTypeList();
        setdisableUpdateBtn(false)
    }, []);

    function getDocTypeList() {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: "DOCU_TYPE",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let list = [];
            data.data.map(item => {
                list.push({ label: item.display_value, value: item.config_key })
            })
            setDocTypeList(list)
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setDocTypeList([])
            }
        });
    }
    function getUserProfileService() {
        let hashMap = {
            _action_code: "11:GET_USER_PROFILE",
            req_profile_seq: __ProfileSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method  
            setShowLoading(false);
            let showRejectionMsgV = false;
            let disableAllFieldV = false;
            let sVerBox = true;
            let rejectionMsgV = data.data[0].verification_comments;
            let user_handle = data.data[0].user_handle;
            setProfileUserHandle(user_handle)
            if (data.data[0].verification == "REJECTED") {
                showRejectionMsgV = true;
            }
            let isVerifiedV = false;
            if (data.data[0].verification == "PENDING") {
                showRejectionMsgV = true;
                disableAllFieldV = true;
                sVerBox = false;
                rejectionMsgV = "Verification data already submitted, pending review of SoTrue team! SoTrue team will review and documents and revert shortly."
            }
            if (data.data[0].verification == "VERIFIED") {
                showRejectionMsgV = false;
                disableAllFieldV = true;
                rejectionMsgV = "";
                isVerifiedV = true;
                sVerBox = false;
            }
            setshowRejectionMsg(showRejectionMsgV);
            setrejectionMsg(rejectionMsgV)
            setisVerified(isVerifiedV)
            setdisableAllField(disableAllFieldV);
            setshowVerificationBox(sVerBox);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (_UnauthErrorList.includes(errorCode)) {
                    AuthValidation(errorCode, data, navigation);
                }
                setShowLoading(false);
                setdisableAllField(false);
                setshowVerificationBox(true);
            }
        });
    }
    const verifyBtnPress = () => {
        Keyboard.dismiss();
        // if (verificationPage == "TYPE") {
        //     if (veriTempPageType.length == 0) {
        //         seterrorMsg(ErrorMessages.veriNotSelectTypeErr);
        //         return;
        //     }
        //     seterrorMsg("")
        //     setverificationPage("FORM")
        //     GenerateDisplayNameForLabel(veriTempPageType)
        //     setverificationPageType(veriTempPageType)
        //     setbtnTxt("VERIFY")
        //     setdisableUpdateBtn(true)
        // }
        // if (verificationPage == "FORM") {
        if (verificationPageType == "INDIVIDUAL" || veriTempPageType == "GROUP") {
            if (takeSelfieData == null) {
                seterrorMsg(ErrorMessages.veriSelfieImageErr);
                return;
            }
            if (addIdData == null) {
                seterrorMsg(ErrorMessages.veriIdImageErr);
                return;
            }
            if (!checkValueLength(selectedDocType)) {
                setSelectedDocTypeErr(ErrorMessages.veriIdIErr)
                return;
            }
            // if (selfieIdData == null) {
            //     seterrorMsg(ErrorMessages.veriSelfieIdImageErr);
            //     return;
            // }
            setShowLoading(true);
            submitProfileDataService();
        }
        if (verificationPageType == "FIRM") {
            if (gstValue.length == 0) {
                setgstValueErr(ErrorMessages.panGstMisMatchErr);
                return
            }
            if (panNo.length == 0) {
                setpanNoErr(ErrorMessages.panGstMisMatchErr);
                return
            }
            if (gstValue.length != 0) {
                let panN = _getPanNoFromGst(gstValue);
                if (panNo.length != 0) {
                    if (panNo != panN) {
                        setgstValueErr(ErrorMessages.panGstMisMatchErr);
                        return
                    }
                }
            }
            uploadVerificationFirm();
        }

        // }


    }
    const gstValueChangeHandler = (text) => {
        let uppercasetext = text.toUpperCase();
        setgstValue(uppercasetext);
        setgstValueErr("");

        if (text.length > 14) {
            let panN = _getPanNoFromGst(text);
            if (panNo.length != 0) {
                if (panNo != panN) {
                    setgstValueErr(ErrorMessages.panGstMisMatchErr);
                }
            }
            else {
                setpanNo(panN);
            }
        }
        setdisableUpdateBtn(false);
    }

    const panNoChangeHandler = (text) => {
        let uppercasetext = text.toUpperCase();
        setpanNo(uppercasetext);
        setpanNoErr("");
        setdisableUpdateBtn(false);
    }
    const selfieBtnPress = () => {
        navigation.navigate('CameraScreen', {
            cameraType: "CAMERA", cameFrom: "SELFIE", isOnlyFrontCam: true,
        });
    }
    const imageIdBtnPress = () => {
        mediaActionRef.current?.show();
    }
    const selfieIdBtnPress = () => {
        navigation.navigate('CameraScreen', {
            cameraType: "CAMERA", cameFrom: "SELFIE_ID", isOnlyFrontCam: true,
        });
    }
    const takeSelfieDataRemove = () => {
        changeVeriSelfieMedia(null);
    }
    const addIdDataRemove = () => {
        changeVeriImageID(null);
    }
    const selfieIdDataRemove = () => {
        changeVeriSelfieId(null);
    }

    const updateBtnPress = () => {
        // navigation.navigate('SuccessfullVerificationScreen')

    }
    function submitProfileDataService() {

        let hashMap = {
            _action_code: "11:UPLOAD_ACCOUNT_VERIFICATION",
            type: verificationPageType,
            docu_type: selectedDocType,
        }
        hashMap.playlist_prog = "NO"
        if (joinPlaylist) {
            hashMap.playlist_prog = "YES"
        }
        let imageHashMap = [];
        if (takeSelfieData != null) {
            let parts = takeSelfieData.uri.split('.');
            let fileFormat = parts[parts.length - 1];
            let captureFile = {
                uri: Platform.OS === 'android' ? takeSelfieData.uri : takeSelfieData.uri.replace('file://', ''),
                name: "verification_image_" + new Date() + "." + fileFormat,
                type: MimeTypeList[fileFormat]
            };
            imageHashMap.push({ inputName: "verification_image", imageData: captureFile });
        }
        if (addIdData != null) {
            let parts = addIdData.uri.split('.');
            let fileFormat = parts[parts.length - 1];
            let captureFile = {
                uri: Platform.OS === 'android' ? addIdData.uri : addIdData.uri.replace('file://', ''),
                name: "document_image_" + new Date() + "." + fileFormat,
                type: MimeTypeList[fileFormat]
            };
            imageHashMap.push({ inputName: "document_image", imageData: captureFile });
        }
        // if (selfieIdData != null) {
        //     let parts = selfieIdData.uri.split('.');
        //     let fileFormat = parts[parts.length - 1];
        //     let captureFile = {
        //         uri: Platform.OS === 'android' ? selfieIdData.uri : selfieIdData.uri.replace('file://', ''),
        //         name: "combined_image_" + new Date() + "." + fileFormat,
        //         type: MimeTypeList[fileFormat]
        //     };
        //     imageHashMap.push({ inputName: "combined_image", imageData: captureFile });
        // }
        // console.log("hashMap", hashMap)
        // console.log("imageHashMap", imageHashMap)
        // setdisableUpdateBtn(false)
        // setShowLoading(false);
        // return

        let connector = new ServerConnector();
        connector.postDataMultiPart(hashMap, imageHashMap, (data) => { // success method
            changeVeriSelfieMedia(null);
            changeVeriImageID(null);
            changeVeriSelfieId(null);
            setdisableUpdateBtn(true)
            setShowLoading(false);
            seterrorMsg("")
            navigation.replace('SuccessfullVerificationScreen', {
                successMsg: data.msg,
            })
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.verification_image) {
                            seterrorMsg(data.data.verification_image)
                            fieldErrorShown = true;
                        }
                        if (data.data.document_image) {
                            seterrorMsg(data.data.document_image)
                            fieldErrorShown = true;
                        }
                        if (data.data.combined_image) {
                            seterrorMsg(data.data.combined_image)
                            fieldErrorShown = true;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage);
                }
            }
        });
    }
    const GenerateDisplayNameForLabel = (type) => {
        if (type == "INDIVIDUAL") {
            settakeSelfieHead("Take A Selfie")
            settakeSelfieDown("Take a front angle selfie and ensure we can clearly see your face! ")

            setaddIdHead("Add Government Approved ID");
            setaddIdDown(" Take a clear picture of your Aadhar/Pan/Passport! ");

            setselfieIdHead("Take a Selfie With The same ID");
            setselfieIdDown("Almost done, just make sure the picture clearly shows you holding the same ID! ");
        }
        if (type == "GROUP") {
            settakeSelfieHead("Take A Group Selfie")
            settakeSelfieDown("Take a front angle selfie and ensure we can clearly see everyone! ")

            setaddIdHead(" Add Government Approved ID’s");
            setaddIdDown("Take a clear picture of all members Aadhar/Pan/Passport!");

            setselfieIdHead("Take a Group Selfie With The Same ID’s");
            setselfieIdDown("Almost done, just make sure the picture clearly shows everyone holding the same ID! ");
        }
    }
    const veriTypeItemPress = (type) => {
        setverificationPageType(type)
    }
    function uploadVerificationFirm() {
        let hashMap = {
            _action_code: "11:UPLOAD_ACCOUNT_VERIFICATION_FIRM",
            type: "FIRM",
            gstn: gstValue,
            pan: panNo,
        }
        hashMap.playlist_prog = "NO"
        if (joinPlaylist) {
            hashMap.playlist_prog = "YES"
        }
        // console.log("hashMap", hashMap)
        // setdisableUpdateBtn(false)
        // setShowLoading(false);
        // return
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method  
            setdisableUpdateBtn(true)
            setShowLoading(false);
            seterrorMsg("")
            navigation.replace('SuccessfullVerificationScreen', {
                successMsg: data.msg,
            })
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.gstn) {
                            setgstValueErr(data.data.gstn)
                            fieldErrorShown = true;
                        }
                        if (data.data.pan) {
                            setpanNoErr(data.data.pan)
                            fieldErrorShown = true;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage);
                }
            }
        });
    }
    const mediaMenuActionViewPress = (clickID, obj) => {
        mediaActionRef.current?.hide();
        if (clickID == "CAPTURE") {
            setisGalleryBtnPress(false);
            navigation.navigate('CameraScreen', {
                cameraType: "CAMERA", cameFrom: "IMAGE_ID"
            });
        }
        if (clickID == "GALLERY") {
            mediaActionRef.current?.hide();
            setTimeout(() => {
                mediaBtnPress();
            }, 500);

        }
    }
    const mediaBtnPress = () => {
        setisGalleryBtnPress(true);
        launchImageLibraryData();
    }
    const launchImageLibraryData = async () => {
        const checkPermission = requestStoragePermission();
        checkPermission.then(res => {
            if (res) {
                openImageFolder();
            }
        })

    }
    const openImageFolder = () => {
        var options = {
            mediaType: 'photo', //to allow only photo to select ...no video
            // saveToPhotos: true,  //to store captured photo via camera to photos or else it will be stored in temp folders and will get deleted on temp clear
            includeBase64: false,
            videoQuality: 'low',
            selectionLimit: 1,

        };
        launchImageLibrary(options, (response) => {

            if (response.didCancel) {
                //console.log('User cancelled image picker');
            } else if (response.error) {
                //console.log('ImagePicker Error: ', response.error);
            } else if (response.customButton) {
                //console.log('User tapped custom button: ', response.customButton);
            } else {
                // if (response.assets) {
                //     //console.log("First Res", response);
                //     const source = response.assets[0];
                //     let uri = source.uri;
                //     compressVideoData(uri);
                // }

                if (response.hasOwnProperty("assets")) {
                    if (response.assets[0].fileSize <= 15360000) { //15mb
                        displayTheImageFile(response);
                    }
                    else {
                        if (response.assets[0].hasOwnProperty("fileSize")) {
                            if (response.assets[0].fileSize == null || response.assets[0].fileSize == 0) {
                                displayTheImageFile(response);
                            }
                            else {
                                seterrorMsg(ErrorMessages.addVerificationMediaSizeErr);
                            }
                        }
                        else {
                            displayTheImageFile(response);
                        }
                    }
                }
                else {
                    seterrorMsg("Somethings went wrong!");
                }
            }
        });
    }
    const displayTheImageFile = (response) => {
        changeVeriImageID({ uri: response.assets[0].uri, captureType: "IMAGE", imageData: response.assets[0] });
    }
    const onDocTypeList = (item) => {
        setSelectedDocType(item.value);
        setSelectedDocTypeErr("");
    }
    const handlePlaylistProgramme = () => {
        setJoinPlaylist(prevState => !prevState);
    }
    const uploadIdInfoButtonPress = () => {

    }
    const viewUploadMediaImage = () => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: addIdData.uri
        })
    }
    const viewSelfieMediaImage = () => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: takeSelfieData.uri
        })
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar
                title="Verify Users"
                navigation={navigation}
                // showTopButton={true}
                extrabackBtn={true}
                extrabackBtnPress={extrabackBtnPress}
                showBorderBottom={false}
                buttonComponent={<TouchableOpacity
                    onPress={() => { verifyBtnPress() }}
                    disabled={disableUpdateBtn}
                ><EntutoTextView style={{ ...defaultStyle.postBtn, opacity: disableUpdateBtn ? 0.4 : 1 }}>
                        Submit</EntutoTextView></TouchableOpacity>} showTopButton={!disableAllField} />{/* showTopButton={!disableAllField}*/}
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside}>
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                    // </View>
                    : null
            }
            <ScrollView
                style={{ backgroundColor: theme.colors.backgroundColor }}>
                {
                    showRejectionMsg ?
                        <View style={style.rejectionBox}>
                            <EntutoTextView style={style.rejectionMsgTxt}>{rejectionMsg}</EntutoTextView>
                        </View>
                        // <View style={{ ...defaultStyle.errorBoxOutside }}>
                        // <SuccessFailureMsgBox visibleAllTime={true} alertMsg={rejectionMsg} alertKey={rejectionMsg} />
                        // </View>
                        : null
                }
                <View style={{ ...defaultStyle.container, marginTop: 15, }}>
                    <FormButtonGroup
                        dataList={[
                            { label: "Individual", value: "INDIVIDUAL" },
                            { label: "Company", value: "FIRM" },
                        ]}
                        selectedValue={verificationPageType}
                        onChange={veriTypeItemPress}
                    />
                    {
                        verificationPageType == "INDIVIDUAL" ?
                            <View style={{ marginTop: 20, }}>
                                <View style={{ ...style.inputGap }}>
                                    <EntutoEditText
                                        labelTxt="Username"
                                        placeholderTxt="Username"
                                        maxLength={64}
                                        value={profileUserHandle}
                                        disabledField={true}
                                        editable={false}

                                    />
                                </View>
                                <View style={{ ...style.inputGap }}>
                                    <EntutoDropdown label='ID' placeholder='Select ID'
                                        value={selectedDocType}
                                        options={docTypeList}
                                        onOptionChange={onDocTypeList}
                                        errorMsg={selectedDocTypeErr} />
                                </View>
                                <View style={{ ...style.inputGap }}>
                                    <View style={style.fileInputBox}>
                                        <View style={style.fileInputBoxLabel}>
                                            <EntutoTextView style={{
                                                ...style.fileInputBoxLabelTxt,
                                                color: addIdData != null ? theme.colors.inputTextColor : theme.colors.inputPlaceholderColor
                                            }} numberOfLines={1}>
                                                {addIdData != null ? "File Selected" : "Upload Document"}
                                            </EntutoTextView>
                                            {addIdData != null ?
                                                <View style={{ marginStart: 8 }}>
                                                    <TouchableOpacity onPress={() => viewUploadMediaImage()}>
                                                        <MaterialCommunityIcons name='eye-outline' color={'#CCC'} size={20} />

                                                    </TouchableOpacity>
                                                </View>
                                                :
                                                <View style={{ marginStart: 8 }}>
                                                    {/* <TouchableOpacity onPress={() => uploadIdInfoButtonPress()}>
                                                        <MaterialCommunityIcons name='information-outline' color={'#CCC'} size={20} />

                                                    </TouchableOpacity> */}
                                                </View>
                                            }
                                        </View>
                                        <View style={style.fileInputBoxAction}>
                                            {
                                                addIdData != null ?
                                                    <View>
                                                        <TouchableOpacity
                                                            onPress={() => addIdDataRemove()}>
                                                            <Image source={CROSS_ICON}
                                                                style={{ ...style.fileInputBoxActionIcon, }}
                                                            />
                                                        </TouchableOpacity>
                                                    </View>
                                                    :
                                                    <View>
                                                        <TouchableOpacity disabled={disableAllField} onPress={() => imageIdBtnPress()}>
                                                            <Image source={GalleryIcon}
                                                                style={{ ...style.fileInputBoxActionIcon, }}
                                                            />
                                                        </TouchableOpacity>
                                                    </View>
                                            }
                                        </View>

                                    </View>
                                </View>
                                <View style={{ ...style.inputGap }}>
                                    <View style={style.fileInputBox}>
                                        <View style={style.fileInputBoxLabel}>
                                            <EntutoTextView style={{
                                                ...style.fileInputBoxLabelTxt,
                                                color: takeSelfieData != null ? theme.colors.inputTextColor : theme.colors.inputPlaceholderColor
                                            }} numberOfLines={1}>
                                                {takeSelfieData != null ? "File Selected" : "Take a selfie"}
                                            </EntutoTextView>
                                            {takeSelfieData != null ?
                                                <View style={{ marginStart: 8 }}>
                                                    <TouchableOpacity onPress={() => viewSelfieMediaImage()}>
                                                        <MaterialCommunityIcons name='eye-outline' color={'#CCC'} size={20} />

                                                    </TouchableOpacity>
                                                </View>
                                                :
                                                null
                                            }
                                        </View>
                                        <View style={style.fileInputBoxAction}>
                                            {
                                                takeSelfieData != null ?
                                                    <View>
                                                        <TouchableOpacity
                                                            onPress={() => takeSelfieDataRemove()}>
                                                            <Image source={CROSS_ICON}
                                                                style={{ ...style.fileInputBoxActionIcon, }}
                                                            />
                                                        </TouchableOpacity>
                                                    </View>
                                                    :
                                                    <View>
                                                        <TouchableOpacity disabled={disableAllField} onPress={() => selfieBtnPress()}>
                                                            <Image source={CameraIcon}
                                                                style={{ ...style.fileInputBoxActionIcon, }}
                                                            />
                                                        </TouchableOpacity>

                                                    </View>
                                            }
                                        </View>
                                    </View>
                                </View>
                                {/* <View style={{ ...style.inputGap }}>
                                    <View style={style.checkboxContainer}>
                                        <TouchableOpacity onPress={() => handlePlaylistProgramme()}>
                                            <View>
                                                {
                                                    joinPlaylist ?
                                                        <Fontisto name='checkbox-active' color={'#CCC'} size={20} />
                                                        :
                                                        <Fontisto name='checkbox-passive' color={'#CCC'} size={20} />
                                                }
                                            </View>
                                        </TouchableOpacity>
                                        <EntutoTextView style={style.checkBoxLabel}>Join Sotrue Playlist Program</EntutoTextView>

                                    </View>
                                </View> */}

                            </View>
                            : null
                    }

                    {
                        verificationPageType == "FIRM" ?
                            <View style={{ marginTop: 20, }}>
                                <KeyboardAvoidingView
                                    enabled={Platform.OS == 'ios' ? true : true}
                                    style={{ flex: 1 }}

                                    behavior={Platform.OS === 'ios' ? 'position' : 'height'}>
                                    <View style={{ ...style.inputGap }}>
                                        <EntutoEditText
                                            labelTxt="Username"
                                            placeholderTxt="Username"
                                            maxLength={64}
                                            value={profileUserHandle}
                                            disabledField={true}
                                            editable={false}

                                        />
                                    </View>
                                    {/* <View style={style.veriHeadTxtBox}>
                                        <EntutoTextView style={style.veriHeadTxt}>Add your GST ID Details</EntutoTextView>
                                    </View> */}
                                    <View style={style.inputGap}>
                                        <EntutoEditText
                                            labelTxt="GST"
                                            placeholderTxt="Purely for tax purposes, if you want!"
                                            maxLength={15}
                                            value={gstValue}
                                            onChangeText={text => gstValueChangeHandler(text)}
                                            showErrorField={gstValueErr.length}
                                            errorMsg={gstValueErr}
                                            autoCapitalize={'characters'}

                                        />
                                        <View style={defaultStyle.inputUnderLineView}>
                                            <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                                Please ensure this is the GST ID registered to your business!</EntutoTextView>
                                        </View>
                                    </View>

                                    {/* <View style={style.veriHeadTxtBox}>
                                        <EntutoTextView style={style.veriHeadTxt}>Add your PAN Details</EntutoTextView>
                                    </View> */}
                                    <View style={style.inputGap}>
                                        <EntutoEditText
                                            labelTxt="PAN Number"
                                            placeholderTxt="Same old, same old!"
                                            maxLength={12}
                                            value={panNo}
                                            onChangeText={text => panNoChangeHandler(text)}
                                            showErrorField={panNoErr.length}
                                            errorMsg={panNoErr}
                                            autoCapitalize={'characters'}

                                        />
                                        <View style={defaultStyle.inputUnderLineView}>
                                            <EntutoTextView style={defaultStyle.inputUnderLineTxt}>
                                                Please ensure this is the PAN registered to your business!</EntutoTextView>
                                        </View>
                                    </View>
                                    {/* <View style={{ ...style.inputGap }}>
                                        <View style={style.checkboxContainer}>
                                            <TouchableOpacity onPress={() => handlePlaylistProgramme()}>
                                                <View>
                                                    {
                                                        joinPlaylist ?
                                                            <Fontisto name='checkbox-active' color={'#CCC'} size={20} />
                                                            :
                                                            <Fontisto name='checkbox-passive' color={'#CCC'} size={20} />
                                                    }
                                                </View>
                                            </TouchableOpacity>
                                            <EntutoTextView style={style.checkBoxLabel}>Join Sotrue Playlist Program</EntutoTextView>

                                        </View>
                                    </View> */}
                                </KeyboardAvoidingView>
                            </View>
                            : null
                    }


                    {/* <View style={{ alignItems: 'center', justifyContent: 'center', paddingHorizontal: 20, }}>
                        <EntutoTextView style={style.uploadTxt}>Uploaded documents are required only for
                            user identity verification purposes</EntutoTextView>
                    </View> */}
                    {/* {
                        verificationPage == "FORM" ?
                            <>
                                {
                                    verificationPageType == "INDIVIDUAL" || verificationPageType == "GROUP" ?
                                        <>
                                            <View style={style.verificationCameraBox}>
                                                {
                                                    takeSelfieData != null ?
                                                        <>
                                                            <Image
                                                                style={style.veriImage}
                                                                source={{ uri: takeSelfieData.uri }}
                                                                resizeMode='cover' />
                                                            <View style={style.changeBox}>
                                                                <LinearGradient colors={[theme.colors.veriGradient1stColor, theme.colors.veriGradient2ndColor]}
                                                                    start={{ x: 0, y: 0 }} end={{ x: 0, y: 1 }}
                                                                    style={style.linearGradient}>
                                                                </LinearGradient>
                                                                <TouchableOpacity style={style.cameraBtnBox}
                                                                    onPress={() => selfieBtnPress()}>
                                                                    <View style={style.cameraBtnBox}>
                                                                        <EntutoTextView style={style.cameraBoxBtn}>Change</EntutoTextView>
                                                                    </View>
                                                                </TouchableOpacity>
                                                                <TouchableOpacity style={style.cameraBtnBox}
                                                                    onPress={() => takeSelfieDataRemove()}>
                                                                    <View style={style.cameraBtnBox}>
                                                                        <EntutoTextView style={style.cameraBoxBtn}>Remove</EntutoTextView>
                                                                    </View>
                                                                </TouchableOpacity>

                                                            </View>
                                                        </>
                                                        :

                                                        <TouchableOpacity disabled={disableAllField} onPress={() => selfieBtnPress()}>
                                                            <View style={style.cameraBox}>
                                                                <Image source={CameraIcon}
                                                                    style={style.cameraIconStyle} />
                                                                <EntutoTextView style={style.cameraIconTxt}>{takeSelfieHead}</EntutoTextView>
                                                                <EntutoTextView style={style.cameraIconExtraTxt}>{takeSelfieDown}</EntutoTextView>
                                                            </View>
                                                        </TouchableOpacity>
                                                }

                                            </View>
                                            <View style={style.verificationCameraBox}>
                                                {
                                                    addIdData ?
                                                        <>
                                                            <Image
                                                                style={style.veriImage}
                                                                source={{ uri: addIdData.uri }}
                                                                resizeMode='cover' />
                                                            <View style={style.changeBox}>
                                                                <LinearGradient colors={[theme.colors.veriGradient1stColor, theme.colors.veriGradient2ndColor]}
                                                                    start={{ x: 0, y: 0 }} end={{ x: 0, y: 1 }}
                                                                    style={style.linearGradient}>
                                                                </LinearGradient>
                                                                <TouchableOpacity style={style.cameraBtnBox}
                                                                    onPress={() => imageIdBtnPress()}>
                                                                    <View style={style.cameraBtnBox}>
                                                                        <EntutoTextView style={style.cameraBoxBtn}>Change</EntutoTextView>
                                                                    </View>
                                                                </TouchableOpacity>
                                                                <TouchableOpacity style={style.cameraBtnBox}
                                                                    onPress={() => addIdDataRemove()}>
                                                                    <View style={style.cameraBtnBox}>
                                                                        <EntutoTextView style={style.cameraBoxBtn}>Remove</EntutoTextView>
                                                                    </View>
                                                                </TouchableOpacity>

                                                            </View>
                                                        </>
                                                        :
                                                        <TouchableOpacity disabled={disableAllField} onPress={() => imageIdBtnPress()}>
                                                            <View style={style.cameraBox}>
                                                                <Image source={GalleryIcon}
                                                                    style={{ ...style.cameraIconStyle, tintColor: '#000' }}
                                                                />
                                                                <EntutoTextView style={style.cameraIconTxt}>{addIdHead}</EntutoTextView>
                                                                <EntutoTextView style={style.cameraIconExtraTxt}>{addIdDown}</EntutoTextView>
                                                            </View>
                                                        </TouchableOpacity>
                                                }

                                            </View>
                                            <View style={style.verificationCameraBox}>
                                                {
                                                    selfieIdData ?
                                                        <>
                                                            <Image
                                                                style={style.veriImage}
                                                                source={{ uri: selfieIdData.uri }}
                                                                resizeMode='cover' />
                                                            <View style={style.changeBox}>
                                                                <LinearGradient colors={[theme.colors.veriGradient1stColor, theme.colors.veriGradient2ndColor]}
                                                                    start={{ x: 0, y: 0 }} end={{ x: 0, y: 1 }}
                                                                    style={style.linearGradient}>
                                                                </LinearGradient>
                                                                <TouchableOpacity style={style.cameraBtnBox}
                                                                    onPress={() => selfieIdBtnPress()}>
                                                                    <View style={style.cameraBtnBox}>
                                                                        <EntutoTextView style={style.cameraBoxBtn}>Change</EntutoTextView>
                                                                    </View>
                                                                </TouchableOpacity>
                                                                <TouchableOpacity style={style.cameraBtnBox}
                                                                    onPress={() => selfieIdDataRemove()}>
                                                                    <View style={style.cameraBtnBox}>
                                                                        <EntutoTextView style={style.cameraBoxBtn}>Remove</EntutoTextView>
                                                                    </View>
                                                                </TouchableOpacity>

                                                            </View>
                                                        </>

                                                        :
                                                        <TouchableOpacity disabled={disableAllField} onPress={() => selfieIdBtnPress()}>
                                                            <View style={style.cameraBox}>
                                                                <Image source={CameraIcon}
                                                                    style={style.cameraIconStyle} />
                                                                <EntutoTextView style={style.cameraIconTxt}>
                                                                    {selfieIdHead}
                                                                </EntutoTextView>
                                                                <EntutoTextView style={style.cameraIconExtraTxt}>{selfieIdDown}</EntutoTextView>

                                                            </View>
                                                        </TouchableOpacity>
                                                }

                                            </View>

                                        </>
                                        : null
                                }
                               
                    {

                        verificationPage == "TYPE" ?
                            <>
                                <View style={{
                                    ...style.verificationTypeBox,
                                    opacity: veriTempPageType == "INDIVIDUAL" ? 0.5 : 1
                                }}>

                                    <TouchableOpacity onPress={() => veriTypeItemPress("INDIVIDUAL")}>
                                        <View style={style.veriTypeBox}>
                                            <Image source={INDIVIDUAL_ICON}
                                                style={style.veriTypeIcon} />
                                            <EntutoTextView style={style.veriTypeIconTxt}>
                                                INDIVIDUAL
                                            </EntutoTextView>
                                            <EntutoTextView style={style.veriTypeIconExtraTxt}>
                                                Are you an independent digital
                                                creator, personality or loyal fan? Get verified here!
                                            </EntutoTextView>

                                        </View>
                                    </TouchableOpacity>
                                </View>

                                <View style={{
                                    ...style.verificationTypeBox,
                                    opacity: veriTempPageType == "GROUP" ? 0.5 : 1
                                }}>
                                    <TouchableOpacity onPress={() => veriTypeItemPress("GROUP")}>
                                        <View style={style.veriTypeBox}>
                                            <Image source={GROUP_ICON}
                                                style={style.veriTypeGIcon} />
                                            <EntutoTextView style={style.veriTypeIconTxt}>
                                                GROUP
                                            </EntutoTextView>
                                            <EntutoTextView style={style.veriTypeIconExtraTxt}>
                                                Are you a group, team
                                                or band? Get verified here!
                                            </EntutoTextView>

                                        </View>
                                    </TouchableOpacity>
                                </View>

                                <View style={{
                                    ...style.verificationTypeBox,
                                    opacity: veriTempPageType == "FIRM" ? 0.5 : 1
                                }}>
                                    <TouchableOpacity onPress={() => veriTypeItemPress("FIRM")}>
                                        <View style={style.veriTypeBox}>
                                            <Image source={FIRM_ICON}
                                                style={style.veriTypeBIcon} />
                                            <EntutoTextView style={style.veriTypeIconTxt}>
                                                BUSINESS
                                            </EntutoTextView>
                                            <EntutoTextView style={style.veriTypeIconExtraTxt}>
                                                Are you a company, institution or collective? Get
                                                verified here!
                                            </EntutoTextView>

                                        </View>
                                    </TouchableOpacity>
                                </View>
                            </>
                            : null
                    }
 */}

                </View>

            </ScrollView>
            <ActionSheet ref={mediaActionRef}
                statusBarTranslucent

                bounciness={4}
                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <MediaMenuActionView mediaMenuActionViewPress={mediaMenuActionViewPress}
                    showChooseMedia={true} showRemoveMedia={false} />
            </ActionSheet >
        </>
    )
}

export default VerificationsScreen;

const styles = theme => StyleSheet.create({
    uploadTxt: {
        color: '#000',
        fontWeight: '400',
        fontSize: theme.calculateFontSize(14),
        textAlign: 'center'

    },
    verificationCameraBox: {
        width: 250,
        height: 167,
        backgroundColor: '#F2EBE9',
        borderRadius: 15,
        marginTop: 20,
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderStyle: 'dashed',
        borderColor: theme.colors.primaryColor,
        elevation: 2,

    },
    veriImage: {
        width: 250,
        height: 167,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 15,
    },
    cameraIconStyle: {
        width: 20,
        height: 20,
    },
    cameraIconTxt: {
        fontSize: theme.calculateFontSize(14),
        fontWeight: '400',
        textAlign: 'center',
        color: theme.colors.primaryColor,
        marginTop: 15,
    },
    cameraIconExtraTxt: {
        fontSize: theme.calculateFontSize(12),
        fontWeight: '400',
        textAlign: 'center',
        color: theme.colors.primaryTextColor,
        marginTop: 4,
    },
    cameraBox: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 15,
    },
    cameraBtnBox: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    cameraBoxBtn: {
        paddingVertical: 8,
        color: '#FFFFFF',
    },
    changeBox: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        flexDirection: 'row',
        borderBottomEndRadius: 15,
        borderBottomStartRadius: 15,

    },
    linearGradient: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        borderRadius: 15,
        borderTopRightRadius: 0,
        borderTopLeftRadius: 0,
    },
    rejectionBox: {
        padding: 12,
        // justifyContent: 'center',
        // alignItems: 'center',
        backgroundColor: theme.colors.rejectBoxColor
    },
    rejectionMsgTxt: {
        color: "#fff"
    },
    verificationTypeBox: {
        width: 250,
        backgroundColor: '#F2EBE9',
        borderRadius: 15,
        marginTop: 20,
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderStyle: 'dashed',
        borderColor: theme.colors.primaryColor,
        elevation: 2,
        paddingVertical: 10,
        position: 'relative'

    },
    selectTypeBox: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        borderRadius: 15,
        zIndex: 9,
        backgroundColor: '#000',
    },
    veriTypeIconTxt: {
        fontSize: theme.calculateFontSize(14),
        fontWeight: '400',
        textAlign: 'center',
        color: theme.colors.primaryColor,
        marginTop: 15,
    },
    veriTypeBox: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 15,
        paddingVertical: 15,
    },
    veriTypeIconExtraTxt: {
        fontSize: theme.calculateFontSize(12),
        fontWeight: '400',
        textAlign: 'center',
        color: theme.colors.primaryTextColor,
        marginTop: 4,
    },
    veriTypeIcon: {
        resizeMode: 'contain',
        height: 100,
        width: 200
    },
    veriTypeGIcon: {
        resizeMode: 'contain',
        height: 100,
        width: 200
    },
    veriTypeBIcon: {
        resizeMode: 'contain',
        height: 100,
        width: 200
    },
    veriHeadTxtBox: {
        marginTop: 15,
    },
    veriHeadTxt: {
        color: theme.colors.primaryColor,
        fontSize: theme.calculateFontSize(17),
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
    },
    inputGap: {
        marginBottom: 20,
    },
    fileInputBox: {
        flexDirection: 'row',
        height: 54,
        borderColor: theme.colors.fileInputBoxBorderC,
        borderWidth: 0.5,
        paddingStart: 10,
        alignItems: 'center'
    },
    fileInputBoxLabel: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        flexWrap: 'nowrap',
    },
    fileInputBoxLabelTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.verifyFileInputBoxLabelTxt),
    },
    fileInputBoxAction: {
        marginLeft: 'auto',
        alignItems: 'center',
        marginEnd: 15,
    },
    fileInputBoxActionIcon: {
        height: 20,
        width: 20,
        resizeMode: 'contain',
        tintColor: theme.colors.fileInputBoxActionIconTintColorC,
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',

    },
    checkBoxLabel: {
        fontSize: theme.calculateFontSize(14),
        marginStart: 10,
    }

})
