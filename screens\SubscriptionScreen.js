import React, { useEffect, useState } from 'react'
import { Image, PermissionsAndroid, Platform, ScrollView, Share, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import LinearGradient from 'react-native-linear-gradient';
import EntutoTextView from '../components/common/EntutoTextView';
import { Dropdown } from 'react-native-element-dropdown';
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import { CurrencySymbol, _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import DropdownArrow from '../assets/Images/icon/profile_three_dot.png';
import ListItem from '../components/ListItem';
import CustomStatusBar from '../components/common/CustomStatusBar';
import { indianMoneyFormatDisplay } from '../utils/Utils';
import { Menu } from 'react-native-paper';
import RNFetchBlob from "rn-fetch-blob";
import ErrorMessages from '../constants/ErrorMessages';
import CustomSnackbar from '../components/common/CustomSnackbar';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import { requestStoragePermission } from '../utils/PermissionManager';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import SubscriptionBarChart from '../components/SubscriptionBarChart';
import PrimaryButton from '../components/common/PrimaryButton';
import SecondaryButton from '../components/common/SecondaryButton';
import PrimarySecondButton from '../components/common/PrimarySecondButton';


const SubscriptionScreen = ({ navigation }) => {
    const [dateValue, setdateValue] = useState("CURRENT_DAY_USER");
    const [dateValueFocus, setdateValueFocus] = useState(false);
    const [dateValueData, setdateValueData] = useState([]);
    const [selectedDateDisplayValue, setSelectedDateDisplayValue] = useState("");

    const [totalEarnVal, settotalEarnVal] = useState(0.00);
    const [subsEarnVal, setsubsEarnVal] = useState(0.00);
    const [referEarnVal, setreferEarnVal] = useState(0.00);

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [snackBarType, setsnackBarType] = useState("FAILED");
    const [showLoading, setShowLoading] = useState(true);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    const [chartData, setChartData] = useState([
        { label: "27", value: 20 },
        { label: "28", value: 12 },
        { label: "29", value: 120 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 200 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 10 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 45 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 78 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 160 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
        { label: "30", value: 80 },
    ]);

    useEffect(() => {
        getDateListService();

        getProfileSummary(dateValue);
    }, [])

    function getDateListService() {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: "USER_REPORT_PERIOD",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            // let extraConfigData = { config_key: "LIFETIME", display_value: "Lifetime" };
            let paymentDateArray = data.data;
            data.data.map(item => {
                if (dateValue == item.config_key) {
                    setSelectedDateDisplayValue(item.display_value)
                }
            })
            // paymentDateArray.push(extraConfigData);
            if (data.data.length != 0) {
                // setdateValue(data.data[0].config_key);
                // getProfileSummary(data.data[0].config_key);
            }
            setdateValueData(paymentDateArray);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setdateValueData([]);
            }
        });
    }
    function getProfileSummary(dateRangeVal) {
        let hashMap = {
            _action_code: "11:GET_PAYMENT_SUMMARY_VALUES",
            date_range: dateRangeVal
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false)
            settotalEarnVal(0);
            setsubsEarnVal(data.data[0].payments_received);
            setreferEarnVal(data.data[0].referral_payment);

        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false)
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                settotalEarnVal(0);
                setsubsEarnVal(0);
                setreferEarnVal(0);
            }
        });
    }
    const renderItem = (item) => {
        return (
            <View style={style.item}>
                <Text allowFontScaling={false} style={[
                    style.textItem,
                    dateValue == item.config_key && style.selectedTextItem
                ]}>{item.display_value}</Text>
            </View>
        );
    };
    const backbtnPress = () => {
        navigation.goBack(null);
    }
    const mySubscriberBtnPress = () => {
        navigation.navigate("MySubscribersScreen");
    }
    const mySubscriptionBtnPress = () => {
        navigation.navigate("MySubscriptionsScreen");
    }
    const changeDateValue = (itemVal) => {
        setdateValue(itemVal);
        setShowLoading(true);
        getProfileSummary(itemVal);
    }
    const [visible, setVisible] = React.useState(false);
    const openMenu = () => setVisible(true);

    const closeMenu = () => setVisible(false);
    const downloadReportBtnPress = () => {
        checkPermission();
    }
    const checkPermission = async () => {
        const checkPermissionV = requestStoragePermission();
        checkPermissionV.then(res => {
            if (res) {
                callReportService();
            }
        });
    }
    const callReportService = () => {
        closeMenu();
        setShowLoading(true);
        getFinancialReportService();

    }
    const downloadReportFile = (urlVal) => {

        // Get today's date to add the time suffix in filename
        let date = new Date();
        // File URL which we want to download
        let FILE_URL = urlVal;

        file_ext = '.xlsx';

        // config: To get response by passing the downloading related options
        // fs: Root directory path to download
        const { config, fs } = RNFetchBlob;
        // let RootDir = fs.dirs.DownloadDir;
        const RootDir = Platform.OS == 'ios' ? fs.dirs.DocumentDir : fs.dirs.DownloadDir
        const finalPath = RootDir + '/report_' + Math.floor(date.getTime() + date.getSeconds() / 2) + file_ext;
        let options = {
            fileCache: true,
            addAndroidDownloads: {
                path: finalPath,
                description: 'downloading file...',
                notification: true,
                // useDownloadManager works with Android only
                useDownloadManager: true,
            },
        };
        config(options)
            .fetch('GET', FILE_URL)
            .then(res => {
                // Alert after successful downloading
                // console.log('res -> ', JSON.stringify(res));
                if (Platform.OS === "ios") {
                    RNFetchBlob.fs.writeFile(finalPath, res.data, 'base64');
                    RNFetchBlob.ios.openDocument(finalPath);
                }
                setSnackbarMsg(ErrorMessages.downloadSuccessMsg);
                setdisplaySnackbar(true);
                setsnackBarType("SUCCESS");
                setrefreshSnackBar(Math.random());
                setShowLoading(false);
            });
    };
    function getFinancialReportService() {
        let hashMap = {
            _action_code: "11:GET_FINANCIAL_REPORT",
            date_range: dateValue,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            downloadReportFile(data.data[0].file_path);
            setShowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
            }
        });
    }
    const viewAllTransactions = () => {
        navigation.navigate('ViewTransactionsScreen', { dateValue: dateValue, })
    }
    function getBartChartListService() {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar title="Subscriptions" showBackBtn={true} navigation={navigation}
                showBorderBottom={false} showTopButton={true}
                buttonComponent={<View style={{ marginLeft: 'auto', zIndex: 2 }}>
                    <View style={style.dropdownContainer}>
                        <Dropdown
                            style={[style.dropdownMain]}
                            placeholderStyle={style.dropdownPlaceholderStyle}
                            selectedTextStyle={style.dropdownSelectedTextStyle}
                            data={dateValueData}
                            maxHeight={300}
                            labelField="display_value"
                            valueField="config_key"
                            placeholder={""}
                            value={dateValue}
                            onChange={item => {
                                changeDateValue(item.config_key);
                                setSelectedDateDisplayValue(item.display_value)
                            }}
                            renderItem={renderItem}
                            renderRightIcon={() => (
                                <Image
                                    source={DropdownArrow}
                                    style={style.dropIcon}
                                    resizeMode='contain' />
                            )}
                        />
                    </View>
                </View>} />
            <ScrollView
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                style={{ backgroundColor: theme.colors.backgroundColor }}>
                <View style={defaultStyle.container}>
                    <View style={{ ...style.subHeadBox, backgroundColor: theme.colors.backgroundColor }}>
                        {/* <View style={style.topHead}>
                     <View style={{ zIndex: 1000 }}>
                        <TouchableOpacity onPress={() => backbtnPress()}>
                            <View style={{ paddingHorizontal: 8, zIndex: 1000 }}>
                                <Image
                                    style={style.backBtn}
                                    source={BackBtn}
                                />
                            </View>
                        </TouchableOpacity>
                    </View>
                    <View style={style.subsTxtBox}>
                        <EntutoTextView style={style.subsTxt}>Subscriptions</EntutoTextView>
                    </View>
                    <View style={{ marginLeft: 'auto', zIndex: 2 }}>
                        <View style={style.dropdownContainer}>
                            <Dropdown
                                style={[style.dropdownMain]}
                                placeholderStyle={style.dropdownPlaceholderStyle}
                                selectedTextStyle={style.dropdownSelectedTextStyle}
                                data={dateValueData}
                                maxHeight={300}
                                labelField="display_value"
                                valueField="config_key"
                                placeholder={"Select Day"}
                                value={dateValue}
                                onChange={item => {
                                    changeDateValue(item.config_key);

                                }}
                                renderItem={renderItem}
                                renderRightIcon={() => (
                                    <Image
                                        source={DropdownArrow}
                                        style={style.dropIcon}
                                        resizeMode='contain' />
                                )}
                            />
                        </View>
                    </View>
                </View> */}
                        {/* <Image
                    style={style.subsVectorImage}
                    source={SubsVecotorIcon} />
                <LinearGradient colors={['#FDAA6A', '#FC6568']}
                    angle={135}
                    start={{ x: 0, y: 0 }} end={{ x: 0.2, y: 1 }}
                    style={style.linearGradient}>
                </LinearGradient> */}

                        <View style={style.earingsBox}>
                            <EntutoTextView style={style.totalEaringsVal}>{CurrencySymbol}{indianMoneyFormatDisplay(totalEarnVal)}</EntutoTextView>
                            {/* <EntutoTextView style={style.totalEaringsValTxt}>Total Earning</EntutoTextView> */}
                            <EntutoTextView style={style.totalEaringsValTxt}>{selectedDateDisplayValue}</EntutoTextView>
                        </View>
                        <View style={{ ...style.earnedBox, display: 'none' }}>
                            <View style={style.earnedTxtBox}>
                                <EntutoTextView style={style.earnedTxtVal}>{CurrencySymbol}{subsEarnVal}</EntutoTextView>
                                <EntutoTextView style={style.earnedTxtValTxt}>Subscriptions Earned</EntutoTextView>
                            </View>
                            <View style={style.earnTxtVerticalLine} />
                            <View style={style.earnedTxtBox2}>
                                <View>
                                    <EntutoTextView style={style.earnedTxtVal}>{CurrencySymbol}{referEarnVal}</EntutoTextView>
                                    <EntutoTextView style={style.earnedTxtValTxt}>Referral Earned</EntutoTextView>
                                </View>
                                <View style={style.threeDotBox}>
                                    <Menu
                                        visible={visible}
                                        onDismiss={closeMenu}
                                        anchorPosition='bottom'
                                        anchor={
                                            <TouchableOpacity style={{ paddingLeft: 10 }}
                                                onPress={openMenu}>
                                                <View style={{ paddingStart: 8 }}>
                                                    <Image
                                                        style={style.profileThreeDot}
                                                        source={require('../assets/Images/icon/profile_three_dot.png')}
                                                    />
                                                </View>
                                            </TouchableOpacity>
                                        }
                                    >
                                        <Menu.Item onPress={() => { downloadReportBtnPress() }} title="Download Report" />
                                    </Menu>
                                    {/* <TouchableOpacity style={{ paddingLeft: 10 }}>
                                <Image
                                    style={style.profileThreeDot}
                                    source={require('../assets/Images/icon/profile_three_dot.png')}
                                />
                            </TouchableOpacity> */}
                                </View>
                            </View>
                        </View>
                        {/* Comment Out for Release */}
                        {/* <View>
                            <SubscriptionBarChart data={chartData} />
                        </View>
                        <View style={{ marginTop: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                            <PrimarySecondButton label={"All Transactions"}
                                onPress={() => viewAllTransactions()} labelStyle={{ fontSize: 12 }}
                                style={{ height: 30 }} />
                        </View> */}
                        <View style={{ marginTop: 15 }}>
                            <SecondaryButton label={"Download Report"}
                                onPress={() => downloadReportBtnPress()} />
                        </View>
                    </View>
                    <View style={{
                        backgroundColor: theme.colors.backgroundColor,
                        flexDirection: 'row',
                        marginTop: 40,
                    }}>
                        {/* <TouchableOpacity onPress={() => mySubscriberBtnPress()}>
                            <ListItem label="My Subscribers" showBottomtxt={true} bottomTxt={"Your true fans!"} />
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => mySubscriptionBtnPress()}>
                            <ListItem label="My Subscriptions" showBottomtxt={true} bottomTxt={"You are a true fan!"} />
                        </TouchableOpacity> */}

                        <View style={{ ...style.subscriptionItem, borderRightColor: "#CCC", borderRightWidth: 1 }}>
                            <TouchableOpacity onPress={() => mySubscriberBtnPress()}>
                                <View>
                                    {/* <EntutoTextView style={style.subscriptionItemValue}>24</EntutoTextView> */}
                                    <EntutoTextView style={style.subscriptionItemLabel}>My</EntutoTextView>
                                    <EntutoTextView style={style.subscriptionItemLabel}>Subscribers</EntutoTextView>

                                </View>
                                {/* <ListItem label="My Subscribers" showBottomtxt={true} bottomTxt={"Your true fans!"} /> */}
                            </TouchableOpacity>
                        </View>
                        <View style={{ ...style.subscriptionItem }}>
                            <TouchableOpacity onPress={() => mySubscriptionBtnPress()}>
                                <View>
                                    {/* <EntutoTextView style={style.subscriptionItemValue}>24</EntutoTextView> */}
                                    <EntutoTextView style={style.subscriptionItemLabel}>My</EntutoTextView>
                                    <EntutoTextView style={style.subscriptionItemLabel}>Subscriptions</EntutoTextView>

                                </View>
                                {/* <ListItem label="My Subscribers" showBottomtxt={true} bottomTxt={"Your true fans!"} /> */}
                            </TouchableOpacity>
                        </View>

                    </View>
                </View>

            </ScrollView>

            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />

        </>
    )
}

export default SubscriptionScreen

const styles = theme => StyleSheet.create({
    subHeadBox: {
        // height: 180,
        position: 'relative',
    },
    linearGradient: {
        position: 'absolute',
        width: '100%',
        height: '100%',
    },
    subsVectorImage: {
        height: 260,
        width: 148.66,
        position: 'absolute',
        right: 0,
        top: 0,
        zIndex: 1,
        opacity: 0.3,
    },
    topHead: {
        flexDirection: 'row',
        position: 'absolute',
        top: 50,
        left: 8.5,
        right: 8.5,
        zIndex: 2,
        alignItems: 'center',
    },
    backBtn: {
        height: 24,
        width: 24,
        resizeMode: "contain",
    },
    subsTxtBox: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1,
    },
    subsTxt: {
        color: '#43180B',
        fontSize: theme.calculateFontSize(17),
        fontWeight: '600',
        alignSelf: 'center'
    },
    dropdownContainer: {
        backgroundColor: 'transparent',
        width: 100,
        alignItems: 'flex-end',
        justifyContent: 'flex-end'

    },
    dropdownMain: {
        height: 20,
        alignItems: 'center',
        width: 100,
    },
    dropdownSelectedTextStyle: {
        fontSize: 1,
        color: theme.colors.backgroundColor,
    },
    dropIcon: {
        width: 10,
        height: 16,
        alignItems: 'center',
        tintColor: theme.colors.dropIconTintColor
    },
    dropdownPlaceholderStyle: {
        fontSize: theme.calculateFontSize(theme.dimensions.subsDropPlaceHolderText),
        color: 'grey'
    },
    item: {
        paddingVertical: 8,
        paddingHorizontal: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    textItem: {
        flex: 1,
        fontSize: theme.calculateFontSize(theme.dimensions.subsDropText),
        color: '#43180B'
    },
    selectedTextItem: {
        flex: 1,
        fontSize: theme.calculateFontSize(theme.dimensions.subsDropText),
        color: theme.colors.primaryColor,
        fontWeight: 'bold',
    },
    earingsBox: {
        // position: 'absolute',
        // top: 0,
        // left: 0,
        // right: 0,
        marginBottom: 20,
        zIndex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    totalEaringsVal: {
        color: theme.colors.totalEaringsVal,
        fontWeight: 'bold',
        fontSize: theme.calculateFontSize(theme.dimensions.subsTotalEaringsValueText),
    },
    totalEaringsValTxt: {
        color: theme.colors.totalEaringsValTxt,
        fontWeight: '600',
        fontSize: theme.calculateFontSize(theme.dimensions.subsTotalEaringsLabelText),
    },
    earnedBox: {
        // position: 'absolute',
        // left: 15,
        // right: 15,
        // bottom: 30,
        height: 53,
        backgroundColor: 'rgba(0, 0, 0,0.2)',
        borderRadius: 5,
        zIndex: 2,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
    earnedTxtBox: {
        flex: 1,
        justifyContent: 'center',
        paddingStart: 20
    },
    earnedTxtBox2: {
        flex: 1,
        justifyContent: 'space-between',
        paddingStart: 20,
        flexDirection: 'row'
    },
    earnTxtVerticalLine: {
        height: 31,
        width: 1,
        backgroundColor: '#FFFFFF',
        justifyContent: 'center',
    },
    earnedTxtVal: {
        color: '#FFFFFF',
        fontWeight: 'bold',
        fontSize: theme.calculateFontSize(theme.dimensions.subsEarnedTxtValText),
    },
    earnedTxtValTxt: {
        color: '#FFFFFF',
        fontWeight: '600',
        fontSize: theme.calculateFontSize(theme.dimensions.subsEarnedTxtLabelText),
    },
    threeDotBox: {
        flexDirection: 'row',
        position: 'relative',
        paddingEnd: 15,
        justifyContent: 'center',
        alignItems: 'center'

    },
    profileThreeDot: {
        width: 5,
        height: 20,

    },
    subscriptionItem: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 15

    },
    subscriptionItemValue: {
        fontSize: theme.calculateFontSize(theme.dimensions.subscriptionItemValue),
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 8,

    },
    subscriptionItemLabel: {
        fontSize: theme.calculateFontSize(theme.dimensions.subscriptionItemLabel),
        color: theme.colors.primaryColor,
        textAlign: 'center'
    }
})
