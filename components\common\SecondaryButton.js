import React from 'react'
import { Platform, StyleSheet } from 'react-native'
import { Button } from 'react-native-paper'
import Dimensions from '../../constants/Dimensions'
import useSThemedStyles from '../../theme/useSThemedStyles'
import useSTheme from '../../theme/useSTheme'

const SecondaryButton = ({ variant = "outlined", upperCase = false, ...props }) => {
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    return <Button {...props}
        uppercase={upperCase}

        labelStyle={{ ...style.labelStyle, ...props.labelStyle }}

        theme={{ ...props.theme, roundness: theme.dimensions.buttonRadius }}
        style={{ ...style.button, ...props.style }}
    >{props.label}</Button>
}

export default SecondaryButton
const styles = theme => StyleSheet.create({
    button: {
        borderColor: "#373737",
        borderWidth: 1,
        backgroundColor: '#373737'
    },
    labelStyle: {
        fontSize: theme.calculateFontSize(theme.dimensions.primaryBtnText),
        padding: 6,
        color: "#FFFFFF",
    },

})
