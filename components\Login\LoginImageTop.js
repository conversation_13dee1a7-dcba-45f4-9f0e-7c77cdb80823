import React from 'react'
import { Image, StyleSheet, View } from 'react-native';
import AppLogo from '../../assets/Images/Logo.png';
import Colors from '../../constants/Colors';
import Dimensions from '../../constants/Dimensions';
import EntutoTextView from '../common/EntutoTextView';
import HeadLineDownTxt from '../common/HeadLineDownTxt';
import HeadLineTxt from '../common/HeadLineTxt';

const LoginImageTop = props => {
    return (
        <View>
            <View style={styles.LoginImageTop}>
                <Image
                    style={styles.loginLogo}
                    source={AppLogo}
                />
            </View>
            <HeadLineTxt style={styles.loginHeadTxt}>{props.heading}</HeadLineTxt>
            <HeadLineDownTxt style={styles.headBodyTxt}>{props.headBodyTxt}</HeadLineDownTxt>
        </View>
    )
}

export default LoginImageTop

const styles = StyleSheet.create({
    LoginImageTop: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 20,
    },
    loginLogo: {
        width: Dimensions.loginLogoWidth,
        resizeMode: 'contain',
        height: Dimensions.loginLogoHeight,

    },
    loginHeadTxt: {
        marginTop: 60,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },
})
