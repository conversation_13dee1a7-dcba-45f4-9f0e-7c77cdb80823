import { Image, Modal, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import CustomStatusBar from '../components/common/CustomStatusBar';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import EntutoTextView from '../components/common/EntutoTextView';
import CustomActivityIndicator from '../components/common/CustomActivityIndicator';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import { FlatList } from 'react-native-gesture-handler';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSThemedStyles from '../theme/useSThemedStyles';
import useSTheme from '../theme/useSTheme';

const SelectDataFieldScreen = ({ navigation, route }) => {
    const { list, selectedValue, title, maxSelectedValue, multiSelect } = route.params;
    const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);
    const [searchQuery, setsearchQuery] = useState("");
    const [isTyped, setisTyped] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setshowLoading] = useState(false);
    const [selectTitle, setselectTitle] = useState("Category");

    const [selectDataList, setselectDataList] = useState(list);
    const [selectedV, setselectedV] = useState(selectedValue)
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const onChangeSearch = query => {
        if (query.length != 0) {
            setisTyped(true);
        }
        else {
            setisTyped(false);
        }
        setsearchQuery(query);
    }
    const clearSearchTxt = () => {
        setsearchQuery("");
        setisTyped(false);
    }

    const doneButtonPress = () => {

    }
    const renderSelectRow = ({ item }) => {
        return (
            <>
                <View style={{ ...defaultStyle.ListCardStyle, ...style.selectRowBox }}>
                    <View>
                        <EntutoTextView style={style.selectRowBoxTxt}>{item.display_value}</EntutoTextView>
                    </View>
                </View>
            </>
        );
    };
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />

            <HomeTopNavigationBar title={title} showBackBtn={true}
                showBorderBottom={false} navigation={navigation}
                showTopButton={true}
                buttonComponent={<TouchableOpacity
                    onPress={() => doneButtonPress()}
                    disabled={disableUpdateBtn}
                ><EntutoTextView style={{ ...defaultStyle.postBtn, opacity: disableUpdateBtn ? 0.4 : 1 }}>Done</EntutoTextView></TouchableOpacity>}
            />
            {/* <Modal style={{ margin: 0, flex: 1 }}>

            </Modal> */}
            <View style={defaultStyle.container}>
                <View style={style.searchBarBox}>
                    <View
                        style={style.searchBar}>
                        <Image
                            source={require('../assets/Images/icon/search_icon.png')}
                            style={style.searchIcon}
                        />
                        <TextInput
                            style={style.input}
                            placeholder="Search..."
                            placeholderTextColor={theme.colors.inputPlaceholderColor}
                            value={searchQuery}
                            autoCorrect={false}
                            onChangeText={onChangeSearch}
                            selectionColor={theme.colors.primaryColor}
                        />
                        {isTyped && (
                            <TouchableOpacity onPress={() => clearSearchTxt()}>
                                <Image
                                    source={require('../assets/Images/icon/close_icon.png')}
                                    style={style.searchCrossIcon}
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
                {
                    showLoading ?
                        <CustomActivityIndicator progress={showLoading} />
                        : null
                }
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside} >
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        : null
                }


            </View>
            <FlatList
                contentContainerStyle={{ paddingBottom: 20 }}
                data={selectDataList}
                renderItem={renderSelectRow}
                keyExtractor={(item, index) => `${index}`} />
        </>
    )
}

export default SelectDataFieldScreen;

const styles = theme => StyleSheet.create({
    searchBarBox: {
        alignItems: "center",
        flexDirection: "row",
        marginTop: 8,
    },
    searchBar: {
        flex: 1,
        flexDirection: "row",
        backgroundColor: '#F2EBE9',
        borderRadius: theme.dimensions.searchBarRadius,
        alignItems: "center",
    },
    searchIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        marginLeft: theme.dimensions.searchInputIconMH,
    },
    input: {
        fontSize: theme.dimensions.searchTextInputSize,
        marginHorizontal: 8,
        flex: 1,
        color: theme.colors.inputTextColor
    },
    searchCrossIcon: {
        height: 15,
        width: 15,
        marginRight: 8,
    },
    selectRowBox: {
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomColor: "#00000050",
        borderBottomWidth: 0.5,
        marginBottom: 8,
    },
    selectRowBoxTxt: {
        color: "#43180B",
        fontSize: 17,
        paddingBottom: 15,
        fontWeight: '600'
    },
})