import React, { useContext, useEffect, useState } from 'react'
import { FlatList, Image, StyleSheet, TouchableOpacity, View } from 'react-native'
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import LinearGradient from 'react-native-linear-gradient';
import ServerConnector from '../../utils/ServerConnector';
import { MaxStoryTxtLimit, _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { AppStateContext } from '../..';
import { hasImageUrlExist } from '../../utils/Utils';
import StoryPlaceholder from '../placeholder/StoryPlaceholder';
import Colors from '../../constants/Colors';
import appData from '../../data/Data';
import ConfirmationPopup from '../common/ConfirmationPopup';
import { ActivityIndicator } from 'react-native-paper';
import useDefaultStyle from '../../theme/useDefaultStyle';

const StoryComponent = ({ navigation, refreshPage, ...props }) => {
    const [storyList, setstoryList] = useState([]);
    const [hasStory, sethasStory] = useState(false);
    const [showLoading, setShowLoading] = useState(true);
    const { fullUserDetails, userProfileImage } = useContext(AppStateContext);
    const __ProfileImage = fullUserDetails.hasOwnProperty("_profile_picture") ? fullUserDetails._profile_picture : "";
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : "";
    const { defaultStyle } = useDefaultStyle();
    const [showConfirmPopup, setshowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());

    const [confirmTitle, setconfirmTitle] = useState("Alert");
    const [confirmMsg, setconfirmMsg] = useState("This story is no longer available!");
    const [warringsData, setwarringsData] = useState({});
    const [storyLoading, setstoryLoading] = useState(false)
    const [selectedProfileSeq, setSelectedProfileSeq] = useState([])

    useEffect(() => {
        setShowLoading(true);
        hasUserProfileStory();
        getStoriesProfile();
    }, [refreshPage]);


    React.useEffect(() => {
        const unsubscribeStory = navigation.addListener('focus', () => {
            if (appData.__StoryPageRefreshCheck == "YES") {
                appData.__StoryPageRefreshCheck = "NO";
                setShowLoading(true);
                hasUserProfileStory();
                getStoriesProfile();

            }
        });
        return unsubscribeStory;
    }, [navigation]);

    function hasUserProfileStory() {
        let hashMap = {
            _action_code: "11:GET_PROFILE_STORIES",
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            sethasStory(true);
            setShowLoading(false);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                sethasStory(false);
                setShowLoading(false);
            }

        });
    }
    function getStoriesProfile() {
        let hashMap = {
            _action_code: "11:GET_STORIES",
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let pList = [];
            data.data.map(obj => {
                pList.push(obj.profile_seq);
            })
            setSelectedProfileSeq(pList)
            setstoryList([...[], ...data.data]);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setstoryList([]);
                setSelectedProfileSeq([])
            }

        });
    }

    const profileStoryBtnPress = (profileSeq) => {
        checkProfileStory(profileSeq);
        setstoryLoading(true);
    }
    const confirmPopupPress = (clickId, obj) => {
        if (clickId == "positive") {
            setShowLoading(true);
            hasUserProfileStory();
            getStoriesProfile();
        }
    }
    function checkProfileStory(profileSeq) {
        let hashMap = {
            _action_code: "11:GET_PROFILE_STORIES",
            profile_seq: profileSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setstoryLoading(false);
            navigation.navigate("StoryStatusScreen", {
                profileSeq: profileSeq,
                selectedProfileSeq: selectedProfileSeq
            })

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setstoryLoading(false);
                if (__ProfileSeq == profileSeq) {
                    navigation.navigate('AddStoryScreen');
                }
                else {
                    setshowConfirmPopup(true);
                    setshowConfirmPopupKey(Math.random());
                }

            }

        });
    }

    const renderStoryItem = ({ item }) => {
        return (
            <TouchableOpacity onPress={() => profileStoryBtnPress(item.profile_seq)}>
                <View style={styles.fullStoryBox}>
                    <View style={{ ...styles.homeStoryBox, }}>

                        <LinearGradient colors={['#FDAA6A', '#FC6568']}
                            start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
                            style={styles.linearGradient} >
                        </LinearGradient>
                        <ProgressiveImage
                            style={styles.homeStoryImage}
                            source={hasImageUrlExist(item.profile_picture) ? { uri: item.profile_picture } : null}
                            defaultImageSource={require('../../assets/Images/full_user_image_place_holder.png')}
                            resizeMode={'cover'}
                        />

                    </View>
                    <EntutoTextView style={styles.storyProfileName}>
                        {((item.display_name).length > MaxStoryTxtLimit) ?
                            (((item.display_name).substring(0, MaxStoryTxtLimit - 3)) + '...') :
                            item.display_name}

                    </EntutoTextView>
                </View>
            </TouchableOpacity>
        );
    };
    const addStoryBtnPress = () => {
        navigation.navigate('AddStoryScreen');
    }
    const [profileImage, setprofileImage] = useState(null);
    useEffect(() => {
        setprofileImage(userProfileImage);
    }, [userProfileImage])
    return (
        <View style={styles.storiesContainer}>
            {
                storyLoading ?
                    <View style={defaultStyle.errorIndicatorBox} >
                        <ActivityIndicator size={'large'} />
                    </View>
                    : null
            }
            {
                showLoading ?
                    <StoryPlaceholder />
                    :
                    <>
                        <TouchableOpacity onPress={() => profileStoryBtnPress(__ProfileSeq)}>
                            <View style={{ ...styles.myStoryBox, ...styles.fullStoryBox }}>
                                <View style={styles.homeStoryBox}>
                                    {
                                        hasStory ?
                                            <LinearGradient colors={['#FDAA6A', '#FC6568']}
                                                start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
                                                style={styles.linearGradient} />
                                            :
                                            <LinearGradient colors={['#DADADA', '#DADADA']}
                                                start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
                                                style={styles.linearGradient} />
                                    }

                                    <ProgressiveImage
                                        style={styles.homeStoryImage}
                                        source={hasImageUrlExist(profileImage) ? { uri: profileImage } : null}
                                        defaultImageSource={require('../../assets/Images/full_user_image_place_holder.png')}
                                        resizeMode='cover'
                                    />
                                    {
                                        !hasStory ?
                                            <View style={[styles.myStoryPlusBox,
                                            !hasStory && styles.myStoryPlusRightBox
                                            ]}>
                                                <TouchableOpacity onPress={() => addStoryBtnPress()}>
                                                    <Image
                                                        style={styles.myStoryPlusIcon}
                                                        source={require("../../assets/Images/icon/story_plus.png")}
                                                        resizeMode={'cover'}
                                                    />
                                                </TouchableOpacity>
                                            </View>
                                            : null
                                    }

                                </View>
                                <EntutoTextView style={styles.storyProfileName}>You</EntutoTextView>
                            </View>
                        </TouchableOpacity>
                        <View style={{ flexDirection: 'row', flex: 1 }}>
                            <FlatList
                                data={storyList}
                                renderItem={renderStoryItem}
                                keyExtractor={item => item.profile_seq}
                                horizontal
                                showsHorizontalScrollIndicator={false} />
                        </View>
                    </>
            }
            {
                showConfirmPopup &&
                <ConfirmationPopup
                    visiblePopupKey={showConfirmPopupKey}
                    visiblePopup={showConfirmPopup}
                    title={confirmTitle}
                    messagebody={confirmMsg}
                    positiveButton="Ok"
                    negativeButton="No"
                    isOkBtnPopup={true}
                    data={warringsData}
                    popupClick={(clickID, data) => { confirmPopupPress(clickID, data) }}
                />
            }

        </View>
    )
}

export default StoryComponent;

const styles = StyleSheet.create({
    fullStoryBox: {
        justifyContent: 'center',
        alignItems: 'center'
    },
    storyProfileName: {
        fontSize: 11,
        color: '#43180B',
        marginTop: 6,
        marginHorizontal: 6
    },
    storiesContainer: {
        flexDirection: 'row'
    },
    myStoryBox: {
        position: 'relative',
        marginRight: 8,

    },
    myStoryPlusBox: {
        position: 'absolute',
        top: 16,
        left: 16,
    },
    myStoryPlusRightBox: {
        position: 'absolute',
        top: 30,
        left: 40,
        backgroundColor: Colors.primaryColor,
        borderRadius: 8,
    },
    myStoryPlusIcon: {
        width: 24,
        height: 24,

    },
    homeStoryBox: {
        position: 'relative',
        width: 56,
        height: 56,
        borderRadius: 15,
        marginRight: 5,
        // overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center'
    },
    homeStoryImage: {
        width: 52,
        height: 52,
        borderRadius: 15,
        borderColor: '#FFFFFF',
        borderWidth: 2,
    },
    linearGradient: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        borderRadius: 15,
    },

})
