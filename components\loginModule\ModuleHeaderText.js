import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import EntutoTextView from '../common/EntutoTextView'
import useSThemedStyles from '../../theme/useSThemedStyles';

const ModuleHeaderText = ({ text = "", secondText = "", style = {} }) => {
    const style2 = useSThemedStyles(styles);
    return (
        <View style={style}>
            <EntutoTextView style={style2.headerText}>{text}</EntutoTextView>
            <EntutoTextView style={style2.headerSecondText}>{secondText}</EntutoTextView>
        </View>
    )
}

export default ModuleHeaderText

const styles = theme => StyleSheet.create({
    headerText: {
        fontSize: theme.calculateFontSize(theme.dimensions.LoginModuleHeaderText),
    },
    headerSecondText: {
        fontSize: theme.calculateFontSize(theme.dimensions.LoginModuleSecondHeaderText),
        marginTop: 6,
        color: '#AAB2B7'
    },

})