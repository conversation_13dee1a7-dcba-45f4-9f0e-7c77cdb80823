import React, { useEffect } from 'react'
import { Image, Pressable, StyleSheet, View } from 'react-native'
import Animated, { Extrapolate, interpolate, useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import HeartIcon from '../../assets/Images/icon/heart.png';
import HeartActive from '../../assets/Images/icon/heart.png';
import useSTheme from '../../theme/useSTheme';

const LikeBtnComponent = ({ likeButtonPress, isLike, inActiveIcon = HeartIcon,
    activeIcon = HeartActive, inActiveTintColor = null, activeTintColor = null,
    disable = false, style }) => {
    const liked = useSharedValue(0);
    const theme = useSTheme();
    useEffect(() => {
        if (isLike) {
            // liked.value = 1;
            liked.value = withSpring(1);
        }
        else {
            liked.value = withSpring(0);
            // liked.value = 0;
        }

    }, [isLike, liked.value])
    const outlineStyle = useAnimatedStyle(() => {
        let testRefresh = Math.random();
        return {
            transform: [
                {
                    scale: interpolate(liked.value, [0, 1], [1, 0], Extrapolate.CLAMP),
                },
            ],
        };
    });

    const fillStyle = useAnimatedStyle(() => {
        let testRefresh = Math.random();
        return {
            transform: [
                {
                    scale: liked.value,
                },
            ],
            opacity: liked.value,
        };
    }, [liked.value]);
    const LikeButtonPress = () => {
        liked.value = withSpring(liked.value ? 0 : 1);
        likeButtonPress(liked.value)
    }

    return (
        <Pressable onPress={() => LikeButtonPress()} disabled={disable}
        style={{zIndex:2}}>
            <Animated.View style={[StyleSheet.absoluteFillObject, outlineStyle]}>
                <Image
                    style={{ ...styles.likeIcon, ...style, tintColor: inActiveTintColor != null ? inActiveTintColor : theme.colors.postInActiveIconColor }}
                    source={inActiveIcon}
                    resizeMode={'contain'}
                />
            </Animated.View>

            <Animated.View style={[fillStyle]}>
                <Image
                    style={{ ...styles.likeIcon, ...style, tintColor: activeTintColor != null ? activeTintColor : theme.colors.postActiveIconColor }}
                    source={activeIcon}
                    resizeMode={'contain'}

                />
            </Animated.View>
        </Pressable>
    );
}

export default LikeBtnComponent;

const styles = StyleSheet.create({
    likeIcon: {
        width: 20,
        height: 17,
        paddingHorizontal:10,
        paddingVertical:10,
    },
})
