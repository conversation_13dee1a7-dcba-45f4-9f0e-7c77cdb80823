import { ScrollView, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';

const SubscriptionBarChart = ({
    data = [],
    chartHeight = 150,
}) => {
    const [chartData, setChartData] = useState([]);
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    useEffect(() => {
        CreateChartUi();
    }, [data]);
    const CreateChartUi = () => {
        let chartTempData = data;
        let maxColValue = 0;
        chartTempData.map(obj => {
            let value = parseFloat(obj.value);
            if (maxColValue < value) {
                maxColValue = value;
            }
        });
        const chartActualHeight = chartHeight - 10;
        let maxPixelValue = (maxColValue / chartActualHeight) * 100;
        chartTempData.map(obj => {
            let heightPer = 0;
            if (maxColValue == parseFloat(obj.value)) {
                heightPer = maxPixelValue;
            }
            else {
                heightPer = (parseFloat(obj.value) / maxPixelValue) * 100;
            }
            obj.height = heightPer;
        });
        setChartData(chartTempData);
    }
    return (
        <View style={{ ...style.chartContainer, height: chartHeight + 40, }}>
            <ScrollView style={{ flex: 1 }}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                horizontal>
                {chartData.map((obj, index) => {
                    return <ChartBarItem key={index} label={obj.label} height={obj.height}
                        chartHeight={chartHeight} value={obj.value}
                    />
                })}
            </ScrollView>
            <View style={style.xAxisDivider} />

        </View>
    )
}
const ChartBarItem = ({
    label = "",
    height = 0,
    chartHeight,
    value = "",

}) => {
    const style = useSThemedStyles(styles);
    return (
        <View style={{ ...style.chartItemContainer, height: chartHeight }}>
            <View style={{ ...style.chartItemValueContainer }}>
                <View style={{ ...style.chartItemValueContainerItem, height: height }} />
            </View>
            <View style={{ ...style.chartItemLabelContainer }}>
                <Text allowFontScaling={false} numberOfLines={1} style={{ ...style.chartItemLabel }}>{label}</Text>
            </View>
        </View>
    );
}

export default SubscriptionBarChart

const styles = theme => StyleSheet.create({
    chartContainer: {
        flexDirection: 'row',
        position: 'relative'
    },
    chartItemContainer: {
        height: '100%',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: 40,
        position: 'relative',

    },
    chartItemValueContainer: {
        height: '100%',
        width: 40,
        borderRadius: 4,
        position: 'relative',

    },
    chartItemValueContainerItem: {
        position: 'absolute',
        bottom: 1,
        left: 16,
        right: 16,
        width: 8,
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 4,
        backgroundColor: theme.colors.primaryColor
    },
    chartItemLabelContainer: {
        position: 'absolute',
        bottom: -25,
        left: 0,
        right: 0,
        width: 40,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        flexWrap: 'nowrap',

    },
    chartItemLabel: {
        fontSize: 8,
        color: "#CCC"
    },
    xAxisDivider: {
        position: 'absolute',
        bottom: 32,
        left: 0,
        right: 0,
        height: 1,
        backgroundColor: "#CCC",
        opacity: 0.2
    }

})