import { StyleSheet, Text, View, Image, Pressable } from 'react-native'
import React from 'react'
import { TouchableOpacity } from 'react-native-gesture-handler';
import VideoPlayer from './common/VideoPlayer';
import { useNavigation } from '@react-navigation/native';
import Dimensions from '../constants/Dimensions';
const ScreenWidth = Dimensions.screenWidth;
const SignupIntroVideo = ({ mediaUri, IntroVideoPress }) => {
    const navigation = useNavigation();
    const closeVideo = () => {
        IntroVideoPress("CLOSE", {});
    }
    return (
        <View style={styles.container}>
            <View style={styles.videoCrossBox}>
                <Pressable onPress={() => closeVideo()}>
                    <Image
                        style={styles.arrowIcon}
                        source={require('../assets/Images/videoIcons/error-icon.png')}
                        resizeMode="contain"
                    />
                </Pressable>
            </View>
            {/* <View> */}
            <VideoPlayer
                source={{ uri: mediaUri, cache: { size: 150, expiresIn: 3600 } }}
                navigator={navigation}
                tapAnywhereToPause={true}
                toggleResizeModeOnFullscreen={true}
                isFullScreen={true}
                thumbnail={require('../assets/Images/app_icon.png')}
                thumbnailType="LOCAL"
                disableBack={true}
                disableVolume={false}
                controlTimeout={5000}
                paused={true}
                seekColor={'#f3997b'}
                mainColor={'#f3997b'}
                autoPlay={true}
            />
        </View>
    )
}

export default SignupIntroVideo

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#00000030',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
    },
    contentVideo: {
        width: ScreenWidth,
        aspectRatio: 1,
        backgroundColor: '#00000030',
        flex: 1,
        height: 231,
        zIndex: 2
    },
    contentVideoPortation: {
        width: ScreenWidth,
        aspectRatio: 1,
        backgroundColor: '#00000030',
        flex: 1,
        height: 231,
        zIndex: 2
    },
    videoCrossBox: {
        position: 'absolute',
        top: 20,
        left: 15,
        zIndex: 3
    },
    arrowIcon: {
        height: 32,
        width: 32,
        marginEnd: 5,
        opacity: 0.8,
        zIndex: 3,
        tintColor: '#FFFFFF'
    },
})