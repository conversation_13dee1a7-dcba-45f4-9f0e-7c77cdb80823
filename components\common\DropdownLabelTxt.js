import React from 'react'
import { View, Text } from 'react-native'
import { Colors } from 'react-native/Libraries/NewAppScreen';
import useDefaultStyle from '../../theme/useDefaultStyle';

const DropdownLabelTxt = ({ placeholderTxt, dropValue, valueFocus, ...props }) => {
    const { defaultStyle } = useDefaultStyle();
    if (dropValue || valueFocus) {
        return (
            <Text
                allowFontScaling={false}
                style={[defaultStyle.dropdownLabel,
                valueFocus && { color: Colors.primaryColor },]}>
                {placeholderTxt}
            </Text>
        );
    }
    return null;
}

export default DropdownLabelTxt
