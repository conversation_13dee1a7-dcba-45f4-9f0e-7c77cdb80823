<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1328499114532394</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.286305401510-28vj1ke5s5tehkhcr4k3ckhar9onc0rq</string>
			</array>
		</dict>
	</array>
	<key>FacebookAppID</key>
	<string>1328499114532394</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Sotrue Test</string>
	<key>FirebaseDynamicLinksCustomDomains</key>
	<array>
		<string>https://sotrue.co.in</string>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tez</string>
		<string>phonepe</string>
		<string>paytmmp</string>
		<string>credpay</string>
		<string>mobikwik</string>
		<string>freecharge</string>
		<string>in.fampay.app</string>
		<string>bhim</string>
		<string>amazonpay</string>
		<string>navi</string>
		<string>kiwi</string>
		<string>payzapp</string>
		<string>jupiter</string>
		<string>omnicard</string>
		<string>icici</string>
		<string>popclubapp</string>
		<string>sbiyono</string>
		<string>myjio</string>
		<string>slice</string>
		<string>bobupi</string>
		<string>shriramone</string>
		<string>indusmobile</string>
		<string>whatsapp</string>
	</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
	<true/>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>FontAwesome5Free-Solid.ttf</string>
		<string>Roboto-Regular.ttf</string>
		<string>Roboto-Italic.ttf</string>
		<string>RedHatDisplay-Bold.ttf</string>
		<string>RedHatDisplay-Medium.ttf</string>
		<string>RedHatDisplay-Regular.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
