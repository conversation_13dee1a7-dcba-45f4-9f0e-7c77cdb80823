import React, { useRef, useState } from 'react'
import { Alert, ImageBackground, Keyboard, KeyboardAvoidingView, Platform, Pressable, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import ActionSheet from 'react-native-actions-sheet';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import CustomSnackbar from '../components/common/CustomSnackbar';
import CustomStatusBar from '../components/common/CustomStatusBar';
import EntutoSwitch from '../components/common/EntutoSwitch';
import EntutoTextView from '../components/common/EntutoTextView';
import HeadingTxt from '../components/common/HeadingTxt';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import ErrorMessages from '../constants/ErrorMessages';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import InactiveBackIcon from "../assets/Images/icon/tickmark_back.png";
import ActiveBackIcon from "../assets/Images/icon/round_circle.png";
import CheckIcon from "../assets/Images/icon/tick_white.png";
import { Image } from 'react-native';
import DeleteAccountSuccessBox from '../components/accountsetting/DeleteAccountSuccessBox';
import Colors from '../constants/Colors';
import EntutoEditText from '../components/common/EntutoEditText';
import PrimaryButton from '../components/common/PrimaryButton';
import Dimensions from '../constants/Dimensions';
import { _clearAllData } from '../utils/AuthLogin';
import appData from '../data/Data';
import { CommonActions } from '@react-navigation/native';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';


const DisableAccountScreen = ({ route, navigation }) => {
    const [disableAccount, setDisableAccount] = useState(true);
    const [deleteAccount, setDeleteAccount] = useState(false);
    const [reasonData, setReasonData] = useState([]);
    const [comment, setComment] = useState("");
    const [commentErr, setCommentErr] = useState("");
    const [otherReason, setOtherReason] = useState("");
    const [otherReasonErr, setOtherReasonErr] = useState("");
    const [selectedReason, setSelectedReason] = useState([]);

    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [snackBarType, setsnackBarType] = useState("FAILED");

    const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);

    const [showLoading, setShowLoading] = useState(true);
    const successSheetRef = useRef(null);
    const [successMessage, setSuccessMessage] = useState("");
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    React.useEffect(() => {
        getDeleteReasonService();
    }, []);

    function getDeleteReasonService() {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: "DELETE_OPTION",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method  
            let tempList = [];
            let reasonNotListedObj = null;
            data.data.map(obj => {
                if (obj.config_key == "RESASON_NOT_LISTED") {
                    reasonNotListedObj = obj;
                }
                else {
                    tempList.push(obj);
                }
            })
            if (reasonNotListedObj != null) {
                tempList.push(reasonNotListedObj);
            }
            setReasonData(tempList);
            setShowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setReasonData([]);
                setShowLoading(false);
            }
        });
    }
    const onDisableAccountChange = () => {
        if (!disableAccount) {
            setDisableAccount(true);
            setDeleteAccount(false);
        }
        else {
            setDisableAccount(false);
            setDeleteAccount(true);
        }
        setdisableUpdateBtn(false);
    }
    const onDeleteAccountChange = () => {
        if (!deleteAccount) {
            setDisableAccount(false);
            setDeleteAccount(true);
        }
        else {
            setDisableAccount(false);
            setDeleteAccount(true);
        }
        setdisableUpdateBtn(false);
    }

    const reasonItemBoxPress = (value) => {
        let tempList = [...selectedReason];
        if (tempList.includes(value)) {
            const index = tempList.indexOf(value);
            if (index > -1) {
                tempList.splice(index, 1);
            }
        }
        else {
            tempList.push(value);
        }
        setSelectedReason(tempList);
    }
    const commentChangeHandler = (text) => {
        setComment(text);
        setCommentErr("");
        setdisableUpdateBtn(false);
    }
    const otherReasonChangeHandler = (text) => {
        setOtherReason(text);
        setOtherReasonErr("");
    }
    const submitBtnPress = () => {
        Keyboard.dismiss();
        if (formValid()) {
            if (!showLoading) {
                setdisplaySnackbar(false);
                setShowLoading(true);
                deleteAccountService();
            }

        }
    }
    const formValid = () => {
        let formValid = true;
        if (selectedReason.length == 0) {
            setSnackbarMsg(ErrorMessages.delAccResasonErr);
            setdisplaySnackbar(true);
            setsnackBarType("FAILED");
            setrefreshSnackBar(Math.random());
            formValid = false;
        }
        if (selectedReason.includes("RESASON_NOT_LISTED") && otherReason.length == 0) {
            setSnackbarMsg(ErrorMessages.otherReasonErr);
            setdisplaySnackbar(true);
            setsnackBarType("FAILED");
            setrefreshSnackBar(Math.random());
            formValid = false;
        }
        if (comment.length == 0) {
            setSnackbarMsg(ErrorMessages.delAccCommentErr);
            setdisplaySnackbar(true);
            setsnackBarType("FAILED");
            setrefreshSnackBar(Math.random());
            formValid = false;
        }
        return formValid;
    }
    function deleteAccountService() {
        let delete_type = "DISABLE";
        if (deleteAccount) {
            delete_type = "DELETE";
        }
        let delete_reason_list = "";
        selectedReason.map(item => {
            if (delete_reason_list.length != 0) {
                delete_reason_list += ",";
            }
            delete_reason_list += item;
        })
        let hashMap = {
            _action_code: "11:DELETE_ACCOUNT",
            delete_reason: JSON.stringify(delete_reason_list),
            delete_comments: comment,
            delete_type: delete_type,
        }
        if (otherReason.length != 0) {
            hashMap.other_reason = otherReason;
        }
        // console.log("hashMap", hashMap);
        // _clearAllData();
        // appData._userDetails = null;
        // setSuccessMessage("Test Data");
        // successSheetRef.current?.show();
        // return;
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setSuccessMessage(data.msg)
            _clearAllData();
            appData._userDetails = null;
            setShowLoading(false);
            successSheetRef.current?.show();

        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.delete_reason) {
                            setSnackbarMsg(data.data.delete_reason);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.delete_comments) {
                            setSnackbarMsg(data.data.delete_comments);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    setSnackbarMsg(errorMessage);
                    setdisplaySnackbar(true);
                    setsnackBarType("FAILED");
                    setrefreshSnackBar(Math.random());
                }
            }
        });
    }
    const boxPopupClick = (clickId) => {
        if (clickId == "SIGNOUT") {
            successSheetRef.current?.hide();
            setTimeout(() => {
                navigation.dispatch(
                    CommonActions.reset({
                        index: 1,
                        routes: [
                            {
                                name: 'LoginScreen',
                                params: { ErrorMsg: "" },
                            },
                        ],
                    })
                );
            }, 50);

        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Disable Account" showBackBtn={true} showBorderBottom={false} navigation={navigation} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                enabled={Platform.OS == 'ios' ? true : true}

                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
                <ScrollView
                    style={{ backgroundColor: theme.colors.backgroundColor }}
                    keyboardShouldPersistTaps={'handled'}>
                    {/* <KeyboardAvoidingView
                    enabled={Platform.OS == 'ios' ? true : true}

                    behavior={Platform.OS === 'ios' ? 'position' : 'height'}> */}
                    <View style={defaultStyle.container}>

                        <View style={{ marginTop: 15, marginBottom: 5, }}>
                            <HeadingTxt>What would you like to do?</HeadingTxt>
                        </View>
                        <View style={style.boxWithSwitchBox}>
                            <View style={{ flexDirection: 'row' }}>
                                <EntutoTextView style={style.boxWithSwitchTxt}>Disable Account</EntutoTextView>
                                <EntutoSwitch value={disableAccount} onValueChange={() => onDisableAccountChange()} />
                            </View>
                        </View>
                        <View style={style.boxWithSwitchBox}>
                            <View style={{ flexDirection: 'row' }}>
                                <EntutoTextView style={style.boxWithSwitchTxt}>Delete Account</EntutoTextView>
                                <EntutoSwitch value={deleteAccount} onValueChange={() => onDeleteAccountChange()} />
                            </View>
                        </View>
                        <View style={style.boxWithSwitchBoxDivider} />
                        <View style={{ marginTop: 30, marginBottom: 5, paddingEnd: 80 }}>
                            <HeadingTxt>Please tell us the reason you want to leave</HeadingTxt>
                        </View>
                        <View style={{ marginTop: 10, marginBottom: 5, }}>
                            {
                                reasonData.map((obj, i) => {
                                    return <ReasonItem
                                        key={i}
                                        title={obj.display_value}
                                        value={obj.config_key}
                                        selectedItem={selectedReason}
                                        itemBoxPress={reasonItemBoxPress} />
                                })
                            }
                        </View>
                        <View style={{ marginTop: 10, marginBottom: 5, }}>
                            <EntutoEditText
                                labelTxt="Tell us your reason"
                                placeholderTxt="Tell us your reason"
                                value={otherReason}
                                onChangeText={(text) => otherReasonChangeHandler(text)}
                                showErrorField={otherReasonErr.length}
                                errorMsg={otherReasonErr}
                            />
                        </View>
                        <View style={{ marginTop: 10, marginBottom: 5, }}>

                            <EntutoEditText
                                labelTxt="Comment"
                                placeholderTxt="Comment"
                                value={comment}
                                onChangeText={(text) => commentChangeHandler(text)}
                                showErrorField={commentErr.length}
                                errorMsg={commentErr}
                            />
                        </View>
                        <View style={{ marginBottom: 15, justifyContent: 'center', alignItems: 'center' }}>
                            <PrimaryButton label="Submit"
                                style={{ marginTop: Dimensions.buttonFormGap, }} uppercase={false}
                                onPress={() => submitBtnPress()} />
                        </View>


                    </View>
                    {/* </KeyboardAvoidingView> */}
                </ScrollView>
            </KeyboardAvoidingView>
            <ActionSheet ref={successSheetRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                closeOnTouchBackdrop={false}
                closeOnPressBack={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled
                    onMomentumScrollEnd={() => {
                        successSheetRef.current?.handleChildScrollEnd();
                    }}
                    style={{ backgroundColor: theme.colors.backgroundColor }}
                >
                    <DeleteAccountSuccessBox successMsg={successMessage} refVal={successSheetRef} boxPopupClick={boxPopupClick} />
                </ScrollView>

            </ActionSheet >
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />

        </>
    )
}
const ReasonItem = ({ title = "", value, selectedItem, itemBoxPress }) => {
    const style = useSThemedStyles(styles);
    return <View style={{
        overflow: 'hidden', borderBottomColor: "#********",
        borderBottomWidth: 0.1,
    }}>
        <Pressable
            android_ripple={{ color: Colors.pressableRippleColor, borderless: true }}
            onPress={() => itemBoxPress(value)}>
            <View style={style.reasonItemBox}>
                <View style={{ flex: 1, paddingEnd: 16 }}>
                    <EntutoTextView style={style.reasonItemText}>{title}</EntutoTextView>
                </View>
                <View>
                    {
                        selectedItem.includes(value) ?
                            <ImageBackground
                                resizeMode='cover'
                                imageStyle={{ borderRadius: 24 }}
                                source={ActiveBackIcon}
                                style={style.checkBoxImageBack} >
                                <Image source={CheckIcon} style={style.checkIcon} resizeMode={'contain'} />
                            </ImageBackground>
                            :
                            <ImageBackground
                                resizeMode='cover'
                                imageStyle={{ borderRadius: 24 }}
                                source={InactiveBackIcon}
                                style={style.checkBoxImageBack} >
                                <Image source={CheckIcon} style={style.checkIcon} resizeMode={'contain'} />
                            </ImageBackground>
                    }


                </View>

            </View>
        </Pressable>


    </View>
}

export default DisableAccountScreen;

const styles = theme => StyleSheet.create({
    boxWithSwitchBox: {
        flexDirection: 'column',
        marginVertical: 15,

    },
    boxWithSwitchTxt: {
        flex: 1,
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.boxWithSwitchTxt),
        fontWeight: '600',
    },
    boxWithSwitchBoxDivider: {
        flex: 1,
        borderWidth: 0.5,
        borderColor: '#000000',
        opacity: 0.2
    },
    reasonItemBox: {
        flexDirection: 'row',
        paddingBottom: 16,
        paddingTop: 16,
        overflow: 'hidden',
    },
    reasonItemText: {
        flex: 1,
        fontSize: theme.calculateFontSize(theme.dimensions.reasonItemText),
        fontWeight: '600'

    },
    checkBoxImageBack: {
        width: 24,
        height: 24,
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkIcon: {
        width: 14,
        height: 14,
    }

})
