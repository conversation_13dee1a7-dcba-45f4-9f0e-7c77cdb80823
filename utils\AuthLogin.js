import AsyncStorage from '@react-native-async-storage/async-storage';

export const _saveUserCredential = async userDetails => {
  try {
    userDetails = JSON.stringify(userDetails);
    await AsyncStorage.setItem('@user_details', userDetails);
  } catch (e) {
    // saving error
    // console.log(error);
  }
};
export const _getUserCredential = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@user_details');
    callback(JSON.parse(jsonValue));
  } catch (e) {
    // error reading value
    return null;
  }
};
export const _changeUserCredential = changeValueArray => {
  let userDetails = _getUserCredential();
  if (userDetails != null) {
    changeValueArray.map(obj => {
      userDetails[obj.key] = obj.value;
    });
    _saveUserCredential(userDetails);
  }
};
export const _clearAllData = async () => {
  const keys = [
    '@user_details',
    '@access__key',
    '@storage_Key',
    '_isSaveFCMKey',
  ];
  try {
    await AsyncStorage.multiRemove(keys);
  } catch (e) {
    // console.log(e);
    // clear error
  }
  // console.log('Done All')
};

export const _setAccessKey = async access_key => {
  try {
    await AsyncStorage.setItem('@access__key', access_key);
  } catch (e) {
    // saving error
    // console.log(e);
  }
};
export const _getAccessKey = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@access__key');
    callback(jsonValue);
  } catch (e) {
    // read error
    // console.log(e);
    callback('');
  }
};
export const _setFirstTimeUser = async value => {
  try {
    await AsyncStorage.setItem('@first_time', value);
  } catch (e) {
    // saving error
  }
};
export const _getFirstTimeUser = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@first_time');
    callback(jsonValue);
  } catch (e) {
    callback('NO');
    // error reading value
  }
};
export const _removeFirstTimeUser = async () => {
  const keys = ['@first_time'];
  try {
    await AsyncStorage.multiRemove(keys);
  } catch (e) {
    // clear error
  }
};
export const _setShowHomeItroIcon = async value => {
  try {
    await AsyncStorage.setItem('@home_intro', value);
  } catch (e) {
    // saving error
  }
};
export const _getShowHomeItroIcon = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@home_intro');
    callback(jsonValue);
  } catch (e) {
    callback('YES');
    // error reading value
  }
};
export const _updateUserSearchKeywards = async keyWards => {
  try {
    let keyWardsVal = JSON.stringify(keyWards);
    await AsyncStorage.setItem('@user_keywards', keyWardsVal);
  } catch (e) {
    // saving error
    // console.log(error);
  }
};
export const _getUserSearchKeywards = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@user_keywards');
    if (jsonValue == null) {
      callback([]);
    } else {
      callback(JSON.parse(jsonValue));
    }
  } catch (e) {
    // error reading value
    callback([]);
  }
};
export const _setFirstTimeUnlockPost = async value => {
  try {
    await AsyncStorage.setItem('@first_t_u_p', value);
  } catch (e) {
    // saving error
  }
};
export const _getFirstTimeUnlockPost = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@first_t_u_p');
    callback(jsonValue);
  } catch (e) {
    callback('YES');
    // error reading value
  }
};
export const _setFirstTimeLikePost = async value => {
  try {
    await AsyncStorage.setItem('@first_t_l_p', value);
  } catch (e) {
    // saving error
  }
};
export const _getFirstTimeLikePost = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@first_t_l_p');
    callback(jsonValue);
  } catch (e) {
    callback('YES');
    // error reading value
  }
};
export const _setFirstTimeFollowPost = async value => {
  try {
    await AsyncStorage.setItem('@first_t_f_p', value);
  } catch (e) {
    // saving error
  }
};
export const _getFirstTimeFollowPost = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@first_t_f_p');
    callback(jsonValue);
  } catch (e) {
    callback('YES');
    // error reading value
  }
};
export const _setAppThemeColor = async value => {
  try {
    await AsyncStorage.setItem('@app_theme', value);
  } catch (e) {
    // saving error
  }
};
export const _getAppThemeColor = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@app_theme');
    callback(jsonValue);
  } catch (e) {
    callback('');
    // error reading value
  }
};
export const _setAppThemeType = async value => {
  try {
    await AsyncStorage.setItem('@app_theme_type', value);
  } catch (e) {
    // saving error
  }
};
export const _getAppThemeType = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@app_theme_type');
    callback(jsonValue);
  } catch (e) {
    callback('');
    // error reading value
  }
};

export const _setUserPreferences = async preferences => {
  try {
    await AsyncStorage.setItem(
      '@user_preferences',
      JSON.stringify(preferences),
    );
  } catch (e) {
    // saving error
  }
};

export const _getUserPreferences = async callback => {
  try {
    const jsonValue = await AsyncStorage.getItem('@user_preferences');
    callback(
      jsonValue != null
        ? JSON.parse(jsonValue)
        : {
            notificationGuide: 'YES',
            personalityType: '',
          },
    );
  } catch (e) {
    callback({
      notificationGuide: 'YES',
      personalityType: '',
    });
  }
};
