import { FlatList, Image, ImageBackground, Platform, Pressable, RefreshControl, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useCallback, useContext, useEffect, useState } from 'react'
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import LinearGradient from 'react-native-linear-gradient';
import { checkValueLength, hasImageUrlExist, onMaxTextLengthReached, pluralize } from '../utils/Utils';
import BackBtn from '../assets/Images/icon/back.png';
import EntutoTextView from '../components/common/EntutoTextView';
import CustomStatusBar from '../components/common/CustomStatusBar';

import ServerConnector from '../utils/ServerConnector';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import { _RedirectionErrorList, MaxPlaylistDescTxtLimit, MaxPlaylistTitleTxtLimit } from '../utils/Appconfig';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import TempData from '../data/TempData';
import ProgressiveImage from '../components/common/ProgressiveImage';
import EpisodeItem from '../components/search/EpisodeItem';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const PlaylistSeasonEpisodeScreen = ({ navigation, route }) => {
    const { seasonSeq } = route.params;
    const insets = useSafeAreaInsets();
    const COVER_HEIGHT = Platform.OS == 'ios' ? 219 - (insets.top / 2) : 219;
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [showLoading, setShowLoading] = useState(true);
    const [progressLoading, setProgressLoading] = useState(false);
    const [pageErrorMessage, setPageErrorMessage] = useState("");
    const [showDetailsData, setShowDetailsData] = useState({
        showTitle: "",
        showBannerImage: null,
        showDescription: "",
        showEpisodeCount: 0,
        yearOfReleased: "",
    });
    useEffect(() => {
        getEpisodeOrSeasonService();
    }, []);
    const [seasonOrEpisodeList, setSeasonOrEpisodeList] = useState([]);
    const getEpisodeOrSeasonService = () => {
        setShowLoading(true);
        let hashMap = {
            _action_code: "11:GET_PLAYLIST_EPISODES",
            type: "SEASON",
            sequence: seasonSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let viewType = "EPISODE";
            const mediaData = [];
            data.data.map(item => {
                let dataObj = {};
                dataObj.id = viewType + "_" + Math.random();
                dataObj.viewTypeSeq = viewType == "EPISODE" ? item.episode_seq : item.season_seq;
                dataObj.media_file = viewType == "EPISODE" ? item.thumb_file : null;
                dataObj.view_type = viewType;
                dataObj.title = viewType == "EPISODE" ? item.title : item.title;
                dataObj.description = viewType == "EPISODE" ? item.description : item.description;
                dataObj.banner_file = viewType == "EPISODE" ? item.banner_file : item.banner_file;
                dataObj.release_year = item.release_year
                mediaData.push(dataObj)
            });
            if (data.hasOwnProperty("season")) {
                setShowDetailsData({
                    showTitle: data.season.title,
                    showBannerImage: checkValueLength(TempData.seasonBannerImage) ? TempData.seasonBannerImage : data.season.cover_image,
                    showDescription: data.season.description,
                    showEpisodeCount: mediaData.length,
                    yearOfReleased: data.season.release_year,
                })
            }
            setSeasonOrEpisodeList(mediaData)
            setShowLoading(false);
            setProgressLoading(false)
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            setProgressLoading(false);
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setSeasonOrEpisodeList([]);
                setPageErrorMessage(errorMessage)
            }
        });
    }
    const backButtonPress = () => {
        navigation.goBack(null)
    }
    const HeaderComponent = () => {
        return <>
            {
                pageErrorMessage.length != 0 ?
                    <>
                        <View style={{ ...style.playlistCoverContainer, marginBottom: 10 }}>
                            <View style={{ ...style.topHeader, marginTop: Platform.OS == 'ios' ? 10 : 40 }}>
                                <View>
                                    <Pressable onPress={() => backButtonPress()}
                                        android_ripple={{
                                            color: theme.colors.pressableRippleColor, borderless: true,
                                            radius: 30,
                                        }}>
                                        <View style={{ padding: 10, }}>
                                            <Image
                                                style={style.backBtnIcon}
                                                resizeMode='contain'
                                                source={BackBtn}
                                            />
                                        </View>
                                    </Pressable>
                                </View>
                            </View>
                        </View>
                        <SuccessFailureMsgBox visibleAllTime={true} alertMsg={pageErrorMessage} alertKey={pageErrorMessage.length} />
                    </>
                    :
                    <>
                        <ProgressiveImage
                            source={hasImageUrlExist(showDetailsData.showBannerImage) ? { uri: showDetailsData.showBannerImage } : null}
                            style={{ ...style.playlistCoverImage, height: COVER_HEIGHT }}
                            defaultImageSource={require("../assets/Images/default_image.jpg")} >
                            {
                                theme.appThemeType == "DARK" ?
                                    <LinearGradient
                                        colors={[theme.colors.playlistLCOne, theme.colors.playlistLCTwo, theme.colors.playlistLCThree, theme.colors.playlistLCFour]}
                                        // locations={[0, 0.45, 0.45, 0.9]}
                                        style={style.linearGradient} />
                                    :
                                    <LinearGradient
                                        colors={[theme.colors.playlistLCOne, theme.colors.playlistLCThree, theme.colors.playlistLCFour]}
                                        // locations={[0, 0.45, 0.45, 0.9]}
                                        style={style.linearGradient} />
                            }
                        </ProgressiveImage>
                        <View style={{
                            ...style.playlistCoverContainer, minHeight: COVER_HEIGHT,
                            marginBottom: 10
                        }}>
                            <View style={{ ...style.topHeader, marginTop: Platform.OS == 'ios' ? 10 : 40 }}>
                                <View>
                                    <Pressable onPress={() => backButtonPress()}
                                        android_ripple={{
                                            color: theme.colors.pressableRippleColor, borderless: true,
                                            radius: 30,
                                        }}>
                                        <View style={{ padding: 10, }}>
                                            <Image
                                                style={style.backBtnIcon}
                                                resizeMode='contain'
                                                source={BackBtn}
                                            />
                                        </View>
                                    </Pressable>
                                </View>

                            </View>
                        </View>
                        <View style={style.contentBody}>
                            <View style={style.playlistTitleBox}>
                                <View style={style.titleBox}>
                                    <EntutoTextView style={style.masterBannerTitle}>
                                        {onMaxTextLengthReached(showDetailsData.showTitle, MaxPlaylistTitleTxtLimit)}
                                    </EntutoTextView>
                                </View>
                                <View style={style.yearCountBox}>
                                    <EntutoTextView style={style.yearCountBoxText}>{showDetailsData.yearOfReleased}</EntutoTextView>
                                    <EntutoTextView style={{ ...style.yearCountBoxText, marginTop: 6 }}>
                                        {pluralize("Episode", showDetailsData.showEpisodeCount) + " " + showDetailsData.showEpisodeCount}
                                    </EntutoTextView>
                                </View>
                            </View>
                            <View style={style.descBox}>
                                <EntutoTextView style={style.descBoxText}>
                                    {onMaxTextLengthReached(showDetailsData.showDescription, MaxPlaylistDescTxtLimit, "")}
                                </EntutoTextView>
                            </View>

                        </View>
                    </>
            }
        </>
    }
    const rowItemPress = (clickID, obj) => {
        if (clickID == "SUBMIT") {
            const selectedOBj = seasonOrEpisodeList[obj.index];
            const dataObj = {
                episode_seq: selectedOBj.viewTypeSeq,
                title: selectedOBj.title,
                description: selectedOBj.description,
                url: selectedOBj.media_file,
                preview_file: selectedOBj.preview_file,
                release_year: selectedOBj.release_year,
                banner_file: selectedOBj.banner_file,
            }
            TempData.episodeData = dataObj;
            navigation.navigate('PlaylistEpisodeScreen', { episodeSeq: selectedOBj.viewTypeSeq });
        }

    }
    const renderItem = useCallback(
        ({ item, index }) => {
            const titleVal = checkValueLength(item.grid_title) ? item.grid_title : item.title;
            return <EpisodeItem index={index} title={titleVal} showsMedia={item.media_file}
                rowItemPress={rowItemPress} />

        },
        [seasonOrEpisodeList]
    );
    const keyExtractor = useCallback((item) => `${item.id}`);
    const ItemSeparatorComponent = () => {
        return <View style={{ height: 16 }} />
    }
    const handleRefresh = () => {
        setProgressLoading(true);
        getEpisodeOrSeasonService();
    }
    return (
        <>
            <CustomStatusBar translucent={true} hidden={true} backgroundColor={'transparent'} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <View style={{ ...style.container, backgroundColor: theme.colors.backgroundColor, }}>
                <FlatList
                    keyboardShouldPersistTaps={'handled'}
                    contentContainerStyle={{ paddingBottom: 80, }}
                    removeClippedSubviews
                    data={seasonOrEpisodeList}
                    numColumns={2}
                    ListHeaderComponent={HeaderComponent}
                    initialNumToRender={8}
                    renderItem={renderItem}
                    keyExtractor={keyExtractor}
                    ItemSeparatorComponent={ItemSeparatorComponent}
                    refreshControl={
                        <RefreshControl tintColor={theme.colors.primaryColor} refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                    }
                />
            </View>

        </>
    )
}

export default PlaylistSeasonEpisodeScreen

const styles = theme => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.backgroundColor,
        position: 'relative',
    },
    playlistCoverContainer: {
        width: '100%',
        position: 'relative',
        zIndex: 1,
    },
    linearGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    playlistCoverImage: {
        width: '100%',
        resizeMode: 'cover',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0

    },
    topHeader: {
        marginHorizontal: 16,
        flexDirection: 'row',
    },
    backBtnIcon: {
        height: 24,
        width: 24,
        tintColor: '#FFFFFF',
        resizeMode: 'contain',


    },
    contentBody: {
        marginHorizontal: 28,
    },
    playlistTitleBox: {
        flexDirection: 'row',
        minHeight: 50,
        flex: 1,
    },
    titleBox: {
        maxHeight: theme.dimensions.playlistTitleBoxHeight,
        marginTop: 2,
        flex: 1,
    },
    masterBannerTitle: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistTitleText),
        color: theme.colors.playlistBannerTitleColor,

    },
    yearCountBox: {
        justifyContent: 'center'
    },
    yearCountBoxText: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistSeasonCountText),//12
        color: theme.colors.playlistBannerYearCountColor
    },
    descBox: {
        marginTop: theme.dimensions.playlistDescTopMargin,
        marginBottom: 24
    },
    descBoxText: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistDescText),//11
        color: theme.colors.playlistBannerDescColor
    }
})