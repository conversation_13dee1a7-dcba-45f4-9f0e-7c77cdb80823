import React, { useContext, useEffect, useState } from 'react'
import { ActivityIndicator, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { AppStateContext } from '..';
import EntutoTextView from '../components/common/EntutoTextView';
import Colors from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import Video from 'react-native-video';
import { FAB } from 'react-native-paper';
import CustomStatusBar from '../components/common/CustomStatusBar';
import ImagePicker from 'react-native-image-crop-picker';
import useSTheme from '../theme/useSTheme';

const ScreenWidth = Dimensions.screenWidth;
const CaptureMediaDisplayScreen = ({ route, navigation }) => {
    const { uriData, captureType, imageData, cameraType, cameFrom } = route.params;
    const { changeCaptureMedia, changeStoryMedia,
        changeVeriSelfieMedia, changeVeriImageID,
        changeVeriSelfieId, changeCaptureProfileImg, changeCaptureCoverImg } = useContext(AppStateContext);
    const [isPortation, setIsPortation] = useState(false);
    const [heightScaled, setHeightScaled] = useState(Dimensions.screenHeight);
    const [videoEnded, setvideoEnded] = useState(false);
    const [mediaUrl, setmediaUrl] = useState(null);
    const [showScreenImage, setshowScreenImage] = useState(true);
    const theme = useSTheme();

    useEffect(() => {
        if (captureType == "IMAGE") {
            if (cameFrom == "CAP_PROFILE" ||
                cameFrom == "CAP_COVER" ||
                cameFrom == "POST" ||
                cameFrom == "STORY"
            ) {
                setshowScreenImage(false);
                let circularOverlay = false;
                let freeStyleCropEnabled = false;
                let corpW = 1280;
                let corpH = 800;

                if (cameFrom == "CAP_PROFILE") {
                    circularOverlay = true;
                    corpW = 800;
                    corpH = 800;
                }
                if (cameFrom == "STORY") {

                    circularOverlay = false;
                    corpW = 1080;
                    corpH = 1080;
                }
                if (cameFrom == "POST") {

                    circularOverlay = false;
                    corpW = 1280;
                    corpH = 1280;
                }
                if (cameFrom == "POST") {
                    ImagePicker.openCropper({
                        freeStyleCropEnabled: false,
                        cropping: false,
                        width: corpW,
                        height: corpH,
                        path: uriData,
                    }).then(image => {
                        setmediaUrl(image.path);
                        okBtnPress(image.path)
                    }).catch(err => {
                        retryBtnPress();
                    });
                }
                else if (cameFrom == "STORY") {
                    ImagePicker.openCropper({
                        freeStyleCropEnabled: true,
                        cropping: true,
                        path: uriData,
                    }).then(image => {
                        setmediaUrl(image.path);
                        okBtnPress(image.path)
                    }).catch(err => {
                        retryBtnPress();
                    });
                }
                else {
                    ImagePicker.openCropper({
                        freeStyleCropEnabled: freeStyleCropEnabled,
                        path: uriData,
                        width: corpW,
                        height: corpH,
                        cropperCircleOverlay: circularOverlay,
                    }).then(image => {
                        setmediaUrl(image.path);
                        okBtnPress(image.path)
                    }).catch(err => {
                        retryBtnPress();
                    });
                }

            }
            else {
                setmediaUrl(uriData);
            }
        }
        else {
            setmediaUrl(uriData);
        }



    }, [])


    let videoRef;
    const okBtnPress = (mediaUrlVal) => {

        if (cameFrom == "POST") {
            changeCaptureMedia({ uri: mediaUrlVal, captureType: captureType, imageData: imageData });
        }
        else if (cameFrom == "STORY") {
            changeStoryMedia({ uri: mediaUrlVal, captureType: captureType, imageData: imageData });
        }
        else if (cameFrom == "SELFIE") {
            changeVeriSelfieMedia({ uri: mediaUrlVal, captureType: captureType, imageData: imageData });
        }
        else if (cameFrom == "IMAGE_ID") {
            changeVeriImageID({ uri: mediaUrlVal, captureType: captureType, imageData: imageData });
        }
        else if (cameFrom == "SELFIE_ID") {
            changeVeriSelfieId({ uri: mediaUrlVal, captureType: captureType, imageData: imageData });
        }
        else if (cameFrom == "CAP_PROFILE") {
            changeCaptureProfileImg({ uri: mediaUrlVal, captureType: captureType, imageData: imageData });
        }
        else if (cameFrom == "CAP_COVER") {
            changeCaptureCoverImg({ uri: mediaUrlVal, captureType: captureType, imageData: imageData });
        }
        navigation.goBack()
    }
    const replayVideo = () => {
        setvideoEnded(false);
        videoRef.seek(0);
    }
    const retryBtnPress = () => {
        navigation.replace('CameraScreen', {
            cameraType: cameraType, cameFrom: cameFrom
        })
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            {
                showScreenImage ?

                    <View style={{ flex: 1, flexDirection: 'column', }}>
                        <View style={{ flexDirection: 'row', height: 56, }}>
                            <TouchableOpacity style={{ flex: 1, }} onPress={() => retryBtnPress()}>
                                <View style={styles.upButton}>
                                    <EntutoTextView style={styles.upButtonTxt}>Retry</EntutoTextView>
                                </View>
                            </TouchableOpacity>
                            <View style={{ height: 56, width: 1, backgroundColor: '#000', borderRadius: 15, }} />
                            <TouchableOpacity style={{ flex: 1, }} onPress={() => okBtnPress(mediaUrl)}>
                                <View style={styles.upButton}>
                                    <EntutoTextView style={styles.upButtonTxt}>Ok</EntutoTextView>
                                </View>
                            </TouchableOpacity>
                        </View>
                        {
                            captureType == "VIDEO" && mediaUrl != null ?
                                <View style={{ position: 'relative', flex: 1, height: Dimensions.screenHeight }}>
                                    <Video
                                        ref={(ref) => { videoRef = ref }}
                                        source={{ uri: mediaUrl }}
                                        style={
                                            isPortation
                                                ? [styles.contentVideoPortation, { height: Dimensions.screenHeight }]
                                                : [styles.contentVideo, { height: Dimensions.screenHeight }]
                                        }
                                        onEnd={() => setvideoEnded(true)}
                                        resizeMode='stretch'
                                    />
                                    {
                                        videoEnded ?
                                            <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, justifyContent: 'center', alignItems: 'center' }}>
                                                <FAB
                                                    style={{ backgroundColor: '#FFFFFF', }}
                                                    small
                                                    icon="refresh"
                                                    onPress={() => replayVideo()}
                                                />
                                            </View>
                                            : null
                                    }


                                </View>
                                :
                                <Image
                                    style={styles.captureImage}
                                    source={{ uri: mediaUrl }}
                                    resizeMode='cover' />
                        }
                    </View>
                    : <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={50} />
            }
        </>
    )
}

export default CaptureMediaDisplayScreen;

const styles = StyleSheet.create({
    captureImage: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'center',
        height: Dimensions.screenHeight,
        width: Dimensions.screenWidth,

    },
    upButton: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        // borderRadius: 15, margin: 5, backgroundColor: '#ccc',

    },
    upButtonTxt: {
        color: Colors.primaryColor,
        fontSize: 16,
        fontWeight: 'bold',

    },
    contentVideo: {
        width: ScreenWidth,
        //aspectRatio: 1,
        //backgroundColor: '#000',
        //flex: 1,
        height: Dimensions.screenHeight,
    },
    contentVideoPortation: {
        width: ScreenWidth,
        //aspectRatio: 1,
        //backgroundColor: '#000',
        //flex: 1,
        height: Dimensions.screenHeight,
    },

})
