import React, { useContext } from 'react'
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native'
import { AppStateContext } from '../..'
import { KEYWARDS_ARRAY_SIZE, UserHandlePrefix } from '../../utils/Appconfig'
import { hasImageUrlExist } from '../../utils/Utils'
import ProgressiveImage from '../common/ProgressiveImage'
import SubheadingBodyTxt from '../common/SubheadingBodyTxt'
import SubheadingTxt from '../common/SubheadingTxt'
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSThemedStyles from '../../theme/useSThemedStyles'
import EntutoTextView from '../common/EntutoTextView'

const SearchProfileRow = ({ navigation, data, searchQueryInsert = false, searchQuery = "", ...props }) => {
    const { fullUserDetails, searchKeywardHistory, changeKeywardHistory } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    const goToProfile = (profileSeq) => {
        if (searchQueryInsert) {
            if (searchQuery.length != 0) {
                let historyKeys = searchKeywardHistory
                if (!historyKeys.includes(searchQuery)) {
                    if (historyKeys.length == KEYWARDS_ARRAY_SIZE) {
                        historyKeys.pop();
                    }
                    historyKeys.unshift(searchQuery);
                    changeKeywardHistory(historyKeys);
                }
            }
        }
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }
    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView, minHeight: 56 }}>
            <View style={style.profileImageBox}>
                <TouchableOpacity onPress={() => goToProfile(data.profile_seq)}>
                    <ProgressiveImage
                        style={style.profileImage}
                        source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                        defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                        resizeMode={'cover'}
                    />
                </TouchableOpacity>

            </View>

            <View style={style.profileNameBox}>
                <TouchableOpacity onPress={() => goToProfile(data.profile_seq)}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        {/* <SubheadingTxt>{data.display_name}</SubheadingTxt> */}
                        <EntutoTextView>{data.display_name}</EntutoTextView>
                        {
                            data.is_verified == "YES" ?
                                <Image
                                    style={style.verifiedIcon}
                                    source={require('../../assets/Images/icon/verifiedicon.png')}
                                    resizeMode={'contain'}
                                />
                                : null
                        }
                    </View>

                    <SubheadingBodyTxt>{UserHandlePrefix}{data.user_handle}</SubheadingBodyTxt>
                </TouchableOpacity>
            </View>

        </View>
    )
}

export default SearchProfileRow

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: theme.dimensions.sugProfileImgH,
        width: theme.dimensions.sugProfileImgW,
        borderRadius: theme.dimensions.sugProfileImgR,
    },
    profileNameBox: {
        flexDirection: 'column',
        marginLeft: theme.dimensions.sugProfileImgGapTxt,
        flex: 1
    },
    verifiedIcon: {
        width: theme.dimensions.sugVerifiedIconW,
        height: theme.dimensions.sugVerifiedIconH,
        marginLeft: theme.dimensions.veritextLeftmargin,
        // position: 'absolute',
        // right: theme.dimensions.sugVerifiedIconRight,
        // top: theme.dimensions.sugVerifiedIconTop,
    },
})
