import React, { useEffect, useState } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import EntutoEditText from '../common/EntutoEditText';
import EntutoTextView from '../common/EntutoTextView';
import { Dropdown } from 'react-native-element-dropdown';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import BottomSheetLoader from '../common/BottomSheetLoader';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import appData from '../../data/Data';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import EntutoDropdown from '../common/EntutoDropdown';
import { PopupNegativeButton, PopupPositiveButton } from '../common/PopupButton';

const UpdateUserLocActionView = ({ navigation, refVal, profileSeq, settingsType = "", ...props }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [reasonVal, setreasonVal] = useState("");
    const [reasonValFocus, setreasonValFocus] = useState(false);
    const [reasonData, setreasonData] = useState([]);
    const [comment, setcomment] = useState("");
    const [commentErr, setcommentErr] = useState("");
    const [isSubmitDisable, setisSubmitDisable] = useState(true);

    const [popupHeading, setpopupHeading] = useState("Block Account");
    const [submitBtnTxt, setsubmitBtnTxt] = useState("Block");



    const [showLoading, setshowLoading] = useState(true);
    const [showSuccessMsg, setshowSuccessMsg] = useState(false);
    const [successMsg, setsuccessMsg] = useState("");

    const [errorMsg, seterrorMsg] = useState("");


    useEffect(() => {
        let serviceCodeV = "BLOCK_CODES";
        if (settingsType === "RESTRICT") {
            setpopupHeading("Restrict Account");
            setsubmitBtnTxt("Restrict");

            serviceCodeV = "RESTRICT_CODES";
        }
        getPostCategoryService(serviceCodeV);

    }, [])
    function getPostCategoryService(serviceCode) {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: serviceCode,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setreasonData(data.data);
            setshowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setreasonData([]);
                setshowLoading(false);
            }
        });
    }
    const commentChangeHandler = (text) => {
        setcomment(text);
        if (text.length != 0) {
            setisSubmitDisable(false);
        }
        else {
            setisSubmitDisable(true);
        }

    }
    const cancelBtnPress = () => {
        props.blockUnrestrictPress("negetive", {})
    }
    const submitBtnPress = () => {
        let isFormValid = true;
        if (reasonVal.length === 0) {
            seterrorMsg("Please select a reason");
            isFormValid = false;
        }
        if (comment.length === 0) {
            setcommentErr("required")
            isFormValid = false;
        }

        if (isFormValid) {
            if (settingsType == "RESTRICT") {
                setshowLoading(true);
                restrictAccountService();
            }
            if (settingsType == "BLOCK") {
                setshowLoading(true);
                blockAccountService();
            }


        }
    }
    function restrictAccountService() {
        let hashMap = {
            _action_code: "11:RESTRICT_ACCOUNT",
            restrict_profile_seq: profileSeq,
            restrict_reason: reasonVal,
            comment: encodeURIComponent(comment),
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true)
            setshowLoading(false);
            appData.__HomePageRefresh = Math.random();
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.block_profile_seq) {
                            seterrorMsg(data.data.block_profile_seq);
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.block_reason) {
                            seterrorMsg(data.data.block_reason);
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.comment) {
                            setcommentErr(data.data.commen);
                            fieldErrorShown = true;
                        }

                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                }

            }
        });
    }
    function blockAccountService() {
        let hashMap = {
            _action_code: "11:BLOCK_ACCOUNT",
            block_profile_seq: profileSeq,
            block_reason: reasonVal,
            comment: encodeURIComponent(comment),
        }



        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true)
            setshowLoading(false);
            appData.__HomePageRefresh = Math.random();
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.block_profile_seq) {
                            seterrorMsg(data.data.block_profile_seq);
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.block_reason) {
                            seterrorMsg(data.data.block_reason);
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.comment) {
                            setcommentErr(data.data.commen);
                            fieldErrorShown = true;
                        }

                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                }

            }
        });
    }
    const dropdownLabelBox = (placeholderTxt, dropValue, valueFocus) => {
        if (dropValue || valueFocus) {
            return (
                <Text
                    style={[defaultStyle.dropdownLabel,
                    valueFocus && { color: theme.colors.primaryColor }]}>
                    {placeholderTxt}
                </Text>
            );
        }
        return null;
    };
    const resonValueChangeHandler = (item) => {
        setreasonVal(item.config_key);
    }
    const closeBtnClick = () => {
        props.blockUnrestrictPress("close", { erMsg: successMsg })
    }

    return (
        <View>
            <View style={{ ...defaultStyle.popupBox, }}>

                <EntutoTextView style={defaultStyle.popupHeadTxt}>{popupHeading}</EntutoTextView>
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                {
                    showSuccessMsg ?
                        <BottomSheetSuccessMsg successMsg={successMsg} cancelBtnClick={() => closeBtnClick()} />
                        : null
                }
                {
                    errorMsg.length != 0 ?
                        // <View style={defaultStyle.errorBoxOutside}>
                        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                        // </View>
                        : null
                }
                <View style={{ ...defaultStyle.dropdownContainer, flexGrow: 1 }}>
                    <EntutoDropdown label='Reason' placeholder='Select Reason'
                        labelField="display_value"
                        valueField="config_key"
                        value={reasonVal}
                        options={reasonData}
                        onOptionChange={resonValueChangeHandler} />
                    {/* {dropdownLabelBox("Reason", reasonVal, reasonValFocus)}
                    <Dropdown
                        style={[defaultStyle.dropdownMain, reasonValFocus && {
                            borderBottomColor: theme.colors.dropdownActiveColor,
                            borderBottomWidth: theme.dimensions.dropdownActiveBorder
                        }]}
                        placeholderStyle={defaultStyle.dropdownPlaceholderStyle}
                        selectedTextStyle={defaultStyle.dropdownSelectedTextStyle}
                        inputSearchStyle={defaultStyle.dropdownInputSearchStyle}
                        data={reasonData}
                        maxHeight={150}
                        labelField="display_value"
                        valueField="config_key"
                        placeholder={"Reason"}
                        value={reasonVal}
                        onFocus={() => setreasonValFocus(true)}
                        onBlur={() => setreasonValFocus(false)}
                        onChange={item => {
                            resonValueChangeHandler(item.config_key);
                            setreasonValFocus(false);
                        }}
                        renderRightIcon={() => (
                            <MaterialIcons style={defaultStyle.dropdownIcon} color={reasonValFocus
                                ? theme.colors.dropdownActiveColor : theme.colors.dropdownInActiveColor}
                                name="keyboard-arrow-down" size={theme.dimensions.dropdownRightIcon} />
                        )}
                    /> */}
                </View>
                <EntutoEditText
                    labelTxt="Comment"
                    placeholderTxt="Comment"
                    value={comment}
                    onChangeText={(text) => commentChangeHandler(text)}
                    showErrorField={commentErr.length}
                    errorMsg={commentErr}
                />

                <View style={{ flexDirection: 'row', flex: 1, marginTop: 16, marginBottom: 15, }}>
                    <View style={{ flex: 1 }}>
                        <PopupNegativeButton
                            onPress={() => cancelBtnPress()}
                            btnText='No'
                            style={{ marginEnd: theme.dimensions.popupBtnGap }} />

                    </View>
                    <View style={{ flex: 1 }}>
                        <PopupPositiveButton
                            disabled={isSubmitDisable}
                            onPress={() => submitBtnPress()}
                            btnText={submitBtnTxt} />

                    </View>
                </View>
            </View>

        </View>
    )
}

export default UpdateUserLocActionView;

const styles = StyleSheet.create({})
