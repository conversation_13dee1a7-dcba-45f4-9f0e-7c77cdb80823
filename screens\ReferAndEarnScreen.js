import React, { useContext, useEffect, useState } from 'react'
import { Image, ImageBackground, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import CustomStatusBar from '../components/common/CustomStatusBar';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import EntutoTextView from '../components/common/EntutoTextView';
import PrimaryButton from '../components/common/PrimaryButton';
import ListItem from '../components/ListItem';
import Share from 'react-native-share';
import Clipboard from '@react-native-clipboard/clipboard';
import { AppStateContext } from '..';
import ConfirmationPopup from '../components/common/ConfirmationPopup';
import ServerConnector from '../utils/ServerConnector';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import ErrorMessages from '../constants/ErrorMessages';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';

const ReferAndEarnScreen = ({ navigation }) => {
    const [referralCode, setreferralCode] = useState("-");
    const [isCopied, setisCopied] = useState(false);
    const referralHistoryPress = () => {
        navigation.navigate('ReferralHistoryScreen');
    }
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    const [errorMsg, seterrorMsg] = useState("");
    const { fullUserDetails } = useContext(AppStateContext);
    const __hasBankDetails = fullUserDetails.hasOwnProperty("_has_bank_details") ? fullUserDetails._has_bank_details : "NO";
    const [showBankErrorPopup, setshowBankErrorPopup] = useState(false);
    const [showBankErrorPopupKey, setshowBankErrorPopupKey] = useState(Math.random());
    const [showLoading, setShowLoading] = useState(true);

    const referralBackPress = () => {
        navigation.goBack();
    }
    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            setShowLoading(true)
            if (__hasBankDetails == "YES") {
                getReferralCodeService();
            }
            else {
                setShowLoading(false);
                setshowBankErrorPopup(true);
                setshowBankErrorPopupKey(Math.random());
            }
        });
        return unsubscribe;
    }, [navigation])

    function getReferralCodeService() {
        let hashMap = {
            _action_code: "11:GET_USER_REFERRAL_CODE",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            seterrorMsg("");
            setreferralCode(data.data[0].referral_code);
            setShowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                seterrorMsg(errorMessage);
                setreferralCode("");
                setShowLoading(false);
            }
        });
    }
    const shareInviiteCodePress = () => {
        onShare()
    }
    const onShare = async () => {
        let referralCodeV = referralCodeCreate();
        const shareOptions = {
            message: referralCodeV
        }
        try {
            const shareResponse = await Share.open(shareOptions);
        } catch (error) {
            // console.log(error.message);
        }
    };
    const copyBtnPress = () => {
        let referralCodeV = referralCodeCreate();

        setisCopied(true);
        Clipboard.setString(referralCodeV);
        setTimeout(() => {
            setisCopied(false);
        }, 3000);
    }
    const errorPopupPress = (clickID, data) => {
        if (clickID == "negative") {
            navigation.goBack();
        }
        else if (clickID == "positive") {
            navigation.navigate('AccountInfoScreen', {
                showBankDetails: "YES",
            });
        }
    }
    function referralCodeCreate() {
        let referralCodeV = "Hey there! Sign up on SoTrue now using my code " + referralCode + " . Can’t wait to see you there!";
        return referralCodeV;
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar title="Refer and Earn" showBackBtn={true} navigation={navigation}
                showBorderBottom={false} />

            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                <View style={style.referTopBox}>
                    {/* <ImageBackground source={VectorIcon} style={style.vectorIcon} />
                    <View style={style.backBtnBox}>
                        <TouchableOpacity onPress={() => referralBackPress()}>
                            <Image source={ReferBackBtn} style={style.referBackBtn} />
                        </TouchableOpacity>
                    </View> */}
                    {
                        errorMsg.length != 0 ?
                            <View style={defaultStyle.errorBoxOutside} >
                                <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                            </View>
                            : <View style={defaultStyle.container}>
                                <View style={style.referBoxStart}>
                                    {/* <HeadLineTxt >Refer and Earn</HeadLineTxt> */}
                                    <HeadLineDownTxt style={{ marginTop: 15, }}>
                                        {/* Refer a friend  to Sotrue and get  10% cashback.
                                    Copy your code, share it with your friends. */}
                                        Wohoo! Share the code to refer your crew, and every month
                                        for an entire year, we'll pay 5% of their earnings to you!
                                    </HeadLineDownTxt>
                                </View>
                                <View style={style.referCodeBox}>
                                    <EntutoTextView style={style.referTxt}>{referralCode}</EntutoTextView>

                                    <View style={{ ...style.copyBtn, marginLeft: 'auto', marginRight: 8, }}>
                                        <TouchableOpacity onPress={() => copyBtnPress()}>
                                            <EntutoTextView style={style.copyBtnTxt}>
                                                {isCopied ? "Copied" : "Copy"}
                                            </EntutoTextView>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                {/* <View style={{ ...style.orTxtBox, marginTop: 16, }}>
                                    <View style={style.orTxtLine} />
                                    <View>
                                        <EntutoTextView style={{ width: 50, textAlign: 'center' }}>OR</EntutoTextView>
                                    </View>
                                    <View style={style.orTxtLine} />
                                </View> */}
                                <PrimaryButton
                                    label="Share"
                                    style={{ marginVertical: 20, marginTop: 40 }}
                                    uppercase={false}
                                    onPress={() => shareInviiteCodePress()} />
                            </View>
                    }

                </View>
                <View style={{ ...defaultStyle.container, }}>
                    <TouchableOpacity onPress={() => referralHistoryPress()}>
                        <ListItem label="Referral History" />
                    </TouchableOpacity>
                </View>
            </View>
            {
                showBankErrorPopup &&
                <ConfirmationPopup
                    visiblePopupKey={showBankErrorPopupKey}
                    visiblePopup={showBankErrorPopup}
                    title="Confirmation"
                    messagebody={ErrorMessages.referralBankCofirmMsg}
                    positiveButton="Yes"
                    negativeButton="No"
                    data={{}}
                    backdrop={true}
                    popupClick={(clickID, data) => { errorPopupPress(clickID, data) }}
                />
            }


        </>
    )
}

export default ReferAndEarnScreen;

const styles = theme => StyleSheet.create({
    referTopBox: {
        backgroundColor: theme.colors.backgroundColor,//'#F2EBE9'
        // height: 427,
    },
    vectorIcon: {
        width: 213,
        height: 427,
        marginLeft: 'auto',
        position: 'absolute',
        right: 0,
        top: 0,
    },
    backBtnBox: {
        marginTop: 56,
        paddingStart: 11,
    },
    referBackBtn: {
        width: 22,
        height: 22,
        tintColor: theme.colors.primaryColor
    },
    referBoxStart: {
        marginTop: 20,
    },
    referCodeBox: {
        backgroundColor: theme.colors.copyTextBackground,
        // borderColor: theme.colors.primaryColor,
        // borderWidth: 1,
        // borderStyle: 'dashed',
        // borderRadius: 24.5,
        marginTop: 20,
        minHeight: 54,
        alignItems: 'center',
        flexDirection: 'row',

    },
    referTxt: {
        color: theme.colors.inputTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.copyLinkText),
        fontWeight: '600',
        paddingLeft: 20,
        width: 240,
    },
    copyBtn: {
        backgroundColor: theme.colors.primaryColor,
        borderRadius: 1,

    },
    copyBtnTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.copyLinkBtnText),
        color: '#FFFFFF',
        fontWeight: 'bold',
        paddingVertical: 6,
        paddingHorizontal: 16,

    },
    orTxtLine: {
        height: 0.5,
        flex: 1,
        backgroundColor: '#00000020',
    },
    orTxtBox: {
        flexDirection: 'row',
        alignItems: 'center',
    }
})
