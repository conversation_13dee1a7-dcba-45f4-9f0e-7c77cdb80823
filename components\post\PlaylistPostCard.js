import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { Image, StyleSheet, View, TouchableOpacity, ScrollView, Pressable, ImageBackground, Platform, TextInput, Text, StatusBar, Keyboard, Modal, Alert } from 'react-native';
import { AppStateContext, SinglePostContext } from '../..';
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import LinearGradient from 'react-native-linear-gradient';
import ActionSheet from "react-native-actions-sheet";
import UnlockPostActionView from './UnlockPostActionView';
import ThreeDotMenuActionView from './ThreeDotMenuActionView';
import { checkValueLength, creationOfCopyLink, decodeHtmlEntitessData, getValueFromReactions, hasImageUrlExist } from '../../utils/Utils';
import LikeBtnComponent from '../common/LikeBtnComponent';
import BookmarkBtnComponent from '../common/BookmarkBtnComponent';
import ProfileImagePlaceholder from '../../assets/Images/full_user_image_place_holder.png';
import ThreeDotIcon from '../../assets/Images/icon/three_dot.png';
import ThreeDotVerticalIcon from '../../assets/Images/icon/profile_three_dot.png';
import PlayBtnIcon from '../../assets/Images/icon/play_btn.png';
import LockIcon from '../../assets/Images/icon/post_lock.png'
import VerifiedIcon from '../../assets/Images/icon/verifiedicon.png';
import ServerConnector from '../../utils/ServerConnector';
import { CurrencySymbol, TAGGED_SYMBOL, UserHandlePrefix, _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import ConfirmationPopup from '../common/ConfirmationPopup';
import appData from '../../data/Data';
import ErrorMessages from '../../constants/ErrorMessages';
import Colors from '../../constants/Colors';
import CustomSnackbar from '../common/CustomSnackbar';
import TagProfileIcon from '../../assets/Images/icon/post_tag_icon.png';
import DescriptionCaptionStyle from '../common/DescriptionCaptionStyle';
import { TapGestureHandler } from 'react-native-gesture-handler';
import Animated, {
    FadeIn,
    FadeInDown,
    FadeOutUp,
    runOnJS,
    useAnimatedStyle,
    useSharedValue,
    withDelay,
    withSpring,
    withTiming,
} from 'react-native-reanimated';
import HeartActive from '../../assets/Images/icon/double_tap.png';
import HeartVideoActive from '../../assets/Images/icon/double_tap.png';
import SHARE_ICON from '../../assets/Images/icon/share_icon.png';
import Dimensions from '../../constants/Dimensions';

import LIKE_ICON from '../../assets/Images/icon/like_icon.png';
import BOOKMARK_ICON from '../../assets/Images/icon/bookmark.png';
import COMMENT_ICON from '../../assets/Images/icon/comment.png'

import PlayViewCount from '../../assets/Images/icon/views_icon.png'
import Video from 'react-native-video';
import CommentComponent from './CommentComponent';
import {
    BottomSheetFooter,
    BottomSheetModal,
    BottomSheetModalProvider,
    BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { ActivityIndicator, } from 'react-native-paper';
import { useSafeAreaFrame, useSafeAreaInsets } from 'react-native-safe-area-context'
import Share from 'react-native-share';
import SharePostProfileFeature from '../common/SharePostProfileFeature';
import SharePostIcon from '../common/SharePostIcon';
import { _getFirstTimeLikePost, _getFirstTimeUnlockPost, _setFirstTimeLikePost, _setFirstTimeUnlockPost } from '../../utils/AuthLogin';
import UpdateUserLocationComponent from '../profile/UpdateUserLocationComponent';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import ResolutionVideoSwitch from './ResolutionVideoSwitch';
import ReelProgressBar from '../videoContents/ReelProgressBar';
import { REACTION_LIKE, REACTION_LIT, REACTION_MEH } from '../../constants/Constants';
import PlaylistPlaceholder from '../../assets/Images/full_user_image_place_holder.png'

const AnimatedImage = Animated.createAnimatedComponent(Image);
const IMAGE_HEIGHT = Dimensions.screenWidth - 40;
const PlaylistPostCard = ({ itemData, navigation, isMyProfile,
    postCardClick, ...props }) => {
    const { fullUserDetails, __commentObj, showCommentCountV, changeShowCommentCount } = useContext(AppStateContext);
    const { changeSingleProfileObj } = useContext(SinglePostContext);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    let __has_state_city = fullUserDetails.hasOwnProperty("_has_state_city") ? fullUserDetails._has_state_city : "NO";
    const __profile_seq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    useEffect(() => {
        __has_state_city = fullUserDetails.hasOwnProperty("_has_state_city") ? fullUserDetails._has_state_city : "NO";
    }, [fullUserDetails]);

    const insets = useSafeAreaInsets();
    const frame = useSafeAreaFrame();
    const BottomGap = Platform.OS == 'ios' ? 10 : insets.bottom
    const AVL_HEIGHT = frame.height - BottomGap; // For top dipsplay minus -insets.top 
    const [postIsLike, setPostIsLike] = useState(false);

    const [likeBtnDisable, setlikeBtnDisable] = useState(true); // TODO

    const [postIsBookmark, setPostIsBookmark] = useState(false);

    const [bookmarkBtnDisable, setbookmarkBtnDisable] = useState(true); // TODO

    const [commentBtnDisable, setcommentBtnDisable] = useState(true);  // TODO


    const [likeCount, setlikeCount] = useState(itemData.hasOwnProperty('likes') ? itemData.likes : 0)

    const [showCommentCount, setShowCommentCount] = useState(false);
    const postSeq = itemData.post_seq;

    const [blockPost, setblockPost] = useState(false);
    const [blockMsg, setblockMsg] = useState("");
    const [blockMsgDB, setblockMsgDB] = useState("");

    const [showConfirmPopup, setshowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());

    const [confirmTitle, setconfirmTitle] = useState("Confirmation");
    const [confirmMsg, setconfirmMsg] = useState("Confirmation");
    const [warringsData, setwarringsData] = useState({});

    const [postComments, setpostComments] = useState("");

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [snackBarType, setsnackBarType] = useState("FAILED");

    const [showCommentActive, setshowCommentActive] = useState(false);
    const [showBookmarkIcon, setshowBookmarkIcon] = useState(true);

    const [showTagUser, setshowTagUser] = useState(false);
    const [validUserHandleList, setvalidUserHandleList] = useState([]);

    const scale = useSharedValue(0);
    const opacity = useSharedValue(1);
    const doubleTapRef = useRef();

    const scaleVideo = useSharedValue(0);
    const opacityVideo = useSharedValue(1);
    const doubleTapVideoRef = useRef();
    const [playVideo, setPlayVideo] = useState(true);
    const [isServiceExecute, setIsServiceExecute] = useState(false);
    const videoPlayerRef = useRef(null)

    const [commentCount, setCommentCount] = useState(0);
    const [videoLoading, setVideoLoading] = useState(true);
    const [showPlayPauseBtn, setShowPlayPauseBtn] = useState(false);
    const [showDefaultVideoImage, setShowDefaultVideoImage] = useState(true);
    const [videoEnded, setVideoEnded] = useState(false);
    const [playViewCount, setPlayViewCount] = useState(0);

    const [showPlayViewIcon, setShowPlayViewIcon] = useState(false);
    const [submitPostViewsExecute, setSubmitPostViewsExecute] = useState(false);

    const [shareBody1, setShareBody1] = useState("");
    const [shareBody2, setShareBody2] = useState("");
    const updateUserLocationRef = useRef(null);
    const [selectedReactions, setSelectedReactions] = useState([]);
    const [userReactions, setUserReactions] = useState({});
    const [isLikeServiceExecute, setIsLikeServiceExecute] = useState(false);
    const [isFollowing, setIsFollowing] = useState(false);
    const [likeRemoveType, setLikeRemoveType] = useState({
        type: "",
        count: 0,
    });
    useEffect(() => {
        // if (fullScreen) {
        //     if (rowIndex == currentVisibleIndex) {
        //         if (!!videoPlayerRef.current) {
        //             videoPlayerRef.current.seek(0);
        //         }
        //         setVideoLoading(true);
        //         setPlayVideo((prevState) => true);
        //     }
        //     else {
        //         setPlayVideo((prevState) => false);
        //     }
        //     console.log("Date", new Date())
        //     console.log("rowIndex", rowIndex)
        //     console.log("currentVisibleIndex", currentVisibleIndex)
        // }

    }, [rowIndex, currentVisibleIndex])

    useEffect(() => {
        let sTag = false;
        if (itemData.hasOwnProperty("is_tagged")) {
            if (itemData.is_tagged == "YES") {
                sTag = true;
            }
        }
        setvalidUserHandleList(itemData.post_caption_tags);
        setshowTagUser(sTag);

        let reaction = [];
        if (itemData.hasOwnProperty("reactions")) {
            itemData.reactions.map(item => {
                reaction.push(item);
            });
        }
        setSelectedReactions([...[], ...reaction]);

        let userReactionsVal = {}
        if (itemData.hasOwnProperty("user_reactions")) {
            userReactionsVal = itemData.user_reactions;
        }
        setUserReactions(userReactionsVal);

    }, [itemData.profile_seq, itemData.post_seq, fullScreen])


    useEffect(() => {
        if (__commentObj != undefined) {
            if (__commentObj.postSeq == postSeq) {
                let sCmnt = false;
                if (__commentObj.commentRight == "YES") {
                    sCmnt = true;
                }
                setshowCommentActive(sCmnt);
            }
        }
    }, [__commentObj]);

    useEffect(() => {
        setShowDefaultVideoImage(true);
        let decodePostCmt = decodeHtmlEntitessData(itemData.post_comments)
        setpostComments(decodePostCmt)
        let isLike = false;
        if (itemData.is_liked === "YES") {
            isLike = true;
        }
        setlikeCount(itemData.likes)
        setPostIsLike(isLike);
        let is_bookmarked = false;
        if (itemData.is_bookmarked === "YES") {
            is_bookmarked = true;
        }
        setPostIsBookmark(is_bookmarked);
        // Comment
        let comntIsActive = false;
        if (itemData.hasOwnProperty("is_commented")) {
            if (itemData.is_commented == "YES") {
                comntIsActive = true;
            }
        }
        setshowCommentActive(comntIsActive)

        let isLikeBtnDisable = true;
        let isBookmarkDisable = true;
        let isCommentDisable = true;
        if (isMyProfile) {
            isLikeBtnDisable = true;
            isBookmarkDisable = true;
            isCommentDisable = false;
            setshowBookmarkIcon(false)
        }
        setlikeBtnDisable(isLikeBtnDisable);
        setbookmarkBtnDisable(isBookmarkDisable);
        setcommentBtnDisable(isCommentDisable);
        setCommentCount(itemData.comments);

    }, [itemData.post_seq, itemData.likes, itemData.is_liked,
    itemData.is_bookmarked, itemData.comments]);

    const unlockSheetRef = useRef(null);
    const threeDotMenuSheetRef = useRef(null);

    let likeTimeout = null;
    const likeIconBtnClick = (type, count, isLike) => {
        if (isMyProfile) {
            setIsServiceExecute(false);
            setPostIsLike(false);
            Alert.alert("Error", "Self Like is not Possible!")
            return;
        }
        let callRemoveService = false;
        if (type == REACTION_LIKE) {
            if (postIsLike) {
                setlikeCount(prevState => prevState == 1 ? 0 : prevState - 1);
                callRemoveService = true
            }
            else {
                setlikeCount(prevState => prevState + 1);
            }
        }
        else {
            setLikeRemoveType({
                type: type,
                count: count,
            });
            callRemoveService = isLike ? true : false;
            const dataObj = JSON.parse(JSON.stringify(userReactions));
            if (dataObj.hasOwnProperty(type)) {
                dataObj[type].selected = isLike ? "NO" : "YES";
                dataObj[type].count = isLike ? dataObj[type].count - 1 : dataObj[type].count + 1;
            }
            else {
                dataObj[type] = {
                    selected: isLike ? "NO" : "YES",
                    count: isLike ? count - 1 : count + 1
                }
            }
            setUserReactions(dataObj);
        }
        if (callRemoveService) {
            setIsServiceExecute(true);
            removePostLike(type);
        }
        else {
            setIsServiceExecute(true);
            submitPostLike(type);
        }
    }

    function submitPostLike(type) {
        setIsLikeServiceExecute(true)
        if (type == REACTION_LIKE) {
            setPostIsLike(current => true);
        }
        let hashMap = {
            _action_code: "11:SUBMIT_POST_LIKE",
            post_seq: postSeq,
            type: type,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            // setlikeCount(data.data.like_count);
            setIsLikeServiceExecute(false)
            sharePostLink("LIKE");

            clearPostCountDisplay(type);
            if (fullScreen) {
                appData._homePagePostRefresh = "YES";
            }
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (errorCode == "UE016") {
                }
                clearPostCountDisplay(type);
                setIsLikeServiceExecute(false)
            }
        });
    }
    function clearPostCountDisplay(type) {
        if (likeTimeout != null) {
            clearTimeout(likeTimeout);
        }
        likeTimeout = setTimeout(() => {
            setLikeRemoveType({
                type: "",
                count: "",
            });
        }, 3000);
    }
    function removePostLike(type) {
        setIsLikeServiceExecute(true)
        if (type == REACTION_LIKE) {
            setPostIsLike(current => false);
        }
        let hashMap = {
            _action_code: "11:REMOVE_POST_LIKE",
            post_seq: postSeq,
            type: type,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setIsLikeServiceExecute(false)
            // setPostIsLike(current => !current);
            // setlikeCount(data.data.like_count);
            clearPostCountDisplay(type);
            if (fullScreen) {
                appData._homePagePostRefresh = "YES";
            }

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                clearPostCountDisplay(type);
                setIsLikeServiceExecute(false)
            }
        });
    }

    const bookmarkIconBtnClick = () => {
        if (postIsBookmark) {
            setIsServiceExecute(true);
            removePostBookmark();
        }
        else {
            submitPostBookmark();
        }
    }
    function submitPostBookmark() {
        let hashMap = {
            _action_code: "11:ADD_BOOKMARK",
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setPostIsBookmark(true);
            sharePostLink("BOOKMARK");
            if (fullScreen) {
                appData._homePagePostRefresh = "YES";
            }
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }
    function removePostBookmark() {
        let hashMap = {
            _action_code: "11:REMOVE_BOOKMARK",
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setPostIsBookmark(false);
            postCardClick("REMOVE_BOOKMARK", {});

            if (fullScreen) {
                appData._homePagePostRefresh = "YES";
            }
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }
    const threeDotMenuClick = () => {
        //threeDotMenuSheetRef.current?.setModalVisible();
        setPlayVideo((prevState) => false)
        setShowPlayPauseBtn((prevState) => true);
        Keyboard.dismiss();
        setTimeout(() => {
            threeDotMenuSheetRef.current?.show();
        }, 100);

        // threeDotMenuSheetRef.current?.show();
    }
    const unlockBtnClick = () => {
        //unlockSheetRef.current?.setModalVisible();
        // navigation.navigate('SinglePostScreen', {
        //     postSeq: postSeq,
        // });
        // return;
        let __has_state_city_val = fullUserDetails.hasOwnProperty("_has_state_city") ? fullUserDetails._has_state_city : "NO";
        if (__has_state_city_val == "YES") {
            unlockSheetRef.current?.show();
            postCardClick("OPEN_UNLOCK_POPUP", {});
        }
        else {
            updateUserLocationRef.current?.show();
            postCardClick("OPEN_UNLOCK_POPUP", {});
            // setconfirmMsg(ErrorMessages.accNotFoundForPayMsg);
            // setshowConfirmPopup(true);
            // setshowConfirmPopupKey(Math.random());
            // setwarringsData({ clickType: "GO_ACCOUNT" });
        }

    }
    const confirmPopupPress = (clickId, obj) => {
        if (clickId == "positive") {
            if (obj.clickType == "GO_ACCOUNT") {
                navigation.navigate('AccountInfoScreen', {
                    locationMandatory: "YES",
                });
            }
        }
    }

    const unlockPostActionClick = (clickId, obj) => {
        if (clickId == "negetive") {
            unlockSheetRef.current?.hide();
            postCardClick("CLOSE_UNLOCK_POPUP", {});
        }
        if (clickId == "close") {
            unlockSheetRef.current?.hide();
            postCardClick("CLOSE_UNLOCK_POPUP", {});
            appData.__HomePageRefresh = Math.random();
            appData._profilePageRefresh = true;
            if (isPaidProfile) {
                setTimeout(() => {
                    navigation.replace('OthersProfileScreen', {
                        profileSeq: itemData.profile_seq,
                    })
                }, 1000);
            }
            else {
                unlockSheetRef.current?.hide();
                postCardClick("CLOSE_UNLOCK_POPUP", {});
                appData.__HomePageRefresh = Math.random();
                appData._profilePostPageRefresh = true;
                setTimeout(() => {
                    navigation.replace('UnlockSinglePostScreen', {
                        postSeq: itemData.post_seq, postProfileSeq: itemData.profile_seq,
                    })
                }, 1000);

            }


        }
    }
    const ThreeDotMenuPress = (clickId, obj) => {
        if (clickId == "blockPost") {
            setblockMsg(obj.msg);
            setblockPost(true);
            setIsServiceExecute(true);
            threeDotMenuSheetRef.current?.hide();

        }
        if (clickId == "deletePost") {
            threeDotMenuSheetRef.current?.hide();
            postCardClick("DELETE_POST", {});

        }
        if (clickId == "sharePost") {
            shareBtnPress();
        }
    }
    const showPostImage = (mediaUri) => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: mediaUri
        })

    }
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const goToProfile = (profileSeq) => {
        if (fullScreen && showVideoContent) {
            setPlayVideo((prevState) => false)
            setShowPlayPauseBtn((prevState) => true);
        }
        if (__ProfileSeq == profileSeq) {
            if (!isMyProfile) {
                navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
            }
        }
        else {
            if (cardType == "TAGGED") {
                navigation.push('OthersProfileScreen', {
                    profileSeq: profileSeq,
                });
            }
            else {
                navigation.navigate('OthersProfileScreen', {
                    profileSeq: profileSeq,
                });
            }

        }
    }
    const videoBtnPress = (media_file, media_cover) => {
        navigation.navigate("VideoDisplayScreen", {
            mediaUri: media_file,
            thumbnailUri: media_cover
        })

    }
    const [showPeopleList, setshowPeopleList] = useState(false);
    const postTagPeoplePress = () => {
        navigation.navigate("TagPeopleListScreen", {
            postSeq: itemData.post_seq
        })
    }
    const postTagNamePress = (userHandleId) => {
        let cameHndleId = userHandleId;
        let userHandle = cameHndleId.substring(2);
        getUserSeqService(userHandle)
    }
    function getUserSeqService(userHandle) {
        let hashMap = {
            _action_code: "11:GET_USER_SEQ",
            user_handle: userHandle,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setSnackbarMsg(errorMessage);
                setsnackBarType("SUCCESS");
                setdisplaySnackbar(true);
                setrefreshSnackBar(Math.random())
            }
        });
    }
    const rStyle = useAnimatedStyle(() => ({
        transform: [{ scale: Math.max(scale.value, 0) }],
    }));
    const onDoubleTap = useCallback(() => {
        if (!likeBtnDisable) {
            scale.value = withSpring(1, undefined, (isFinished) => {
                if (isFinished) {
                    scale.value = withDelay(500, withSpring(0));
                }
            });
            likeThePost(REACTION_LIKE);
        }

    }, [likeBtnDisable, postIsLike]);
    const onSingleTap = useCallback(() => {
        if (!fullScreen) {
            // showImage();
            openSinglePostScreen();
        }


        // opacity.value = withTiming(0, undefined, (isFinished) => {
        //     if (isFinished) {
        //         runOnJS(showImage)();
        //     }
        // });
    }, [postIsLike, likeCount, postIsBookmark, commentCount]);
    const showImage = () => {
        showPostImage(itemData.media_file)
    }
    const likeThePost = (type) => {
        if (!postIsLike) {
            likeIconBtnClick(type, likeCount, postIsLike);
        }
    }
    const rStyleVideo = useAnimatedStyle(() => ({
        transform: [{ scale: Math.max(scaleVideo.value, 0) }],
    }));
    const onDoubleTapVideo = useCallback(() => {
        if (!likeBtnDisable) {
            scaleVideo.value = withSpring(1, undefined, (isFinished) => {
                if (isFinished) {
                    scaleVideo.value = withDelay(500, withSpring(0));
                }
            });
            likeThePost(REACTION_LIKE);
        }

    }, [likeBtnDisable, postIsLike]);
    const onSingleTapVideo = useCallback(() => {
        // if (fullScreen) {
        //     if (!playVideo) {
        //         if (videoEnded) {
        //             videoPlayerRef.current.seek(0);
        //             setVideoEnded(false);
        //         }

        //     }
        //     setPlayVideo((prevState) => !prevState)
        //     setShowPlayPauseBtn((prevState) => !prevState);
        // }
        // else {
        //     openSinglePostScreen();
        //     // showVideo();
        // }
        if (fullScreen) {
            if (!playVideo) {
                if (videoEnded) {
                    videoPlayerRef.current.seek(0);
                    setVideoEnded(false);
                }

            }
            setPlayVideo((prevState) => !prevState)
            setShowPlayPauseBtn((prevState) => !prevState);
        }
        else {
            navigation.navigate("VideoContentScreen", {
                postSeq: itemData.post_seq, postProfileSeq: itemData.profile_seq,
                cameFrom: "POST"
            });
        }

    }, [playVideo, postIsLike, likeCount, postIsBookmark, commentCount]);
    const showVideo = () => {
        videoBtnPress(itemData.media_file, itemData.media_cover)
    }
    const ImageOverlay = () => {
        return <LinearGradient
            colors={[
                '#000000',
                'transparent',
                'transparent',
                '#000000'
            ]}
            locations={[0, 0.11, 0.8, 0.97]}
            style={style.NSLinearGradient}
        />
    }
    const ImageOverlayNew = () => {
        return <LinearGradient
            colors={[
                '#000000',
                'transparent',
                'transparent',
                '#000000'
            ]}
            locations={[0, 0.18, 0.8, 0.97]}
            style={style.NSSLinearGradient}
        />
    }
    const VideoOverlay = () => {
        return <LinearGradient
            colors={[
                '#000000',
                'transparent',
                'transparent',
                '#000000'
            ]}
            locations={[0, 0.11, 0.8, 0.97]}
            style={style.videoOverlayGrad}
        />
    }
    const [videoDisplayCount, setVideoDisplayCount] = useState(0)
    const onVideoEnd = () => {
        setVideoDisplayCount(prevState => prevState + 1);
        if (!repeatVideo) {
            setVideoEnded(true);
            setPlayVideo((prevState) => false)
            setShowPlayPauseBtn(true);
        }
        // if (videoDisplayCount == 3) {
        //     setVideoEnded(true);
        //     setPlayVideo((prevState) => false)
        //     setShowPlayPauseBtn(true);
        // }

    }
    const snapPoints = useMemo(() => ["40%"], []);
    const [openCommentPopup, setOpenCommentPopup] = useState(false);
    const [openCommentPopupKey, setOpenCommentPopupKey] = useState(Math.random());
    const commentBtnPress = (singlePost = true) => {
        setPlayVideo((prevState) => false)
        navigation.navigate('CommentScreen', {
            postSeq: itemData.post_seq, postProfileSeq: itemData.profile_seq,
        });
        // if (singlePost) {
        //     setOpenCommentPopup(true);
        //     setOpenCommentPopupKey(Math.random());
        // }
        // else {
        //     navigation.navigate('CommentScreen', {
        //         postSeq: itemData.post_seq, postProfileSeq: itemData.profile_seq,
        //     });
        // }

        // openCommentSheetRef.current?.present();
        // navigation.navigate('CommentScreen', {
        //     postSeq: itemData.post_seq, postProfileSeq: itemData.profile_seq,
        // });
    }
    const commentComponentClick = (clickID, obj) => {
        if (clickID == "SUBMIT") {
            setIsServiceExecute(true);
            setCommentCount(obj.comment_count);
        }
        else {
            setOpenCommentPopup(false);
            setOpenCommentPopupKey(Math.random());
        }

    }
    const openSinglePostScreen = () => {
        let updatedObj = {
            ...itemData, likes: likeCount, is_liked: postIsLike ? "YES" : "NO",
            is_bookmarked: postIsBookmark ? "YES" : "NO", comments: commentCount,
        }
        changeSingleProfileObj(updatedObj);
        navigation.navigate('SinglePostScreen', {
            postSeq: postSeq, isServiceExecute: "YES"
        });
    }

    const onVideoLoaded = () => {
        setVideoLoading(false);
    }



    const videoLoadStart = () => {
        // console.log("Video Start")
        setShowDefaultVideoImage(false);
    }
    function closeActionSheet() {

    }
    function onVidePlayStart() {
        let currentTime = new Date();
        let timeDiff = Math.abs(appData.videoViewExecuteTime - currentTime);
        let second = Math.floor((timeDiff / 1000))
        if (second >= 3) {
            appData.videoViewExecuteTime = new Date();
            if (__ProfileSeq != itemData.profile_seq) {
                if (!submitPostViewsExecute) {
                    setSubmitPostViewsExecute(true);
                    submitPostViewService()
                }

            }
        }

    }
    function submitPostViewService() {
        let hashMap = {
            _action_code: "11:SUBMIT_POST_VIEW",
            post_seq: postSeq,
            profile_seq: itemData.profile_seq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setPlayViewCount(data.data.view_count);
        }, (errorCode, errorMessage, data) => { // failure method

        });
    }
    const reelsVideoStart = () => {
        let currentTime = new Date();
        let timeDiff = Math.abs(appData.videoViewExecuteTime - currentTime);
        let second = Math.floor((timeDiff / 1000))
        if (second >= 3) {
            appData.videoViewExecuteTime = new Date();
            if (__ProfileSeq != itemData.profile_seq) {
                if (!submitPostViewsExecute) {
                    setSubmitPostViewsExecute(true);
                    submitPostViewService()
                }
            }
        }
    }
    const shareBtnPress = () => {
        submitShareCountService();
        sharePostLink("SHARE");
    }
    const sharePostLink = async (type) => {
        let body1 = "";
        let body2 = "";
        if (type == "SHARE") {
            let copyLinkText = creationOfCopyLink("POST", itemData.post_seq);
            const shareOptions = {
                message: "Exclusive content on SoTrue\n",
                url: copyLinkText
            }
            try {
                const shareResponse = await Share.open(shareOptions);
            } catch (error) {
                // console.log(error.message);
            }
            return;
        }
        else {
            if (type == "BOOKMARK") {
                setShareBody1(ErrorMessages.bookmarkPostBody1);
                setShareBody2(ErrorMessages.bookmarkPostBody2);
                openShareModal();
            }
            else if (type == "LIKE") {
                _getFirstTimeLikePost((data) => {
                    if (data != null) {
                        if (data == "YES") {
                            _setFirstTimeLikePost("NO");
                            setShareBody1(ErrorMessages.likePostBody1);
                            setShareBody2(ErrorMessages.likePostBody2);
                            openShareModal();
                        }
                    }
                    else {
                        _setFirstTimeLikePost("NO");
                        setShareBody1(ErrorMessages.likePostBody1);
                        setShareBody2(ErrorMessages.likePostBody2);
                        openShareModal();
                    }
                });
            }
            else if (type == "UNLOCK_POST") {
                _getFirstTimeUnlockPost((data) => {
                    if (data != null) {
                        if (data == "YES") {
                            _setFirstTimeUnlockPost("NO");
                            setShareBody1(ErrorMessages.unlockPostBody1);
                            setShareBody2(ErrorMessages.unlockPostBody2);
                            openShareModal();
                        }
                    }
                    else {
                        _setFirstTimeUnlockPost("NO");
                        setShareBody1(ErrorMessages.unlockPostBody1);
                        setShareBody2(ErrorMessages.unlockPostBody2);
                        openShareModal();
                    }
                });
            }


        }


    };
    function submitShareCountService() {
        let hashMap = {
            _action_code: "11:UPDATE_SHARE_COUNT",
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
        }, (errorCode, errorMessage, data) => { // failure method

        });
    }
    const [displaySharePopup, setDisplaySharePopup] = useState(false);
    const openShareModal = () => {
        setDisplaySharePopup(true)
    }
    const sharePostProfileCallback = (clickID, obj) => {
        if (clickID == "CLOSE") {
            setDisplaySharePopup(false)
        }

    }
    const updateUserLocationPopupPress = (clickId, obj) => {
        if (clickId == "negetive") {
            updateUserLocationRef.current?.hide();
        }
        if (clickId == "close") {
            updateUserLocationRef.current?.hide();
            unlockSheetRef.current?.show();
            postCardClick("OPEN_UNLOCK_POPUP", {});
        }
    }
    const [showLikeCountBox, setShowLikeCountBox] = useState(false);
    useEffect(() => {
        if (isLikeServiceExecute) {
            setShowLikeCountBox(true)
        }
        const showCountTimeOut = setTimeout(() => {
            setShowLikeCountBox(false);
        }, 3000);
        return () => {
            clearTimeout(showCountTimeOut)
        }
    }, [postIsLike]);
    const [showMehCountBox, setShowMehCountBox] = useState(false);
    useEffect(() => {
        if (isLikeServiceExecute) {
            setShowMehCountBox(true)
        }
        const showCountTimeOut2 = setTimeout(() => {
            setShowMehCountBox(false);
        }, 3000);
        return () => {
            clearTimeout(showCountTimeOut2)
        }
    }, [postIsMeh]);
    const [showLitCountBox, setShowLitCountBox] = useState(false);
    useEffect(() => {
        if (isLikeServiceExecute) {
            setShowLitCountBox(true)
        }
        const showCountTimeOut3 = setTimeout(() => {
            setShowLitCountBox(false);
        }, 3000);
        return () => {
            clearTimeout(showCountTimeOut3)
        }
    }, [postIsLit]);
    const [videoCompletedPercentage, setVideoCompletedPercentage] = useState(0)
    const onVideoProgress = (e) => {
        const currentTime = parseFloat(e.currentTime);
        const playableDuration = parseFloat(e.playableDuration)
        const percentage = (currentTime / playableDuration) * 100;
        setVideoCompletedPercentage(prevState => prevState = percentage);
    }
    const VideoOverlayReels = () => {
        return <LinearGradient
            colors={[
                '#000000',
                'transparent',
                'transparent',
                '#000000'
            ]}
            locations={[0, 0.2, 0.8, 0.97]}
            style={style.videoOverlayGrad}
        />
    }

    return (
        <View >
            <BottomSheetModalProvider>
                <View style={{ height: AVL_HEIGHT, width: Dimensions.screenWidth }}>
                    {/* <View style={{ ...style.videoResolutionContainer, zIndex: 1 }}>
                                <ResolutionVideoSwitch />
                            </View> */}
                    <View style={{ ...style.playListCountContainer, zIndex: 1, marginStart: 4 }}>
                        <View style={style.NSProfileView}>

                            <View>
                                <TouchableOpacity onPress={() => goToProfile(itemData.profile_seq)}>
                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                        <EntutoTextView style={style.NSProfileName}>{itemData.display_name}</EntutoTextView>
                                        {
                                            itemData.is_verified == "YES" ?
                                                <Image
                                                    style={style.verifiedIcon}
                                                    source={VerifiedIcon}
                                                    resizeMode={'contain'}
                                                />
                                                :
                                                null
                                        }
                                    </View>

                                </TouchableOpacity>
                                {
                                    cameFrom == "EPISODE" ?
                                        <View style={style.playListBox}>
                                            <View style={style.playlistCircleBox} />
                                            <Image source={playlistLogo != null ? { uri: playlistLogo } : PlaylistPlaceholder} style={style.playlistCover} />
                                            <EntutoTextView style={style.playListCountText}>3/12</EntutoTextView>
                                        </View>
                                        :
                                        <>
                                            {
                                                showPlayViewIcon ?
                                                    <View style={{ ...style.NSActionIconBox, paddingStart: 0, marginTop: 8 }}>
                                                        <Image
                                                            style={{
                                                                ...style.NSActionIcon,
                                                                tintColor: '#FFFFFF', height: 24, width: 24
                                                            }}
                                                            source={PlayViewCount}
                                                            resizeMode="contain"
                                                        />
                                                        <EntutoTextView style={style.NSActionCountTxt}>{playViewCount}</EntutoTextView>
                                                    </View>
                                                    : null
                                            }
                                        </>
                                }

                                {/* <EntutoTextView style={style.NSProfileID}>{UserHandlePrefix}{itemData.user_handle}</EntutoTextView> */}
                            </View>
                        </View>
                    </View>


                    {
                        blockPost ?
                            <>
                                {
                                    blockMsg.length != 0 ?
                                        <BottomSheetSuccessMsg successMsg={blockMsg} showCloseBtn={false} />
                                        : null
                                }
                                {
                                    blockMsgDB.length != 0 ?
                                        <View style={style.blockErrMsgMainBox}>
                                            <View style={style.blockErrMsgBox}>
                                                <EntutoTextView style={style.blockErrMsgHeading} >Post Blocked</EntutoTextView>
                                                <EntutoTextView style={style.blockErrMsg}>{blockMsgDB}</EntutoTextView>
                                            </View>
                                        </View>
                                        : null
                                }

                            </>

                            : null
                    }




                    <View style={{ flex: 1, flexDirection: 'column', width: '100%', }}>
                        {
                            showDefaultVideoImage ?
                                <View style={{
                                    backgroundColor: "black",
                                    height: "100%",
                                    width: "100%",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}>
                                    <ActivityIndicator size={24} style={{ backgroundColor: '#00000080', zIndex: 3, padding: 20, borderRadius: 15 }} />
                                    <View style={{
                                        position: 'absolute',
                                        top: 0, left: 0, right: 0, bottom: 0,
                                        alignItems: 'center', justifyContent: 'center',
                                        zIndex: 1
                                    }}>
                                        <ImageBackground
                                            imageStyle={{ borderRadius: 0 }}
                                            source={hasImageUrlExist(itemData.media_cover) ? { uri: itemData.media_cover } : null}
                                            style={style.videPostImage} >

                                        </ImageBackground>
                                    </View>
                                </View>
                                : null
                        }
                        <TapGestureHandler waitFor={doubleTapVideoRef} onActivated={onSingleTapVideo}>
                            <View style={style.videoBox}>
                                <TapGestureHandler
                                    maxDelayMs={250}
                                    ref={doubleTapVideoRef}
                                    numberOfTaps={2}
                                    onActivated={onDoubleTap}>
                                    <Animated.View>
                                        <Video
                                            ref={videoPlayerRef}
                                            poster={hasImageUrlExist(itemData.media_cover) ? itemData.media_cover : null}
                                            posterResizeMode={'cover'}
                                            source={{ uri: itemData.media_file }}
                                            resizeMode='contain'
                                            repeat={true}
                                            paused={!playVideo}
                                            muted={false}
                                            onLoadStart={() => reelsVideoStart()}
                                            onLoad={() => reelsVideoStart()}
                                            onBuffer={() => onVideoLoaded()}
                                            onProgress={(e) => onVideoProgress(e)}
                                            // onError={(e) => console.log(e)}
                                            onEnd={() => onVideoEnd()}
                                            style={{ ...style.NSPostVideo, height: AVL_HEIGHT }}
                                        />
                                        <VideoOverlayReels />
                                        <AnimatedImage
                                            source={HeartVideoActive}
                                            style={[
                                                style.videPostHeartImage,
                                                {
                                                    shadowOffset: { width: 0, height: Platform.OS == 'ios' ? 2 : 20 },
                                                    shadowOpacity: 0.35,
                                                    shadowRadius: Platform.OS == 'ios' ? 2 : 35,
                                                    position: 'absolute',
                                                    top: '40%',
                                                    left: '35%', zIndex: 3
                                                },
                                                rStyle,
                                            ]}
                                            resizeMode={'contain'}
                                        />
                                        {
                                            videoLoading ?
                                                <View style={style.playBtnBox}>
                                                    <ActivityIndicator size={32} />
                                                </View>
                                                : null

                                        }
                                        {showPlayPauseBtn ?
                                            <View style={style.playBtnBox}>
                                                <Image
                                                    style={style.playBtnBoxIcon}
                                                    source={PlayBtnIcon}
                                                    resizeMode="cover"
                                                />
                                            </View>
                                            : null
                                        }
                                    </Animated.View>
                                </TapGestureHandler>
                            </View>
                        </TapGestureHandler>
                        {/* <VideoOverlay /> */}
                    </View>
                    {
                        !blockPost && !playVideo ?
                            <>
                                <View style={{ ...style.likeBtnBox, left: 0, zIndex: 99 }}>

                                    {
                                        selectedReactions.map((item, i) => {
                                            let countValue = 0;
                                            let isLike = false;
                                            const dataObj = getValueFromReactions(item);
                                            if (userReactions.hasOwnProperty(item)) {
                                                countValue = userReactions[item].count;
                                                if (userReactions[item].selected == "YES") {
                                                    isLike = true;
                                                }
                                            }
                                            if (dataObj != null) {
                                                return <View key={i} style={{ ...style.NSActionIconBox, ...style.iconGap, paddingStart: 16, }}>
                                                    <LikeBtnComponent
                                                        inActiveIcon={dataObj.icon}
                                                        activeIcon={dataObj.icon}
                                                        disable={likeBtnDisable}
                                                        likeButtonPress={() => likeIconBtnClick(item, countValue, isLike)}
                                                        isLike={isLike}
                                                        style={style.reelIcon} />

                                                    {
                                                        likeRemoveType.type == item ?
                                                            <EntutoTextView style={style.NSActionCountTxt}>{countValue}</EntutoTextView>
                                                            : null}
                                                </View>
                                            }

                                        })
                                    }
                                    <View style={{ ...style.NSActionIconBox, paddingStart: 16, }}>
                                        <LikeBtnComponent
                                            inActiveIcon={LIKE_ICON}
                                            activeIcon={LIKE_ICON}
                                            disable={likeBtnDisable}
                                            likeButtonPress={() => likeIconBtnClick(REACTION_LIKE, likeCount, postIsLike)}
                                            isLike={postIsLike}
                                            style={style.reelIcon} />
                                        {
                                            showLikeCountBox ?
                                                <EntutoTextView style={style.NSActionCountTxt}>{likeCount}</EntutoTextView>
                                                : null}
                                    </View>

                                </View>
                                <View style={{ ...style.likeBtnBox, right: 0, zIndex: 99, alignItems: 'flex-end' }}>
                                    <View style={{ ...style.NSActionIconBox, ...style.iconGap, justifyContent: 'flex-end', }}>
                                        <TouchableOpacity disabled={commentBtnDisable}
                                            onPress={() => commentBtnPress(false)}
                                            style={{ flexDirection: 'row' }}>
                                            {/* <EntutoTextView style={{ ...style.NSActionCountTxt, marginRight: 10 }}>{commentCount}</EntutoTextView> */}

                                            <Image
                                                style={{ ...style.reelIcon, tintColor: theme.colors.postInActiveIconColor }}
                                                source={COMMENT_ICON}
                                                resizeMode="contain"
                                            />
                                        </TouchableOpacity>
                                    </View>
                                    <View style={{ ...style.NSActionIconBox, ...style.iconGap }}>
                                        {
                                            showBookmarkIcon ?
                                                <BookmarkBtnComponent
                                                    activeIcon={BOOKMARK_ICON}
                                                    inActiveIcon={BOOKMARK_ICON}
                                                    disable={bookmarkBtnDisable}
                                                    bookmarkButtonPress={() => bookmarkIconBtnClick()}
                                                    isBookmark={postIsBookmark}
                                                    style={style.reelIcon} />
                                                : null
                                        }

                                    </View>
                                    <View style={{ ...style.NSActionIconBox, zIndex: 9, }}>
                                        <TouchableOpacity
                                            onPress={() => shareBtnPress()}>
                                            <Image
                                                style={{ ...style.NSActionIcon, tintColor: '#FFFFFF' }}
                                                source={SHARE_ICON}
                                                resizeMode="contain"
                                            />
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </>
                            : null
                    }
                    <View style={{ ...style.reelProgressBarContainer, zIndex: 1 }}>
                        <ReelProgressBar value={videoCompletedPercentage} />
                    </View>

                </View>

                {
                    openCommentPopup ?
                        <CommentComponent
                            refreshKey={openCommentPopupKey}
                            openPopup={openCommentPopup}
                            postSeq={itemData.post_seq}
                            postProfileSeq={itemData.profile_seq}
                            navigation={navigation}
                            isSheetComment={true}
                            commentComponentClick={commentComponentClick} />
                        : null
                }


            </BottomSheetModalProvider >
            <CustomSnackbar snackMsg={SnackbarMsg} snackType={snackBarType} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />

            <ActionSheet ref={unlockSheetRef}
                // statusBarTranslucent
                bounceOnOpen={false}
                onClose={() => closeActionSheet()}
                closeOnPressBack={false}
                gestureEnabled={false}
                closeOnTouchBackdrop={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        unlockSheetRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <UnlockPostActionView
                        postSeq={itemData.post_seq}
                        profileSeq={itemData.profile_seq}
                        amountValue={unlockFeesValue}
                        refVal={unlockSheetRef}
                        isPaidProfile={isPaidProfile}
                        unlockPostActionClick={(clickId, obj) => unlockPostActionClick(clickId, obj)} />
                </ScrollView>
            </ActionSheet >
            <ActionSheet ref={threeDotMenuSheetRef}
                statusBarTranslucent
                bounciness={4}
                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}
                onClose={() => {
                    setPlayVideo((prevState) => true)
                    setShowPlayPauseBtn((prevState) => false);
                }}
            >
                <ThreeDotMenuActionView navigation={navigation} isMyProfile={isMyProfile} unlockPost={unlockPost} postSeq={itemData.post_seq}
                    ThreeDotMenuPress={(clickId, obj) => ThreeDotMenuPress(clickId, obj)} />
            </ActionSheet >
            {
                showConfirmPopup &&
                <ConfirmationPopup
                    visiblePopupKey={showConfirmPopupKey}
                    visiblePopup={showConfirmPopup}
                    title={confirmTitle}
                    messagebody={confirmMsg}
                    positiveButton="Yes"
                    negativeButton="No"
                    data={warringsData}
                    popupClick={(clickID, data) => { confirmPopupPress(clickID, data) }}
                />
            }
            {/* They Want to remove this at 09-09-24  */}
            {/*<Modal
                animationType="slide"
                transparent
                visible={displaySharePopup}
                onRequestClose={() => setDisplaySharePopup(false)}
            >
                <SharePostProfileFeature shareBody1={shareBody1}
                    shareBody2={shareBody2}
                    shareType="POST"
                    shareSeq={itemData.post_seq}
                    sharePostProfileCallback={sharePostProfileCallback} />
            </Modal> */}

            <ActionSheet ref={updateUserLocationRef}
                statusBarTranslucent
                bounciness={4}
                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        updateUserLocationRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <UpdateUserLocationComponent navigation={navigation}
                        updateUserLocationPopupPress={(clickId, obj) => updateUserLocationPopupPress(clickId, obj)}

                    />
                </ScrollView>
            </ActionSheet >
        </View>
    )
}

export default React.memo(PlaylistPostCard);


const styles = theme => StyleSheet.create({
    postCard: {
        flexDirection: "column",
        position: 'relative',
    },
    postCardHeader: {
        flexDirection: "row",
        flex: 1,
        alignItems: 'center',
        paddingHorizontal: 4,
    },
    newPostHeader: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 999,
        backgroundColor: '#00000001',
        minHeight: 64,
        paddingTop: 8
    },
    headerImageBox: {
        height: 31.5,
        width: 31.5,
        marginRight: 10,
        borderRadius: 31.5,
        marginLeft: 18,

    },
    headerImage: {
        height: 31.5,
        width: 31.5,
        borderRadius: 31.5
    },
    verifiedIcon: {
        width: 13,
        height: 13,
        marginLeft: 8,
        // position: 'absolute',
        // right: -4,
        // top: 12,
    },
    headerTextBox: {
        flexDirection: 'column',
        alignContent: 'center'
    },
    headerDisplayName: {
        fontSize: 12,//13
        color: "#FFFFFF",//#43180B
        fontFamily: theme.getFontFamily('bold'),
        flexDirection: 'row',
    },
    headerDate: {
        fontSize: 9,//12
        color: "#FFFFFF",//#000000
        fontWeight: '400',
    },
    headerOptionIconBox: {
        marginLeft: 'auto',
        flexDirection: 'row',
        alignItems: 'center',
    },
    headerOptionIcon: {
        width: 18,
        height: 18,
        resizeMode: 'contain',
        tintColor: '#FFFFFF',
        zIndex: 1000,
    },
    headerOptionIconNew: {
        width: 18,
        height: 14,
        resizeMode: 'contain',
        tintColor: '#FFFFFF',
        zIndex: 1000,
    },
    postImageBox: {
        flex: 1,
        position: 'relative',

    },
    postImageContainer: {
        width: '100%',
        minHeight: 450,
        maxHeight: '100%',
        resizeMode: 'cover',
        position: 'relative',
        backgroundColor: '#CCC',
        // paddingVertical: 1
    },
    postImageContainerImg: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
        alignItems: 'center',
        justifyContent: 'center',
    },
    postImageContainerHeartIcon: {
        width: 150,
        height: 150,
        minHeight: 150,
        maxHeight: 150,
    },
    postImage: {
        width: '100%',
        // minHeight: IMAGE_HEIGHT,
        flex: 1,
        // minHeight: 300,
        borderRadius: 15,
    },
    postFuzzayImage: {
        width: '100%',
        height: '100%',
        minHeight: IMAGE_HEIGHT,
        resizeMode: 'cover',
        borderRadius: 27,
    },
    videPostImage: {
        width: '100%',
        height: "100%", //400
        minHeight: IMAGE_HEIGHT,
        // resizeMode: 'cover',
        alignItems: 'center',
        justifyContent: 'center'
    },
    videPostHeartImage: {
        width: 120,
        height: 120,
        minHeight: 120,
        maxHeight: 120,
    },
    postActionIconBox: {
        flexDirection: 'row',
        alignItems: 'center',
        // backgroundColor: '#FFF',
        justifyContent: 'space-around',
    },

    postActionIcon: {
        width: 22,
        height: 22,
    },
    postActionIconNew: {
        width: 18,
        height: 18,
    },
    postActionCommentIcon: {
        width: 22,
        height: 22,
        tintColor: theme.colors.primaryColor,
    },
    postActionTextIconBox: {
        flexDirection: 'column',
        // marginRight: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    postActionText: {
        fontSize: 11,
        marginLeft: 6,
    },
    newCountText: {
        fontSize: 16,
        color: '#FFF',

        // color: '#707070',
        marginLeft: 0,
        // marginTop: -4
    },
    postDescTxtBox: {
        marginBottom: 8,
        paddingHorizontal: 4,
    },
    postDescTxt: {
        fontSize: 14,
    },
    videoBox: {
        width: '100%',
        minHeight: 450,
        maxHeight: '100%',
        resizeMode: 'cover',
        borderRadius: 15,
        position: 'relative',
        backgroundColor: '#000',
    },
    playBtnBox: {
        height: 63,
        width: 63,
        position: 'absolute',
        top: '43%',
        left: '43%',
        backgroundColor: '#00000080',
        borderRadius: 15,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.50,
        shadowRadius: 1.41,


        // elevation: 2,
    },
    playBtnBoxIcon: {
        width: 34,
        height: 40,
        // tintColor: '#000',
        backgroundColor: 'transparent'
    },
    linearGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        borderRadius: 15,
    },

    lockBox: {
        width: '100%',
        minHeight: 450,
        resizeMode: 'cover',
        borderRadius: 15,
        position: 'relative',

    },
    lockBtnBox: {
        position: 'absolute',
        top: '25%',
        left: 0,
        right: 0,
        alignItems: 'center'
    },
    lockBoxIcon: {
        width: 34,
        height: 40,
    },
    lockButton: {
        borderRadius: 14,
        backgroundColor: '#FFFFFF',
        borderColor: '#FFFFFF',
        borderWidth: 2,
        zIndex: 20
    },
    lockBtnTextBox: {
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 15,
    },
    lockButtonTxt: {
        color: '#333333',
        fontSize: 14,
        fontFamily: theme.getFontFamily('bold'),
    },
    lockButtonPriceTxt: {
        color: '#E59D80',
        fontSize: 14,
        fontFamily: theme.getFontFamily('bold'),
    },
    blockErrMsgMainBox: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.backgroundColor,
        zIndex: 2,

    },
    blockErrMsgBox: {
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 15,
        marginBottom: 15,

    },
    blockErrMsgHeading: {
        fontSize: 20,
        fontWeight: '600',
        paddingHorizontal: 15,
        color: Colors.primaryColor
    },
    blockErrMsg: {
        fontSize: 18,
        paddingHorizontal: 15,
        color: Colors.errorColor
    },
    bookmarkWarringBox: {
        borderRadius: 8,
        paddingHorizontal: 8,
        paddingVertical: 8,
        backgroundColor: '#ff323250',
        marginHorizontal: 14
    },
    bookmarkWarringTxt: {
        color: '#FFF',
        fontSize: 11,
    },
    tagPIconBox: {
        position: 'absolute',
        right: 10,
        bottom: 10,
        backgroundColor: "#FFF",
        borderRadius: 32,
    },
    tagPIconBoxIcon: {
        width: 28,
        height: 28,
    },


    NSContainer: {
        backgroundColor: '#000000',
        // height: Dimensions.screenHeight - 100,
        width: Dimensions.screenWidth,
        position: 'relative',
        flexDirection: 'row'
    },
    NSmediaImage: {
        width: Dimensions.screenWidth,
        // height: Dimensions.screenHeight - StatusBar.currentHeight,
        backgroundColor: '#000',
        zIndex: 2,
        alignItems: 'center',
        justifyContent: 'center',
    },
    NSLinearGradient: {

        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    NSBottomActionView: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 3,
    },
    NSBottomActionViewBox: {
        height: 56,
        position: 'relative',
        zIndex: 3,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    NSLikeCmntBox: {
        flex: 3,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 14,
    },
    NSActionIconBox: {
        flexDirection: 'row',

        paddingEnd: 16,
        alignItems: 'center',
    },
    BNSActionIconBox: {
        flexDirection: 'row',
        paddingStart: 30,
        paddingEnd: 24,
        alignItems: 'center',
    },
    NSActionIcon: {
        width: 24,
        height: 24,
        marginRight: 6
    },
    reelIcon: {
        width: 32,
        height: 32,
        marginRight: 6
    },
    fullScreenIcon: {
        width: 32,
        height: 32,
        marginRight: 6
    },
    listPostIcon: {
        width: 32,
        height: 32,
    },
    NSActionCountTxt: {
        fontSize: 18,
        color: '#FFFFFF',
        marginStart: 6
    },
    NSLinearGradBox: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    NSProfileSection: {
        position: 'absolute',
        bottom: 56,
        left: 30,
        right: 48,
        minHeight: 100,
        maxHeight: 120,

    },
    NSProfileView: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: 20,
    },
    NSProfileViewImage: {
        width: 38,
        height: 38,
        borderRadius: 38,
        marginRight: 10
    },
    NSProfileName: {
        color: '#FFFFFF',
        fontSize: 12,
    },
    NSProfileID: {
        color: '#FFFFFF',
        fontSize: 9,
        marginTop: 5,
    },
    NSPostDescTxt: {
        marginTop: 12,
        color: '#FFFFFF',
        fontSize: 11,

    },
    NSPostVideo: {
        // height: Dimensions.screenHeight,
        width: Dimensions.screenWidth,
    },

    addCommentBox: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        backgroundColor: '#FFFF',
        minHeight: 76,
        paddingVertical: 5,
        flexDirection: 'row',
        width: Dimensions.screenWidth,
        alignItems: 'center',
        height: 56,
        borderColor: '#000',
        borderWidth: 2,
    },
    NSSLinearGradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    NSTopHeaderThreeDotIcon: {
        position: 'absolute',
        top: Platform.OS == 'ios' ? 40 : 32,
        right: 16,
        zIndex: 3
    },
    videoOverlayGrad: {

        position: 'absolute',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
    },
    mainProfileBoxContainer: {
        minHeight: 10,
        backgroundColor: theme.colors.backgroundColor,
        paddingTop: 14,
        paddingBottom: 14,
    },
    bellowProfileBox: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingTop: 8,
        paddingStart: 14,
        alignItems: 'center',
    },
    bellowDescription: {
        paddingTop: 10,
        paddingHorizontal: 14
    },
    nameWithFollowBox: {
        flexDirection: 'row',
    },
    bellowProfileName: {
        fontSize: 14,
        color: theme.colors.primaryTextColor,
        fontFamily: theme.getFontFamily('bold'),
    },
    bellowFollowBtn: {
        paddingHorizontal: 10,
        paddingVertical: 2,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: theme.colors.primaryTextColor,
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 16,
    },
    bellowFollowBtnText: {
        fontSize: 10,
        color: theme.colors.primaryTextColor,
    },
    bellowTimeFieldTextBox: {
        paddingHorizontal: 14,
        marginVertical: 6
    },
    bellowTimeFieldText: {
        fontSize: 12,
        color: theme.colors.primaryTextColor,
        opacity: 0.5
    },
    bellowCommentText: {
        marginBottom: 12,
        color: theme.colors.primaryTextColor,
        fontSize: 12,
    },
    //New Reels
    videoResolutionContainer: {
        position: 'absolute',
        top: 26,
        right: 36,
        zIndex: 1,
    },
    reelProgressBarContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,

    },
    likeBtnBox: {
        position: 'absolute',
        bottom: 30,
    },
    iconGap: {
        marginBottom: 24
    },
    playListCountContainer: {
        position: 'absolute',
        top: 42,
        left: 16,
    },
    playlistProfileText: {
        fontSize: 12,
        color: '#FFF',
    },
    playListBox: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 6,
        paddingLeft: 6,
        position: 'relative',
        // borderWidth: 1,
        height: 32,
    },
    playlistCircleBox: {
        width: 26,
        height: 26,
        borderRadius: 14,
        backgroundColor: '#000000',
        position: 'absolute',
        left: 0,
        top: 3,
        bottom: 0,
        right: 0,
        borderWidth: 1,
        borderColor: '#707070',
    },
    playlistCover: {
        width: 30,
        height: 32,
        resizeMode: 'contain',
        borderRadius: 2,
    },
    playListCountText: {
        marginLeft: 8,
    },

})
