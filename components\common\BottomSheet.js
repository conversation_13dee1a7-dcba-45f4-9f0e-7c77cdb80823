import React, { useState } from 'react'
import { Animated, Dimensions, Modal, StyleSheet, Text, View } from 'react-native'

const BottomSheet = () => {
    const [modalVisible, setModalVisible] = useState(false);
    const showPopup = () => {
        setModalVisible(true)
    }
    const closePopup = () => {
        setModalVisible(false)
    }

    return (
        <Modal
            animated
            animationType="fade"
            visible={modalVisible}
            transparent={true}
            onRequestClose={() => closePopup()}
        >
            <View style={{ flex: 1, backgroundColor: "#000000AA", justifyContent: 'flex-end' }}>

            </View>
        </Modal>
    )
}

export default BottomSheet

const styles = StyleSheet.create({
    overlay: {
        backgroundColor: 'rgba(0,0,0,0.2)',
        flex: 1,
        justifyContent: 'flex-end',
    },
    container: {
        backgroundColor: 'white',
        paddingTop: 12,
        borderTopRightRadius: 12,
        borderTopLeftRadius: 12,
    },
})
