import { Image, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import AppLogo from '../../assets/Images/Spalash_logo.gif'
import Dimensions from '../../constants/Dimensions'
import Video from 'react-native-video'
import Colors from '../../constants/Colors'
import { FAB } from 'react-native-paper'
const IntroSliderBoxItem = ({ itemData, currentIndex, currentVisibleIndex, videoEnd }) => {
    const [heightScaled, setHeightScaled] = useState(231);
    const [videoEnded, setvideoEnded] = useState(false);
    const videoRef = useRef(null);
    const replayVideo = () => {
        setvideoEnded(false);
        videoRef.current.seek(0);
    }
    const VideoItemEnded = () => {
        // videoEnd();
        setvideoEnded(true);
    }
    const OnErrorOccure = () => {
        // videoEnd();
        setvideoEnded(true);
    }
    return (
        <View style={styles.sliderItemBox}>
            <Video
                key={itemData.id}
                ref={videoRef}
                rate={1} volume={1}
                source={{
                    uri: itemData.source,
                    cache: { size: 1500, expiresIn: 3600 }
                }}
                muted={true}
                paused={currentIndex !== currentVisibleIndex}
                // repeat={true}
                onEnd={VideoItemEnded}
                onError={OnErrorOccure}
                onLoad={item => {
                    const { width, height } = item.naturalSize;
                    const heightScaledV = height * (Dimensions.screenWidth / width);
                    setHeightScaled(heightScaledV);
                }}
                style={{ ...styles.contentVideoPortation, }}
                resizeMode='contain'

            />
            {
                videoEnded ?
                    <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, justifyContent: 'center', alignItems: 'center' }}>
                        <FAB
                            style={{ backgroundColor: '#FFFFFF', }}
                            small
                            icon="refresh"
                            onPress={() => replayVideo()}
                        />
                    </View>
                    : null
            }
        </View>
    )
}

export default IntroSliderBoxItem

const styles = StyleSheet.create({
    sliderItemBox: {
        //  borderWidth: 1, 
        position: 'relative',
        width: Dimensions.screenWidth,
        // height: Dimensions.screenHeight - 120,
        flex: 1,
    },
    contentVideoPortation: {
        // width: Dimensions.screenWidth,
        // height: Dimensions.screenHeight - 120,
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
    },
})