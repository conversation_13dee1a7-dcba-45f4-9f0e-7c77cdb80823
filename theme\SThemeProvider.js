import React, {useEffect, useState} from 'react';
import {DEFAULT_THEME_COLOR} from '../constants/Colors';
import Dimensions from '../constants/Dimensions';
import {ThemeColorList} from '../constants/Constants';
import {_setAppThemeColor} from '../utils/AuthLogin';
import {fontFamilies} from '../constants/Fonts';
import {
  horizontalScale,
  verticalScale,
  moderateScaleCustom,
} from './ResponsiveMatrix';
import appData from '../data/Data';
import {getFontSize} from './ResponsiveFont';

export const ThemeContext = React.createContext();

const SThemeProvider = ({children}) => {
  const [appThemeType, setAppThemeType] = useState('DARK');
  const [appThemeColor, setAppThemeColor] = useState(DEFAULT_THEME_COLOR);
  const [selectedColorType, setSelectedColorType] = useState('');

  const BASE_CONFIG = {
    guidelineBaseWidth: 390,
    guidelineBaseHeight: 680,
  };

  useEffect(() => {
    // changeThemeColor("COLOR_1");
    // changeAppTheme("DARK");
  }, []);
  const LightTheme = {
    ...appThemeColor,
    backgroundColor: '#FFFFFF',
    statusBarColor: '#FFFFFF',
    primaryTextColor: '#000000',
    loginSignupGradient1: '#FFFFFF', //FC6767
    loginSignupGradient2: '#FFFFFF', //FC6767
    loginSignupGradient3: '#FFFFFF', //759fd2

    placeholderBackgroundColor: '#ccc',
    placeholderForegroundColor: '#999',

    listRowTextColor: '#43180B',
    listRowIconTintColor: '#43180B',

    appBarBackgroundColor: '#FFFFFF',
    topHeaderBottomColor: '#CCC',
    topHeaderColor: '#43180B',
    mainHeadingColor: '#43180B',
    inputUnderLineTxt: '#43180B',

    inputPlaceholderColor: '#171F24',
    inputTextColor: '#000000',
    inputDisabledTextColor: '#00000050',
    inputPrimaryColor: '#FC6767',
    inputOutlineColor: 'rgba(60, 60, 67, 0.3)',
    switchTextColor: '#000000',
    notiHeaderText: '#171F24',
    notiHeaderViewAllText: '#AAB2B7',

    threeDotMenuDivider: '#CCC',
    threeDotMenuText: '#000000',

    cancelBtnBackground: '#FFFFFF',
    cancelBtnText: '#43180B',
    cancelBtnBorder: '#EEEEEE',
    submitBtnBackground: '#FC6767',
    submitBtnBorder: '#FC6767',
    disableSubmitBtnBackground: '#FC676740',
    submitBtnText: '#FFFFFF',

    homeBottomTabInActiveColor: '#77838F',
    postInActiveIconColor: '#000000',
    bottomTabBackground: '#FFFFFF',
    dropdownTextColor: '#000000',
    dropdownBorderColor: '#000000',
    dropdownSelectedTextColor: '#000000',
    dropdownPlaceHolderColor: '#171F24',
    dropdownLabelColor: '#000000',
    copyTextBackground: '#CCC',
    copyTextBtnColor: '#000000',

    buttonGroupBackColor: '#FFFFFF',
    buttonGroupBorderColor: '#EEEEEE',
    buttonGroupInActiveTextColor: '#778087',
    buttonGroupActiveTextColor: '#FFFFFF',

    switchInActiveC: '#EEEEEE',
    switchInActiveThumbC: '#CCCCCC',

    totalEaringsVal: '#000000',
    totalEaringsValTxt: '#000000',
    dropIconTintColor: '#000000',
    listItemIcon: '#000000',
    accVerifiedFlushMsg: '#000000',

    postProgressBarBackground: '#EEEEEE',
    progressBarItemIcon: '#000000',
    fileInputBoxBorderC: '#000000',
    fileInputBoxActionIconTintColorC: '#000000',
    profileImageBoxGradientOne: '#00000000',
    profileImageBoxGradientTwo: '#FFFFFF',

    profileCountLabelC: '#000000',
    profileCountValueC: '#000000',
    profileBoxHeaderOptionIconC: '#FFFFFF',
    profileBoxNameC: '#000000',
    profileBoxIdC: '#91909A',
    profileBoxBioC: '#91909A',
    PItitleC: '#A1A1A1',

    playlistBannerTitleColor: '#707070',
    playlistBannerYearCountColor: '#707070',
    playlistBannerDescColor: '#707070',
    descriptionBoxText: '#707070',
    playlistLCOne: '#FFFFFF00',
    playlistLCTwo: '#FFFFFF00',
    playlistLCThree: '#88888877',
    playlistLCFour: '#FFFFFF',

    homeShowCardText: '#FFFFFF',
    homeShowCardTextShadow: 'rgba(0, 0, 0, 0.75)',

    optionSelectionItemColor: '#768390',
    selectBoxSearchIconTintColor: '#CCCCCC',
    searchBoxBorder: '#707070',

    dateTextColor: '#000000',

    shareSocialIconTintColor: '#000000',
    shareSocialTintIconTintColor: '#000000',

    errorPopupMsgColor: '#707070',

    ...appData.backupAppThemeColor,
  };
  const DarkTheme = {
    ...appThemeColor,
    backgroundColor: '#111111',
    statusBarColor: '#111111',
    primaryTextColor: '#FFFFFF',
    loginSignupGradient1: '#111111',
    loginSignupGradient2: '#111111',
    loginSignupGradient3: '#111111',

    placeholderBackgroundColor: '#111111',
    placeholderForegroundColor: '#ccc',

    listRowTextColor: '#FFFFFF',
    listRowIconTintColor: '#FFFFFF',

    appBarBackgroundColor: '#111111',
    topHeaderBottomColor: '#ccc',
    topHeaderColor: '#FFFFFF',
    mainHeadingColor: '#FFFFFF',
    inputUnderLineTxt: '#FFFFFF',

    inputPlaceholderColor: '#ccc',
    inputTextColor: '#FFFFFF',
    inputDisabledTextColor: '#FFFFFF50',
    inputPrimaryColor: '#FFFFFF',
    inputOutlineColor: 'rgba(60, 60, 67, 0.3)',
    switchTextColor: '#FFFFFF',
    notiHeaderText: '#FFFFFF',
    notiHeaderViewAllText: '#FFFFFF',

    threeDotMenuDivider: '#CCC',
    threeDotMenuText: '#FFFFFF',

    cancelBtnBackground: '#F2EBE9',
    cancelBtnText: '#43180B',
    cancelBtnBorder: '#F2EBE9',
    submitBtnBackground: '#FC6767',
    submitBtnBorder: '#FC6767',
    disableSubmitBtnBackground: '#FC676740',
    submitBtnText: '#FFFFFF',

    homeBottomTabInActiveColor: '#FFFFFF',
    postInActiveIconColor: '#FFFFFF',

    bottomTabBackground: '#000000',
    dropdownTextColor: '#FFFFFF',
    dropdownBorderColor: '#FFFFFF',
    dropdownSelectedTextColor: '#FFFFFF',
    dropdownPlaceHolderColor: '#CCC',
    dropdownLabelColor: '#FFFFFF',
    copyTextBackground: '#373737',
    copyTextBtnColor: '#FFFFFF',

    buttonGroupBackColor: '#373737',
    buttonGroupBorderColor: '#373737',
    buttonGroupInActiveTextColor: '#FFFFFF',
    buttonGroupActiveTextColor: '#FFFFFF',

    switchInActiveC: '#383b37',
    switchInActiveThumbC: '#50524e',

    totalEaringsVal: '#FFFFFF',
    totalEaringsValTxt: '#FFFFFF',

    dropIconTintColor: '#FFFFFF',
    listItemIcon: '#FFFFFF',
    accVerifiedFlushMsg: '#111111',
    postProgressBarBackground: '#030001',
    progressBarItemIcon: '#FFFFFF',

    fileInputBoxBorderC: '#FFFFFF',
    fileInputBoxActionIconTintColorC: '#FFFFFF',
    profileImageBoxGradientOne: '#00000050',
    profileImageBoxGradientTwo: '#00000050',

    profileCountLabelC: '#FFFFFF',
    profileCountValueC: '#FFFFFF',
    profileBoxHeaderOptionIconC: '#FFFFFF',
    profileBoxNameC: '#FFFFFF',
    profileBoxIdC: '#FFFFFF',
    profileBoxBioC: '#FFFFFF',
    PItitleC: '#FFFFFF',

    playlistBannerTitleColor: '#FFFFFF',
    playlistBannerYearCountColor: '#FFFFFF',
    playlistBannerDescColor: '#FFFFFF',
    descriptionBoxText: '#FFFFFF',

    playlistLCOne: '#111111',
    playlistLCTwo: '#00000070',
    playlistLCThree: '#11111170',
    playlistLCFour: '#000',

    homeShowCardText: '#FFFFFF',
    homeShowCardTextShadow: 'rgba(0, 0, 0, 0.75)',

    optionSelectionItemColor: '#768390',
    selectBoxSearchIconTintColor: '#CCCCCC',
    searchBoxBorder: '#707070',

    dateTextColor: '#FFFFFF',

    shareSocialIconTintColor: '#FFFFFF',
    shareSocialTintIconTintColor: '#FFFFFF4D',

    errorPopupMsgColor: '#707070',

    ...appData.backupAppThemeColor,
  };
  const changeThemeColor = (type = 'DEFAULT') => {
    let colorTheme = {};
    if (type != 'DEFAULT') {
      let selectedColorObj = ThemeColorList.filter(obj => obj.value == type);
      if (selectedColorObj.length > 0) {
        colorTheme.primaryColor = selectedColorObj[0].color;
        colorTheme.awesomeText = selectedColorObj[0].color;
        colorTheme.homeBottomTabActiveColor = selectedColorObj[0].color;
        colorTheme.postActiveIconColor = selectedColorObj[0].color;
        colorTheme.dropdownActiveColor = selectedColorObj[0].color;
        colorTheme.buttonBorderColor = selectedColorObj[0].color;
        colorTheme.buttonTextColor = selectedColorObj[0].color;
        colorTheme.cancelBtnText = selectedColorObj[0].color;
        colorTheme.submitBtnBackground = selectedColorObj[0].color;
        colorTheme.submitBtnBorder = selectedColorObj[0].color;
        colorTheme.disableSubmitBtnBackground =
          selectedColorObj[0].color + '40';
        colorTheme.pressableRippleColor = selectedColorObj[0].color + '40';
      }
    }
    const mainColorTheme = {...appThemeColor, ...colorTheme};
    appData.backupAppThemeColor = colorTheme;
    setSelectedColorType(type);
    _setAppThemeColor(type);
    setAppThemeColor(prevState => ({
      ...prevState,
      ...mainColorTheme,
    }));
  };
  const changeAppTheme = themeType => {
    if (themeType === 'DARK') {
      setAppThemeType('DARK');
      setAppThemeColor(prevState => ({
        ...prevState,
        ...DarkTheme,
      }));
    } else if (themeType === 'LIGHT') {
      setAppThemeType('LIGHT');
      setAppThemeColor(prevState => ({
        ...prevState,
        ...LightTheme,
      }));
    }
  };
  const getFontFamily = (weight = 'normal') => {
    const selectedFontFamily = fontFamilies.RED_HAT;
    return selectedFontFamily[weight];
  };
  const calculateFontSize = fontValue => {
    // return moderateScaleCustom(fontValue);
    return getFontSize(fontValue, 375);
  };
  const calculateFontSizeNew = fontValue => {
    // console.log("Dimensions.screenWidth", Dimensions.screenWidth);
    // console.log("Before fontValue", fontValue)
    // if (Dimensions.screenWidth < 390) {
    //     if (Dimensions.screenWidth < 380) {
    //         fontValue = fontValue * 0.8;
    //     }
    //     else {
    //         fontValue = fontValue * 0.9;
    //     }
    //     // console.log("After fontValue", fontValue)
    //     return moderateScaleCustom(fontValue);
    // }
    // console.log("moderateScale", moderateScaleCustom(fontValue))
    return getFontSize(fontValue, 375);
  };
  const theme = {
    colors: appThemeColor,
    dimensions: Dimensions,
    appThemeType,
    selectedColorType,
    changeThemeColor,
    changeAppTheme,
    getFontFamily,
    calculateFontSize: calculateFontSize,
    calculateFontSizeNew: calculateFontSizeNew,
    horizontalScale: horizontalScale,
    verticalScale: verticalScale,
  };

  return (
    <ThemeContext.Provider value={theme}>{children}</ThemeContext.Provider>
  );
};

export default SThemeProvider;
