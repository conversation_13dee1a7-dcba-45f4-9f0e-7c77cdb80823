import React from 'react'
import { StyleSheet, View } from 'react-native'
import ContentLoader, { Rect, Circle } from 'react-content-loader/native';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';

const NotiRowPlaceholder = ({ countVal = 6 }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    return (
        <View style={{ ...defaultStyle.container, }}>
            {Array.from(Array(countVal), (e, i) => {
                return <ContentLoader
                    key={i}
                    height={80}
                    speed={theme.dimensions.placeholderSpeed}
                    backgroundColor={theme.colors.placeholderBackgroundColor}
                    foregroundColor={theme.colors.placeholderForegroundColor}
                    viewBox="0 0 380 70">
                    {/* Only SVG shapes */}
                    <Circle cx="30" cy="30" r="30" />
                    <Rect x="80" y="17" rx="4" ry="4" width="300" height="13" />
                    <Rect x="80" y="40" rx="3" ry="3" width="250" height="10" />
                </ContentLoader>
            })}
        </View>
    )
}

export default NotiRowPlaceholder;

const styles = StyleSheet.create({})
