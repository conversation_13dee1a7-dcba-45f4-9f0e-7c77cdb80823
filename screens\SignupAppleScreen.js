import React, { useState, useCallback, useEffect, useContext } from 'react'
import { StyleSheet, View, ScrollView, TouchableOpacity, Linking, Image, Modal } from 'react-native'
import Colors from '../constants/Colors';
import { Checkbox } from 'react-native-paper';
import EntutoTextView from '../components/common/EntutoTextView';
import EntutoEditText from '../components/common/EntutoEditText';
import HeadLineTxt from '../components/common/HeadLineTxt';
import HeadLineDownTxt from '../components/common/HeadLineDownTxt';
import TopNavigationBar from '../components/TopNavigationBar';
import PrimaryButton from '../components/common/PrimaryButton';
import Dimensions from '../constants/Dimensions';
import ServerConnector from '../utils/ServerConnector';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import ErrorMessages from '../constants/ErrorMessages';
import { _getFirstTimeUser, _setFirstTimeUser } from '../utils/AuthLogin';
import { TERMS_AND_COND_URL } from '../utils/Appconfig';
import { AppStateContext } from '..';
import { CommonActions } from '@react-navigation/native';
import CustomStatusBar from '../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import { appleAuth } from '@invertase/react-native-apple-authentication';
import useDefaultStyle from '../theme/useDefaultStyle';

const SignupAppleScreen = ({ route, navigation }) => {
    const [emailId, setemailId] = useState("");
    const [emailIdErr, setemailIdErr] = useState("");
    const [password, setpassword] = useState("");
    const [passwordErr, setpasswordErr] = useState("");
    const [fullName, setfullName] = useState("");
    const [fullNameErr, setfullNameErr] = useState("");
    const [referralCode, setreferralCode] = useState("");
    const [referralCodeErr, setreferralCodeErr] = useState("");
    const [checked, setChecked] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const [refreshKey, setrefreshKey] = useState(Math.random());
    const { changeUserDetails, acceptTerms, changeAcceptTerms } = useContext(AppStateContext)
    const userInfoDD = route.params != undefined ? route.params.userInfo : {};
    const { defaultStyle } = useDefaultStyle();
    useEffect(() => {
        if (userInfoDD.hasOwnProperty("user_email")) {
            let emailT = userInfoDD.user_email;
            let passT = userInfoDD.password_val;
            let fullNameT = userInfoDD.full_name;
            setemailId(emailT);
            setpassword(passT);
            setfullName(fullNameT);

        }
    }, [])


    const termsUrl = TERMS_AND_COND_URL;
    const termsBtnClick = useCallback(async () => {
        // Checking if the link is supported for links with custom URL scheme.
        const supported = await Linking.canOpenURL(termsUrl);

        if (supported) {
            // Opening the link with some app, if the URL scheme is "http" the web link should be opened
            // by some browser in the mobile
            await Linking.openURL(termsUrl);
        } else {
            Alert.alert(`Don't know how to open this URL: ${termsUrl}`);
        }
    }, [termsUrl]);

    function registerBtnClick() {
        if (!acceptTerms) {
            navigation.navigate('TermsAndConditionScreen')
            return
        }
        var isValid = true;
        setemailIdErr("");
        setpasswordErr("");
        setfullNameErr("");
        // if (emailId.length === 0) {
        //     setemailIdErr(ErrorMessages.signupEmailIdErr);
        //     isValid = false;
        // }
        // if (password.length === 0) {
        //     setpasswordErr(ErrorMessages.signupPasswordErr);
        //     isValid = false;
        // }
        if (fullName.length === 0) {
            setfullNameErr(ErrorMessages.signupFullNameErr);
            isValid = false;
        }
        if (isValid) {
            refreshToken();
        }
    }
    function appServiceCall(authorizationCodeV) {
        let hashMap = {
            _action_code: "11:APP_REGISTER",
            full_name: fullName,
            // email: emailId,
            password: authorizationCodeV,
            mode: "APPLE"
        }
        if (referralCode.trim().length !== 0) {
            hashMap.referral_code = referralCode;
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(true);
            seterrorMsg("");
            changeAcceptTerms(false);
            appAppleLoginServiceCall(data.data.key, data.data.email);


        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.full_name) {
                        seterrorMsg(data.data.full_name);
                        setrefreshKey(Math.random());
                        fieldErrorShown = true;
                    }
                    if (data.data.email) {
                        seterrorMsg(data.data.email);
                        setrefreshKey(Math.random());
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        seterrorMsg(data.data.password);
                        setrefreshKey(Math.random());
                        fieldErrorShown = true;
                    }
                    if (data.data.referral_code) {
                        setreferralCodeErr(data.data.referral_code);
                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }
    function appAppleLoginServiceCall(keyVal, userID) {
        let hashMap = {
            _action_code: "11:APPLE_LOGIN",
            key: keyVal,
            user_id: userID,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let userDeatails = {
                _username: data.data.uid,
                _password: data.data.pwd,
                _profile_seq: data.data.profile_seq,
                _user_seq: data.data.user_seq,
                _user_handle: data.data.user_handle,
                _user_account_type: data.data.account_type,
                _user_display_name: "",
                _has_bank_details: "NO",
                _is_profile_verified: "NO",
                _is_gmail_login: "YES",
                _max_file_size: data.data.max_file_size,
            }
            changeUserDetails(userDeatails);
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'HomeScreen',
                        },
                    ],
                })
            );
            // navigation.replace('HomeScreen')
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_id) {
                        seterrorMsg(data.data.user_id);
                        setrefreshKey(Math.random())
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        seterrorMsg(data.data.password);
                        setrefreshKey(Math.random())

                        fieldErrorShown = true;
                    }
                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }
    const loginBtnPress = () => {
        navigation.goBack(null);
    }
    async function refreshToken() {
        if (appleAuth.isSupported) {
            try {
                const appleAuthRequestResponse = await appleAuth.performRequest({
                    requestedOperation: appleAuth.Operation.REFRESH,
                    // Note: it appears pu`tting FULL_NAME f`irst is important, see issue #293
                    requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
                });

                // console.log(appleAuthRequestResponse);

                if (!appleAuthRequestResponse.authorizationCode) {
                    seterrorMsg('Apple Sign-In failed - no authorization code returned');
                    setrefreshKey(Math.random())
                }
                const { authorizationCode } = appleAuthRequestResponse;
                setShowLoading(true);
                appServiceCall(authorizationCode);
            } catch (error) {
                // console.log("Type",error.message)
                if (error.message != "The operation couldn’t be completed. (com.apple.AuthenticationServices.AuthorizationError error 1001.)") {
                    seterrorMsg(error.message);
                    setrefreshKey(Math.random())
                }
            }
        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={{ flex: 1, position: 'relative' }}>
                <LoginSignUpLinearGrad />
                <CustomProgressDialog
                    showLoading={showLoading}
                />
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View>
                        <View style={defaultStyle.signupTextBox}>
                            <EntutoTextView style={defaultStyle.signupText}>Signup to create</EntutoTextView>
                            <EntutoTextView style={defaultStyle.signupText}>new account</EntutoTextView>
                        </View>
                        <View style={defaultStyle.signUpFormBox}>
                            <EntutoEditText
                                showLeftIcon={false}
                                // leftImage={UserIcon}
                                placeholderTxt="Full Name"
                                value={fullName}
                                onChangeText={text => { setfullName(text); setfullNameErr("") }}
                                showErrorField={fullNameErr.length}
                                errorMsg={fullNameErr}
                            />
                            {/* <EntutoEditText
                            showLeftIcon={true}
                            leftImage={MailIcon}
                            placeholderTxt="Email"
                            value={emailId}
                            onChangeText={text => { }}
                            showErrorField={emailIdErr.length}
                            errorMsg={emailIdErr}
                            editable={false}
                        /> */}
                            <EntutoEditText
                                showLeftIcon={false}
                                // leftImage={UserRefIcon}
                                placeholderTxt="Referral Code(optional)"
                                value={referralCode}
                                onChangeText={text => { setreferralCode(text); setreferralCodeErr("") }}
                                showErrorField={referralCodeErr.length}
                                errorMsg={referralCodeErr}
                            />

                        </View>
                        <View style={defaultStyle.signUpBtnBox}>
                            <TouchableOpacity style={{ ...defaultStyle.signUpBtn, backgroundColor: '#E59D80' }}
                                onPress={() => registerBtnClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Register With Apple</EntutoTextView>
                            </TouchableOpacity>
                            <TouchableOpacity style={defaultStyle.signUpBtn}
                                onPress={() => loginBtnPress()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Back</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
            </View>
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                    : null
            }
        </>
    )
}

export default SignupAppleScreen;

const styles = StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: Colors.bodyTextColor
    },
    signupTxtVal: {
        color: Colors.primaryColor,
    },
    termsconBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 5

    },
    termsTxt: {
        borderBottomWidth: 1,
        color: Colors.bodyTextColor
    },
    loginHeadTxt: {
        marginTop: 20,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },

})
