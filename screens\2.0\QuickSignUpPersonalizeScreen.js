import {
  Image,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Alert,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useDefaultStyle from '../../theme/useDefaultStyle';
import {_setAppThemeType} from '../../utils/AuthLogin';
import CustomStatusBar from '../../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../../components/common/LoginSignUpLinearGrad';
import {ScrollView} from 'react-native';
import ModuleAppBar from '../../components/loginModule/ModuleAppBar';
import CustomProgressDialog from '../../components/common/CustomProgressDialog';
import EntutoTextView from '../../components/common/EntutoTextView';
import ModuleHeaderText from '../../components/loginModule/ModuleHeaderText';
import LoginModuleProgress from '../../components/loginModule/LoginModuleProgress';
import LoginModuleTitle from '../../components/loginModule/LoginModuleTitle';
import EntutoEditText from '../../components/common/EntutoEditText';
import PrimaryButton from '../../components/common/PrimaryButton';
import ThemeColorComponent from '../../components/ThemeColorComponent';
import SearchIcon from '../../assets/Images/icon/search_icon.png';
import SelectBoxComponent from '../../components/common/SelectBoxComponent';
import {CommonActions} from '@react-navigation/native';
import ServerConnector from '../../utils/ServerConnector';
import SuccessFailureMsgBox from '../../components/common/SuccessFailureMsgBox';
import OptionSelectionItem from '../../components/common/OptionSelectionItem';
import StyleSelector from '../../components/Login/ThemeToggle';
import ActiveAngryIcon from '../../assets/Images/icon/reactions/Angry_active.png';
import InactiveAngryIcon from '../../assets/Images/icon/reactions/Angry_inactive.png';
import angryLottie from '../../assets/reactions/jsonfile/Awkward.json';
import boredBeforeLottie from '../../assets/reactions/jsonfile/bored before.json';
import happyAfterLottie from '../../assets/reactions/jsonfile/happy after.json';
import heartbrokenBeforeLottie from '../../assets/reactions/jsonfile/heartbroken before.json';
import hmmLottie from '../../assets/reactions/jsonfile/hmm.json';
import loveBeforeLottie from '../../assets/reactions/jsonfile/love before.json';
import nostalgiaLottie from '../../assets/reactions/jsonfile/nostalgia.json';
import scaredAfterLottie from '../../assets/reactions/jsonfile/scared after.json';
import sureLottie from '../../assets/reactions/jsonfile/sure.json';
import wowLottie from '../../assets/reactions/jsonfile/wow.json';
import irritatedLottie from '../../assets/reactions/jsonfile/irritated.json';
import {HARDCODED_EMOTIONS} from '../../data/MetaData';
import Sound from 'react-native-sound';

Sound.setCategory('Playback');

const QuickSignUpPersonalizeScreen = ({navigation}) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {defaultStyle} = useDefaultStyle();
  const [selectedStyle, setSelectedStyle] = useState(
    theme.appThemeType === 'LIGHT' ? 'Light' : 'Dark',
  );
  const [showLoading, setShowLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [errorMsgType, setErrorMsgType] = useState('FAILED');
  const [refreshKey, setRefreshKey] = useState(Math.random());

  // Default topics and genres that will be shown if API requests fail
  const defaultTopics = [
    {label: 'Elephant', value: 'Elephant,'},
    {label: 'Kangaroo', value: 'Kangaroo,'},
    {label: 'Dolphin', value: 'Dolphin,'},
    {label: 'Panda', value: 'Panda,'},
    {label: 'Lion', value: 'Lion,'},
    {label: 'Wolf', value: 'Wolf,'},
    {label: 'Fox', value: 'Fox,'},
    {label: 'Owl', value: 'Owl,'},
    {label: 'Penguin', value: 'Penguin,'},
    {label: 'Oak', value: 'Oak,'},
    {label: 'Cactus', value: 'Cactus,'},
    {label: 'Fern', value: 'Fern,'},
    {label: 'Bamboo', value: 'Bamboo,'},
    {label: 'Maple', value: 'Maple,'},
    {label: 'Pine', value: 'Pine,'},
    {label: 'Tulip', value: 'Tulip,'},
    {label: 'Lavender', value: 'Lavender,'},
    {label: 'Daisy', value: 'Daisy,'},
    {label: 'Pizza', value: 'Pizza,'},
    {label: 'Spaghetti', value: 'Spaghetti,'},
    {label: 'Ice Cream', value: 'Ice Cream,'},
    {label: 'Sushi', value: 'Sushi,'},
    {label: 'Salad', value: 'Salad,'},
    {label: 'Bread', value: 'Bread,'},
    {label: 'Steak', value: 'Steak,'},
    {label: 'Sandwich', value: 'Sandwich,'},
    {label: 'Burger', value: 'Burger,'},
    {label: 'Chocolate', value: 'Chocolate,'},
    {label: 'Hospital', value: 'Hospital,'},
    {label: 'School', value: 'School,'},
    {label: 'Bank', value: 'Bank,'},
    {label: 'Museum', value: 'Museum,'},
    {label: 'Library', value: 'Library,'},
    {label: 'Park', value: 'Park,'},
    {label: 'Airport', value: 'Airport,'},
    {label: 'Stadium', value: 'Stadium,'},
    {label: 'Beach', value: 'Beach,'},
    {label: 'Zoo', value: 'Zoo,'},
    {label: 'Chair', value: 'Chair,'},
    {label: 'Sofa', value: 'Sofa,'},
    {label: 'Lamp', value: 'Lamp,'},
    {label: 'Bed', value: 'Bed,'},
    {label: 'Mirror', value: 'Mirror,'},
    {label: 'Clock', value: 'Clock,'},
    {label: 'Refrigerator', value: 'Refrigerator,'},
    {label: 'Knife', value: 'Knife,'},
    {label: 'Spoon', value: 'Spoon,'},
    {label: 'Cup', value: 'Cup,'},
    {label: 'Car', value: 'Car,'},
    {label: 'Bicycle', value: 'Bicycle,'},
    {label: 'Bus', value: 'Bus,'},
    {label: 'Train', value: 'Train,'},
    {label: 'Motorcycle', value: 'Motorcycle,'},
    {label: 'Truck', value: 'Truck,'},
    {label: 'Airplane', value: 'Airplane,'},
    {label: 'Boat', value: 'Boat,'},
    {label: 'Helicopter', value: 'Helicopter,'},
    {label: 'Scooter', value: 'Scooter,'},
    {label: 'Doctor', value: 'Doctor,'},
    {label: 'Teacher', value: 'Teacher,'},
    {label: 'Engineer', value: 'Engineer,'},
    {label: 'Chef', value: 'Chef,'},
    {label: 'Pilot', value: 'Pilot,'},
    {label: 'Nurse', value: 'Nurse,'},
    {label: 'Police Officer', value: 'Police Officer,'},
    {label: 'Artist', value: 'Artist,'},
    {label: 'Writer', value: 'Writer,'},
    {label: 'Scientist', value: 'Scientist,'},
    {label: 'Football', value: 'Football,'},
    {label: 'Basketball', value: 'Basketball,'},
    {label: 'Tennis', value: 'Tennis,'},
    {label: 'Cricket', value: 'Cricket,'},
    {label: 'Swimming', value: 'Swimming,'},
    {label: 'Baseball', value: 'Baseball,'},
    {label: 'Golf', value: 'Golf,'},
    {label: 'Volleyball', value: 'Volleyball,'},
    {label: 'Hockey', value: 'Hockey,'},
    {label: 'Rugby', value: 'Rugby,'},
    {label: 'Guitar', value: 'Guitar,'},
    {label: 'Piano', value: 'Piano,'},
    {label: 'Drums', value: 'Drums,'},
    {label: 'Violin', value: 'Violin,'},
    {label: 'Saxophone', value: 'Saxophone,'},
    {label: 'Trumpet', value: 'Trumpet,'},
    {label: 'Flute', value: 'Flute,'},
    {label: 'Cello', value: 'Cello,'},
    {label: 'Harp', value: 'Harp,'},
    {label: 'Clarinet', value: 'Clarinet,'},
    {label: 'Hammer', value: 'Hammer,'},
    {label: 'Screwdriver', value: 'Screwdriver,'},
    {label: 'Saw', value: 'Saw,'},
    {label: 'Drill', value: 'Drill,'},
    {label: 'Wrench', value: 'Wrench,'},
    {label: 'Chisel', value: 'Chisel,'},
    {label: 'Pliers', value: 'Pliers,'},
    {label: 'Level', value: 'Level,'},
    {label: 'Tape Measure', value: 'Tape Measure,'},
    {label: 'Spanner', value: 'Spanner,'},
    {label: 'Sofa Bed', value: 'Sofa Bed,'},
    {label: 'Table', value: 'Table,'},
    {label: 'Wardrobe', value: 'Wardrobe,'},
    {label: 'Desk', value: 'Desk,'},
    {label: 'Dresser', value: 'Dresser,'},
    {label: 'Bookshelf', value: 'Bookshelf,'},
    {label: 'Cabinet', value: 'Cabinet,'},
    {label: 'Ottoman', value: 'Ottoman,'},
    {label: 'House', value: 'House,'},
    {label: 'Skyscraper', value: 'Skyscraper,'},
    {label: 'Cottage', value: 'Cottage,'},
    {label: 'Bungalow', value: 'Bungalow,'},
    {label: 'Temple', value: 'Temple,'},
    {label: 'Church', value: 'Church,'},
    {label: 'Castle', value: 'Castle,'},
    {label: 'Bridge', value: 'Bridge,'},
    {label: 'Lighthouse', value: 'Lighthouse,'},
    {label: 'Tower', value: 'Tower,'},
    {label: 'Head', value: 'Head,'},
    {label: 'Arm', value: 'Arm,'},
    {label: 'Leg', value: 'Leg,'},
    {label: 'Hand', value: 'Hand,'},
    {label: 'Foot', value: 'Foot,'},
    {label: 'Eye', value: 'Eye,'},
    {label: 'Nose', value: 'Nose,'},
    {label: 'Mouth', value: 'Mouth,'},
    {label: 'Finger', value: 'Finger,'},
    {label: 'Smartphone', value: 'Smartphone,'},
    {label: 'Laptop', value: 'Laptop,'},
    {label: 'Tablet', value: 'Tablet,'},
    {label: 'Camera', value: 'Camera,'},
    {label: 'TV', value: 'TV,'},
    {label: 'Speaker', value: 'Speaker,'},
    {label: 'Watch', value: 'Watch,'},
    {label: 'Router', value: 'Router,'},
    {label: 'Printer', value: 'Printer,'},
    {label: 'Drone', value: 'Drone,'},
    {label: 'Rain', value: 'Rain,'},
    {label: 'Snow', value: 'Snow,'},
    {label: 'Thunderstorm', value: 'Thunderstorm,'},
    {label: 'Hurricane', value: 'Hurricane,'},
    {label: 'Tornado', value: 'Tornado,'},
    {label: 'Fog', value: 'Fog,'},
    {label: 'Hail', value: 'Hail,'},
    {label: 'Lightning', value: 'Lightning,'},
    {label: 'Rainbow', value: 'Rainbow,'},
    {label: 'Wind', value: 'Wind,'},
    {label: 'Happiness', value: 'Happiness,'},
    {label: 'Sadness', value: 'Sadness,'},
    {label: 'Anger', value: 'Anger,'},
    {label: 'Fear', value: 'Fear,'},
    {label: 'Surprise', value: 'Surprise,'},
    {label: 'Disgust', value: 'Disgust,'},
    {label: 'Pride', value: 'Pride,'},
    {label: 'Love', value: 'Love,'},
    {label: 'Jealousy', value: 'Jealousy,'},
    {label: 'Joy', value: 'Joy,'},
    {label: 'Vase', value: 'Vase,'},
    {label: 'Plate', value: 'Plate,'},
    {label: 'Bowl', value: 'Bowl,'},
    {label: 'Kettle', value: 'Kettle,'},
    {label: 'Mug', value: 'Mug,'},
    {label: 'Fork', value: 'Fork,'},
    {label: 'Spoon', value: 'Spoon,'},
    {label: 'Towel', value: 'Towel,'},
    {label: 'Basket', value: 'Basket,'},
    {label: 'Road', value: 'Road,'},
    {label: 'Bridge', value: 'Bridge,'},
    {label: 'Tunnel', value: 'Tunnel,'},
    {label: 'Highway', value: 'Highway,'},
    {label: 'Crosswalk', value: 'Crosswalk,'},
    {label: 'Traffic Light', value: 'Traffic Light,'},
    {label: 'Bus Stop', value: 'Bus Stop,'},
    {label: 'Roundabout', value: 'Roundabout,'},
    {label: 'Underpass', value: 'Underpass,'},
    {label: 'Painting', value: 'Painting,'},
    {label: 'Sculpture', value: 'Sculpture,'},
    {label: 'Dance', value: 'Dance,'},
    {label: 'Theater', value: 'Theater,'},
    {label: 'Photography', value: 'Photography,'},
    {label: 'Literature', value: 'Literature,'},
    {label: 'Cinema', value: 'Cinema,'},
    {label: 'Music', value: 'Music,'},
    {label: 'Architecture', value: 'Architecture,'},
    {label: 'Pottery', value: 'Pottery,'},
    {label: 'Sun', value: 'Sun,'},
    {label: 'Moon', value: 'Moon,'},
    {label: 'Stars', value: 'Stars,'},
    {label: 'Mountain', value: 'Mountain,'},
    {label: 'River', value: 'River,'},
    {label: 'Ocean', value: 'Ocean,'},
    {label: 'Desert', value: 'Desert,'},
    {label: 'Volcano', value: 'Volcano,'},
    {label: 'Glacier', value: 'Glacier,'},
    {label: 'Sparrow', value: 'Sparrow,'},
    {label: 'Crow', value: 'Crow,'},
    {label: 'Pigeon', value: 'Pigeon,'},
    {label: 'Peacock', value: 'Peacock,'},
    {label: 'Parrot', value: 'Parrot,'},
    {label: 'Robin', value: 'Robin,'},
    {label: 'Swan', value: 'Swan,'},
    {label: 'Falcon', value: 'Falcon,'},
    {label: 'Owl', value: 'Owl,'},
    {label: 'Seagull', value: 'Seagull,'},
    {label: 'Heron', value: 'Heron,'},
    {label: 'Woodpecker', value: 'Woodpecker,'},
    {label: 'Kingfisher', value: 'Kingfisher,'},
    {label: 'Flamingo', value: 'Flamingo,'},
    {label: 'Vulture', value: 'Vulture,'},
    {label: 'Stork', value: 'Stork,'},
    {label: 'Toucan', value: 'Toucan,'},
    {label: 'Pelican', value: 'Pelican,'},
    {label: 'Ant', value: 'Ant,'},
    {label: 'Bee', value: 'Bee,'},
    {label: 'Butterfly', value: 'Butterfly,'},
    {label: 'Dragonfly', value: 'Dragonfly,'},
    {label: 'Mosquito', value: 'Mosquito,'},
    {label: 'Fly', value: 'Fly,'},
    {label: 'Grasshopper', value: 'Grasshopper,'},
    {label: 'Moth', value: 'Moth,'},
    {label: 'Wasp', value: 'Wasp,'},
    {label: 'Ladybug', value: 'Ladybug,'},
    {label: 'Locust', value: 'Locust,'},
    {label: 'Firefly', value: 'Firefly,'},
    {label: 'Caterpillar', value: 'Caterpillar,'},
    {label: 'Damselfly', value: 'Damselfly,'},
    {label: 'Praying Mantis', value: 'Praying Mantis,'},
    {label: 'Earwig', value: 'Earwig,'},
    {label: 'Termite', value: 'Termite,'},
    {label: 'Cicada', value: 'Cicada,'},
    {label: 'Dolphin', value: 'Dolphin,'},
    {label: 'Whale', value: 'Whale,'},
    {label: 'Shark', value: 'Shark,'},
    {label: 'Octopus', value: 'Octopus,'},
    {label: 'Jellyfish', value: 'Jellyfish,'},
    {label: 'Crab', value: 'Crab,'},
    {label: 'Lobster', value: 'Lobster,'},
    {label: 'Shrimp', value: 'Shrimp,'},
    {label: 'Seahorse', value: 'Seahorse,'},
    {label: 'Tuna', value: 'Tuna,'},
    {label: 'Cod', value: 'Cod,'},
    {label: 'Salmon', value: 'Salmon,'},
    {label: 'Mackerel', value: 'Mackerel,'},
    {label: 'Swordfish', value: 'Swordfish,'},
    {label: 'Eel', value: 'Eel,'},
    {label: 'Clam', value: 'Clam,'},
    {label: 'Oyster', value: 'Oyster,'},
    {label: 'Mussel', value: 'Mussel,'},
    {label: 'Snake', value: 'Snake,'},
    {label: 'Lizard', value: 'Lizard,'},
    {label: 'Crocodile', value: 'Crocodile,'},
    {label: 'Alligator', value: 'Alligator,'},
    {label: 'Chameleon', value: 'Chameleon,'},
    {label: 'Gecko', value: 'Gecko,'},
    {label: 'Iguana', value: 'Iguana,'},
    {label: 'Turtle', value: 'Turtle,'},
    {label: 'Chameleon', value: 'Chameleon,'},
    {label: 'Skink', value: 'Skink,'},
    {label: 'Monitor Lizard', value: 'Monitor Lizard,'},
    {label: 'Viper', value: 'Viper,'},
    {label: 'Boa', value: 'Boa,'},
    {label: 'Python', value: 'Python,'},
    {label: 'Garter Snake', value: 'Garter Snake,'},
    {label: 'Cobra', value: 'Cobra,'},
    {label: 'Rattlesnake', value: 'Rattlesnake,'},
    {label: 'Anaconda', value: 'Anaconda,'},
    {label: 'Komodo Dragon', value: 'Komodo Dragon,'},
    {label: 'Rose', value: 'Rose,'},
    {label: 'Lily', value: 'Lily,'},
    {label: 'Tulip', value: 'Tulip,'},
    {label: 'Sunflower', value: 'Sunflower,'},
    {label: 'Orchid', value: 'Orchid,'},
    {label: 'Daffodil', value: 'Daffodil,'},
    {label: 'Marigold', value: 'Marigold,'},
    {label: 'Peony', value: 'Peony,'},
    {label: 'Chrysanthemum', value: 'Chrysanthemum,'},
    {label: 'Hibiscus', value: 'Hibiscus,'},
    {label: 'Lavender', value: 'Lavender,'},
    {label: 'Iris', value: 'Iris,'},
    {label: 'Poppy', value: 'Poppy,'},
    {label: 'Zinnia', value: 'Zinnia,'},
    {label: 'Begonia', value: 'Begonia,'},
    {label: 'Geranium', value: 'Geranium,'},
    {label: 'Snapdragon', value: 'Snapdragon,'},
    {label: 'Jasmine', value: 'Jasmine,'},
    {label: 'Camellia', value: 'Camellia,'},
    {label: 'Kimono', value: 'Kimono,'},
    {label: 'Rice', value: 'Rice,'},
    {label: 'Wheat', value: 'Wheat,'},
    {label: 'Corn', value: 'Corn,'},
    {label: 'Barley', value: 'Barley,'},
    {label: 'Oats', value: 'Oats,'},
    {label: 'Rye', value: 'Rye,'},
    {label: 'Sorghum', value: 'Sorghum,'},
    {label: 'Millet', value: 'Millet,'},
    {label: 'Quinoa', value: 'Quinoa,'},
    {label: 'Spelt', value: 'Spelt,'},
    {label: 'Triticale', value: 'Triticale,'},
    {label: 'Buckwheat', value: 'Buckwheat,'},
    {label: 'Amaranth', value: 'Amaranth,'},
    {label: 'Teff', value: 'Teff,'},
    {label: 'Fonio', value: 'Fonio,'},
    {label: 'Einkorn', value: 'Einkorn,'},
    {label: 'Emmer', value: 'Emmer,'},
    {label: 'Farro', value: 'Farro,'},
    {label: 'Kamut', value: 'Kamut,'},
    {label: "Job's Tears", value: "Job's Tears,"},
    {label: 'Milk', value: 'Milk,'},
    {label: 'Cheese', value: 'Cheese,'},
    {label: 'Yogurt', value: 'Yogurt,'},
    {label: 'Butter', value: 'Butter,'},
    {label: 'Cream', value: 'Cream,'},
    {label: 'Ice Cream', value: 'Ice Cream,'},
    {label: 'Whey', value: 'Whey,'},
    {label: 'Ghee', value: 'Ghee,'},
    {label: 'Cottage Cheese', value: 'Cottage Cheese,'},
    {label: 'Sour Cream', value: 'Sour Cream,'},
    {label: 'Buttermilk', value: 'Buttermilk,'},
    {label: 'Clotted Cream', value: 'Clotted Cream,'},
    {label: 'Skim', value: 'Skim,'},
    {label: 'Curd', value: 'Curd,'},
    {label: 'Ricotta', value: 'Ricotta,'},
    {label: 'Mascarpone', value: 'Mascarpone,'},
    {label: 'Paneer', value: 'Paneer,'},
    {label: 'Feta', value: 'Feta,'},
    {label: 'Mozzarella', value: 'Mozzarella,'},
    {label: 'Coffee', value: 'Coffee,'},
    {label: 'Tea', value: 'Tea,'},
    {label: 'Juice', value: 'Juice,'},
    {label: 'Water', value: 'Water,'},
    {label: 'Soda', value: 'Soda,'},
    {label: 'Smoothie', value: 'Smoothie,'},
    {label: 'Milkshake', value: 'Milkshake,'},
    {label: 'Beer', value: 'Beer,'},
    {label: 'Wine', value: 'Wine,'},
    {label: 'Whiskey', value: 'Whiskey,'},
    {label: 'Vodka', value: 'Vodka,'},
    {label: 'Rum', value: 'Rum,'},
    {label: 'Brandy', value: 'Brandy,'},
    {label: 'Liqueur', value: 'Liqueur,'},
    {label: 'Champagne', value: 'Champagne,'},
    {label: 'Cider', value: 'Cider,'},
    {label: 'Tonic Water', value: 'Tonic Water,'},
    {label: 'Lemonade', value: 'Lemonade,'},
    {label: 'Kombucha', value: 'Kombucha,'},
    {label: 'Cake', value: 'Cake,'},
    {label: 'Pie', value: 'Pie,'},
    {label: 'Pudding', value: 'Pudding,'},
    {label: 'Brownie', value: 'Brownie,'},
    {label: 'Cupcake', value: 'Cupcake,'},
    {label: 'Cookie', value: 'Cookie,'},
    {label: 'Muffin', value: 'Muffin,'},
    {label: 'Doughnut', value: 'Doughnut,'},
    {label: 'Tart', value: 'Tart,'},
    {label: 'Cheesecake', value: 'Cheesecake,'},
    {label: 'Macaron', value: 'Macaron,'},
    {label: 'Eclair', value: 'Eclair,'},
    {label: 'Cannoli', value: 'Cannoli,'},
    {label: 'Creme Brulee', value: 'Creme Brulee,'},
    {label: 'Souffle', value: 'Souffle,'},
    {label: 'Gelato', value: 'Gelato,'},
    {label: 'Sorbet', value: 'Sorbet,'},
    {label: 'Parfait', value: 'Parfait,'},
    {label: 'Mousse', value: 'Mousse,'},
    {label: 'Biscotti', value: 'Biscotti,'},
    {label: 'Rock', value: 'Rock,'},
    {label: 'Pop', value: 'Pop,'},
    {label: 'Jazz', value: 'Jazz,'},
    {label: 'Classical', value: 'Classical,'},
    {label: 'Hip-Hop', value: 'Hip-Hop,'},
    {label: 'Blues', value: 'Blues,'},
    {label: 'Reggae', value: 'Reggae,'},
    {label: 'Country', value: 'Country,'},
    {label: 'Electronic', value: 'Electronic,'},
    {label: 'Folk', value: 'Folk,'},
    {label: 'Metal', value: 'Metal,'},
    {label: 'Punk', value: 'Punk,'},
    {label: 'Soul', value: 'Soul,'},
    {label: 'R&B', value: 'R&B,'},
    {label: 'Funk', value: 'Funk,'},
    {label: 'Disco', value: 'Disco,'},
    {label: 'Gospel', value: 'Gospel,'},
    {label: 'Opera', value: 'Opera,'},
    {label: 'K-Pop', value: 'K-Pop,'},
    {label: 'EDM', value: 'EDM,'},
    {label: 'Watercolor', value: 'Watercolor,'},
    {label: 'Oil Painting', value: 'Oil Painting,'},
    {label: 'Acrylic', value: 'Acrylic,'},
    {label: 'Ink Drawing', value: 'Ink Drawing,'},
    {label: 'Charcoal', value: 'Charcoal,'},
    {label: 'Pastel', value: 'Pastel,'},
    {label: 'Collage', value: 'Collage,'},
    {label: 'Digital Art', value: 'Digital Art,'},
    {label: 'Fresco', value: 'Fresco,'},
    {label: 'Printmaking', value: 'Printmaking,'},
    {label: 'Etching', value: 'Etching,'},
    {label: 'Lithography', value: 'Lithography,'},
    {label: 'Sculpture', value: 'Sculpture,'},
    {label: 'Ceramics', value: 'Ceramics,'},
    {label: 'Mosaics', value: 'Mosaics,'},
    {label: 'Encaustic', value: 'Encaustic,'},
    {label: 'Graffiti', value: 'Graffiti,'},
    {label: 'Stained Glass', value: 'Stained Glass,'},
    {label: 'Textile Art', value: 'Textile Art,'},
    {label: 'Calligraphy', value: 'Calligraphy,'},
    {label: 'Apartment', value: 'Apartment,'},
    {label: 'Villa', value: 'Villa,'},
    {label: 'Bungalow', value: 'Bungalow,'},
    {label: 'Skyscraper', value: 'Skyscraper,'},
    {label: 'Cottage', value: 'Cottage,'},
    {label: 'Mansion', value: 'Mansion,'},
    {label: 'Duplex', value: 'Duplex,'},
    {label: 'Townhouse', value: 'Townhouse,'},
    {label: 'Warehouse', value: 'Warehouse,'},
    {label: 'Barn', value: 'Barn,'},
    {label: 'Farmhouse', value: 'Farmhouse,'},
    {label: 'Castle', value: 'Castle,'},
    {label: 'Fortress', value: 'Fortress,'},
    {label: 'Hotel', value: 'Hotel,'},
    {label: 'Hostel', value: 'Hostel,'},
    {label: 'Dormitory', value: 'Dormitory,'},
    {label: 'Chalet', value: 'Chalet,'},
    {label: 'Palace', value: 'Palace,'},
    {label: 'Cathedral', value: 'Cathedral,'},
    {label: 'Temple', value: 'Temple,'},
    {label: 'Hat', value: 'Hat,'},
    {label: 'Scarf', value: 'Scarf,'},
    {label: 'Gloves', value: 'Gloves,'},
    {label: 'Belt', value: 'Belt,'},
    {label: 'Necklace', value: 'Necklace,'},
    {label: 'Bracelet', value: 'Bracelet,'},
    {label: 'Earrings', value: 'Earrings,'},
    {label: 'Ring', value: 'Ring,'},
    {label: 'Watch', value: 'Watch,'},
    {label: 'Sunglasses', value: 'Sunglasses,'},
    {label: 'Hairband', value: 'Hairband,'},
    {label: 'Brooch', value: 'Brooch,'},
    {label: 'Cufflinks', value: 'Cufflinks,'},
    {label: 'Tie', value: 'Tie,'},
    {label: 'Anklet', value: 'Anklet,'},
    {label: 'Headscarf', value: 'Headscarf,'},
    {label: 'Shawl', value: 'Shawl,'},
    {label: 'Pocket Square', value: 'Pocket Square,'},
    {label: 'Pin', value: 'Pin,'},
    {label: 'Corsage', value: 'Corsage,'},
    {label: 'Hiking', value: 'Hiking,'},
    {label: 'Camping', value: 'Camping,'},
    {label: 'Fishing', value: 'Fishing,'},
    {label: 'Hunting', value: 'Hunting,'},
    {label: 'Rock Climbing', value: 'Rock Climbing,'},
    {label: 'Surfing', value: 'Surfing,'},
    {label: 'Kayaking', value: 'Kayaking,'},
    {label: 'Canoeing', value: 'Canoeing,'},
    {label: 'Horseback Riding', value: 'Horseback Riding,'},
    {label: 'Archery', value: 'Archery,'},
    {label: 'Cycling', value: 'Cycling,'},
    {label: 'Running', value: 'Running,'},
    {label: 'Skateboarding', value: 'Skateboarding,'},
    {label: 'Rollerblading', value: 'Rollerblading,'},
    {label: 'Snowboarding', value: 'Snowboarding,'},
    {label: 'Skiing', value: 'Skiing,'},
    {label: 'Ice Skating', value: 'Ice Skating,'},
    {label: 'Bungee Jumping', value: 'Bungee Jumping,'},
    {label: 'Paragliding', value: 'Paragliding,'},
    {label: 'Zip Lining', value: 'Zip Lining,'},
    {label: 'Screwdriver', value: 'Screwdriver,'},
    {label: 'Pliers', value: 'Pliers,'},
    {label: 'Hammer', value: 'Hammer,'},
    {label: 'Wrench', value: 'Wrench,'},
    {label: 'Chisel', value: 'Chisel,'},
    {label: 'File', value: 'File,'},
    {label: 'Rasp', value: 'Rasp,'},
    {label: 'Awl', value: 'Awl,'},
    {label: 'Crowbar', value: 'Crowbar,'},
    {label: 'Mallet', value: 'Mallet,'},
    {label: 'Handsaw', value: 'Handsaw,'},
    {label: 'Plane', value: 'Plane,'},
    {label: 'Level', value: 'Level,'},
    {label: 'Square', value: 'Square,'},
    {label: 'Sledgehammer', value: 'Sledgehammer,'},
    {label: 'Hacksaw', value: 'Hacksaw,'},
    {label: 'Allen Key', value: 'Allen Key,'},
    {label: 'Utility Knife', value: 'Utility Knife,'},
    {label: 'Vice', value: 'Vice,'},
    {label: 'Clamp', value: 'Clamp,'},
    {label: 'Frying Pan', value: 'Frying Pan,'},
    {label: 'Saucepan', value: 'Saucepan,'},
    {label: 'Wok', value: 'Wok,'},
    {label: 'Blender', value: 'Blender,'},
    {label: 'Whisk', value: 'Whisk,'},
    {label: 'Peeler', value: 'Peeler,'},
    {label: 'Grater', value: 'Grater,'},
    {label: 'Mixing Bowl', value: 'Mixing Bowl,'},
    {label: 'Tongs', value: 'Tongs,'},
    {label: 'Ladle', value: 'Ladle,'},
    {label: 'Spatula', value: 'Spatula,'},
    {label: 'Rolling Pin', value: 'Rolling Pin,'},
    {label: 'Mortar and Pestle', value: 'Mortar and Pestle,'},
    {label: 'Cutting Board', value: 'Cutting Board,'},
    {label: 'Measuring Cup', value: 'Measuring Cup,'},
    {label: 'Strainer', value: 'Strainer,'},
    {label: 'Salad Spinner', value: 'Salad Spinner,'},
    {label: 'Hoe', value: 'Hoe,'},
    {label: 'Rake', value: 'Rake,'},
    {label: 'Spade', value: 'Spade,'},
    {label: 'Trowel', value: 'Trowel,'},
    {label: 'Pruning Shears', value: 'Pruning Shears,'},
    {label: 'Watering Can', value: 'Watering Can,'},
    {label: 'Garden Fork', value: 'Garden Fork,'},
    {label: 'Wheelbarrow', value: 'Wheelbarrow,'},
    {label: 'Lawn Mower', value: 'Lawn Mower,'},
    {label: 'Sprinkler', value: 'Sprinkler,'},
    {label: 'Edger', value: 'Edger,'},
    {label: 'Cultivator', value: 'Cultivator,'},
    {label: 'Dibber', value: 'Dibber,'},
    {label: 'Seeder', value: 'Seeder,'},
    {label: 'Leaf Blower', value: 'Leaf Blower,'},
    {label: 'Compost Bin', value: 'Compost Bin,'},
    {label: 'Garden Hose', value: 'Garden Hose,'},
    {label: 'Shovel', value: 'Shovel,'},
    {label: 'Weeder', value: 'Weeder,'},
    {label: 'Garden Knife', value: 'Garden Knife,'},
    {label: 'Pen', value: 'Pen,'},
    {label: 'Pencil', value: 'Pencil,'},
    {label: 'Marker', value: 'Marker,'},
    {label: 'Highlighter', value: 'Highlighter,'},
    {label: 'Fountain Pen', value: 'Fountain Pen,'},
    {label: 'Mechanical Pencil', value: 'Mechanical Pencil,'},
    {label: 'Stylus', value: 'Stylus,'},
    {label: 'Crayon', value: 'Crayon,'},
    {label: 'Chalk', value: 'Chalk,'},
    {label: 'Quill', value: 'Quill,'},
    {label: 'Ink', value: 'Ink,'},
    {label: 'Whiteboard Marker', value: 'Whiteboard Marker,'},
    {label: 'Permanent Marker', value: 'Permanent Marker,'},
    {label: 'Calligraphy Pen', value: 'Calligraphy Pen,'},
    {label: 'Gel Pen', value: 'Gel Pen,'},
    {label: 'Paint Pen', value: 'Paint Pen,'},
    {label: 'Brush Pen', value: 'Brush Pen,'},
    {label: 'Ballpoint Pen', value: 'Ballpoint Pen,'},
    {label: 'Fineliner', value: 'Fineliner,'},
    {label: 'Rollerball Pen', value: 'Rollerball Pen,'},
    {label: 'Microwave', value: 'Microwave,'},
    {label: 'Blender', value: 'Blender,'},
    {label: 'Toaster', value: 'Toaster,'},
    {label: 'Coffee Maker', value: 'Coffee Maker,'},
    {label: 'Dishwasher', value: 'Dishwasher,'},
    {label: 'Refrigerator', value: 'Refrigerator,'},
    {label: 'Washing Machine', value: 'Washing Machine,'},
    {label: 'Dryer', value: 'Dryer,'},
    {label: 'Iron', value: 'Iron,'},
    {label: 'Vacuum Cleaner', value: 'Vacuum Cleaner,'},
    {label: 'Air Conditioner', value: 'Air Conditioner,'},
    {label: 'Heater', value: 'Heater,'},
    {label: 'Oven', value: 'Oven,'},
    {label: 'Electric Kettle', value: 'Electric Kettle,'},
    {label: 'Rice Cooker', value: 'Rice Cooker,'},
    {label: 'Slow Cooker', value: 'Slow Cooker,'},
    {label: 'Food Processor', value: 'Food Processor,'},
    {label: 'Ice Maker', value: 'Ice Maker,'},
    {label: 'Dehumidifier', value: 'Dehumidifier,'},
    {label: 'Electric Grill', value: 'Electric Grill,'},
    {label: 'Diamond', value: 'Diamond,'},
    {label: 'Ruby', value: 'Ruby,'},
    {label: 'Emerald', value: 'Emerald,'},
    {label: 'Amethyst', value: 'Amethyst,'},
    {label: 'Topaz', value: 'Topaz,'},
    {label: 'Opal', value: 'Opal,'},
    {label: 'Garnet', value: 'Garnet,'},
    {label: 'Jade', value: 'Jade,'},
    {label: 'Quartz', value: 'Quartz,'},
    {label: 'Turquoise', value: 'Turquoise,'},
    {label: 'Onyx', value: 'Onyx,'},
    {label: 'Agate', value: 'Agate,'},
    {label: 'Tourmaline', value: 'Tourmaline,'},
    {label: 'Citrine', value: 'Citrine,'},
    {label: 'Peridot', value: 'Peridot,'},
    {label: 'Spinel', value: 'Spinel,'},
    {label: 'Lapis Lazuli', value: 'Lapis Lazuli,'},
    {label: 'Moonstone', value: 'Moonstone,'},
  ];
  const defaultGenres = [
    {label: 'Martial Arts', value: 'Martial Arts,'},
    {label: 'Superhero', value: 'Superhero,'},
    {label: 'Heist', value: 'Heist,'},
    {label: 'Spy/Espionage', value: 'Spy/Espionage,'},
    {label: 'Disaster', value: 'Disaster,'},
    {label: 'Survival', value: 'Survival,'},
    {label: 'Quest', value: 'Quest,'},
    {label: 'Swashbuckler', value: 'Swashbuckler,'},
    {label: 'Road Movie', value: 'Road Movie,'},
    {label: 'Jungle Adventure', value: 'Jungle Adventure,'},
    {label: 'Epic Adventure', value: 'Epic Adventure,'},
    {label: 'Satire', value: 'Satire,'},
    {label: 'Slapstick', value: 'Slapstick,'},
    {label: 'Dark Comedy', value: 'Dark Comedy,'},
    {label: 'Romantic Comedy', value: 'Romantic Comedy,'},
    {label: 'Parody', value: 'Parody,'},
    {label: 'Sketch', value: 'Sketch,'},
    {label: 'Noir', value: 'Noir,'},
    {label: 'Police Procedural', value: 'Police Procedural,'},
    {label: 'Gangster', value: 'Gangster,'},
    {label: 'Legal', value: 'Legal,'},
    {label: 'True Crime', value: 'True Crime,'},
    {label: 'Melodrama', value: 'Melodrama,'},
    {label: 'Political', value: 'Political,'},
    {label: 'Coming-of-Age', value: 'Coming-of-Age,'},
    {label: 'Period Piece', value: 'Period Piece,'},
    {label: 'Family Drama', value: 'Family Drama,'},
    {label: 'High Fantasy', value: 'High Fantasy,'},
    {label: 'Dark Fantasy', value: 'Dark Fantasy,'},
    {label: 'Urban Fantasy', value: 'Urban Fantasy,'},
    {label: 'Sword & Sorcery', value: 'Sword & Sorcery,'},
    {label: 'Mythical', value: 'Mythical,'},
    {label: 'Period Piece', value: 'Period Piece,'},
    {label: 'War Drama', value: 'War Drama,'},
    {label: 'Biographical Drama', value: 'Biographical Drama,'},
    {label: 'Epic Historical', value: 'Epic Historical,'},
    {label: 'Costume Drama', value: 'Costume Drama,'},
    {label: 'Slasher', value: 'Slasher,'},
    {label: 'Monster', value: 'Monster,'},
    {label: 'Gothic', value: 'Gothic,'},
    {label: 'Paranormal', value: 'Paranormal,'},
    {label: 'Psychological Horror', value: 'Psychological Horror,'},
    {label: 'Body Horror', value: 'Body Horror,'},
    {label: 'Jukebox Musical', value: 'Jukebox Musical,'},
    {label: 'Rock Musical', value: 'Rock Musical,'},
    {label: 'Dance Musical', value: 'Dance Musical,'},
    {label: 'Disney Musical', value: 'Disney Musical,'},
    {label: 'Stage Adaptation', value: 'Stage Adaptation,'},
    {label: 'Detective', value: 'Detective,'},
    {label: 'Whodunit', value: 'Whodunit,'},
    {label: 'Hard-Boiled', value: 'Hard-Boiled,'},
    {label: 'Cozy Mystery', value: 'Cozy Mystery,'},
    {label: 'Conspiracy', value: 'Conspiracy,'},
    {label: 'Contemporary', value: 'Contemporary,'},
    {label: 'Historical', value: 'Historical,'},
    {label: 'Paranormal', value: 'Paranormal,'},
    {label: 'Romantic Suspense', value: 'Romantic Suspense,'},
    {label: 'LGBTQ+', value: 'LGBTQ+,'},
    {label: 'Erotic', value: 'Erotic,'},
    {label: 'Cyberpunk', value: 'Cyberpunk,'},
    {label: 'Space Opera', value: 'Space Opera,'},
    {label: 'Time Travel', value: 'Time Travel,'},
    {label: 'Dystopian', value: 'Dystopian,'},
    {label: 'Post-Apocalyptic', value: 'Post-Apocalyptic,'},
    {label: 'Alien Invasion', value: 'Alien Invasion,'},
    {label: 'Psychological', value: 'Psychological,'},
    {label: 'Political', value: 'Political,'},
    {label: 'Legal', value: 'Legal,'},
    {label: 'Techno-thriller', value: 'Techno-thriller,'},
    {label: 'Conspiracy', value: 'Conspiracy,'},
    {label: 'Supernatural Thriller', value: 'Supernatural Thriller,'},
    {label: 'Combat', value: 'Combat,'},
    {label: 'POW', value: 'POW,'},
    {label: 'Military History', value: 'Military History,'},
    {label: 'Espionage', value: 'Espionage,'},
    {label: 'Anti-war', value: 'Anti-war,'},
    {label: 'Historical War', value: 'Historical War,'},
    {label: 'Classic Western', value: 'Classic Western,'},
    {label: 'Revisionist Western', value: 'Revisionist Western,'},
    {label: 'Spaghetti Western', value: 'Spaghetti Western,'},
    {label: 'Outlaw Western', value: 'Outlaw Western,'},
    {label: 'Anime', value: 'Anime,'},
    {label: 'Stop-motion', value: 'Stop-motion,'},
    {label: '2D Animation', value: '2D Animation,'},
    {label: '3D Animation', value: '3D Animation,'},
    {label: 'Claymation', value: 'Claymation,'},
    {label: 'Adult Animation', value: 'Adult Animation,'},
    {label: 'Biographical', value: 'Biographical,'},
    {label: 'Investigative', value: 'Investigative,'},
    {label: 'Nature', value: 'Nature,'},
    {label: 'Science', value: 'Science,'},
    {label: 'Political', value: 'Political,'},
    {label: 'Social Issue', value: 'Social Issue,'},
    {label: 'Avant-garde', value: 'Avant-garde,'},
    {label: 'Surreal', value: 'Surreal,'},
    {label: 'Non-linear Narrative', value: 'Non-linear Narrative,'},
    {label: 'Art House', value: 'Art House,'},
    {label: 'Meta-film', value: 'Meta-film,'},
    {label: 'Abstract', value: 'Abstract,'},
    {label: 'Coming-of-Age', value: 'Coming-of-Age,'},
    {label: 'Animal Stories', value: 'Animal Stories,'},
    {label: 'Animated Family', value: 'Animated Family,'},
    {label: 'Adventure Family', value: 'Adventure Family,'},
    {label: 'Educational Family', value: 'Educational Family,'},
    {label: 'Sports Drama', value: 'Sports Drama,'},
    {label: 'Athlete Biopic', value: 'Athlete Biopic,'},
    {label: 'Documentary', value: 'Documentary,'},
    {label: 'Live Sports', value: 'Live Sports,'},
    {label: 'Sports Commentary', value: 'Sports Commentary,'},
    {label: 'Musician Biopic', value: 'Musician Biopic,'},
    {label: 'Historical Figure Biopic', value: 'Historical Figure Biopic,'},
    {label: 'Political Figure Biopic', value: 'Political Figure Biopic,'},
    {label: 'Sports Figure Biopic', value: 'Sports Figure Biopic,'},
  ];
  // Topic state variables
  const [topicList, setTopicList] = useState(defaultTopics);
  const [selectedInterests, setSelectedInterests] = useState([]);
  const [displayTopicList, setDisplayTopicList] = useState(defaultTopics);

  // Genre state variables
  const [genreList, setGenreList] = useState(defaultGenres);
  const [selectedGenres, setSelectedGenres] = useState([]);
  const [displayGenreList, setDisplayGenreList] = useState(defaultGenres);
  const [backupGenreList, setBackupGenreList] = useState([]);
  //Emotions
  const [selectedEmotions, setSelectedEmotions] = useState([]);
  const [displayEmotionList, setDisplayEmotionList] = useState([]);

  const [selectedUIColor, setSelectedUIColor] = useState('COLOR_1');

  // Modal state variables
  const [openInterestList, setOpenInterestList] = useState(false);
  const [openGenreList, setOpenGenreList] = useState(false);
  const [openEmotionList, setOpenEmotionList] = useState(false);

  useEffect(() => {
    // setShowLoading(true);
    // getGenreListService();
    setDisplayEmotionList(HARDCODED_EMOTIONS);
  }, []);

  const getInterestListService = () => {
    let hashMap = {
      _action_code: '11:GET_INTEREST_CATEGORIES', //genres
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        let tempList = [];
        data.data.map(item => {
          tempList.push({
            label: item.category,
            value: item.category,
          });
        });
        setTopicList(tempList);
        getUserInterestListService(tempList, data.random);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        createUpdatedList(
          [],
          defaultTopics,
          defaultTopics.map(item => item.value),
        );
      },
    );
  };

  const getGenreListService = () => {
    let hashMap = {
      _action_code: '11:GET_GENRE_CATEGORIES', //genres
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        let tempList = [];
        data.data.map(item => {
          tempList.push({
            label: item.category,
            value: item.category,
          });
        });
        setGenreList(tempList);
        // Modified to directly use createUpdatedGenreList instead of calling getUserGenreListService
        createUpdatedGenreList(
          [],
          tempList,
          tempList.map(item => item.value),
        );
      },
      (errorCode, errorMessage, data) => {
        // failure method
        createUpdatedGenreList(
          [],
          defaultGenres,
          defaultGenres.map(item => item.value),
        );
      },
    );
  };

  const getUserInterestListService = (tempList, randomList) => {
    let hashMap = {
      _action_code: '11:GET_USER_INTERESTS',
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        let selectedInterestList = [];
        data.data.map(item => {
          selectedInterestList.push(item.interest_name);
        });
        setSelectedInterests(selectedInterestList);
        createUpdatedList(selectedInterestList, tempList, randomList);
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        createUpdatedList([], tempList, randomList);
      },
    );
  };

  // Removed getUserGenreListService function

  const letsGetStartedBtnClick = () => {
    if (formValid()) {
      setShowLoading(true);
      updateUserInterestService();
    }
  };
  const searchEmotionBtnPress = () => {
    setOpenEmotionList(true);
  };

  const formValid = () => {
    let isValid = true;
    if (selectedInterests.length < 9) {
      setErrorMsg(
        'Please select minimum of 9 interesting topics, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    } else if (selectedGenres.length < 9) {
      setErrorMsg(
        'Please select minimum of 9 genres, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    } else if (selectedEmotions.length < 9) {
      setErrorMsg(
        'Please select minimum of 9 emotions, for the best experience.',
      );
      setRefreshKey(Math.random());
      isValid = false;
    }
    return isValid;
  };

  const themeColorChange = themeValue => {
    setSelectedUIColor(themeValue);
  };

  const handleStyleChange = style => {
    setSelectedStyle(style);
    // Convert the style value to the format expected by the theme system
    const themeType = style === 'Light' ? 'LIGHT' : 'DARK';
    // Update the global theme state
    theme.changeAppTheme(themeType);
    // Persist the theme change in AsyncStorage
    _setAppThemeType(themeType);
  };

  const selectInterestBoxClick = (clickID, obj) => {
    setOpenInterestList(false);
    if (clickID == 'DONE') {
      setSelectedInterests([...[], ...obj.selectedItem]);
      createUpdatedList(
        obj.selectedItem,
        topicList,
        topicList.map(item => item.value),
      );
    }
  };

  const selectGenreBoxClick = (clickID, obj) => {
    setOpenGenreList(false);
    if (clickID == 'DONE') {
      setSelectedGenres([...[], ...obj.selectedItem]);
      createUpdatedGenreList(
        obj.selectedItem,
        genreList,
        genreList.map(item => item.value),
      );
    }
  };

  const createUpdatedList = (selectedItems, tempList, tempBackUplList) => {
    let tempTopicList = JSON.parse(JSON.stringify(tempList));
    let list = [];

    // First add all selected items
    tempTopicList.forEach((item, index) => {
      if (selectedItems.includes(item.value)) {
        item.isChecked = true;
        list.push(item);
      }
    });

    // Then add non-selected items until we reach max 9
    if (list.length < 9) {
      tempTopicList.forEach(item => {
        if (!selectedItems.includes(item.value) && list.length < 9) {
          item.isChecked = false;
          list.push(item);
        }
      });
    }

    setDisplayTopicList(list);
  };

  const createUpdatedGenreList = (selectedItems, tempList, tempBackUpList) => {
    let tempGenreList = JSON.parse(JSON.stringify(tempList));
    let list = [];

    // First add all selected items
    tempGenreList.forEach((item, index) => {
      if (selectedItems.includes(item.value)) {
        item.isChecked = true;
        list.push(item);
      }
    });

    // Then add non-selected items until we reach max 6
    if (list.length < 9) {
      tempGenreList.forEach(item => {
        if (!selectedItems.includes(item.value) && list.length < 9) {
          item.isChecked = false;
          list.push(item);
        }
      });
    }

    setDisplayGenreList(list);
  };

  const onInterestItemPress = (clickID, obj) => {
    if (clickID == 'ITEM_CLICK') {
      let tempTopicList = JSON.parse(JSON.stringify(displayTopicList));
      let list = [...tempTopicList];
      list[obj.index].isChecked = !list[obj.index].isChecked;
      if (list[obj.index].isChecked) {
        setSelectedInterests([...selectedInterests, obj.value]);
      } else {
        setSelectedInterests(
          selectedInterests.filter(item => item !== obj.value),
        );
      }
      setDisplayTopicList(list);
    }
  };

  const onGenreItemPress = (clickID, obj) => {
    if (clickID == 'ITEM_CLICK') {
      let tempGenreList = JSON.parse(JSON.stringify(displayGenreList));
      let list = [...tempGenreList];
      list[obj.index].isChecked = !list[obj.index].isChecked;
      if (list[obj.index].isChecked) {
        setSelectedGenres([...selectedGenres, obj.value]);
      } else {
        setSelectedGenres(selectedGenres.filter(item => item !== obj.value));
      }
      setDisplayGenreList(list);
    }
  };
  const onEmotionItemPress = (clickID, obj) => {
    if (clickID === 'ITEM_CLICK') {
      let tempEmotionList = JSON.parse(JSON.stringify(displayEmotionList));
      let list = [...tempEmotionList];
      list[obj.index].isChecked = !list[obj.index].isChecked;

      if (list[obj.index].isChecked) {
        setSelectedEmotions([...selectedEmotions, obj.value]);
      } else {
        setSelectedEmotions(
          selectedEmotions.filter(item => item !== obj.value),
        );
      }

      setDisplayEmotionList(list);
    }
  };
  const updateUserInterestService = () => {
    let hashMap = {
      _action_code: '11:UPDATE_USER_INTERESTS',
      interests: JSON.stringify(selectedInterests),
      genres: JSON.stringify(selectedGenres),
    };
    let connector = new ServerConnector();
    // connector.postData(
    //   hashMap,
    //   data => {
    //     // success method
    //     setShowLoading(true);
    //     updateUserColorService();
    //   },
    //   (errorCode, errorMessage, data) => {
    //     // failure method
    //     setShowLoading(false);
    //     var fieldErrorShown = false;
    //     if (errorCode === 'E006') {
    //       if (data && data != null && data.data) {
    //         if (data.data.interests) {
    //           setErrorMsg(data.data.interests);
    //           setErrorMsgType('FAILED');
    //           setRefreshKey(Math.random());
    //           fieldErrorShown = true;
    //         } else if (data.data.genres) {
    //           setErrorMsg(data.data.genres);
    //           setErrorMsgType('FAILED');
    //           setRefreshKey(Math.random());
    //           fieldErrorShown = true;
    //         }
    //       }
    //     }
    //     if (!fieldErrorShown) {
    //       setErrorMsg(errorMessage);
    //       setErrorMsgType('FAILED');
    //       setRefreshKey(Math.random());
    //     }
    //   },
    // );
    navigation.navigate('NotificationGuideSelectionScreen');
  };
  const selectEmotionBoxClick = (clickID, obj) => {
    setOpenEmotionList(false);
    if (clickID == 'DONE') {
      setSelectedEmotions([...[], ...obj.selectedItem]);
      // Create updated list of emotions
      let tempEmotionList = JSON.parse(JSON.stringify(displayEmotionList));
      tempEmotionList.forEach(item => {
        item.isChecked = obj.selectedItem.includes(item.label);
      });
      setDisplayEmotionList(tempEmotionList);
    }
  };
  const updateUserColorService = () => {
    let hashMap = {
      _action_code: '11:UPDATE_USER_UI_COLOUR',
      ui_colour: selectedUIColor,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setShowLoading(false);
        theme.changeThemeColor(selectedUIColor);
        navigation.dispatch(
          CommonActions.reset({
            index: 1,
            routes: [
              {
                name: 'VideoContentScreen',
                params: {
                  postSeq: -1,
                  postProfileSeq: -1,
                  cameFrom: 'PERSONALIZATION',
                },
              },
            ],
          }),
        );
      },
      (errorCode, errorMessage, data) => {
        // failure method
        setShowLoading(false);
        var fieldErrorShown = false;
        if (errorCode === 'E006') {
          if (data && data != null && data.data) {
            if (data.data.ui_colour) {
              setErrorMsg(data.data.ui_colour);
              setErrorMsgType('FAILED');
              setRefreshKey(Math.random());
              fieldErrorShown = true;
            }
          }
        }
        if (!fieldErrorShown) {
          setErrorMsg(errorMessage);
          setErrorMsgType('FAILED');
          setRefreshKey(Math.random());
        }
      },
    );
  };

  const searchTopicBtnPress = () => {
    setOpenInterestList(true);
  };

  const searchGenreBtnPress = () => {
    setOpenGenreList(true);
  };

  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <CustomProgressDialog showLoading={showLoading} />
      <View style={{flex: 1, position: 'relative'}}>
        <LoginSignUpLinearGrad />
        <ModuleAppBar navigation={navigation} />
        <ScrollView keyboardShouldPersistTaps="handled">
          <View style={defaultStyle.loginModuleContainer}>
            <LoginModuleTitle
              firstTitleText={`Let's Personalize Your`}
              secondTitleText="Digital Experience!"
              style={{marginTop: theme.dimensions.loginModuleTitleMT}}
            />
            <View style={defaultStyle.loginModuleFormContainer}>
              <StyleSelector
                selectedStyle={selectedStyle}
                onStyleChange={handleStyleChange}
              />
              {/* Genre */}
              <View>
                <EntutoTextView style={style.labelText}>
                  What genres of content are you interested in?
                </EntutoTextView>
              </View>
              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>

              <View
                style={{
                  marginTop: theme.dimensions.loginModuleInputMT,
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                {displayGenreList.slice(0, 9).map((obj, i) => {
                  return (
                    <OptionSelectionItem
                      key={i}
                      index={i}
                      label={obj.label}
                      isChecked={obj.isChecked}
                      value={obj.value}
                      onItemSelected={onGenreItemPress}
                    />
                  );
                })}

                <OptionSelectionItem
                  isSearchIcon={true}
                  inactiveImageValue={SearchIcon}
                  activeImageValue={SearchIcon}
                  onItemSelected={searchGenreBtnPress}
                  showImage={true}
                />
              </View>

              {/* Topics */}
              <View
                style={{marginTop: theme.dimensions.loginModuleInputMT * 1.5}}>
                <EntutoTextView style={style.labelText}>
                  Why don't you select some topics as well?
                </EntutoTextView>
              </View>

              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>

              <View
                style={{
                  marginTop: theme.dimensions.loginModuleInputMT,
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                {displayTopicList.slice(0, 9).map((obj, i) => {
                  return (
                    <OptionSelectionItem
                      key={i}
                      index={i}
                      label={obj.label}
                      isChecked={obj.isChecked}
                      value={obj.value}
                      onItemSelected={onInterestItemPress}
                    />
                  );
                })}

                <OptionSelectionItem
                  isSearchIcon={true}
                  inactiveImageValue={SearchIcon}
                  activeImageValue={SearchIcon}
                  onItemSelected={searchTopicBtnPress}
                  showImage={true}
                />
              </View>
              {/* Emotions  */}
              <View
                style={{marginTop: theme.dimensions.loginModuleInputMT * 1.5}}>
                <EntutoTextView style={style.labelText}>
                  How about some emotions you connect with?
                </EntutoTextView>
              </View>

              <EntutoTextView style={style.warringInterestText}>
                Select at least 9
              </EntutoTextView>

              <View
                style={{
                  marginTop: theme.dimensions.loginModuleInputMT,
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                {displayEmotionList.map((obj, i) => {
                  return (
                    <OptionSelectionItem
                      key={i}
                      index={i}
                      label={obj.label}
                      isChecked={obj.isChecked}
                      value={obj.value}
                      onItemSelected={onEmotionItemPress}
                      inactiveImageValue={obj.inactivePath}
                      activeImageValue={obj.activePath}
                      showImage={true}
                      lottieFile={obj.lottiePath}
                      isAnimated={true}
                      soundFileName={obj.audioPath}
                    />
                  );
                })}
                <OptionSelectionItem
                  isSearchIcon={true}
                  inactiveImageValue={SearchIcon}
                  activeImageValue={SearchIcon}
                  onItemSelected={searchEmotionBtnPress}
                  showImage={true}
                />
              </View>
              <View style={{marginTop: 54}}>
                <PrimaryButton
                  label="You Are All Set!"
                  style={{}}
                  uppercase={false}
                  onPress={() => letsGetStartedBtnClick()}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
      {/* Interest Selection Modal */}
      <Modal
        animationType="fade"
        visible={openInterestList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectInterestBoxClick}
          list={JSON.parse(JSON.stringify(topicList))}
          selectedValue={selectedInterests}
          title="Select Interest"
          maxSelectedValue={10}
          multiSelect={true}
          labelField="label"
          valueField="value"
        />
      </Modal>
      {/* Emotion Selection Modal */}
      <Modal
        animationType="fade"
        visible={openEmotionList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectEmotionBoxClick}
          list={JSON.parse(JSON.stringify(displayEmotionList))}
          selectedValue={selectedEmotions}
          title="Select Emotions"
          maxSelectedValue={15} // Increased from 6 to allow more selections
          multiSelect={true}
          isEmotions={true} // Set this to true for emotions
        />
      </Modal>
      {/* Genre Selection Modal */}
      <Modal
        animationType="fade"
        visible={openGenreList}
        style={{margin: 0, flex: 1}}>
        <SelectBoxComponent
          selectBoxClick={selectGenreBoxClick}
          list={JSON.parse(JSON.stringify(genreList))}
          selectedValue={selectedGenres}
          title="Select Genre"
          maxSelectedValue={7}
          multiSelect={true}
          labelField="label"
          valueField="value"
        />
      </Modal>

      {errorMsg.length != 0 ? (
        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
      ) : null}
    </>
  );
};

export default QuickSignUpPersonalizeScreen;

const styles = theme =>
  StyleSheet.create({
    labelText: {
      fontSize: theme.calculateFontSize(theme.dimensions.personalizeLabelText),
    },
    itemContainer: {
      padding: theme.dimensions.loginModuleInputPadding,
      borderWidth: 1,
      justifyContent: 'center',
      alignItems: 'center',
      height: 36,
      // borderRadius: 36 / 2,
      marginEnd: 8,
      marginBottom: 8,
      minWidth: 90,
    },
    itemContainerText: {
      paddingHorizontal: 25,
    },
    searchIcon: {
      width: 17,
      height: 17,
      resizeMode: 'contain',
      tintColor: '#707070',
      paddingHorizontal: 25,
    },
    warringInterestText: {
      color: '#FF0000',
      marginTop: 10,
    },
  });
