import React, { useRef } from 'react'
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native'
import ChangePassword from '../components/accountsetting/ChangePassword'
import CustomStatusBar from '../components/common/CustomStatusBar'
import EntutoTextView from '../components/common/EntutoTextView'
import HomeTopNavigationBar from '../components/HomeTopNavigationBar'
import ListItem from '../components/ListItem'
import ActionSheet from "react-native-actions-sheet";
import DeleteAccount from '../components/accountsetting/DeleteAccount'
import useDefaultStyle from '../theme/useDefaultStyle'
import useSTheme from '../theme/useSTheme'

const AccountSettingsScreen = ({ navigation }) => {
    const changePassRef = useRef(null);
    const deleteAccountRef = useRef(null);
    const theme = useSTheme();
    const { defaultStyle } = useDefaultStyle();
    const accountInfoPress = () => {
        navigation.navigate("AccountInfoScreen");
    }
    const blockedAccPress = () => {
        navigation.navigate("BlockedAccountScreen");
    }
    const restrictAccPress = () => {
        navigation.navigate("RestrictedAccountScreen");
    }
    const notiSettingsPress = () => {
        navigation.navigate("NotificationSettingsScreen");
    }
    const privacySafetyPress = () => {
        navigation.navigate("PrivacySafetyScreen");
    }
    const subscriptionBtnPress = () => {
        navigation.navigate("SubscriptionScreen");
    }
    const disableAccountPress = () => {
        navigation.navigate("DisableAccountScreen");
    }
    const changePasswordPress = () => {
        changePassRef.current?.show();
    }
    const changePasswordActionClick = (clickId) => {
        if (clickId == "negetive") {
            changePassRef.current?.hide();
        }
    }
    const deleteAccountPress = () => {
        deleteAccountRef.current?.show();
    }
    const deleteAccountActionClick = (clickId) => {
        if (clickId == "negetive") {
            deleteAccountRef.current?.hide();
        }
    }

    return (
        <><CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Account Settings" navigation={navigation} />
            <ScrollView
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                style={{ backgroundColor: theme.colors.backgroundColor }}
            >
                <View style={defaultStyle.container}>
                    <TouchableOpacity onPress={() => accountInfoPress()}>
                        <ListItem label="Account Info" />
                    </TouchableOpacity>
                    {/* <TouchableOpacity onPress={() => changePasswordPress()}>
                        <ListItem label="Change Password" />
                    </TouchableOpacity> */}
                    {/* <TouchableOpacity onPress={() => restrictAccPress()}>
                        <ListItem label="Restricted Accounts"
                            bottomTxt="Protect yourself from unwanted interactions without losing followers or subscribers."
                            showBottomtxt={true} />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => blockedAccPress()}>
                        <ListItem label="Blocked Accounts" />
                    </TouchableOpacity> */}
                    <TouchableOpacity onPress={() => privacySafetyPress()}>
                        <ListItem label="Privacy" />{/*  and Safety */}
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => subscriptionBtnPress()}>
                        <ListItem label="Subscriptions" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => notiSettingsPress()}>
                        <ListItem label="Notification" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => disableAccountPress()}>
                        <ListItem label="Disable Account" />
                    </TouchableOpacity>
                    {/* <TouchableOpacity onPress={() => deleteAccountPress()}>
                        <ListItem label="Delete Account" />
                    </TouchableOpacity> */}
                </View>
            </ScrollView>
            <ActionSheet ref={changePassRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled
                    onMomentumScrollEnd={() => {
                        changePassRef.current?.handleChildScrollEnd();
                    }}
                    style={{ backgroundColor: theme.colors.backgroundColor }}
                >
                    <ChangePassword refVal={changePassRef} changePasswordActionClick={(clickId, obj) => changePasswordActionClick(clickId, obj)} />
                </ScrollView>

            </ActionSheet >
            <ActionSheet ref={deleteAccountRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        deleteAccountRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <DeleteAccount refVal={deleteAccountRef} navigation={navigation}
                        deleteAccountActionClick={(clickId, obj) => deleteAccountActionClick(clickId, obj)} />
                </ScrollView>
            </ActionSheet >
        </>
    )
}

export default AccountSettingsScreen;

const styles = StyleSheet.create({})
