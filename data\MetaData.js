import * as AngerIcon from '../assets/Images/icon/reactions/Anger.png';
import * as Irritated<PERSON>ottie from '../assets/reactions/jsonfile/irritated.json';
import * as BoredBeforeLottie from '../assets/reactions/jsonfile/bored before.json';
import * as HappyAfterLottie from '../assets/reactions/jsonfile/happy after.json';
import * as HeartbrokenBeforeLottie from '../assets/reactions/jsonfile/heartbroken before.json';
import * as HmmLottie from '../assets/reactions/jsonfile/hmm.json';
import * as LoveBeforeLottie from '../assets/reactions/jsonfile/love before.json';
import * as NostalgiaLottie from '../assets/reactions/jsonfile/nostalgia.json';
import * as ScaredAfterLottie from '../assets/reactions/jsonfile/scared after.json';
import * as SureLottie from '../assets/reactions/jsonfile/sure.json';
import * as WowLottie from '../assets/reactions/jsonfile/wow.json';

export const REACTION_LIST = [
  {value: 'ANGER', label: 'Anger', icon: AngerIcon},
  // { value: "AUD", label: "Audience", icon: require("../assets/Images/icon/reactions/Audience.png") },
  {value: 'BOO', label: 'Boo', icon: AngerIcon},
  {value: 'BLADY', label: 'Bosslady', icon: AngerIcon},
  {value: 'BMAN', label: 'BossMan', icon: AngerIcon},
  {value: 'CELEB', label: 'Celebration', icon: AngerIcon},
  {value: 'CLAP', label: 'Clap', icon: AngerIcon},
  {value: 'DISG', label: 'Disgust', icon: AngerIcon},
  {value: 'FEAR', label: 'Fear', icon: AngerIcon},
  {value: 'MEH', label: 'Meh', icon: AngerIcon},
  {value: 'LIT', label: 'Potty', icon: AngerIcon},
  {value: 'SURP', label: 'Surprise', icon: AngerIcon},
  {value: 'TEASE', label: 'Tease', icon: AngerIcon},
  {value: 'TPASS', label: 'Timepass', icon: AngerIcon},
  {value: 'WEVER', label: 'Whatever', icon: AngerIcon},
  {value: 'YAY', label: 'Yay', icon: AngerIcon},
  {value: 'YUM', label: 'Yum', icon: AngerIcon},

  // { value: "LIKE", label: "Luv", icon: require("../assets/Images/icon/reactions/Luv.png") },
];

export const HARDCODED_EMOTIONS = [
  {
    value: 'ANGRY',
    label: 'Angry',
    icon: AngerIcon,
    lottiePath: IrritatedLottie,
    audioPath: 'bored.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'BORED',
    label: 'Bored',
    icon: AngerIcon, // Placeholder
    lottiePath: BoredBeforeLottie,
    audioPath: 'bored.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'HAPPY',
    label: 'Happy',
    icon: AngerIcon, // Placeholder
    lottiePath: HappyAfterLottie,
    audioPath: 'happy_after.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'HEARTBROKEN',
    label: 'Heartbroken',
    icon: AngerIcon, // Placeholder
    lottiePath: HeartbrokenBeforeLottie,
    audioPath: 'heartbroken_before_2_1.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'HMM',
    label: 'Hmm',
    icon: AngerIcon, // Placeholder
    lottiePath: HmmLottie,
    audioPath: 'hmm_1.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'LOVED',
    label: 'Loved',
    icon: AngerIcon, // Placeholder
    lottiePath: LoveBeforeLottie,
    audioPath: 'love_before_2.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'NOSTALGIA',
    label: 'Nostalgia',
    icon: AngerIcon, // Placeholder
    lottiePath: NostalgiaLottie,
    audioPath: 'nostalgia_2_1.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'SCARED',
    label: 'Scared',
    icon: AngerIcon, // Placeholder
    lottiePath: ScaredAfterLottie,
    audioPath: 'scared_after_2_1.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'SURE',
    label: 'Sure',
    icon: AngerIcon, // Placeholder
    lottiePath: SureLottie,
    audioPath: 'sure_03.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
  {
    value: 'WOW',
    label: 'Wow',
    icon: AngerIcon, // Placeholder
    lottiePath: WowLottie,
    audioPath: 'wow_1.mp3',
    vibrationPattern: [0, 50, 50, 50],
    isChecked: false,
  },
];
