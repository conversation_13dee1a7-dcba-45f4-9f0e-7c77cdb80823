import React, { useEffect, useRef, useState } from 'react'
import { Alert, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import EntutoTextView from '../common/EntutoTextView';
import RazorpayCheckout from 'react-native-razorpay';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import BottomSheetLoader from '../common/BottomSheetLoader';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import appData from '../../data/Data';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import CustomSnackbar from '../common/CustomSnackbar';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import { PopupNegativeButton, PopupPositiveButton } from '../common/PopupButton';

const UnlockPostActionView = ({ refVal, amountValue, profileSeq, postSeq, isPaidProfile, ...props }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [showLoading, setshowLoading] = useState(true);
    const [showSuccessMsg, setshowSuccessMsg] = useState(false);
    const [successMsg, setsuccessMsg] = useState("");

    const [errorMsg, seterrorMsg] = useState("");
    const [errorMsgRef, seterrorMsgRef] = useState(Math.random());
    const [refreshPaymentStatusBtn, setRefreshPaymentStatusBtn] = useState(false);
    const [orderIdVal, setOrderIdVal] = useState("");
    const [updatePaymentOrderExecute, setUpdatePaymentOrderExecute] = useState(false);

    const modalFlashRef = useRef(null);
    const [paymentObjectD, setpaymentObjectD] = useState({
        amount: 0,
        app_name: "",
        contact: "",
        currency: "INR",
        description: "",
        email: "",
        init_seq: -1,
        name: "",
        order_id: "",
    });

    useEffect(() => {
        setTimeout(() => {
            setshowLoading(false);
        }, 200);
    }, []);

    const cancelBtnPress = () => {
        props.unlockPostActionClick("negetive", {})
    }
    const closeBtnClick = () => {

        props.unlockPostActionClick("close", { erMsg: successMsg });
    }
    const unlockBtnPress = () => {
        setshowLoading(true)
        seterrorMsg("");
        seterrorMsgRef(Math.random())
        getPaymentOrder();

    }
    const getPaymentOrder = () => {
        // console.log("amountValue", amountValue)
        let hashMap = {
            _action_code: "11:GET_PAYMENT_ORDER",
            amount: amountValue,
            req_profile_seq: profileSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let payObj = {
                amount: data.data.amount,
                app_name: data.data.app_name,
                contact: data.data.contact,
                currency: data.data.currency,
                description: data.data.description,
                email: data.data.email,
                init_seq: data.data.init_seq,
                name: data.data.name,
                order_id: data.data.order_id
            }
            appData.__paymentDataObj = payObj;
            setpaymentObjectD(payObj);
            setshowLoading(false);
            setTimeout(() => {
                addPaymentBoxService(data.data, data.data.amount);
            }, 500);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                seterrorMsg(errorMessage);
                seterrorMsgRef(Math.random());
            }
        });
    }
    function addPaymentBoxService(dataVal, mainAmt) {
        // setRefreshPaymentStatusBtn(true);
        setOrderIdVal(dataVal.order_id)
        const options2 = {
            key: dataVal.key,
            amount: String(mainAmt),
            currency: dataVal.currency,
            name: dataVal.app_name,
            description: dataVal.description,
            image: "https://www.sotrue.co.in/assets/images/logo.png",
            order_id: dataVal.order_id,
            prefill: {
                name: dataVal.name,
                email: dataVal.email,
            },
            theme: {
                "color": "#f3997b"
            }
        }
        // setTimeout(() => {
        // console.log("options", options2)
        RazorpayCheckout.open(options2).then((data) => {
            // handle success
            // if (!showLoading) {
            // setRefreshPaymentStatusBtn(false);
            updatePaymentOrder(data, "SUCCESS")
            // }

        }).catch((error) => {
            // handle failure
            // if (!showLoading) {
            // setRefreshPaymentStatusBtn(false);
            updatePaymentOrder(error, "FAILED")
            // }

        });
        // }, 500);

    }
    const updatePaymentOrder = (rawResp, status) => {
        setshowLoading(true);
        // setRefreshPaymentStatusBtn(true);
        // console.log("paymentObject2", paymentObjectD);
        // console.log("appData.__paymentDataObj", appData.__paymentDataObj);
        let hashMap = {
            _action_code: "11:UPDATE_PAYMENT_ORDER",
            init_seq: appData.__paymentDataObj.init_seq,
            status: status,
            order_id: appData.__paymentDataObj.order_id,
            // raw_resp: JSON.stringify(rawResp),
            amount: appData.__paymentDataObj.amount,
        }
        if (rawResp != null) {
            hashMap.raw_resp = JSON.stringify(rawResp);
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setUpdatePaymentOrderExecute(true);
            if (status === "SUCCESS") {
                if (isPaidProfile) {
                    subscribeProfileService();
                }
                else {
                    subscribePostService();
                }

            }
            else {
                let ErrorMsg = "";
                if (rawResp.hasOwnProperty("details")) {
                    if (rawResp.details.error.hasOwnProperty("description")) {
                        ErrorMsg = rawResp.details.error.description;
                    }
                }
                if (rawResp.hasOwnProperty("error")) {
                    if (rawResp.error.hasOwnProperty("description")) {
                        ErrorMsg = rawResp.error.description;
                    }
                }
                if (ErrorMsg == "You may have cancelled the payment or there was a delay in response from the UPI app") {
                    ErrorMsg = "You may have cancelled the payment or there was a delay in response from the UPI app, try again?"
                }
                else if (ErrorMsg == "undefined") {
                    ErrorMsg = "You may have cancelled the payment or there was a delay in response from the UPI app, try again?"
                }

                // setRefreshPaymentStatusBtn(false);
                setshowLoading(false);
                seterrorMsg(ErrorMsg);
                seterrorMsgRef(Math.random())
            }
        }, (errorCode, errorMessage, data) => { // failure method
            setUpdatePaymentOrderExecute(true);
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                seterrorMsg(errorMessage);
                seterrorMsgRef(Math.random())
            }
        });
    }
    function subscribeProfileService() {
        let hashMap = {
            _action_code: "11:SUBSCRIBE_PROFILE",
            subscribe_profile_seq: profileSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true);
            setshowLoading(false)
            appData._profilePageRefresh = true;
            // setRefreshPaymentStatusBtn(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                seterrorMsg(errorMessage);
                seterrorMsgRef(Math.random())
                setshowLoading(false);
            }
        });
    }
    function subscribePostService() {
        let hashMap = {
            _action_code: "11:SUBSCRIBE_POST",
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true);
            setshowLoading(false)
            appData._profilePostPageRefresh = true;
            // setRefreshPaymentStatusBtn(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                seterrorMsg(errorMessage);
                seterrorMsgRef(Math.random());
                setshowLoading(false)
            }
        });
    }
    function refreshPaymentBtnPress() {
        setshowLoading(true)
        getPayOrderStatusService();
    }
    function getPayOrderStatusService() {
        let hashMap = {
            _action_code: "11:GET_PAY_ORDER_STATUS",
            order_id: orderIdVal,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false)
            if (!updatePaymentOrderExecute) {
                updatePaymentOrder(null, "SUCCESS");
            }
            else {
                if (isPaidProfile) {
                    subscribeProfileService();
                }
                else {
                    subscribePostService();
                }
            }

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                seterrorMsg(errorMessage);
                seterrorMsgRef(Math.random());
            }
        });
    }
    return (
        <View>
            <View style={defaultStyle.popupBox}>
                {
                    !refreshPaymentStatusBtn ?
                        <EntutoTextView style={defaultStyle.popupHeadTxt}>{
                            isPaidProfile ? "Subscribe Profile" : "Unlock Post"
                        }</EntutoTextView>
                        : null
                }
                {
                    refreshPaymentStatusBtn ?
                        <View style={style.crossBtnBox}>
                            <TouchableOpacity style={{ paddingHorizontal: 15, paddingVertical: 15 }} onPress={() => cancelBtnPress()}>
                                <MaterialCommunityIcons name='close' color={'#000'} size={24} />
                            </TouchableOpacity>

                        </View>
                        : null
                }
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                {
                    showSuccessMsg ?
                        <BottomSheetSuccessMsg successMsg={successMsg} cancelBtnClick={() => closeBtnClick()} />
                        : null
                }
                {/* {
                    errorMsg.length != 0 ?
                        // <View style={defaultStyle.errorBoxOutside}>
                        <SuccessFailureMsgBox forceOpenSnackBar={true} alertMsg={errorMsg} alertKey={errorMsgRef} />
                        // </View>
                        : null
                } */}
                <CustomSnackbar snackMsg={errorMsg} displaySnackbar={errorMsg.length != 0}
                    refreshSnack={errorMsgRef} showInsideFlashRef={true} insideFlashRef={modalFlashRef} />

                {
                    refreshPaymentStatusBtn ?
                        <View style={{ justifyContent: 'center', alignItems: 'center', minHeight: 150 }}>

                            <EntutoTextView style={{ ...defaultStyle.popupBodyTxt, marginBottom: 20, marginTop: 10 }}>
                                Oh no! We failed to authenticate your Payment. You can refresh the status by tapping the link below.
                            </EntutoTextView>

                            <TouchableOpacity onPress={() => refreshPaymentBtnPress()}>
                                <EntutoTextView style={style.refreshBtnText}>Refresh Payment Status</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                        :
                        <View>
                            <EntutoTextView style={defaultStyle.popupBodyTxt}>
                                {/* Please fill in your payment details next, shall we? */}
                                Add your payment details to view this exclusive content now!
                            </EntutoTextView>

                            <EntutoTextView style={style.popupWarringTxt}>
                                This SoTrue content is owned by the creator and protected by us, check out this exclusive content for you!
                                {/* All SoTrue content is copyrighted and tracked and you’re one step away from accessing exclusive content for your eyes only. */}
                            </EntutoTextView>
                            <View style={{ flexDirection: 'row', flex: 1, marginTop: 16 }}>
                                <View style={{ flex: 1 }}>
                                    <PopupNegativeButton
                                        onPress={() => cancelBtnPress()}
                                        btnText='No'
                                        style={{ marginEnd: theme.dimensions.popupBtnGap }} />

                                </View>
                                <View style={{ flex: 1 }}>
                                    <PopupPositiveButton
                                        onPress={() => unlockBtnPress()}
                                        btnText={isPaidProfile ? "Subscribe" : "Unlock"} />

                                </View>
                            </View>

                        </View>
                }
            </View>
        </View>
    )
}

export default React.memo(UnlockPostActionView);

const styles = theme => StyleSheet.create({

    popupWarringTxt: {
        // color: '#FF4963',
        color: theme.colors.unlockPopupWarringTxt,
        fontSize: theme.calculateFontSize(theme.dimensions.popupWarringTxt),
        marginTop: 10
    },
    popupBtn: {
        flex: 1,
        backgroundColor: theme.colors.unlockPopupBtnBackground,
        borderRadius: 14,
    },
    refreshBtnText: {
        textDecorationLine: 'underline',
        color: theme.colors.unlockPopupRefreshBtnText,
        fontSize: theme.calculateFontSize(theme.dimensions.popupRefreshBtnText),
    },
    crossBtnBox: {
        position: 'absolute',
        top: 0,
        right: 0,
        zIndex: 1
    }
})
