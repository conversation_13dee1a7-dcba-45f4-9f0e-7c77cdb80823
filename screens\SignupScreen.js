import React, { useState, useCallback, useEffect, useContext } from 'react'
import { StyleSheet, View, ScrollView, TouchableOpacity, Linking, Image, Modal } from 'react-native'
import Colors from '../constants/Colors';
import { Checkbox } from 'react-native-paper';
import EntutoTextView from '../components/common/EntutoTextView';
import EntutoEditText from '../components/common/EntutoEditText';
import ServerConnector from '../utils/ServerConnector';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import PasswordIcon from '../assets/Images/icon/password.png';
import ErrorMessages from '../constants/ErrorMessages';
import SignupIntroVideo from '../components/SignupIntroVideo';
import { _getFirstTimeUser, _setFirstTimeUser } from '../utils/AuthLogin';
import GoogleSignButton from '../components/common/GoogleSignButton';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { TERMS_AND_COND_URL, WEB_CLIENT_ID } from '../utils/Appconfig';
import CustomStatusBar from '../components/common/CustomStatusBar';
import { AppleButton, appleAuth } from '@invertase/react-native-apple-authentication';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import { AppStateContext } from '..';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';


const SignupScreen = ({ route, navigation }) => {
    const [emailId, setemailId] = useState("");
    const [emailIdErr, setemailIdErr] = useState("");
    const [password, setpassword] = useState("");
    const [passwordErr, setpasswordErr] = useState("");
    const [fullName, setfullName] = useState("");
    const [fullNameErr, setfullNameErr] = useState("");
    const [referralCode, setreferralCode] = useState("");
    const [referralCodeErr, setreferralCodeErr] = useState("");
    const { acceptTerms, changeAcceptTerms } = useContext(AppStateContext)
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [checked, setChecked] = useState(false);

    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const [refreshKey, setrefreshKey] = useState(Math.random());

    const [introVideoVisible, setintroVideoVisible] = useState(false);
    // useEffect(() => {
    //     if (appleAuth.isSupported) {
    //         // onCredentialRevoked returns a function that will remove the event listener. useEffect will call this function when the component unmounts
    //         return appleAuth.onCredentialRevoked(async () => {
    //             console.log('If this function executes, User Credentials have been Revoked');
    //         });
    //     }
    // }, []);

    // React.useEffect(() => {
    //     const unsubscribe = navigation.addListener('focus', () => {
    //         GoogleSignin.configure({
    //             webClientId: WEB_CLIENT_ID,
    //             offlineAccess: true,
    //         });
    //         try {
    //             GoogleSignin.signOut()
    //         } catch (error) {
    //             console.log("Google", error);
    //         }
    //     });
    //     return unsubscribe;
    // }, [navigation]);


    const loginTxtClick = () => {
        navigation.goBack(null);
    }
    const termsUrl = TERMS_AND_COND_URL;
    const termsBtnClick = useCallback(async () => {
        // Checking if the link is supported for links with custom URL scheme.
        const supported = await Linking.canOpenURL(termsUrl);

        if (supported) {
            // Opening the link with some app, if the URL scheme is "http" the web link should be opened
            // by some browser in the mobile
            await Linking.openURL(termsUrl);
        } else {
            Alert.alert(`Don't know how to open this URL: ${termsUrl}`);
        }
    }, [termsUrl]);

    function registerBtnClick() {
        if (!acceptTerms) {
            navigation.navigate('TermsAndConditionScreen')
            return
        }
        var isValid = true;
        setemailIdErr("");
        setpasswordErr("");
        setfullNameErr("");
        if (emailId.length === 0) {
            setemailIdErr(ErrorMessages.signupEmailIdErr);
            isValid = false;
        }
        if (password.length === 0) {
            setpasswordErr(ErrorMessages.signupPasswordErr);
            isValid = false;
        }
        if (fullName.length === 0) {
            setfullNameErr(ErrorMessages.signupFullNameErr);
            isValid = false;
        }
        if (isValid) {
            setShowLoading(true);
            appServiceCall();
        }
    }
    function appServiceCall() {
        let hashMap = {
            _action_code: "11:APP_REGISTER",
            full_name: fullName,
            email: emailId,
            password: password,
            mode: "REGULAR"
        }
        if (referralCode.trim().length !== 0) {
            hashMap.referral_code = referralCode;
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            seterrorMsg("");

            navigation.navigate("SignupOtpScreen", {
                _data: data,
                _emailId: emailId,
                _password: password,
                _user_seq: data.data.user_seq
            })

        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.full_name) {
                        setfullNameErr(data.data.full_name);
                        fieldErrorShown = true;
                    }
                    if (data.data.email) {
                        setemailIdErr(data.data.email);
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        setpasswordErr(data.data.password);
                        fieldErrorShown = true;
                    }
                    if (data.data.referral_code) {
                        setreferralCodeErr(data.data.referral_code);
                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }
    const registerGoogleBtnPress = async () => {
        try {
            await GoogleSignin.hasPlayServices();
            const userInfo = await GoogleSignin.signIn();
            if (userInfo.hasOwnProperty("idToken")) {
                navigation.navigate("SignupGmailScreen", {
                    userInfo: userInfo,
                })
            }

        } catch (error) {
            if (error.code === statusCodes.SIGN_IN_CANCELLED) {
                // user cancelled the login flow
                // seterrorMsg("user cancelled the login flow");
                // setrefreshKey(Math.random())
            } else if (error.code === statusCodes.IN_PROGRESS) {
                // operation (e.g. sign in) is in progress already
                seterrorMsg("operation (e.g. sign in) is in progress already");
                setrefreshKey(Math.random())
            } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
                // play services not available or outdated
                seterrorMsg("play services not available or outdated");
                setrefreshKey(Math.random())
            } else {
                // some other error happened
                // console.log("error", error)
                // seterrorMsg(error);
                // setrefreshKey(Math.random())
            }
        }
    }
    async function onAppleButtonPress() {


        try {
            const appleAuthRequestResponse = await appleAuth.performRequest({
                requestedOperation: appleAuth.Operation.LOGIN,
                // Note: it appears putting FULL_NAME first is important, see issue #293
                requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
            });
            if (!appleAuthRequestResponse.authorizationCode) {
                seterrorMsg('Apple Sign-In failed - no identify token returned');
                setrefreshKey(Math.random())
            }
            const { authorizationCode } = appleAuthRequestResponse;
            navigation.navigate("SignupAppleScreen", {
                userInfo: {
                    user_email: "",
                    password_val: authorizationCode,
                    full_name: "",
                },
            })

        } catch (error) {
            seterrorMsg(error.message);
            setrefreshKey(Math.random())
        }
    }
    const loginBtnPress = () => {
        navigation.replace('SignInNScreen');
    }
    return (
        <>
            <CustomStatusBar translucent={true} hidden={false} />
            <View style={{ flex: 1, position: 'relative' }}>
                <LoginSignUpLinearGrad />
                <CustomProgressDialog
                    showLoading={showLoading}
                />
                <ScrollView
                    keyboardShouldPersistTaps="handled">

                    <View>
                        <View style={defaultStyle.signupTextBox}>
                            <EntutoTextView style={defaultStyle.signupText}>Signup to create</EntutoTextView>
                            <EntutoTextView style={defaultStyle.signupText}>new account</EntutoTextView>
                        </View>
                        <View style={defaultStyle.signUpFormBox}>
                            <EntutoEditText
                                showLeftIcon={false}
                                placeholderTxt="Full Name"
                                value={fullName}
                                onChangeText={text => { setfullName(text); setfullNameErr("") }}
                                showErrorField={fullNameErr.length}
                                errorMsg={fullNameErr}
                            />
                            <EntutoEditText
                                showLeftIcon={false}
                                placeholderTxt="Email"
                                value={emailId}
                                onChangeText={text => { setemailId(text); setemailIdErr("") }}
                                showErrorField={emailIdErr.length}
                                errorMsg={emailIdErr}
                            />
                            <EntutoEditText
                                showLeftIcon={false}
                                placeholderTxt="Password"
                                value={password}
                                onChangeText={text => { setpassword(text); setpasswordErr("") }}
                                showErrorField={passwordErr.length}
                                errorMsg={passwordErr}
                                secureEntryTxt={true}
                                showRightIcon={true}
                            />
                            <EntutoEditText
                                showLeftIcon={false}
                                placeholderTxt="Referral Code(optional)"
                                value={referralCode}
                                onChangeText={text => { setreferralCode(text); setreferralCodeErr("") }}
                                showErrorField={referralCodeErr.length}
                                errorMsg={referralCodeErr}
                            />
                        </View>
                        <View style={defaultStyle.signUpBtnBox}>
                            <TouchableOpacity style={{ ...defaultStyle.signUpBtn, backgroundColor: theme.colors.signUpBtnBackground }}
                                onPress={() => registerBtnClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Signup</EntutoTextView>
                            </TouchableOpacity>
                            <TouchableOpacity style={defaultStyle.signUpBtn}
                                onPress={() => loginBtnPress()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Login</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
            </View>
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                    : null
            }
        </>
    )
}

export default SignupScreen;

const styles = StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: Colors.bodyTextColor
    },
    signupTxtVal: {
        color: Colors.primaryColor,
    },
    termsconBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 5

    },
    termsTxt: {
        borderBottomWidth: 1,
        color: Colors.bodyTextColor
    },
    loginHeadTxt: {
        marginTop: 20,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },


    signupTextBox: {
        paddingStart: 24,
        paddingEnd: 24,
        marginTop: 60
    },
    signupText: {
        color: '#FFFFFF',
        fontSize: 34,
        marginBottom: 4
    },
    signUpFormBox: {
        minHeight: 200,
        backgroundColor: '#FFFFFF',
        borderTopEndRadius: 24,
        borderBottomEndRadius: 24,
        flex: 1,
        marginEnd: 44,
        marginTop: 40,
        paddingHorizontal: 24,
        paddingVertical: 15,
    },
    signUpBtnBox: {
        marginTop: 100,
        flexDirection: 'row',
        marginBottom: 80
    },
    signUpBtn: {
        minHeight: 58,
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
        borderTopEndRadius: 58,
        borderBottomEndRadius: 58,
    },
    signUpBtnText: {
        color: '#FFFFFF',
        fontSize: 16,
    }

})
