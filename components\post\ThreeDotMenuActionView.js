import React, { useRef } from 'react'
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import ActionSheet from 'react-native-actions-sheet';
import EntutoTextView from '../common/EntutoTextView';
import HelpSetting from '../settings/HelpSetting';
import CopyLinkActionView from './CopyLinkActionView';
import DeletePostActionView from './DeletePostActionView';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';

const ThreeDotMenuActionView = ({ unlockPost, navigation, isMyProfile, postSeq, ThreeDotMenuPress, cameFrom = "" }) => {
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    const postBtnRef = useRef(null);
    const copyLinkBtnRef = useRef(null);
    const deleteBtnRef = useRef(null);
    const reportPostBtnPress = () => {
        postBtnRef.current?.show();
    }
    const deletePostBtnPress = () => {
        deleteBtnRef.current?.show();
    }

    const copyLinkBtnPress = () => {
        copyLinkBtnRef.current?.show();
    }
    const shareLinkBtnPress = () => {
        ThreeDotMenuPress("sharePost", {});
    }
    const postBtnActionClick = (clickId, obj) => {
        if (clickId == "negetive") {
            postBtnRef.current?.hide();
        }
        if (clickId == "close") {
            postBtnRef.current?.hide();
            ThreeDotMenuPress("blockPost", { msg: obj.erMsg });

        }
    }
    const deletePostActionPress = (clickId, obj) => {
        if (clickId == "negetive") {
            deleteBtnRef.current?.hide();
        }
        if (clickId == "close") {
            deleteBtnRef.current?.hide();
            ThreeDotMenuPress("deletePost", { msg: obj.erMsg });

        }
    }
    const editPostBtnPress = () => {
        ThreeDotMenuPress("editPost", {});
        navigation.navigate("EditPostScreen", {
            postSeq: postSeq,
        });
    }
    return (
        <View>
            <View style={style.popupBox}>
                <View style={style.popupListRowBox}>
                    <TouchableOpacity style={style.linkBox}
                        onPress={() => copyLinkBtnPress()}>
                        <EntutoTextView style={style.linkTxt}>Copy link</EntutoTextView>
                    </TouchableOpacity>
                </View>
                <View style={style.popupDivider} />
                <View style={style.popupListRowBox}>
                    <TouchableOpacity style={style.linkBox}
                        onPress={() => shareLinkBtnPress()}>
                        <EntutoTextView style={style.linkTxt}>Share</EntutoTextView>
                    </TouchableOpacity>
                </View>
                <View style={style.popupDivider} />
                {
                    !isMyProfile && unlockPost ?
                        <View style={style.popupListRowBox}>
                            <TouchableOpacity style={style.linkBox}
                                onPress={() => reportPostBtnPress()}>
                                <EntutoTextView style={style.linkTxt}>Report Post</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                        : null
                }
                {
                    isMyProfile && cameFrom != "EPISODE" ?
                        <>
                            <View style={style.popupDivider} />
                            <View style={style.popupListRowBox}>
                                <TouchableOpacity style={style.linkBox}
                                    onPress={() => editPostBtnPress()}>
                                    <EntutoTextView style={style.linkTxt}>Edit Post</EntutoTextView>
                                </TouchableOpacity>
                            </View>
                        </>
                        : null
                }
                {
                    isMyProfile && cameFrom != "EPISODE" ?
                        <>
                            <View style={style.popupDivider} />
                            <View style={style.popupListRowBox}>
                                <TouchableOpacity style={style.linkBox}
                                    onPress={() => deletePostBtnPress()}>
                                    <EntutoTextView style={style.linkTxt}>Delete Post</EntutoTextView>
                                </TouchableOpacity>
                            </View>
                        </>
                        : null
                }

            </View>
            <ActionSheet ref={postBtnRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                closeOnTouchBackdrop={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        postBtnRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <HelpSetting refVal={postBtnRef} settingsType="POST" postSeq={postSeq}
                        helpBtnActionClick={(clickId, obj) => postBtnActionClick(clickId, obj)} />
                </ScrollView>
            </ActionSheet >
            <ActionSheet ref={copyLinkBtnRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                closeOnTouchBackdrop={true}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        copyLinkBtnRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <CopyLinkActionView copyLinkID={postSeq} copyLinkType="POST" />
                </ScrollView>
            </ActionSheet >
            <ActionSheet ref={deleteBtnRef}
                statusBarTranslucent
                bounceOnOpen={false}

                gestureEnabled={false}
                closeOnTouchBackdrop={true}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ScrollView
                    nestedScrollEnabled={true}
                    onMomentumScrollEnd={() =>
                        deleteBtnRef.current?.handleChildScrollEnd()
                    }
                    style={{ backgroundColor: theme.colors.backgroundColor }}>
                    <DeletePostActionView postSeq={postSeq} navigation={navigation}
                        deletePostActionPress={(clickId, obj) => deletePostActionPress(clickId, obj)}
                    />
                </ScrollView>
            </ActionSheet >
        </View>
    )
}

export default ThreeDotMenuActionView

const styles = theme => StyleSheet.create({
    popupBox: {
        paddingHorizontal: 16,
        paddingBottom: 16,
        backgroundColor: theme.colors.backgroundColor
    },
    popupDivider: {
        height: 0.5,
        backgroundColor: theme.colors.threeDotMenuDivider,
        opacity: 0.5,
    },
    popupListRowBox: {

    },
    linkBox: {
        paddingVertical: 15,
    },
    linkTxt: {
        color: theme.colors.threeDotMenuText,
        fontSize: theme.calculateFontSize(theme.dimensions.threeDotMenuText),

    },
})
