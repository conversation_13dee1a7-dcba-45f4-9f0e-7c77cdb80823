import React, { useEffect, useState } from 'react'
import { Image, StyleSheet, TouchableOpacity, View, RefreshControl } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import EntutoTextView from '../components/common/EntutoTextView'
import HeadingTxt from '../components/common/HeadingTxt'
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox'
import StoryComponent from '../components/story/StoryComponent'
import SuggestionProfile from '../components/SuggestionProfile'
import { _RedirectionErrorList } from '../utils/Appconfig'
import { RedirectionUrlFunction } from '../utils/RedirectionUrl'
import ServerConnector from '../utils/ServerConnector';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import CustomStatusBar from '../components/common/CustomStatusBar';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';

const SearchScreen = ({ route, navigation }) => {
    const [suggestionList, setsuggestionList] = useState([]);
    const [listRefresh, setlistRefresh] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setshowLoading] = useState(true);
    useEffect(() => {
        setshowLoading(true);
        getSuggestionsService();
    }, []);
    const renderSuggestionItem = ({ item }) => {
        return (
            <SuggestionProfile data={item} navigation={navigation} />
        );
    };
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const searchBtnClick = () => {
        navigation.navigate("SearchResultScreen");
    }
    function getSuggestionsService() {
        let hashMap = {
            _action_code: "11:GET_PROFILE_SUGGESTIONS",
            _start_row: 0,
            _rows_page: 50,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setlistRefresh(false);
            setshowLoading(false);
            setsuggestionList([...[], ...data.data]);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                setlistRefresh(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setlistRefresh(false);
                setshowLoading(false);
                setsuggestionList([]);
                seterrorMsg(errorMessage);
            }
        });
    }
    const handleRefresh = () => {
        setlistRefresh(true);
        setshowLoading(true);
        getSuggestionsService();
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={defaultStyle.container}>
                <TouchableOpacity onPress={() => searchBtnClick()}>
                    <View style={style.searchBarBox}>
                        <View
                            style={style.searchBar}>
                            <Image
                                source={require('../assets/Images/icon/search_icon.png')}
                                style={style.searchIcon}
                                resizeMode='cover'
                            />
                            <View style={style.input}>
                                <EntutoTextView style={{ color: 'grey', fontSize: theme.dimensions.searchTextInputSize }}>
                                    Search by name, username or category!
                                </EntutoTextView>
                            </View>
                        </View>
                    </View>
                </TouchableOpacity>
            </View>
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>

                {
                    showLoading ?
                        <NotiRowPlaceholder />
                        :
                        <FlatList
                            contentContainerStyle={{ paddingBottom: 20 }}
                            data={suggestionList}
                            ListHeaderComponent={
                                <View style={defaultStyle.container}>
                                    <View style={{ marginVertical: 15, }}>
                                        <StoryComponent navigation={navigation} refreshPage={listRefresh} />
                                    </View>
                                    <View style={{ marginTop: 8, }}>
                                        <HeadingTxt>Suggestions</HeadingTxt>
                                    </View>
                                </View>
                            }
                            ListEmptyComponent={
                                <View style={defaultStyle.container}>
                                    <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                                </View>
                            }
                            initialNumToRender={4}
                            renderItem={renderSuggestionItem}
                            keyExtractor={(item, index) => `${index}`}
                            refreshControl={
                                <RefreshControl refreshing={listRefresh} onRefresh={() => handleRefresh()} />
                            } />
                }
            </View>

        </>
    )
}

export default SearchScreen;

const styles = theme => StyleSheet.create({
    searchBarBox: {
        justifyContent: "flex-start",
        alignItems: "center",
        flexDirection: "row",
    },
    searchBar: {
        flex: 5,
        marginTop: 8,
        flexDirection: "row",
        backgroundColor: theme.colors.extraBackgroundColor,
        borderRadius: theme.dimensions.searchBarRadius,
        alignItems: "center",
        paddingVertical: 15,
    },
    searchIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        marginLeft: theme.dimensions.searchInputIconMH,
    },
    input: {
        fontSize: theme.dimensions.searchTextInputSize,
        marginHorizontal: 8,
        flex: 1,

    },

})
