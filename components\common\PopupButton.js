import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { TouchableOpacity } from 'react-native'
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import EntutoTextView from './EntutoTextView';

export const PopupPositiveButton = ({
    btnText = "",
    onPress,
    disabled = false,

}) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    return (
        <View>
            <TouchableOpacity
                disabled={disabled}
                onPress={onPress}
                style={{
                    ...defaultStyle.popupBtn,
                    backgroundColor: disabled ? theme.colors.disableSubmitBtnBackground : theme.colors.submitBtnBackground
                }}>
                <EntutoTextView style={{
                    ...defaultStyle.popupBtnText,
                    color: theme.colors.submitBtnText,
                }}>
                    {btnText}
                </EntutoTextView>
            </TouchableOpacity>
        </View>
    )
}

export const PopupNegativeButton = ({
    btnText = "",
    onPress,
    disabled = false,
    style = {}

}) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    return (
        <View>
            <TouchableOpacity
                disabled={disabled}
                onPress={onPress}
                style={{
                    ...defaultStyle.popupBtn,
                    borderWidth: 1,
                    borderColor: theme.colors.cancelBtnBorder,
                    backgroundColor: theme.colors.cancelBtnBackground,
                    ...style
                }}>
                <EntutoTextView style={{
                    ...defaultStyle.popupBtnText,
                    color: theme.colors.cancelBtnText,
                }}>
                    {btnText}
                </EntutoTextView>
            </TouchableOpacity>
        </View>
    )
}




const styles = StyleSheet.create({})