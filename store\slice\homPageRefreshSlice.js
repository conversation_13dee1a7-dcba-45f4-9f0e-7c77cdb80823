import { createSlice } from '@reduxjs/toolkit'

export const homPageRefreshSlice = createSlice({
    name: 'homPageRefreshSlice',
    initialState: {
        value: {
            refresh: "NO",
        },
    },
    reducers: {
        refreshHomePage: (state, action) => {
            state.value.refresh = action.payload;
        },

    },
});

// Action creators are generated for each case reducer function
export const { refreshHomePage } = homPageRefreshSlice.actions

export default homPageRefreshSlice.reducer