import React, { useContext, useEffect, useRef, useState } from 'react'
import { ActivityIndicator, Image, Modal, PermissionsAndroid, Platform, Pressable, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import EntutoEditText from '../components/common/EntutoEditText'
import EntutoSwitch from '../components/common/EntutoSwitch';
import EntutoTextView from '../components/common/EntutoTextView';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import Slider from '@react-native-community/slider';
import HeadingTxt from '../components/common/HeadingTxt';
import DateTimePicker from '@react-native-community/datetimepicker';
import { dateDbFormat, DateDisplayFormat, dbTimeFormat, decodeHtmlEntitessData, encodeHtmlEntitessData, isVideo, _inputFormatTextForTag, } from '../utils/Utils';
import CustomStatusBar from '../components/common/CustomStatusBar';
import { AppStateContext } from '..';
import ServerConnector from '../utils/ServerConnector';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import { CurrencySymbol, DEFAULT_MAX_FILE_SIZE, TAGGED_SYMBOL, UserHandlePrefix, _RedirectionErrorList } from '../utils/Appconfig';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import MimeTypeList from '../utils/MimeTypeList';
import CustomSnackbar from '../components/common/CustomSnackbar';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import appData from '../data/Data';
import { FAB, Menu } from 'react-native-paper';
import ConfirmationPopup from '../components/common/ConfirmationPopup';
import Colors from '../constants/Colors';
import { getRealPath } from 'react-native-compressor';
import { moveFile, stat, TemporaryDirectoryPath, unlink as deleteFile } from 'react-native-fs';
import ImagePicker from 'react-native-image-crop-picker';
import ErrorMessages from '../constants/ErrorMessages';
import { FlatList } from 'react-native-gesture-handler';
import TagPeopleRow from '../components/tag/TagPeopleRow';
import VirtualizedList from '../components/common/VirtualizedList';
import InputTagPeopleSearchComponent from '../components/tag/InputTagPeopleSearchComponent';
import Video from 'react-native-video';
import { requestStoragePermission } from '../utils/PermissionManager';
import useDefaultStyle from '../theme/useDefaultStyle';

const thumb = require('../assets/Images/icon/slider_thumb.png');
const AddPostScreen = ({ route, navigation }) => {
    const { defaultStyle } = useDefaultStyle();
    const [description, setdescription] = useState("");
    const [selection, setSelection] = useState("FREE");
    const [isSchdule, setisSchdule] = useState(false);
    const [sliderValue, setsliderValue] = useState(9);
    const [sliderDefaultValue, setsliderDefaultValue] = useState(20);
    const [date, setDate] = useState(new Date());
    const [mode, setMode] = useState('date');
    const [show, setShow] = useState(false);
    const [showExpiryDate, setshowExpiryDate] = useState(false);
    const { captureMedia, changeCaptureMedia, fullUserDetails, selectedTagProfileList,
        changeTagProfileList, changeUserDetails } = useContext(AppStateContext);
    const [mediaData, setmediaData] = useState(null);
    const [errorMsg, seterrorMsg] = useState("");
    const [refresfErrorMsg, setrefresfErrorMsg] = useState(Math.random());
    let __hasBankDetails = fullUserDetails.hasOwnProperty("_has_bank_details") ? fullUserDetails._has_bank_details : "NO";
    let __is_profile_verified = fullUserDetails.hasOwnProperty("_is_profile_verified") ? fullUserDetails._is_profile_verified == "YES" ? true : false : false;
    let __is_profile_paid = fullUserDetails.hasOwnProperty("_user_account_type") ? fullUserDetails._user_account_type == "PAID" ? true : false : false;
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const __MAX_FILE_SIZE = fullUserDetails.hasOwnProperty("_max_file_size") ? fullUserDetails._max_file_size : DEFAULT_MAX_FILE_SIZE;
    const mediaSizeErrorMsg = `Oh no! Your content exceeds the temporary ${__MAX_FILE_SIZE}MB limit. Please try posting something else?`
    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [snackBarType, setsnackBarType] = useState("FAILED");
    const [showLoading, setShowLoading] = useState(false);

    const [showConfirmPopup, setshowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());

    const [showAccountVerification, setshowAccountVerification] = useState(false);
    const [isGalleryBtnPress, setisGalleryBtnPress] = useState(false);

    const [disabledBtn, setdisabledBtn] = useState(true);

    const [tagPList, settagPList] = useState([]);

    const tagPeopleRef = useRef(null);

    const [currentPointPos, setcurrentPointPos] = useState(0);

    const [showTagPopup, setshowTagPopup] = useState(false);
    const [tagName, settagName] = useState("");
    const descInputRef = useRef(null);
    let videoDestUri = null;
    useEffect(() => {
        __hasBankDetails = fullUserDetails.hasOwnProperty("_has_bank_details") ? fullUserDetails._has_bank_details : "NO";
        __is_profile_verified = fullUserDetails.hasOwnProperty("_is_profile_verified") ? fullUserDetails._is_profile_verified == "YES" ? true : false : false;
        __is_profile_paid = fullUserDetails.hasOwnProperty("_user_account_type") ? fullUserDetails._user_account_type == "PAID" ? true : false : false;

    }, [fullUserDetails]);


    useEffect(() => {
        changeCaptureMedia(null);
        changeTagProfileList([]);
        setShowLoading(false);
        var future = new Date();
        future.setDate(future.getDate() + 30);
        setDate(future);
        if (!__is_profile_verified) {
            getUserProfileService();
        }
    }, [])

    useEffect(() => {
        setsliderValue(sliderDefaultValue);
    }, []);
    useEffect(() => {
        if (captureMedia != null) {
            setdisabledBtn(false)
            seterrorMsg("");
        }
        setmediaData(captureMedia);
    }, [captureMedia]);
    useEffect(() => {
        settagPList([...[], ...selectedTagProfileList]);
    }, [selectedTagProfileList]);
    const descriptionChangeHandler = (text) => {
        let txtNewLineArr = text.split("\n");
        let lastNewLineWord = txtNewLineArr[txtNewLineArr.length - 1];
        let textStArr = lastNewLineWord.split(" ");
        let lastIndex = textStArr.length - 1;
        let lastWord = textStArr[lastIndex]
        if (lastWord == TAGGED_SYMBOL) {//.charAt(0)
            setshowTagPopup(true);
            settagName(lastWord);
        }
        else {
            settagName("");
            setshowTagPopup(false);
        }

        setdescription(text)
        setdisabledBtn(false)
        seterrorMsg("");
    }
    const onToggleSwitch = () => {
        setisSchdule(!isSchdule);
        setdisabledBtn(false)
    }
    const onToggleExpirySwitch = () => {
        setshowExpiryDate(!showExpiryDate);
        setdisabledBtn(false)
    }
    const onChange = (event, selectedDate) => {
        const currentDate = selectedDate || date;
        setShow(Platform.OS === 'ios');
        setDate(currentDate);
        setdisabledBtn(false)
        seterrorMsg("");
    };

    const showMode = (currentMode) => {
        setShow(true);
        setMode(currentMode);
        setdisabledBtn(false)
    };

    const showDatepicker = () => {
        showMode('date');
    };

    const showTimepicker = () => {
        showMode('time');
    };
    const removeMediaBox = () => {
        changeCaptureMedia(null);
        seterrorMsg("");
    }
    const postBtnPress = async () => {
        seterrorMsg("");

        setrefresfErrorMsg(Math.random());
        if (selection == "PAID") {
            if (__hasBankDetails == "NO") { //TODO
                setshowConfirmPopup(true);
                setshowConfirmPopupKey(Math.random())
                return;
            }
        }
        if (description.length == 0) {
            if (selection != "FREE" || __is_profile_paid) {
                seterrorMsg(ErrorMessages.addPostCaptionErr);
                setrefresfErrorMsg(Math.random());
                return;
            }
        }
        if (captureMedia == null) {
            seterrorMsg(ErrorMessages.addPostNoMediaErr);
            setrefresfErrorMsg(Math.random());
            return;
        }

        setShowLoading(true);
        if (mediaData.captureType == "VIDEO") {
            if (isGalleryBtnPress) {
                validateVideoFile(mediaData.uri);
            }
            else {
                const statResult = await stat(mediaData.uri);
                // console.log("Second Result", statResult.size);
                let fileMaxSize = __MAX_FILE_SIZE * 1024 * 1000;
                if (statResult.size <= fileMaxSize) {
                    validateVideoFile(mediaData.uri);
                }
                else {
                    seterrorMsg(mediaSizeErrorMsg);
                    setrefresfErrorMsg(Math.random());
                    setShowLoading(false);
                }
            }

        }
        else {
            submitPost(mediaData.uri, mediaData.captureType);
        }


    }
    function submitPost(mediaUriValue, mediaUriType) {
        let fileFormat = "";
        let dbfileFormat = "";
        setdisabledBtn(true)
        if (isGalleryBtnPress) {
            if (mediaUriType == "IMAGE") {
                let parts = mediaUriValue.split('.');
                fileFormat = parts[parts.length - 1];
                dbfileFormat = fileFormat;
                // let mType = mediaData.imageData.type.split('/');
                // let mTypeVal = mType[mType.length - 1];
                // fileFormat = mTypeVal;

            }
            else {
                let parts = mediaUriValue.split('.');
                fileFormat = parts[parts.length - 1];
                dbfileFormat = fileFormat;
                if (fileFormat.toUpperCase() == "MOV") {
                    dbfileFormat = "QUICKTIME";
                }
            }
        }
        else {
            let parts = mediaData.uri.split('.');
            fileFormat = parts[parts.length - 1];
            dbfileFormat = fileFormat
        }

        let viewerFee = 0;
        if (selection != "FREE") {
            viewerFee = sliderValue;
        }
        setShowLoading(true);
        let encodeSt = encodeHtmlEntitessData(description);
        //Tag Part
        let taggedUsers = [];
        tagPList.map(obj => {
            taggedUsers.push({ profile_seq: obj.profile_seq });

        })
        let hashMap = {
            _action_code: "11:SUBMIT_POST",
            file_format: dbfileFormat.toUpperCase(),
            media_type: mediaUriType,
            comments: encodeURIComponent(encodeSt),
            post_type: selection,
            viewer_fee: viewerFee,
        }
        if (showExpiryDate) {
            hashMap.expire_on = dateDbFormat(date);
        }

        let captureFile = {
            uri: Platform.OS === 'android' ? mediaUriValue : mediaUriValue.replace('file://', ''),
            name: "media_" + dateDbFormat(new Date()) + "." + fileFormat,
            type: MimeTypeList[fileFormat]
        };
        let imageHashMap = [];
        if (mediaData != null) {
            imageHashMap.push({ inputName: "File1", imageData: captureFile });
        }
        if (taggedUsers.length != 0) {
            hashMap.tagged_users = encodeURIComponent(JSON.stringify(taggedUsers));
        }
        // console.log("imageHashMap", imageHashMap)
        // console.log("hashMap", hashMap);

        let connector = new ServerConnector();
        connector.postDataMultiPart(hashMap, imageHashMap, (data) => { // success method

            // setSnackbarMsg(data.msg);
            // setdisplaySnackbar(true);
            // setsnackBarType("SUCCESS");
            // setrefreshSnackBar(Math.random()); //Changes 19/01/24
            changeCaptureMedia(null);
            changeTagProfileList([]);
            setMode("FREE");
            setdescription("");
            setshowExpiryDate(false);
            setShowLoading(false);
            // appData.__HomePageRefresh = Math.random();
            appData._profilePagePostRefresh = true;
            if (videoDestUri != null) {
                deleteTempFile(videoDestUri);
            }
            setTimeout(() => {
                // navigation.replace("HomeScreen");
                navigation.replace("AddPostSuccessScreen", {
                    postSeq: data.data.post_seq
                });
            }, 500);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setdisabledBtn(false)
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.comments) {
                            seterrorMsg(data.data.comments);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                        if (data.data.file_format) {
                            seterrorMsg(data.data.file_format);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                        if (data.data.media_type) {
                            seterrorMsg(data.data.media_type);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                        if (data.data.post_type) {
                            seterrorMsg(data.data.post_type);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                        if (data.data.viewer_fee) {
                            seterrorMsg(data.data.viewer_fee);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage);
                    setrefresfErrorMsg(Math.random());
                }

            }
        });
    }
    const mediaBtnPress = () => {
        setisGalleryBtnPress(true);
        launchImageLibraryData();
    }
    const launchImageLibraryData = async () => {
        const checkPermission = requestStoragePermission();
        checkPermission.then(res => {
            if (res) {
                openImageFolder();
            }
        })

    }
    const openImageFolder = () => {

        setShowLoading(true);

        var options = {
            mediaType: 'mixed', //to allow only photo to select ...no video
            // saveToPhotos: true,  //to store captured photo via camera to photos or else it will be stored in temp folders and will get deleted on temp clear
            includeBase64: false,
            videoQuality: 'low',
            selectionLimit: 1,

        };
        launchImageLibrary(options, (response) => {

            if (response.didCancel) {
                //console.log('User cancelled image picker');
                setShowLoading(false);
            } else if (response.error) {
                setShowLoading(false);
                //console.log('ImagePicker Error: ', response.error);
            } else if (response.customButton) {
                setShowLoading(false);
                //console.log('User tapped custom button: ', response.customButton);
            } else {
                // if (response.assets) {
                //     //console.log("First Res", response);
                //     const source = response.assets[0];
                //     let uri = source.uri;
                //     compressVideoData(uri);
                // }
                setShowLoading(false);
                if (response.hasOwnProperty("assets")) {
                    if (response.assets[0].fileSize <= 153600000) { //150MB //30 MB  30720000
                        displayTheImageFile(response);
                    }
                    else {
                        if (response.assets[0].hasOwnProperty("fileSize")) {
                            if (response.assets[0].fileSize == null || response.assets[0].fileSize == 0) {
                                displayTheImageFile(response, true);
                            }
                            else {
                                seterrorMsg(ErrorMessages.addPostMediaSizeErr);
                                setrefresfErrorMsg(Math.random());
                            }
                        }
                        else {
                            displayTheImageFile(response, true);
                        }
                    }
                }
                else {
                    setSnackbarMsg("Somethings went wrong!");
                    setsnackBarType("FAILED");
                    setdisplaySnackbar(true);
                    setrefreshSnackBar(Math.random());
                }
            }
        });
    }
    const displayTheImageFile = (response, errorHappen = false) => {
        let captureType = "IMAGE";
        let responseUri = response.assets[0].uri;
        let mType = response.assets[0].type.split('/');
        // let mTypeVal = mType[mType.length - 1]
        let mTypeVal = mType[0]
        // console.log("response", response.assets[0])
        // console.log("respons2e", mTypeVal)
        if (mTypeVal === 'video') {
            captureType = "VIDEO";
        }
        // console.log("captureType", captureType)
        // console.log("captureImage", response)
        if (captureType == "IMAGE") {
            if (errorHappen) {
                changeCaptureMedia({ uri: responseUri, captureType: "IMAGE", imageData: response.assets[0] });
            }
            else {
                corpGalleryImage(responseUri);
            }
        }
        else {
            changeCaptureMedia({ uri: responseUri, captureType: captureType, imageData: response.assets[0] });

        }
    }
    const playVideo = () => {
        navigation.navigate("VideoDisplayScreen", {
            mediaUri: mediaData.uri,
            thumbnailUri: null
        })

    }
    const showImage = () => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: mediaData.uri
        })

    }
    const imageBtnPress = () => {
        if (mediaData.captureType == "IMAGE") {
            showImage();
        }
        if (mediaData.captureType == "VIDEO") {
            playVideo();
        }
    }
    const confirmPopupClick = (clickID, data) => {
        if (clickID == "positive") {
            navigation.navigate("AccountInfoScreen", {
                showBankDetails: "YES",
            });
        }
    }
    const selectionTypeChange = (type) => {
        if (!__is_profile_verified) {
            if (type === "PAID") {
                setshowAccountVerification(true);
                return;
            }
        }
        setSelection(type)
    }
    const verificationBtnPress = () => {
        navigation.navigate('VerificationsScreen');
    }
    const cameraBtnPress = () => {
        setisGalleryBtnPress(false);
        navigation.navigate("CameraScreen", {
            cameraType: "ALL", cameFrom: "POST"
        })
    }
    const validateVideoFile = async (sourceVideo) => {

        let videouri = sourceVideo;
        if (sourceVideo.startsWith('content://')) {
            let mType = mediaData.imageData.type.split('/');
            let fileFormat = mType[mType.length - 1]
            if (fileFormat.toUpperCase() == "QUICKTIME") {
                fileFormat = "mov";
            }
            let fileName = dateDbFormat(new Date()) + "." + fileFormat;
            const destPath = `${TemporaryDirectoryPath}/${fileName}`;
            moveFile(sourceVideo, destPath)
                .then(() => {
                    videouri = destPath;//"file://" +
                    uploadToServer(videouri);

                })
                .catch((err) => {
                    // console.log(err.message);
                    seterrorMsg(ErrorMessages.addPostMediaInvalidErr);
                    setrefresfErrorMsg(Math.random());
                    setShowLoading(false);
                });

        }
        else {
            uploadToServer(videouri);
        }
    }
    const uploadToServer = async (videoUri) => {
        videoDestUri = videoUri;
        const realPath = await getRealPath(videoUri, 'video');
        submitPost(realPath, "VIDEO");
    }
    const corpGalleryImage = (imgUrl) => {
        // if (Platform.OS == "ios") {
        //     imgUrl = imgUrl.replace('file://', '')
        //     changeCaptureMedia({ uri: imgUrl, captureType: "IMAGE", imageData: {} });
        // }
        // else {
        setTimeout(() => {
            ImagePicker.openCropper({
                freeStyleCropEnabled: false,
                cropping: false,
                width: 1280,
                height: 1280,
                path: imgUrl,
            }).then(image => {
                changeCaptureMedia({ uri: image.path, captureType: "IMAGE", imageData: {} });
            }).catch(err => {
                // console.log(err)
            });
        }, 500);

        // }
    }
    const onTagPeoplePress = () => {
        if (captureMedia != null) {
            navigation.navigate('TagPeopleScreen');
        }
        else {
            seterrorMsg(ErrorMessages.imageNeedForTagErr);
            setrefresfErrorMsg(Math.random());
        }
    }
    const tagLeftBtnPress = () => {
        let indexCount = currentPointPos - 1;
        if (indexCount < 0) {
            indexCount = 0;
        }
        setcurrentPointPos(indexCount);
        tagPeopleRef.current?.scrollToIndex({ animated: true, index: indexCount });
    }
    const tagRightBtnPress = () => {
        let indexCount = currentPointPos + 1;
        if (indexCount > tagPList.length - 1) {
            indexCount = tagPList.length - 1;
        }
        setcurrentPointPos(indexCount);
        tagPeopleRef.current?.scrollToIndex({ animated: true, index: indexCount });
    }
    const tagPeopleSearchPress = (clickID, obj) => {
        setshowTagPopup(false);
        if (clickID == "SELECTED") {
            let formatTxt = _inputFormatTextForTag(description, TAGGED_SYMBOL + obj.user_handle);
            setshowTagPopup(false);
            setdescription(formatTxt + " ");
            descInputRef.current.focus()
        }
    }
    function getUserProfileService() {
        let hashMap = {
            _action_code: "11:GET_USER_PROFILE",
            req_profile_seq: __ProfileSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let hasBankDetails = "NO";
            if (data.data[0].bank_account !== null) {
                if (data.data[0].bank_account.length !== 0) {
                    hasBankDetails = "YES";
                }
            }
            let userDeatails = {
                _has_bank_details: hasBankDetails,
                _is_profile_verified: data.data[0].is_verified,
            };
            changeUserDetails(userDeatails);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
            }
        });
    }
    const deleteTempFile = (tempuri) => {
        try {
            // console.log("Deletion tempuri", tempuri);
            deleteFile(tempuri)
                .then(() => {
                    // console.log('FILE DELETED', tempuri);
                    if (tempuri == videoDestUri) {
                        videoDestUri = null;
                    }
                })
        } catch (error) {
            // console.log("DeletionErr", error);
        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar title="New Post" showTopButton={true} navigation={navigation}
                buttonComponent={<TouchableOpacity disabled={disabledBtn} onPress={() => { postBtnPress() }}
                ><EntutoTextView style={{ opacity: disabledBtn ? 0.4 : 1, ...defaultStyle.postBtn }}>POST</EntutoTextView></TouchableOpacity>} />
            {
                showAccountVerification ?
                    <View style={styles.verificationBox}>
                        <EntutoTextView>{ErrorMessages.accVerifiedFlushMsg}</EntutoTextView>
                        <TouchableOpacity onPress={() => verificationBtnPress()}><EntutoTextView style={{ color: Colors.primaryColor }}> Click here for verification</EntutoTextView>
                        </TouchableOpacity>
                    </View>
                    : null
            }
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside}>
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refresfErrorMsg} />
                    // </View>
                    : null
            }

            <VirtualizedList
                nestedScrollEnabled={true}>
                <View style={defaultStyle.container}>
                    <View style={styles.addMediaBox}>
                        {
                            mediaData != null ?
                                <View>

                                    {
                                        mediaData.captureType == "VIDEO" ?
                                            <View>
                                                <TouchableOpacity onPress={() => imageBtnPress()}>
                                                    <View style={{ position: 'relative', height: 200 }}>
                                                        <Video source={{ uri: mediaData.uri }}
                                                            pointerEvents={'none'}
                                                            style={styles.addMediaImage}
                                                            paused={true}
                                                            controls={false}
                                                        />
                                                        <View style={{
                                                            position: 'absolute', top: 0, left: 0, right: 0, bottom: 0,
                                                            justifyContent: 'center', alignItems: 'center', zIndex: 999
                                                        }}>
                                                            <FAB
                                                                style={{ backgroundColor: '#FFFFFF', }}
                                                                small
                                                                icon="play-circle-outline"
                                                            />
                                                        </View>
                                                    </View>

                                                </TouchableOpacity>
                                            </View>
                                            :
                                            <TouchableOpacity onPress={() => imageBtnPress()}>
                                                <View>

                                                    <Image
                                                        style={styles.addMediaImage}
                                                        source={{ uri: mediaData.uri }}
                                                        resizeMode='cover' />
                                                </View>
                                            </TouchableOpacity>
                                    }



                                    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                                        <TouchableOpacity onPress={() => removeMediaBox()}>
                                            <EntutoTextView style={styles.removeMediaTxt}>Remove Media</EntutoTextView>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                :
                                <View style={{ flexDirection: 'row', }}>
                                    <EntutoTextView style={styles.addMediaTxt}>
                                        Add Media
                                    </EntutoTextView>
                                    <View style={{ marginLeft: 'auto', flexDirection: 'row', }}>
                                        <TouchableOpacity onPress={() => mediaBtnPress()}>
                                            <Image
                                                source={require('../assets/Images/icon/addmedia.png')}
                                                resizeMode={'cover'}
                                                style={styles.addMediaIcon} />
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => cameraBtnPress()}>
                                            <Image
                                                source={require('../assets/Images/icon/addcamera.png')}
                                                resizeMode={'cover'}
                                                style={styles.addMediaIcon} />
                                        </TouchableOpacity>
                                    </View>
                                </View>
                        }
                    </View>
                    <View style={{ ...styles.addNewDesc, marginBottom: 20 }}>
                        <EntutoEditText
                            refValue={descInputRef}
                            multiline
                            placeholderTxt="Add New Description"
                            labelTxt="Add New Description"
                            value={description}
                            maxLength={1000}
                            maxHeight={150}
                            onChangeText={(text) => descriptionChangeHandler(text)} />
                        {/* <View style={{ flexDirection: 'row', flexWrap: 'wrap', borderWidth: 1, }}> */}

                        {/* {
                            description.split("\n").map((newTxt, j) => {
                                return <View key={j}>
                                    <EntutoTextView>
                                        {
                                            newTxt.split(" ").map((text, i) => {
                                                if (text.charAt(0) == "@") {
                                                    return <EntutoTextView key={i} style={defaultStyle.boldTagTxt}>{text + " "}</EntutoTextView>
                                                }
                                                else {
                                                    return text + " ";
                                                }
                                            })
                                        }
                                    </EntutoTextView>
                                </View>
                            })
                        } */}
                        <View style={defaultStyle.inputUnderCountBox}>
                            <EntutoTextView style={defaultStyle.inputUnderCountBoxTxt}>{description.length}/1000</EntutoTextView>
                        </View>

                    </View>
                    {
                        !__is_profile_paid ?
                            <View style={styles.chooseOptionBox}>
                                <EntutoTextView style={styles.addMediaTxt}>
                                    Choose Option
                                </EntutoTextView>
                                <View style={styles.optionBox}>
                                    <View style={styles.btnGroup}>
                                        <TouchableOpacity
                                            style={[styles.btn, selection === "FREE" ? { backgroundColor: "#F3997B" } : null]}
                                            onPress={() => selectionTypeChange("FREE")}>
                                            <Text style={[styles.btnText, selection === "FREE" ? { color: "#FFFFFF" } : null]}>Free</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={[styles.btn, selection === "PAID" ? { backgroundColor: "#F3997B", } : null, { marginLeft: 10 }]}
                                            onPress={() => selectionTypeChange("PAID")}>
                                            <Text style={[styles.btnText, selection === "PAID" ? { color: "#FFFFFF" } : null]}>Paid</Text>
                                        </TouchableOpacity>

                                    </View>
                                </View>
                            </View>
                            : null
                    }

                    {
                        selection === "PAID" ?
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <View style={{ flex: 1, }}>
                                    <Slider
                                        style={{ ...styles.slider, }}
                                        minimumValue={9}
                                        maximumValue={49}
                                        minimumTrackTintColor="#F3997B"
                                        maximumTrackTintColor="#000000"
                                        // thumbTintColor="#F3997B"]
                                        tapToSeek={true}
                                        thumbImage={thumb}
                                        step={1}

                                        value={sliderValue}
                                        onValueChange={(value) => { setsliderValue(value) }}
                                        onSlidingComplete={(value) => { setsliderValue(value) }}
                                    />
                                </View>
                                <View style={{
                                    // borderColor: '#F3997B',
                                    // borderWidth: 1, padding: 8,
                                    // borderRadius: 5,
                                    // backgroundColor: '#F3997B',
                                    width: 80,
                                    alignItems: 'center',
                                }}>
                                    <HeadingTxt style={{ color: '#43180B', fontSize: 12 }}>Set Price</HeadingTxt>
                                    <HeadingTxt style={{ color: '#F3997B', fontSize: 17 }}>{CurrencySymbol} {sliderValue}.00</HeadingTxt>
                                </View>


                            </View>
                            : null

                    }
                    {
                        !__is_profile_paid ?
                            <View style={{ borderBottomColor: '#ccc', borderBottomWidth: 1 }} />
                            : null
                    }
                    <View style={{ ...styles.expiryBox, overflow: 'hidden', }}>
                        <Pressable
                            onPress={() => onTagPeoplePress()}
                            android_ripple={{ color: Colors.pressableRippleColor, borderless: false }}>
                            <View style={{ flexDirection: 'row', }}>
                                <EntutoTextView style={styles.addMediaTxt}>
                                    Tag People
                                </EntutoTextView>
                            </View>
                        </Pressable>
                    </View>
                    {
                        tagPList.length != 0 ?
                            <View style={{ flexDirection: 'row', marginBottom: 10, marginTop: 10 }}>
                                <TouchableOpacity
                                    onPress={() => tagLeftBtnPress()}>
                                    <View style={styles.tagIconBox}>
                                        <Image style={styles.tagLeftIcon}
                                            resizeMode="contain"
                                            source={require('../assets/Images/icon/Arrow.png')} />
                                    </View>
                                </TouchableOpacity>
                                <FlatList
                                    ref={tagPeopleRef}
                                    style={{ marginHorizontal: 6, }}
                                    horizontal
                                    showsHorizontalScrollIndicator={false}
                                    data={tagPList}
                                    renderItem={({ item: rowData, index }) => {
                                        let extraTxt = ", ";
                                        if (index == (tagPList.length - 1)) {
                                            extraTxt = "";
                                        }
                                        return (
                                            <View key={index}>
                                                <EntutoTextView style={styles.tagTxt}>{UserHandlePrefix}{rowData.user_handle}
                                                    {extraTxt}
                                                </EntutoTextView>
                                            </View>
                                        );
                                    }}
                                    keyExtractor={(item, index) => `${index}`}
                                />
                                <TouchableOpacity
                                    onPress={() => tagRightBtnPress()}>
                                    <View style={styles.tagIconBox}>
                                        <Image style={styles.tagRightIcon}
                                            resizeMode="contain"
                                            source={require('../assets/Images/icon/Arrow.png')} />
                                    </View>
                                </TouchableOpacity>
                            </View>
                            :
                            <View style={{ marginBottom: 16 }} />
                    }

                    <View style={{ borderBottomColor: Colors.borderBottomColor, borderBottomWidth: 1 }} />

                    <View style={styles.expiryBox}>
                        <View style={{ flexDirection: 'row' }}>
                            <EntutoTextView style={styles.addMediaTxt}>
                                Expiry Post
                            </EntutoTextView>
                            <View style={{ marginLeft: 'auto' }}>
                                <EntutoSwitch value={showExpiryDate} onValueChange={() => onToggleExpirySwitch()} />

                            </View>
                        </View>
                        <View style={defaultStyle.listUnderLineView}>
                            <EntutoTextView style={defaultStyle.listUnderLineTxt}>
                                Your post automatically gets deleted on the set date, tell your fans don’t be late!
                            </EntutoTextView>
                        </View>

                        {show && (
                            <DateTimePicker
                                testID="dateTimePicker"
                                value={date}
                                mode={'date'}
                                is24Hour={true}
                                display="default"
                                onChange={onChange}
                            />
                        )}
                    </View>
                    {
                        showExpiryDate ?
                            <View style={{ ...styles.dateBox, marginVertical: 8 }}>
                                <EntutoTextView style={{ ...styles.dateTxt, flex: 2 }}>{DateDisplayFormat(date)}</EntutoTextView>
                                <TouchableOpacity
                                    style={[styles.btn, { backgroundColor: "#F3997B", }]}
                                    onPress={() => showDatepicker()}>
                                    <Text style={[styles.btnText, { color: "#FFFFFF" }]}>Change Date</Text>
                                </TouchableOpacity>
                            </View>
                            : null
                    }


                    {/* <View style={styles.chooseOptionBox}>
                    <EntutoTextView style={styles.addMediaTxt}>
                        Scheduled Post
                    </EntutoTextView>
                    <View style={{ marginLeft: 'auto' }}>
                        <EntutoSwitch value={isSchdule} onValueChange={onToggleSwitch} />
                    </View>
                </View>
                <View style={{ borderBottomColor: '#ccc', borderBottomWidth: 1 }} /> */}

                </View>

            </VirtualizedList>

            {
                showConfirmPopup &&
                <ConfirmationPopup
                    visiblePopupKey={showConfirmPopupKey}
                    visiblePopup={showConfirmPopup}
                    title="Confirmation"
                    messagebody={ErrorMessages.paidPostBankDetailsConfirmMsg}
                    positiveButton="Yes"
                    negativeButton="No"
                    data={{}}
                    popupClick={(clickID, data) => { confirmPopupClick(clickID, data) }}
                />
            }
            <Modal
                animationType="fade"
                visible={showTagPopup}
                onRequestClose={() => setshowTagPopup(false)}
                style={{ margin: 0, flex: 1, zIndex: 1 }}>
                <InputTagPeopleSearchComponent
                    tagPeopleSearchPress={tagPeopleSearchPress}
                    navigation={navigation}
                    preSearchStr={tagName}
                    captionTxt={description} />
            </Modal>
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />

        </>
    )
}

export default AddPostScreen

const styles = StyleSheet.create({

    addMediaBox: {
        flexDirection: 'column',
        backgroundColor: '#F2EBE9',
        borderRadius: 5,
        paddingHorizontal: 10,
        paddingVertical: 22,

    },
    addMediaTxt: {
        fontSize: 17,
        color: '#000000',
        fontWeight: '700'
    },

    addMediaIcon: {
        width: 24,
        height: 24,
        marginHorizontal: 10,
    },
    addNewDesc: {
        marginVertical: 20,
    },
    chooseOptionBox: {
        flexDirection: 'row',
        paddingHorizontal: 10,
        paddingVertical: 18,
        alignItems: 'center',
        marginTop: 10,
    },
    optionBox: {
        marginLeft: 'auto',
        width: 160,

    },
    btnGroup: {
        flexDirection: 'row',
        alignItems: "center",
    },
    btn: {
        flex: 1,
        backgroundColor: '#F2EBE9',
        width: 74,
        borderRadius: 5,
    },
    btnText: {
        textAlign: 'center',
        paddingVertical: 7,
        fontSize: 15,
        color: '#43180B'
    },
    slider: {
        width: 'auto',
        opacity: 1,
        height: 50,
        marginTop: 10,

    },
    expiryBox: {
        flexDirection: 'column',
        paddingHorizontal: 10,
        paddingTop: 18,
    },
    dateBox: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingBottom: 18,

    },
    dateTxt: {
        flex: 1,
        fontSize: 17,
        color: '#F3997B',
        fontWeight: 'bold'
    },
    addMediaImage: {
        width: '100%',
        height: 200,
        marginBottom: 15,
        zIndex: 99,
        // borderRadius: 15,
    },
    removeMediaTxt: {
        color: '#FF4963',
        paddingHorizontal: 10,

    },
    errorBoxTxt: {
        color: "#000",
        textAlign: 'center',
        fontSize: 17,
    },
    verificationBox: {
        backgroundColor: 'rgb(232, 244, 253)',
        color: 'rgb(13, 60, 97)',
        paddingHorizontal: 20,
        paddingVertical: 15,
        marginVertical: 8,
        flexDirection: 'row',
        flexWrap: 'wrap',

    },
    tagTxt: {
        fontSize: 17,
        color: '#F3997B',
        fontWeight: 'bold'
    },
    tagIconBox: {
        // borderWidth: 1,
        padding: 0,
        margin: 0,
    },
    tagLeftIcon: {
        height: 24,
        width: 24,
    },
    tagRightIcon: {
        height: 24,
        width: 24,
        transform: [{ rotateY: '180deg' }]
    },

})
