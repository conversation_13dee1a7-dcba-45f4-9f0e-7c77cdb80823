import React, { useEffect, useState } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import EntutoTextView from '../common/EntutoTextView';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import BottomSheetLoader from '../common/BottomSheetLoader';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { _clearAllData } from '../../utils/AuthLogin';
import ErrorMessages from '../../constants/ErrorMessages';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import { PopupNegativeButton, PopupPositiveButton } from '../common/PopupButton';

const DeleteStoryActionView = ({ profileSeq, storySeq, ...props }) => {

    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [showLoading, setshowLoading] = useState(false);
    const [showSuccessMsg, setshowSuccessMsg] = useState(false);
    const [successMsg, setsuccessMsg] = useState("");

    const [errorMsg, seterrorMsg] = useState("");

    const [popupHeading, setpopupHeading] = useState("Delete Story");
    const [submitBtnTxt, setsubmitBtnTxt] = useState("Confirm");
    const [confirmTxt, setconfirmTxt] = useState(ErrorMessages.storyDeleteConfirmMsg);

    const cancelBtnPress = () => {
        props.deletePostActionPress("negetive", {})
    }
    const closeBtnClick = () => {
        props.deletePostActionPress("close", { erMsg: successMsg })
    }

    const confirmDeleteBtnPress = () => {
        setshowLoading(true);
        deletePostService();


    }
    function deletePostService() {
        let hashMap = {
            _action_code: "11:DELETE_STORY",
            story_seq: storySeq
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setshowLoading(false);
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.story_seq) {
                            seterrorMsg(data.data.story_seq)
                            return;
                        }
                    }

                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                }
            }
        });
    }
    return (
        <View>
            <View style={defaultStyle.popupBox}>
                <EntutoTextView style={defaultStyle.popupHeadTxt}>{popupHeading}</EntutoTextView>
                <EntutoTextView style={{ ...defaultStyle.popupBodyTxt, marginBottom: 40, marginTop: 20, }}>
                    {confirmTxt}
                </EntutoTextView>
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                {
                    showSuccessMsg ?
                        <BottomSheetSuccessMsg successMsg={successMsg} cancelBtnClick={() => closeBtnClick()} />
                        : null
                }

                {
                    errorMsg.length != 0 ?
                        // <View style={defaultStyle.errorBoxOutside}>
                        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                        // </View>
                        : null
                }
                <View style={{ flexDirection: 'row', flex: 1, marginTop: 16, marginBottom: 16, }}>
                    <View style={{ flex: 1 }}>
                        <PopupNegativeButton
                            onPress={() => cancelBtnPress()}
                            btnText='No'
                            style={{ marginEnd: theme.dimensions.popupBtnGap }} />

                    </View>
                    <View style={{ flex: 1 }}>
                        <PopupPositiveButton
                            onPress={() => confirmDeleteBtnPress()}
                            btnText={submitBtnTxt} />

                    </View>
                </View>
            </View>
        </View>
    )
}

export default DeleteStoryActionView;

const styles = StyleSheet.create({})
