import { Pressable, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import Colors from '../../constants/Colors';
import EntutoTextView from './EntutoTextView';
import { TouchableOpacity } from 'react-native-gesture-handler';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';

const EntutoFlushMessage = ({ viewStyle, textStyle, text, closeButtonClick, showCloseBtn = false, }) => {
    const closeBtnPress = () => {
        closeButtonClick(clikID);
    }
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    return (
        <View style={{ ...style.flushMessageBox, ...viewStyle }}>
            <View style={{ flex: 9, }}>
                <EntutoTextView style={{ ...style.flushMessageBoxTxt, ...textStyle }}>{text}</EntutoTextView>
            </View>
            {
                showCloseBtn ?
                    <View style={style.flushMessageBoxCloseIcon}>
                        <Pressable onPress={() => closeBtnPress()}
                            style={({ pressed }) => [
                                {
                                    backgroundColor: pressed
                                        ? theme.colors.pressableColor
                                        : 'transparent'
                                },
                                { borderRadius: 8, }
                            ]}>
                            <View>
                                <MaterialIcons
                                    color={"#111111"}
                                    name="close"
                                    size={24}
                                />
                            </View>

                        </Pressable>
                    </View>
                    : null
            }

        </View>
    );
};

export default EntutoFlushMessage;

const styles = theme => StyleSheet.create({
    flushMessageBox: {
        padding: 10,
        backgroundColor: theme.colors.extraBackgroundColor,
        position: 'relative',
        borderRadius: 8,
        marginVertical: 8,
        flexDirection: 'row',
        flex: 1,
        alignItems: 'center'
    },
    flushMessageBoxTxt: {
        color: '#111111'
    },
    flushMessageBoxCloseIcon: {
        flex: 1,
        alignItems: 'center',
        alignContent: 'flex-end',
        justifyContent: 'flex-end',
    },
    wrapperCustom: {
        borderRadius: 8,
    }

});
