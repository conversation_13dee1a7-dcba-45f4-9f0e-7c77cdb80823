import {
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import {FlatList} from 'react-native-gesture-handler';
import SuccessFailureMsgBox from './SuccessFailureMsgBox';
import EntutoTextView from './EntutoTextView';
import CustomStatusBar from './CustomStatusBar';
import HeadingTxt from './HeadingTxt';
import CustomSnackbar from './CustomSnackbar';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import ProgressiveImage from './ProgressiveImage';
import {hasImageUrlExist} from '../../utils/Utils';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

const SelectBoxComponent = ({
  list,
  selectedValue,
  title,
  labelField = 'config_key',
  valueField = 'display_value',
  maxSelectedValue,
  multiSelect,
  selectBoxClick,
  isEmotions = false, // New prop to identify if we're handling emotions
}) => {
  const {defaultStyle} = useDefaultStyle();
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);
  const [searchQuery, setsearchQuery] = useState('');
  const [isTyped, setisTyped] = useState(false);
  const [errorMsg, seterrorMsg] = useState('');
  const [selectDataList, setselectDataList] = useState([]);
  const [selectDataListB, setselectDataListB] = useState([]);
  const [selectedV, setselectedV] = useState([]);

  const [SnackbarMsg, setSnackbarMsg] = useState('');
  const [displaySnackbar, setdisplaySnackbar] = useState(false);
  const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
  const modalFlashRef = useRef(null);

  useEffect(() => {
    let selV = [];
    if (multiSelect) {
      selV = selectedValue;
    } else {
      selV.push(selectedValue);
    }

    list.map(obj => {
      obj.isChecked = false;
      // For emotions, use value directly as config_key and display_value
      if (isEmotions) {
        obj.config_key = obj.value;
        obj.display_value = obj.value;
      } else {
        obj.config_key = obj[valueField];
        obj.display_value = obj[labelField];
      }

      if (selV.includes(obj.config_key)) {
        obj.isChecked = true;
      }
    });

    setselectedV(selV);
    setselectDataList(list);
    setselectDataListB(list);
  }, [list, selectedValue]);

  const onChangeSearch = query => {
    let currentList = [];
    let newList = [];
    if (query.length != 0) {
      setisTyped(true);
      currentList = selectDataListB;
      newList = currentList.filter(obj => {
        const lc = obj.display_value.toLowerCase();
        const filter = query.toString().toLowerCase();
        return lc.includes(filter);
      });
    } else {
      newList = selectDataListB;
      setisTyped(false);
    }
    setselectDataList([...[], ...newList]);
    if (newList.length == 0) {
      seterrorMsg(
        'While we review this personalised genre for you, please select your remaining preferences. \n We will get back to you shortly!',
      );
    } else {
      seterrorMsg('');
    }
    setsearchQuery(query);
  };

  const clearSearchTxt = () => {
    setsearchQuery('');
    seterrorMsg('');
    setselectDataList([...[], ...selectDataListB]);
    setisTyped(false);
  };

  const doneButtonPress = () => {
    if (multiSelect) {
      selectBoxClick('DONE', {selectedItem: selectedV});
    } else {
      let selValue = '';
      if (selectedV.length != 0) {
        selValue = selectedV[0];
      }
      selectBoxClick('DONE', {selectedItem: selValue});
    }
  };

  const goBackPrevious = () => {
    selectBoxClick('BACK', {});
  };

  const listRowClick = (value, isChecked) => {
    let listData = selectDataListB;
    let selectedVTemp = selectedV;
    if (isChecked && multiSelect && selectedVTemp.length >= maxSelectedValue) {
      let errMsg = `Maximum ${maxSelectedValue} selection allowed.`;
      setSnackbarMsg(errMsg);
      setdisplaySnackbar(true);
      setrefreshSnackBar(Math.random());
      return;
    }
    let selV = [];
    listData.map(obj => {
      if (multiSelect) {
        if (obj.config_key == value) {
          obj.isChecked = isChecked;
        }
      } else {
        obj.isChecked = false;
        if (obj.config_key == value) {
          obj.isChecked = isChecked;
        }
      }
      if (obj.isChecked) {
        selV.push(obj.config_key);
      }
    });
    setselectedV([...[], ...selV]);
    setdisableUpdateBtn(false);
  };
  const renderEmptyRow = () => {
    return (
      <View
        style={{
          overflow: 'hidden',
          borderBottomColor: '#00000050',
          borderBottomWidth: 0.5,
        }}>
        <Pressable
          android_ripple={{
            color: theme.colors.pressableRippleColor,
            borderless: true,
          }}
          onPress={() => doneButtonPress()}>
          <View style={{...defaultStyle.ListCardStyle, ...style.selectRowBox}}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <EntutoTextView style={style.emptyRowBoxTxt}>
                {searchQuery}
              </EntutoTextView>
            </View>
            <View
              style={{
                marginLeft: 'auto',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Image
                source={require('../../assets/Images/icon/add_img_btn.png')}
                style={style.addIcon}
              />
            </View>
          </View>
        </Pressable>
      </View>
    );
  };
  const renderSelectRow = ({item}) => {
    return (
      <View
        style={{
          overflow: 'hidden',
          borderBottomColor: '#00000050',
          borderBottomWidth: 0.5,
        }}>
        <Pressable
          android_ripple={{
            color: theme.colors.pressableRippleColor,
            borderless: true,
          }}
          onPress={() => listRowClick(item.config_key, !item.isChecked)}>
          <View style={{...defaultStyle.ListCardStyle, ...style.selectRowBox}}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              {/* For emotions, display the icon */}
              {isEmotions && item.inactivePath ? (
                <Image
                  style={style.emotionIcon}
                  source={item.inactivePath}
                  resizeMode={'contain'}
                />
              ) : item.hasOwnProperty('image') ? (
                <ProgressiveImage
                  style={style.profilePageIcon}
                  source={
                    hasImageUrlExist(item.image) ? {uri: item.image} : null
                  }
                  defaultImageSource={require('../../assets/Images/full_user_image_place_holder.png')}
                  resizeMode={'cover'}
                />
              ) : null}

              <EntutoTextView style={style.selectRowBoxTxt}>
                {item.display_value}
              </EntutoTextView>
            </View>
            <View
              style={{
                marginLeft: 'auto',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              {selectedV.includes(item.config_key) ? (
                <Image
                  style={style.iconTick}
                  source={require('../../assets/Images/icon/Round_Tick_Coloured.png')}
                />
              ) : null}
            </View>
          </View>
        </Pressable>
      </View>
    );
  };

  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const handleShowKeyboard = e => {
    const height = e.endCoordinates.height;
    setKeyboardHeight(height);
  };

  const handleHideKeyboard = () => {
    setKeyboardHeight(0);
  };

  React.useEffect(() => {
    if (Platform.OS == 'android') {
      return;
    }
    const keyboardDidShowSubscribtion = Keyboard.addListener(
      'keyboardDidShow',
      handleShowKeyboard,
    );
    const keyboardWillHideSubscribtion = Keyboard.addListener(
      'keyboardWillHide',
      handleHideKeyboard,
    );
    return () => {
      Keyboard.removeSubscription?.(keyboardDidShowSubscribtion);
      Keyboard.removeSubscription?.(keyboardWillHideSubscribtion);
    };
  }, []);

  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <View style={[style.appBar, style.borderBottom]}>
        <View style={{paddingHorizontal: 8, zIndex: 1000}}>
          <TouchableOpacity onPress={() => goBackPrevious()}>
            <View style={{paddingHorizontal: 12}}>
              <Image
                style={style.arrowIcon}
                resizeMode="cover"
                source={require('../../assets/Images/icon/Arrow.png')}
              />
            </View>
          </TouchableOpacity>
        </View>

        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <HeadingTxt>{title}</HeadingTxt>
        </View>
        <View style={{marginLeft: 'auto'}}>
          <TouchableOpacity
            onPress={() => doneButtonPress()}
            disabled={disableUpdateBtn}>
            <EntutoTextView
              style={{
                ...defaultStyle.postBtn,
                opacity: disableUpdateBtn ? 0.4 : 1,
              }}>
              Done
            </EntutoTextView>
          </TouchableOpacity>
        </View>
      </View>
      <View
        style={{
          flex: 1,
          backgroundColor: theme.colors.backgroundColor,
        }}>
        <View
          style={{
            ...defaultStyle.container,
            backgroundColor: theme.colors.backgroundColor,
            paddingTop: 15,
          }}>
          <View style={style.searchBarBox}>
            <View style={style.searchBar}>
              <TextInput
                style={style.input}
                placeholder="Search..."
                placeholderTextColor={theme.colors.inputPlaceholderColor}
                value={searchQuery}
                autoCorrect={false}
                onChangeText={onChangeSearch}
                selectionColor={theme.colors.primaryColor}
              />
              {isTyped && (
                <TouchableOpacity onPress={() => clearSearchTxt()}>
                  <Image
                    source={require('../../assets/Images/icon/close_icon.png')}
                    style={style.searchCrossIcon}
                  />
                </TouchableOpacity>
              )}
              <Image
                source={require('../../assets/Images/icon/search_icon.png')}
                style={style.searchIcon}
              />
            </View>
          </View>
        </View>
        <View>
          {Platform.OS == 'android' ? (
            <FlatList
              data={selectDataList.length > 0 || !isTyped ? selectDataList : []}
              renderItem={renderSelectRow}
              keyExtractor={(item, index) => `${index}`}
              initialNumToRender={30}
              maxToRenderPerBatch={30}
              updateCellsBatchingPeriod={50}
              windowSize={21}
              removeClippedSubviews={true}
              scrollEventThrottle={16}
              onEndReachedThreshold={0.5}
              contentContainerStyle={{
                paddingBottom: 20,
              }}
              keyboardShouldPersistTaps={'handled'}
              ListEmptyComponent={
                isTyped && searchQuery.length > 0 ? renderEmptyRow : null
              }
            />
          ) : null}
          <View style={{}}>
            {errorMsg.length != 0 ? (
              <SuccessFailureMsgBox
                visibleAllTime={true}
                alertMsg={errorMsg}
                alertKey={errorMsg}
              />
            ) : null}
          </View>
          {Platform.OS == 'ios' ? (
            <KeyboardAvoidingView behavior="height" enabled={true}>
              <FlatList
                keyboardShouldPersistTaps={'handled'}
                contentContainerStyle={{paddingBottom: keyboardHeight + 80}}
                data={selectDataList}
                renderItem={renderSelectRow}
                keyExtractor={(item, index) => `${index}`}
              />
            </KeyboardAvoidingView>
          ) : null}
        </View>
      </View>
      <CustomSnackbar
        snackMsg={SnackbarMsg}
        displaySnackbar={displaySnackbar}
        refreshSnack={refreshSnackBar}
        showInsideFlashRef={true}
        insideFlashRef={modalFlashRef}
      />
    </>
  );
};

export default SelectBoxComponent;

const styles = theme =>
  StyleSheet.create({
    searchBarBox: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    addIcon: {
      height: 24,
      width: 24,
      tintColor: theme.colors.primaryColor,
    },
    searchBar: {
      flex: 1,
      flexDirection: 'row',
      backgroundColor: theme.colors.backgroundColor,
      borderRadius: 1,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.searchBoxBorder,
      minHeight: 48,
    },
    searchIcon: {
      height: theme.dimensions.searchInputIconH,
      width: theme.dimensions.searchInputIconH,
      marginRight: theme.dimensions.searchInputIconMH,
      tintColor: theme.colors.selectBoxSearchIconTintColor,
    },
    iconTick: {
      height: 25,
      width: 25,
    },
    emotionIcon: {
      height: 32,
      width: 32,
      marginRight: 16,
    },
    input: {
      fontSize: theme.calculateFontSize(theme.dimensions.selectBoxText),
      marginHorizontal: 8,
      flex: 1,
      color: theme.colors.inputTextColor,
    },
    searchCrossIcon: {
      height: theme.dimensions.searchInputIconH,
      width: theme.dimensions.searchInputIconH,
      marginRight: 8,
      tintColor: theme.colors.selectBoxSearchIconTintColor,
    },
    selectRowBox: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 0,
      marginTop: 0,
      height: 60,
    },
    selectRowBoxTxt: {
      color: theme.colors.primaryTextColor,
      fontSize: theme.calculateFontSize(theme.dimensions.selectBoxText),
      fontWeight: '600',
      paddingVertical: 15,
      minWidth: 200,
    },
    emptyRowBoxTxt: {
      fontSize: theme.calculateFontSize(theme.dimensions.selectBoxText),
      fontWeight: '600',
      paddingVertical: 15,
      minWidth: 200,
      color: theme.colors.primaryColor,
    },
    appBar: {
      height: 56,
      flexDirection: 'row',
      alignItems: 'center',
      paddingRight: 8,
      backgroundColor: theme.colors.backgroundColor,
    },
    borderBottom: {
      borderBottomWidth: 0.5,
      borderBottomColor: theme.colors.topHeaderBottomColor,
    },
    arrowIcon: {
      height: 20,
      width: 12,
      tintColor: theme.colors.topHeaderColor,
    },
    tickIcon: {
      height: 24,
      width: 24,
      tintColor: theme.colors.primaryColor,
    },
    profilePageBox: {
      height: 40,
      width: 40,
      borderRadius: 20,
      marginRight: 16,
    },
    profilePageIcon: {
      height: 40,
      width: 40,
      borderRadius: 20,
      resizeMode: 'cover',
      marginEnd: 14,
    },
  });
