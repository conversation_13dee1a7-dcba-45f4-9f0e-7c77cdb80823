import React from 'react'
import { Image, StyleSheet, View } from 'react-native'
import EntutoTextView from './EntutoTextView'
import useSThemedStyles from '../../theme/useSThemedStyles';

const GoogleSignButton = props => {
    const style = useSThemedStyles(styles);
    return <View
        style={{ ...style.button, ...props.style }}>
        <Image
            source={require('../../assets/Images/icon/google_icon.png')}
            style={style.ImageIconStyle}
        />
        <EntutoTextView style={style.btnTxt}>{props.label}</EntutoTextView>
    </View>
}

export default GoogleSignButton;

const styles = theme => StyleSheet.create({
    button: {
        backgroundColor: theme.colors.googleSignBtnBackground,
        borderRadius: 14,
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 18,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
    },
    ImageIconStyle: {
        width: 23,
        height: 23,
        position: 'absolute',
        top: 19,
        left: 25,
    },
    btnTxt: {
        fontSize: theme.calculateFontSize(20),
        color: theme.colors.googleSignBtnText,
        fontWeight: '600',
        marginLeft: 10,

    },

})
