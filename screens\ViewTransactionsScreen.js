import React, { useEffect, useState } from 'react'
import { RefreshControl, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { FlatList } from 'react-native-gesture-handler';
import CustomStatusBar from '../components/common/CustomStatusBar';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import MySubscriptionProfileRow from '../components/subscribers/MySubscriptionProfileRow';
import { DefaultRowsPerPage, _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import PostCard from '../components/post/PostCard'
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import appData from '../data/Data';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const ViewTransactionsScreen = ({ navigation }) => {
    const [selectedTab, setselectedTab] = useState("POST");
    const [subsPostList, setsubsPostList] = useState([]);
    const [subsProfileList, setsubsProfileList] = useState([]);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [progressLoading, setprogressLoading] = useState(true);
    const RowsPerPage = DefaultRowsPerPage;
    const [errorMsg, seterrorMsg] = useState("");
    useEffect(() => {
        setprogressLoading(true);
        getPostSubsService(0, RowsPerPage, true);
    }, []);
    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            if (appData._homePagePostRefresh == "YES") {
                appData._homePagePostRefresh = "NO";
                setprogressLoading(true);
                getPostSubsService(0, RowsPerPage, true)
            }


        });
        return unsubscribe;
    }, [navigation]);
    const postCardClick = (clickID, obj) => {

    }
    const renderPostItem = ({ item }) => {
        return (
            <PostCard itemData={item} navigation={navigation} isForceUnlock={true} isMyProfile={false} postCardClick={postCardClick} />
        );
    };
    const renderProfileItem = ({ item }) => {
        return (
            <MySubscriptionProfileRow navigation={navigation} data={item} />
        );
    };
    const onTabChange = (item) => {
        seterrorMsg("");
        setselectedTab(item);
        setprogressLoading(true);
        if (item == "PROFILE") {
            getProfileSubsService(0, RowsPerPage, true)
        }
        else {
            getPostSubsService(0, RowsPerPage, true)
        }
    }
    const handleRefresh = () => {

    }

    function getPostSubsService(startRecord, rowsPerPage, isFirstSearch) {
        let hashMap = {
            _action_code: "11:GET_SUBSCRIBED_POSTS",
            _start_row: startRecord,
            _rows_page: rowsPerPage,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            seterrorMsg("");
            data.data.map(item => item.is_subscribed = "YES");
            setsubsPostList(data.data);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (isFirstSearch) {
                    setprogressLoading(false);
                    seterrorMsg(errorMessage);
                    setsubsPostList([]);
                }

            }
        });
    }
    function getProfileSubsService(startRecord, rowsPerPage, isFirstSearch) {
        let hashMap = {
            _action_code: "11:GET_SUBSCRIBED_PROFILES",
            type: "ACTIVE",
            _start_row: startRecord,
            _rows_page: rowsPerPage,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            seterrorMsg("");
            setsubsProfileList(data.data);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (isFirstSearch) {
                    setprogressLoading(false);
                    seterrorMsg(errorMessage);
                    setsubsProfileList([]);
                }

            }
        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="View Transactions" showBackBtn={true} navigation={navigation}
                showBorderBottom={false} />

            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside} >
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        : null
                }
                {
                    selectedTab == "POST" ?
                        <>
                            {
                                progressLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <FlatList
                                        contentContainerStyle={{ paddingBottom: 20 }}
                                        data={subsPostList}
                                        renderItem={renderPostItem}
                                        keyExtractor={(item, index) => index.toString()}
                                        refreshControl={
                                            <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                                        }
                                    />
                            }

                        </>
                        : null

                }
                {
                    selectedTab == "PROFILE" ?
                        <>
                            {
                                progressLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <FlatList
                                        contentContainerStyle={{ paddingBottom: 20 }}
                                        data={subsProfileList}
                                        renderItem={renderProfileItem}
                                        keyExtractor={(item, index) => index.toString()}
                                        refreshControl={
                                            <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                                        }
                                    />
                            }
                        </>
                        : null

                }
            </View>
        </>
    )
}

export default ViewTransactionsScreen;

const styles = StyleSheet.create({

})
