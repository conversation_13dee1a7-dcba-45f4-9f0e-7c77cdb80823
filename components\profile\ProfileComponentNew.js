import {
  FlatList,
  Image,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useCallback, useContext, useEffect, useState} from 'react';
import useSTheme from '../../theme/useSTheme';
import {AppStateContext} from '../..';
import useDefaultStyle from '../../theme/useDefaultStyle';
import appData from '../../data/Data';
import ServerConnector from '../../utils/ServerConnector';
import {_RedirectionErrorList, _UnauthErrorList} from '../../utils/Appconfig';
import {RedirectionUrlFunction} from '../../utils/RedirectionUrl';
import {AuthValidation} from '../../utils/AuthValidation';
import {hasImageUrlExist} from '../../utils/Utils';
import EntutoTextView from '../common/EntutoTextView';
import LinearGradient from 'react-native-linear-gradient';
import DescriptionCaptionStyle from '../common/DescriptionCaptionStyle';
import Dimensions from '../../constants/Dimensions';
import Colors from '../../constants/Colors';
import useSThemedStyles from '../../theme/useSThemedStyles';
import ThreeDotIcon from '../../assets/Images/icon/three_dot.png';
import BackBtn from '../../assets/Images/icon/back.png';
import LockIcon from '../../assets/Images/icon/post_lock.png';

const ProfileComponentNew = ({
  isOthersProfile,
  profileSeq,
  navigation,
  ...props
}) => {
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const {fullUserDetails, changeUserDetails} = useContext(AppStateContext);
  let __has_state_city = fullUserDetails.hasOwnProperty('_has_state_city')
    ? fullUserDetails._has_state_city
    : 'NO';
  const {defaultStyle} = useDefaultStyle();
  useEffect(() => {
    __has_state_city = fullUserDetails.hasOwnProperty('_has_state_city')
      ? fullUserDetails._has_state_city
      : 'NO';
  }, [fullUserDetails]);
  const [userDetails, setUserDetails] = useState({
    profileImg: null,
    postsCount: 0,
    showPostCount: false,
    fanCount: 0,
    followingCount: 0,
    showFollowingCount: false,
    subsCount: 0,
    showSubsCountBox: false,
  });
  const DefaultTab = 'POST';
  const [selectedTab, setSelectedTab] = useState(DefaultTab);
  const [mainSelectedTab, setMainSelectedTab] = useState(DefaultTab);

  useEffect(() => {
    setMainSelectedTab(selectedTab);
  }, [selectedTab]);

  const [postList, setPostList] = useState([]);
  useEffect(() => {
    appData._copyLinkProfileSeq = profileSeq;
    getUserProfileService();
  }, []);
  async function getUserProfileService(serviceCall = 'ALL') {
    // API CALL
    let hashMap = {
      _action_code: '11:GET_USER_PROFILE',
      req_profile_seq: profileSeq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        const profileData = data.data[0];

        let profileImg = null;
        if (profileData.profile_picture !== null) {
          if (profileData.profile_picture.length !== 0) {
            profileImg = profileData.profile_picture;
          }
        }
        setUserDetails(prevState => ({
          ...prevState,
          profileImg: profileImg,
        }));
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          if (_UnauthErrorList.includes(errorCode)) {
            AuthValidation(errorCode, data, navigation);
          }
        }
      },
    );
  }
  const onTabChange = item => {
    if (item !== 'PLAYLIST') {
      setSelectedTab(item);
    }
    // console.log("onTabChange", selectedTab)
    // seterrorMsg("");
    // setrefreshKey(Math.random());
    // setlistRefresh(true);
    // getProfilePostListService(item, 0, RowsPerPage);
    if (item == 'PLAYLIST') {
      navigation.navigate('PlaylistShowScreen');
    }
  };
  const threeDotMenuBtnPress = () => {};
  const backBtnProfileClick = () => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate('HomeScreen');
    }
  };

  const HeaderComponent = () => {
    return (
      <>
        <View style={{position: 'relative', zIndex: 1, height: 340}}>
          {/* <Image
            source={
              hasImageUrlExist(userDetails.profileImg)
                ? {uri: userDetails.profileImg}
                : null
            }
            style={style.profileImageCover}
          /> */}
          <View style={style.profileBackBtn}>
            <View>
              <TouchableOpacity
                onPress={() => backBtnProfileClick()}
                style={{paddingHorizontal: 2, paddingVertical: 8}}>
                <Image
                  style={style.headerOptionIcon}
                  resizeMode="contain"
                  source={BackBtn}
                />
              </TouchableOpacity>
            </View>
          </View>
          <View style={style.profileThreeDot}>
            <TouchableOpacity
              style={{paddingLeft: 10}}
              onPress={() => threeDotMenuBtnPress()}>
              <View>
                {!isOthersProfile ? (
                  <Image
                    style={{...style.headerOptionIcon, tintColor: '#FFF'}}
                    resizeMode="contain"
                    // source={require('../../assets/Images/icon/myprofile_setting.png')}
                    source={ThreeDotIcon}
                  />
                ) : (
                  <Image
                    style={style.headerOptionIcon}
                    resizeMode="contain"
                    source={ThreeDotIcon}
                  />
                )}
              </View>
            </TouchableOpacity>
          </View>
          <LinearGradient
            colors={['#00000000', '#111111']}
            style={style.linearGradient}
          />
          <View style={style.profileCountBox}>
            <TouchableOpacity>
              <CountItemBox label={'Post'} count={26} />
            </TouchableOpacity>
            <TouchableOpacity>
              <CountItemBox label={'Fans'} count={26} />
            </TouchableOpacity>
            <TouchableOpacity>
              <CountItemBox label={'Following'} count={26} />
            </TouchableOpacity>
          </View>
          <View style={style.profileWithActionBtnBox}>
            <View style={style.profileBox}>
              <EntutoTextView style={style.profileName}>
                Sidhanth Kakar
              </EntutoTextView>
            </View>
            <View>
              <TouchableOpacity style={style.profileActionBtn}>
                <EntutoTextView style={style.profileActionBtnText}>
                  FOLLOWING
                </EntutoTextView>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <View style={style.container}>
          <DescriptionCaptionStyle
            validHandleList={[]}
            mainText={'Bio will come here'}
            mainTextStyle={style.profileBio}
            highlightStyle={defaultStyle.boldTagTxt}
          />
        </View>
        <View style={style.tabBox}>
          <CategoryItemRow
            selectedTab={selectedTab}
            tabLabel={'Posts'}
            tabValue={'POST'}
            onTabChange={onTabChange}
          />
          <CategoryItemRow
            selectedTab={selectedTab}
            tabLabel={'Tags'}
            tabValue={'TAGGED'}
            onTabChange={onTabChange}
          />
          <CategoryItemRow
            selectedTab={selectedTab}
            tabLabel={'Playlist'}
            tabValue={'PLAYLIST'}
            onTabChange={onTabChange}
          />
        </View>

        <View style={style.lockBtnBox}>
          <Image source={LockIcon} style={style.lockIcon} />
          <View>
            <EntutoTextView style={style.lockBtnBoxText}>
              SUBSCRIBE FOR ₹99
            </EntutoTextView>
          </View>
        </View>
      </>
    );
  };
  const renderItem = useCallback(
    ({item}) => {
      return <View style={style.listItem}></View>;
    },
    [postList],
  );
  const keyExtractor = useCallback(item => `${item.id}`);
  return (
    <View style={{backgroundColor: theme.colors.backgroundColor, flex: 1}}>
      <FlatList
        contentContainerStyle={{paddingBottom: 80}}
        removeClippedSubviews
        data={postList}
        ListHeaderComponent={HeaderComponent}
        initialNumToRender={4}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
      />
    </View>
  );
};

export default ProfileComponentNew;
const CountItemBox = ({count, label}) => {
  const style = useSThemedStyles(styles);
  return (
    <View style={{justifyContent: 'center', alignItems: 'center'}}>
      <EntutoTextView style={style.countValue}>{count}</EntutoTextView>
      <EntutoTextView style={style.countLabel}>{label}</EntutoTextView>
    </View>
  );
};
const CategoryItemRow = ({selectedTab, tabValue, tabLabel, onTabChange}) => {
  const style = useSThemedStyles(styles);
  const theme = useSTheme();
  return (
    <TouchableOpacity
      style={[
        style.categoryLabel,
        selectedTab === tabValue
          ? {
              borderBottomWidth: theme.dimensions.tabBorderBottomWidth,
              borderBottomColor: theme.colors.primaryColor,
            }
          : null,
      ]}
      onPress={() => onTabChange(tabValue)}>
      <Text
        style={[
          style.categoryLabelText,
          selectedTab === tabValue
            ? {
                color: theme.colors.primaryColor,
              }
            : null,
        ]}
        allowFontScaling={false}>
        {tabLabel}
      </Text>
    </TouchableOpacity>
  );
};

const styles = theme =>
  StyleSheet.create({
    container: {
      paddingHorizontal: 24,
    },
    profileImageCover: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      width: '100%',
      height: 340,
      resizeMode: 'cover',
    },
    profileCountBox: {
      position: 'absolute',
      top: 220,
      left: 24,
      right: 24,
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    countValue: {
      fontSize: 20,
      color: '#FFFFFF',
      fontWeight: 'bold',
    },
    countLabel: {
      fontSize: 14,
      color: '#FFFFFF',
    },
    linearGradient: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    profileWithActionBtnBox: {
      position: 'absolute',
      top: 290,
      left: 24,
      right: 24,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    profileBox: {
      flexDirection: 'row',
    },
    profileName: {
      fontSize: 25,
      color: '#FFFFFF',
    },
    profileActionBtn: {
      borderRadius: 5,
    },
    profileActionBtnText: {
      fontSize: 14,
      color: theme.colors.primaryColor,
    },
    profileBio: {
      fontSize: 13,
      color: '#91909A',
    },
    tabBox: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 12,
      paddingHorizontal: 15,
      marginBottom: 16,
    },
    categoryLabel: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 20,
      marginHorizontal: 24,
    },
    categoryLabelText: {
      color: theme.colors.profileCatInActiveColor,
      fontSize: 14,
      fontWeight: 'bold',
      paddingVertical: 12,
      flexWrap: 'nowrap',
    },
    profileBackBtn: {
      position: 'absolute',
      left: 24,
      top: 46,
      zIndex: 1,
    },
    profileThreeDot: {
      position: 'absolute',
      right: 24,
      top: 46,
      zIndex: 1,
    },
    headerOptionIcon: {
      width: 24,
      height: 24,
      tintColor: '#FFF',
    },
    lockBtnBox: {
      marginTop: 80,
      alignItems: 'center',
      justifyContent: 'center',
    },
    lockIcon: {
      width: 67,
      height: 83,
      resizeMode: 'contain',
    },
    lockBtnBoxText: {
      color: theme.colors.primaryColor,
      fontSize: 19,
      marginTop: 32,
    },
  });
