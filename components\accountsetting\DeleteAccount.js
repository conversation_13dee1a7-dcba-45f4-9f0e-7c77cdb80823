import React, { useEffect, useState } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import EntutoTextView from '../common/EntutoTextView';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import BottomSheetLoader from '../common/BottomSheetLoader';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { _clearAllData } from '../../utils/AuthLogin';
import { Dropdown } from 'react-native-element-dropdown';
import EntutoEditText from '../common/EntutoEditText';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import ErrorMessages from '../../constants/ErrorMessages';
import { CommonActions } from '@react-navigation/native';
import appData from '../../data/Data';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import EntutoDropdown from '../common/EntutoDropdown';
import { PopupNegativeButton, PopupPositiveButton } from '../common/PopupButton';


const DeleteAccount = ({ refVal, navigation, ...props }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [reasonVal, setreasonVal] = useState("");
    const [reasonValFocus, setreasonValFocus] = useState(false);
    const [reasonData, setreasonData] = useState([]);

    const [comment, setcomment] = useState("");
    const [commentErr, setcommentErr] = useState("");

    const [showLoading, setshowLoading] = useState(true);
    const [showSuccessMsg, setshowSuccessMsg] = useState(false);
    const [successMsg, setsuccessMsg] = useState("");

    const [errorMsg, seterrorMsg] = useState("");

    const [isSubmitDisable, setisSubmitDisable] = useState(true);

    useEffect(() => {
        getPostCategoryService();
    }, [])

    const cancelBtnPress = () => {
        props.deleteAccountActionClick("negetive", {})
    }
    const closeBtnClick = () => {
        _clearAllData();
        appData._userDetails = null;
        navigation.dispatch(
            CommonActions.reset({
                index: 1,
                routes: [
                    {
                        name: 'LoginScreen',
                        params: { ErrorMsg: successMsg },
                    },
                ],
            })
        );
        // navigation.replace("LoginScreen", {
        //     ErrorMsg: successMsg,
        // });
        // props.deleteAccountActionClick("negetive", {})
    }
    const dropdownLabelBox = (placeholderTxt, dropValue, valueFocus) => {
        if (dropValue || valueFocus) {
            return (
                <Text
                    style={[defaultStyle.dropdownLabel,
                    valueFocus && { color: theme.colors.primaryColor }]}>
                    {placeholderTxt}
                </Text>
            );
        }
        return null;
    };
    const confirmDeleteBtnPress = () => {
        seterrorMsg("");
        let isFormValid = true;
        if (reasonVal.length === 0) {
            seterrorMsg(ErrorMessages.delAccResasonErr);
            isFormValid = false;
        }
        if (comment.length === 0) {
            setcommentErr(ErrorMessages.delAccCommentErr)
            isFormValid = false;
        }
        if (isFormValid) {
            setshowLoading(true);
            deleteAccountService();
        }
    }
    function getPostCategoryService() {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: "DELETE_OPTION",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setreasonData(data.data);
            setshowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setreasonData([]);
                setshowLoading(false);
            }
        });
    }
    function deleteAccountService() {
        let hashMap = {
            _action_code: "11:DELETE_ACCOUNT",
            delete_reason: reasonVal,
            delete_comments: comment,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setshowLoading(false);
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                }
            }
        });
    }
    const resonValueChangeHandler = (item) => {
        setreasonVal(item.config_key);
    }
    const commentChangeHandler = (text) => {
        setcomment(text);
        if (text.length != 0) {
            setisSubmitDisable(false);
        }
        else {
            setisSubmitDisable(true);
        }

    }
    return (
        <View>
            <View style={defaultStyle.popupBox}>
                <EntutoTextView style={defaultStyle.popupHeadTxt}>Delete Account</EntutoTextView>
                {/* <EntutoTextView style={{ ...defaultStyle.popupBodyTxt, marginBottom: 40, marginTop: 20, }}>
                    Are you sure you want to delete the account?
                </EntutoTextView> */}
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                {
                    showSuccessMsg ?
                        <BottomSheetSuccessMsg successMsg={successMsg} cancelBtnClick={() => closeBtnClick()} />
                        : null
                }

                {
                    errorMsg.length != 0 ?
                        // <View style={defaultStyle.errorBoxOutside}>
                        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                        // </View>
                        : null
                }
                <View style={{ ...defaultStyle.dropdownContainer, flexGrow: 1 }}>
                    <EntutoDropdown label='Reason' placeholder='Select Reason'
                        labelField="display_value"
                        valueField="config_key"
                        value={reasonVal}
                        options={reasonData}
                        onOptionChange={resonValueChangeHandler} />
                    {/* {dropdownLabelBox("Reason", reasonVal, reasonValFocus)}
                    <Dropdown
                        style={[defaultStyle.dropdownMain, reasonValFocus && {
                            borderBottomColor: theme.colors.dropdownActiveColor,
                            borderBottomWidth: theme.dimensions.dropdownActiveBorder
                        }]}
                        placeholderStyle={defaultStyle.dropdownPlaceholderStyle}
                        selectedTextStyle={defaultStyle.dropdownSelectedTextStyle}
                        inputSearchStyle={defaultStyle.dropdownInputSearchStyle}
                        data={reasonData}
                        maxHeight={200}
                        labelField="display_value"
                        valueField="config_key"
                        placeholder={"Reason"}
                        value={reasonVal}
                        onFocus={() => setreasonValFocus(true)}
                        onBlur={() => setreasonValFocus(false)}
                        onChange={item => {
                            resonValueChangeHandler(item.config_key);
                            setreasonValFocus(false);
                        }}
                        renderRightIcon={() => (
                            <MaterialIcons style={defaultStyle.dropdownIcon} color={reasonValFocus
                                ? theme.colors.dropdownActiveColor : theme.colors.dropdownInActiveColor}
                                name="keyboard-arrow-down" size={theme.dimensions.dropdownRightIcon} />
                        )}
                    /> */}
                </View>
                <EntutoEditText
                    labelTxt="Comment"
                    placeholderTxt="Comment"
                    value={comment}
                    onChangeText={(text) => commentChangeHandler(text)}
                    showErrorField={commentErr.length}
                    errorMsg={commentErr}
                />
                <View style={{ flexDirection: 'row', flex: 1, marginTop: 16, marginBottom: 16, }}>
                    <View style={{ flex: 1 }}>
                        <PopupNegativeButton
                            onPress={() => cancelBtnPress()}
                            btnText='No'
                            style={{ marginEnd: theme.dimensions.popupBtnGap }} />

                    </View>
                    <View style={{ flex: 1 }}>
                        <PopupPositiveButton
                            disabled={isSubmitDisable}
                            onPress={() => confirmDeleteBtnPress()}
                            btnText={"Confirm"} />

                    </View>
                </View>
            </View>
        </View>
    )
}

export default DeleteAccount;

const styles = StyleSheet.create({})
