import React, { useContext, useEffect, useState } from 'react'
import { FlatList, Image, StyleSheet, TouchableOpacity, View } from 'react-native'
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import LinearGradient from 'react-native-linear-gradient';
import ServerConnector from '../../utils/ServerConnector';
import { MaxStoryTxtLimit, _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { AppStateContext } from '../..';
import { hasImageUrlExist } from '../../utils/Utils';
import StoryPlaceholder from '../placeholder/StoryPlaceholder';
import Colors from '../../constants/Colors';
import appData from '../../data/Data';
import ConfirmationPopup from '../common/ConfirmationPopup';
import { ActivityIndicator } from 'react-native-paper';

const TopProfileListComponent = ({ navigation, refreshPage, ...props }) => {
    const [storyList, setstoryList] = useState([]);
    const [hasStory, sethasStory] = useState(false);
    const [showLoading, setShowLoading] = useState(true);
    const { fullUserDetails, userProfileImage } = useContext(AppStateContext);
    const __ProfileImage = fullUserDetails.hasOwnProperty("_profile_picture") ? fullUserDetails._profile_picture : "";
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : "";

    const [showConfirmPopup, setshowConfirmPopup] = useState(false);
    const [showConfirmPopupKey, setshowConfirmPopupKey] = useState(Math.random());

    const [confirmTitle, setconfirmTitle] = useState("Alert");
    const [confirmMsg, setconfirmMsg] = useState("This story is no longer available!");
    const [warringsData, setwarringsData] = useState({});
    const [storyLoading, setstoryLoading] = useState(false)

    useEffect(() => {
        setShowLoading(true);
        hasUserProfileStory();
        getStoriesProfile();
    }, [refreshPage]);


    React.useEffect(() => {
        const unsubscribeStory = navigation.addListener('focus', () => {
            if (appData.__StoryPageRefreshCheck == "YES") {
                appData.__StoryPageRefreshCheck = "NO";
                setShowLoading(true);
                hasUserProfileStory();
                getStoriesProfile();

            }
        });
        return unsubscribeStory;
    }, [navigation]);

    function hasUserProfileStory() {
        let hashMap = {
            _action_code: "11:GET_PROFILE_STORIES",
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            sethasStory(true);
            setShowLoading(false);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                sethasStory(false);
                setShowLoading(false);
            }

        });
    }
    function getStoriesProfile() {
        let hashMap = {
            _action_code: "11:GET_STORIES",
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setstoryList([...[], ...data.data]);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setstoryList([]);
            }

        });
    }

    const profileBtnPress = (profileSeq) => {
        navigation.navigate('OthersProfileScreen', {
            profileSeq: profileSeq,
        })
    }
    const confirmPopupPress = (clickId, obj) => {
        if (clickId == "positive") {
            setShowLoading(true);
            hasUserProfileStory();
            getStoriesProfile();
        }
    }


    const renderStoryItem = ({ item }) => {
        return (
            <TouchableOpacity onPress={() => profileBtnPress(item.profile_seq)}>
                <View style={styles.fullStoryBox}>
                    <View style={{ ...styles.homeStoryBox, }}>
                        <ProgressiveImage
                            style={styles.homeStoryImage}
                            source={hasImageUrlExist(item.profile_picture) ? { uri: item.profile_picture } : null}
                            defaultImageSource={require('../../assets/Images/full_user_image_place_holder.png')}
                            resizeMode={'cover'}
                        />

                    </View>
                    <EntutoTextView style={styles.storyProfileName}>
                        {((item.display_name).length > MaxStoryTxtLimit) ?
                            (((item.display_name).substring(0, MaxStoryTxtLimit - 3)) + '...') :
                            item.display_name}

                    </EntutoTextView>
                </View>
            </TouchableOpacity>
        );
    };
    const [profileImage, setprofileImage] = useState(null);
    useEffect(() => {
        setprofileImage(userProfileImage);
    }, [userProfileImage])
    return (

        <View style={styles.storiesContainer}>
            <EntutoTextView style={styles.topText}>Top to 10 Today</EntutoTextView>
            {
                showLoading ?
                    <StoryPlaceholder />
                    :
                    <>

                        <View style={{ flexDirection: 'row', flex: 1 }}>
                            <FlatList
                                data={storyList}
                                renderItem={renderStoryItem}
                                keyExtractor={item => item.profile_seq}
                                horizontal
                                showsHorizontalScrollIndicator={false} />
                        </View>
                    </>
            }
            {
                showConfirmPopup &&
                <ConfirmationPopup
                    visiblePopupKey={showConfirmPopupKey}
                    visiblePopup={showConfirmPopup}
                    title={confirmTitle}
                    messagebody={confirmMsg}
                    positiveButton="Ok"
                    negativeButton="No"
                    isOkBtnPopup={true}
                    data={warringsData}
                    popupClick={(clickID, data) => { confirmPopupPress(clickID, data) }}
                />
            }

        </View>
    )
}

export default TopProfileListComponent;

const styles = StyleSheet.create({
    topText: {
        color: '#333333',
        fontSize: 16,
        marginBottom: 10,
    },
    fullStoryBox: {
        justifyContent: 'center',
        alignItems: 'center'
    },
    storyProfileName: {
        fontSize: 14,
        color: '#333333',
        marginTop: 6,

    },
    storiesContainer: {
        flexDirection: 'column'
    },
    myStoryBox: {
        position: 'relative',
        marginRight: 8,

    },
    myStoryPlusBox: {
        position: 'absolute',
        top: 16,
        left: 16,
    },
    myStoryPlusRightBox: {
        position: 'absolute',
        top: 30,
        left: 40,
        backgroundColor: Colors.primaryColor,
        borderRadius: 8,
    },
    myStoryPlusIcon: {
        width: 24,
        height: 24,

    },
    homeStoryBox: {
        position: 'relative',
        width: 76,
        height: 76,
        borderRadius: 15,
        marginRight: 5,
        // overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center'
    },
    homeStoryImage: {
        width: 76,
        height: 76,
        borderRadius: 15,
    },
    linearGradient: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        borderRadius: 15,
    },

})
