import React, { useEffect, useState } from 'react'
import { Modal, StyleSheet, Text, View } from 'react-native'
import { Button, Dialog, Portal } from 'react-native-paper'
import EntutoTextView from './EntutoTextView'
import useSTheme from '../../theme/useSTheme'
import PrimaryButton from './PrimaryButton'
import { color } from 'react-native-reanimated'
import { TouchableOpacity } from 'react-native'
import useDefaultStyle from '../../theme/useDefaultStyle'

const ErrorMessagePopup = ({ errorBoxTitle = "Error", errorBoxMsg = "", visiblePopupKey = 1, ...props }) => {
    const [visiblePopup, setVisiblePopup] = useState(props.visiblePopup);
    const theme = useSTheme();
    const { defaultStyle } = useDefaultStyle();
    useEffect(() => {
        if (props.visiblePopup) {
            setVisiblePopup(true);
        }
        return () => { setVisiblePopup(false) };
    }, [visiblePopupKey]);

    const dialogBtnPress = (type) => {
        if (type == "negative") {
            setVisiblePopup(false);
        }
    }
    return (
        <Modal visible={visiblePopup} onDismiss={() => dialogBtnPress("negative")}
            dismissable={true}
            transparent={true}
        >
            <TouchableOpacity style={defaultStyle.backdropModalView} onPress={() => dialogBtnPress("negative")}>
                <TouchableOpacity style={defaultStyle.centeredView} activeOpacity={1}>
                    <View style={{ ...defaultStyle.defaultModalView, }}>
                        <View >
                            <EntutoTextView style={{
                                color: theme.colors.primaryColor,
                                fontSize: theme.calculateFontSize(theme.dimensions.errorMsgPopupTitle), marginBottom: 16, marginTop: 10,
                                fontWeight: 'bold',
                                textAlign: 'center'
                            }}>
                                {errorBoxTitle}
                            </EntutoTextView>
                        </View>
                        <View>
                            <EntutoTextView style={{
                                fontSize: theme.calculateFontSize(theme.dimensions.errorMsgPopupMsgText),
                                textAlign: 'center', color: theme.colors.errorPopupMsgColor
                            }}>{errorBoxMsg}</EntutoTextView>
                        </View>
                        <View style={defaultStyle.popupActionBox}>
                            <PrimaryButton label="Okay" onPress={() => dialogBtnPress("negative")}
                                labelStyle={{ fontSize: theme.calculateFontSize(theme.dimensions.errorMsgPopupBtnText), paddingVertical: 2 }}
                                style={{ width: 200 }} />
                        </View>
                    </View>
                </TouchableOpacity>
            </TouchableOpacity>
        </Modal>
        // <Portal>
        //     <Dialog visible={visiblePopup} onDismiss={() => dialogBtnPress("negative")}>
        //         <Dialog.Title style={{ color: theme.colors.primaryColor, textAlign: 'center' }}>{errorBoxTitle}</Dialog.Title>
        //         <Dialog.Content>
        //             <EntutoTextView style={{ fontSize: theme.calculateFontSize(14), textAlign: 'center', color: theme.colors.errorPopupMsgColor }}>{errorBoxMsg}</EntutoTextView>
        //         </Dialog.Content>
        //         <Dialog.Actions style={{ justifyContent: 'center', marginBottom: 10 }}>
        //             <PrimaryButton label="Okay" onPress={() => dialogBtnPress("negative")}
        //                 labelStyle={{ fontSize: theme.calculateFontSize(14), paddingVertical: 2 }}
        //                 style={{ width: 200 }} />
        //         </Dialog.Actions>
        //     </Dialog>
        // </Portal>
    )
}

export default ErrorMessagePopup

const styles = StyleSheet.create({})
