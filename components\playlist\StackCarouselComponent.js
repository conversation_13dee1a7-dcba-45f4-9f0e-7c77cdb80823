import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import { useSharedValue } from 'react-native-reanimated';
import StackCardItem from './StackCardItem';

const StackCarouselComponent = ({ data = [], maxVisibleItems = 3, lockSlide = false, itemRowPress }) => {
  const animatedValue = useSharedValue(0);
  const currentIndex = useSharedValue(0);
  const prevIndex = useSharedValue(0);
  const currentVisibleIndex = useSharedValue(0);
  const [visibleIndex, setVisibleIndex] = useState(0)
  const slideCallback = (pIndex, cIndex, type) => {
    let indexVal = cIndex;
    // console.log("type", type);
    // // console.log("pIndex", pIndex);
    // console.log("cIndex", cIndex);


    setVisibleIndex(prevState => prevState = indexVal)

  }
  // useEffect(() => {
  //   console.log("currentVisibleIndex " + new Date(), currentIndex.value)
  //   console.log("prevIndex " + new Date(), prevIndex.value)
  //   console.log("animatedValue " + new Date(), animatedValue.value)
  // }, [currentIndex.value, prevIndex.value, animatedValue.value])
  return (
    <>
      {data.map((item, index) => {
        return (
          <StackCardItem
            maxVisibleItems={maxVisibleItems}
            item={item}
            index={index}
            indexValue={index}
            dataLength={data.length}
            animatedValue={animatedValue}
            currentIndex={currentIndex}
            prevIndex={prevIndex}
            key={index}
            lockSlide={lockSlide}
            slideCallback={slideCallback}
            visibleIndex={visibleIndex}
            currentVisibleIndex={currentVisibleIndex}
            itemRowPress={itemRowPress}
          />
        );
      })}
    </>
  )
}

export default StackCarouselComponent

const styles = StyleSheet.create({})