import { Animated, Pressable, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import EntutoTextView from '../common/EntutoTextView';

const ResolutionVideoSwitch = () => {
    const style = useSThemedStyles(styles);
    const [value, setValue] = useState(false);
    const [animatedValue] = useState(new Animated.Value(value ? 1 : 0));
    useEffect(() => {
        Animated.timing(animatedValue, {
            toValue: value ? 1 : 0,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, [value]);
    const translateX = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [1, 30], // Adjust the distance of the switch head
    });
    const toggleSwitch = () => {
        const newValue = !value;
        setValue(newValue);
    };
    const theme = useSTheme();
    return (
        <Pressable onPress={toggleSwitch}
            style={{ ...style.resolutionSwitch, backgroundColor: value ? theme.colors.primaryColor + "30" : "#383b37" }}>
            <View style={style.innerContainer}>
                <Animated.View
                    style={{
                        transform: [{ translateX }],
                    }}>
                    <View style={{ ...style.thumbBox, backgroundColor: value ? theme.colors.primaryColor : "#50524e" }} >
                        <EntutoTextView style={style.thumbBoxText}>{value ? "HD" : "SD"}</EntutoTextView>
                    </View>
                </Animated.View>
            </View>
        </Pressable>
    )
}

export default ResolutionVideoSwitch;
const styles = theme => StyleSheet.create({

    resolutionSwitch: {
        width: 60,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#333',
        justifyContent: 'center',
    },
    innerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        position: 'relative',
    },
    thumbBox: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',

    },
    thumbBoxText: {
        fontSize: theme.calculateFontSize(theme.dimensions.videoSwitchText),
        color: '#FFF',

    },
})