
import { Dimensions, StyleSheet } from 'react-native';

const screenWidth = Dimensions.get('screen').width;
const screenHeight = Dimensions.get('screen').height;
export default {
    screenWidth: screenWidth,
    screenHeight: screenHeight,

    splashLogoWidthSize: screenWidth,
    splashLogoHeightSize: screenHeight,
    splashUltsSize: screenHeight / 2,

    loginLogoWidth: screenWidth / 1.8,
    loginLogoHeight: 100,
    defaultSideGap: 15,

    //Roundness
    textInputRadius: 0,
    buttonRadius: 0,

    listCardElevation: 5,

    //Input text    
    inputTextGap: 2,
    inputTextBorderBootom: 1,

    inputTextIconHeight: 24,
    inputTextIconWidth: 24,



    buttonFormGap: 30,
    bottonTabNavHeight: 56,

    placeholderHeight: 50,
    placeholderSpeed: 1,

    tabBorderBottomWidth: 3,


    searchInputIconH: 24,
    searchInputIconW: 24,
    searchInputIconMH: 8,
    searchBarRadius: 10,

    sugProfileImgH: 50,
    sugProfileImgW: 50,
    sugProfileImgR: 50,
    sugProfileImgGapTxt: 15,
    sugVerifiedIconH: 15,
    sugVerifiedIconW: 16,
    sugVerifiedIconRight: -6,
    sugVerifiedIconTop: 20,


    dropdownActiveBorder: 2,
    dropdownInActiveBorder: 0.5,
    dropdownRightIcon: 30,

    veritextLeftmargin: 8,
    notificationListGap: 10,

    loginModuleAppBarHeight: 56,
    loginModuleHeaderTextMT: 10,
    loginModuleProgressBarMT: 28,
    loginModuleTitleMT: 60,
    loginModuleButtonMT: 27,
    loginModuleInputMT: 18,
    dropdownRightIcon: 30,

    playlistTitleBoxHeight: 70,

    yearCountBoxTop: 14,
    playlistDescTopMargin: 12,

    popupBtnGap: 15,

    // --------------------------------------------- Font Size For Screen Width 390 ----------------------------------//
    postProfileNameText: 14,
    postDescText: 12,
    postDateText: 12,
    postFollowBtnText: 10,

    playlistTitleText: 16,
    playlistSeasonCountText: 16,
    playlistDescText: 12,
    playlistClipText: 14,
    playlistClipMinuteText: 12,

    playlistCardTitleText: 16,

    profileDisplayname: 20,//20
    profileUserHandleText: 14,//14
    profileBioText: 13,//13
    profileActionBtnText: 16,//16
    profileCountValueText: 18,//18
    profileCountLabelText: 12,//12
    profileTabText: 14,//14
    profileCatText: 14,//14
    profileLockBtnText: 16,//16
    profilePlaylistUrlText: 16,//16
    profilePlaylistUrl2Text: 14,//16


    // --------------------------------------------- Font Size For Screen Width 350 ----------------------------------//

    listItemText: 18,//17
    blockAccountBtnText: 16,
    accDeActiveSuccessText: 16,//
    commentText: 14,
    commentProfileText: 14,
    bottomSheetSuccessMsgText: 18,
    confirmDialogMsgText: 14,
    dropdownFontText: 14,
    inputTextFontSize: 16,//17
    inputTextErrorText: 12,
    errorMsgPopupTitle: 18,
    errorMsgPopupMsgText: 14,
    errorMsgPopupBtnText: 14,

    headlineTxt: 24,
    HeadingTxt: 18,
    HeadingLineTxt: 14,
    SubheadingTxt: 14,
    SubheadingBodyTxt: 12,
    buttonGroupItemText: 16,
    notificationHeaderText: 16,
    notificationViewAllText: 12,
    primaryBtnText: 16,
    selectBoxText: 14,

    shareProfileAwesomeText: 28,
    shareProfileBody1BoxText: 14,
    shareProfileBody2BoxText: 14,
    shareProfileShareItNowText: 16,

    failureErrorBoxText: 17,
    rowBtnText: 16,
    loginModuleTitleText: 28,
    LoginModuleHeaderText: 20,
    LoginModuleSecondHeaderText: 12,

    copyLinkText: 16,//17
    copyLinkBtnText: 16,//17

    postActionCountText: 16,
    postLockBtnText: 14,
    postBlockErrorHeadingText: 20,
    postBlockErrorMsgText: 18,
    postBookmarkWarringText: 12,//11
    postNSActionCountText: 18,
    postFullScreenProfileNameText: 12,
    postFullScreenProfileIDText: 10,
    postFullScreenProfileDescText: 10,

    videoSwitchText: 10,
    threeDotMenuText: 14,
    popupWarringTxt: 14,
    popupRefreshBtnText: 18,

    rowProfileNameText: 14,
    rowProfileIDText: 12,
    rowProfilePriceText: 16,
    rowProfileDateText: 12,
    rowProfileStatusText: 10,

    selectedTagNameText: 12,
    sliderPriceHeadingText: 12,
    sliderPriceText: 16,

    addPostSuccessCongratsText: 28,
    addPostSuccessBodyText: 16,

    copyLinkText: 16,
    shareItNowText: 16,

    commentReplyTextBtn: 12,
    commentPostBtnText: 14,

    boxWithSwitchTxt: 16,
    reasonItemText: 16,
    loginCardText: 24,

    searchResultInputText: 14,
    searchResultPopularText: 14,
    searchResultMoreText: 14,
    searchResultCancelBtnText: 16,
    searchResultHistoryItemText: 12,
    searchTextInputSize: 16,//17

    subsDropPlaceHolderText: 12,
    subsDropText: 12,
    subsTotalEaringsValueText: 24,
    subsTotalEaringsLabelText: 14,
    subsEarnedTxtValText: 16,
    subsEarnedTxtLabelText: 10,
    subscriptionItemValue: 24,
    subscriptionItemLabel: 14,

    verifyFileInputBoxLabelTxt: 14,
    personalizeLabelText: 16,

    postingJSwitchText: 14,
    postingJPreviewText: 14,
    postingJHeading: 16,
    postingJDateText: 14,
    postingJSliderText: 14,
    postingJModalHeadingText: 18,
    postingJModalBodyText: 14,

    defaultTabBarLabelTxt: 14,
    defaultPostBtnTxt: 18,//17
    defaultPopupHeadTxt: 24,
    defaultPopupBodyTxt: 14,
    defaultPopupWarringTxt: 14,
    defaultPopupBtnText: 16,
    defaultDropdownLabel: 12,//11
    defaultDropdownPlaceholder: 14,
    defaultDropdownSelectedText: 14,
    defaultInputUnderLineTxt: 10,
    defaultVerCameraIconTxt: 14,
    defaultVerCameraIconExtraTxt: 14,
    defaultUnderInputTxt: 12,
    defaultListUnderLineTxt: 10,
    defaultCustomMultiSelectSingleTxt: 16,
    defaultErrorTxt: 12,
    defaultInputUnderCountBoxTxt: 12,
    defaultSignupText: 32,
    defaultSignupSmallText: 16,
    defaultSignUpBtnText: 16,
}