import { StyleSheet, Text, View, Platform, PermissionsAndroid, Image, Dimensions, TouchableOpacity, ToastAndroid, Alert, ScrollView, Linking } from 'react-native'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { CameraRoll } from "@react-native-camera-roll/camera-roll";
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import CustomProgressDialog from './common/CustomProgressDialog';
import { ActivityIndicator, FAB } from 'react-native-paper';
import { openSettingForStorage, requestStoragePermission } from '../utils/PermissionManager';
import BottomSheet, { BottomSheetFlatList, BottomSheetModal } from '@gorhom/bottom-sheet';
import EntutoTextView from './common/EntutoTextView';
import { FlatList } from 'react-native-gesture-handler';
import { checkValueLength } from '../utils/Utils';
import { check, request, PERMISSIONS, RESULTS, openLimitedPhotoLibraryPicker } from 'react-native-permissions';
import ErrorMessages from '../constants/ErrorMessages';
import useAppIsActive from './common/useAppIsActive';



const GalleryImageDisplayComponent = ({
    onMediaSelectionBtnPress = null,
    mediaType = "All",
    MAX_FILE_SIZE = 60,
    tabChangeManual = false
}) => {
    const [mediaList, setMediaList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pageNumber, setPageNumber] = useState(1);
    const [hasNextPage, setHasNextPage] = useState(false);
    const [selectedMedia, setSelectedMedia] = useState(null);
    const [endCursorValue, setEndCursorValue] = useState(0);
    const openGallerySheetRef = useRef(null);
    const mediaScrollSheetRef = useRef(null);
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [pageLoading, setPageLoading] = useState(true);
    const [fromAppStateActive, setFromAppStateActive] = useState(false);

    useEffect(() => {
        if (tabChangeManual) {
            setSelectedMedia(null)
        }
    }, [tabChangeManual])
    useAppIsActive(() => {
        if (Platform.OS == 'ios') {
            console.log("AppisActiveState d")
            setPageLoading(true);
            getMediaList(1, 0, true, true);
        }
    });


    const supportedMimeTypesByTheBackEnd = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'video/mp4',
        'video/mpeg',
        'video/quicktime',
    ];

    const ItemWidth = parseInt((Dimensions.get('screen').width - 9) / 3);
    useEffect(() => {
        openGallerySheetRef.current?.present();
        setPageLoading(true);
        getMediaList(1, 0, true, false);
    }, []);

    const getMediaList = async (page, endCursorVal, checkPerm = true, fromAppState = false) => {
        if (checkPerm) {
            if (Platform.OS === "android" && !(await hasAndroidPermission())) {
                onMediaSelectionBtnPress("PERMISSION_ERROR", null);
                return;
            }
        }

        // setLoading(true);

        try {
            if (checkPerm) {
                if (Platform.OS == 'ios' && !(await hasIosPermission(fromAppState))) {
                    return;
                }
            }
            const media = await CameraRoll.getPhotos({
                first: 21,
                assetType: "All",
                after: page === 1 ? null : endCursorVal,
                include: ['fileSize', 'filename', 'fileExtension'],
                groupTypes: 'All',
            });
            if (page == 1) {
                if (media.edges.length > 0) {
                    const fileMaxSize = MAX_FILE_SIZE * 1024 * 1000;
                    // console.log("log",media.edges[1])
                    if (fileMaxSize >= media.edges[0].node.image.fileSize) {
                        let validImage = true;
                        if (media.edges[0].node.image.extension.toLowerCase() == 'heic') {
                            validImage = false;
                            // notifyMessage(ErrorMessages.heicFileErr)
                        }
                        if (validImage) {
                            onMediaSelectionBtnPress("SUCCESS", media.edges[0], 0)
                            setSelectedMedia(media.edges[0])
                        }
                    }
                }
                else {
                    let noDataFoundError = true;
                    if (Platform.OS == 'ios') {
                        const permissionStatus = await check(PERMISSIONS.IOS.PHOTO_LIBRARY);
                        if (permissionStatus != RESULTS.GRANTED) {
                            noDataFoundError = false;
                            // if (permissionStatus == RESULTS.LIMITED) {
                            //     showLimitedPermissionAlert();
                            // }                          
                        }
                    }
                    if (noDataFoundError) {
                        Alert.alert("Error", "There is no media to display!")
                    }

                    // if (Platform.OS == 'ios') {
                    //     Alert.alert("Error", "Please grant us access to your photo gallery for the best SoTrue experience!",
                    //         [
                    //             { text: 'Cancel', onPress: () => console.log('Cancel Pressed!') },
                    //             { text: 'OK', onPress: () => Linking.openSettings("PHOTOS") },
                    //         ],
                    //     )
                    // }
                    // else {
                    //     Alert.alert("Error", "There is no media to display!")
                    // }

                }
            }
            setEndCursorValue(media.page_info.end_cursor)
            if (page == 1) {
                setMediaList((prevState) => [...[], ...media.edges]);
            }
            else {
                setMediaList((prevState) => [...prevState, ...media.edges]);
            }

            // const imageDtos = convertCameraRollPicturesToImageDtoType(cameraRollPictures.edges);
            setHasNextPage(media.page_info.has_next_page);
            setPageNumber(page);
            setLoading(false);
            setPageLoading(false);
        } catch (error) {
            // console.log(error);
            setLoading(false);
            setPageLoading(false);
        }
    }

    const convertCameraRollPicturesToImageDtoType = (cameraRollPictures) => {
        return cameraRollPictures.map((picture) => {
            return {
                id: picture.node.image.uri,
                uri: picture.node.image.uri,
                width: picture.node.image.width,
                height: picture.node.image.height,
                type: picture.node.type,
                timestamp: picture.node.timestamp,
            };
        });
    };
    async function hasAndroidPermission() {

        const getCheckPermissionPromise = async () => {
            if (Platform.Version >= 33) {
                const [hasReadMediaImagesPermission, hasReadMediaVideoPermission] = await Promise.all([
                    PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES),
                    PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO),
                ]);
                return hasReadMediaImagesPermission && hasReadMediaVideoPermission;
            } else {
                return PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
            }
        };
        const hasPermission = await getCheckPermissionPromise();
        if (hasPermission) {
            return true;
        }
        const getRequestPermissionPromise = async () => {
            if (Platform.Version >= 33) {
                const statuses = await PermissionsAndroid.requestMultiple([
                    PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
                    PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO,
                ]);
                let storagePermission = false;
                if (statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES] ===
                    PermissionsAndroid.RESULTS.GRANTED &&
                    statuses[PermissionsAndroid.PERMISSIONS.READ_MEDIA_VIDEO] ===
                    PermissionsAndroid.RESULTS.GRANTED) {
                    storagePermission = true;
                }
                if (!storagePermission) {
                    openSettingForStorage();
                }
                return storagePermission;
            } else {
                const status = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
                return status === PermissionsAndroid.RESULTS.GRANTED;
            }
        };
        return await getRequestPermissionPromise();
    }
    async function hasIosPermission(fromAppState) {
        const permissionStatus = await check(PERMISSIONS.IOS.PHOTO_LIBRARY);

        if (permissionStatus === RESULTS.GRANTED) {
            return true;
        }
        if (permissionStatus == RESULTS.LIMITED) {
            if (!fromAppState) {
                showLimitedPermissionAlert();
            }

            return true;
        }
        if (permissionStatus == RESULTS.BLOCKED) {
            showPermissionAlert();
            return false;
        }

        const permission = await request(PERMISSIONS.IOS.PHOTO_LIBRARY);
        console.log("permission", permission)
        if (permission === RESULTS.GRANTED) {
            return true;
        }
        else if (permission == RESULTS.LIMITED) {
            console.log("LimitedBlock", permission)

            return true;
        }
        return false;
    }

    const showPermissionAlert = () => {
        Alert.alert("Error", "Please grant us access to your photo gallery for the best SoTrue experience!", [
            { text: 'Cancel', onPress: () => console.log('Cancel Pressed!') },
            {
                text: 'OK', onPress: () => {
                    setPermissionSelection(true);
                    Linking.openSettings("PHOTOS")
                }
            },
        ]);
    };
    const showLimitedPermissionAlert = () => {
        Alert.alert(ErrorMessages.permissionLimitedStorageTitle,
            ErrorMessages.permissionLimitedStorageMsg, [
            { text: 'Select More Photos...', onPress: () => openLimitAccessSettings() },
            { text: 'Keep Current Selection', onPress: () => console.log('') },
        ]);
    };
    const renderItem = useCallback(
        ({ item, index }) => {
            const typeArray = item.node.type.split('/');
            let mediaType = item.node.type;
            if (typeArray.length > 0) {
                mediaType = typeArray[0];
            }
            return (
                <View style={{
                    marginBottom: 2, justifyContent: 'center',
                    borderColor: selectedMedia == null ? theme.colors.backgroundColor : item.node.id == selectedMedia.node.id ? theme.colors.primaryColor : theme.colors.backgroundColor,
                    borderWidth: 2
                }}>
                    <TouchableOpacity onPress={() => handleSelectMedia(item, index)}>

                        <Image source={checkValueLength(item.node.image.uri) ? { uri: item.node.image.uri } : null} style={{
                            width: ItemWidth - 2, height: ItemWidth - 2
                        }} />
                        {
                            mediaType == "video" ?
                                <View style={{
                                    position: 'absolute', top: 0, left: 0, right: 0, bottom: 0,
                                    justifyContent: 'center', alignItems: 'center', zIndex: 999,
                                    backgroundColor: theme.colors.backgroundColor + "50"
                                }}>
                                    <FAB
                                        style={{ backgroundColor: 'transparent', }}
                                        small
                                        icon="play-circle-outline"
                                    />

                                </View>
                                : null
                        }

                    </TouchableOpacity>
                </View>
            )
        },
        [mediaList, selectedMedia],
    );
    const onEndReached = () => {
        if (!loading) {
            if (!loading && hasNextPage) {
                getMediaList(pageNumber + 1, endCursorValue, false, false);
            }
        }
    }
    const handleSelectMedia = (media, index) => {

        if (onMediaSelectionBtnPress) {
            // setPageLoading(true)
            const dataIndex = parseInt(index / 3);
            // 
            setTimeout(() => {
                mediaScrollSheetRef.current?.scrollToIndex({
                    animated: true,
                    index: dataIndex, // 0
                })
                const fileMaxSize = MAX_FILE_SIZE * 1024 * 1000;
                const mediaSizeErrorMsg = `Oh no! Your content exceeds the temporary ${MAX_FILE_SIZE}MB limit.`
                if (fileMaxSize >= media.node.image.fileSize) {
                    let validImage = true;
                    if (media.node.image.extension.toLowerCase() == 'heic') {
                        validImage = false;
                        notifyMessage(ErrorMessages.heicFileErr)
                    }
                    if (validImage) {
                        onMediaSelectionBtnPress("SUCCESS", media, index);
                        setSelectedMedia(media);
                    }

                } else {
                    notifyMessage(ErrorMessages.addPostMediaSizeErr)
                }
                setPageLoading(false)
            }, 100);
            // setTimeout(() => {
            //     openGallerySheetRef.current?.snapToIndex(0);
            // }, 500);
        }

    };
    function notifyMessage(msg) {
        if (Platform.OS === 'android') {
            Alert.alert("Alert", msg);
            // ToastAndroid.show(msg, ToastAndroid.SHORT)
        } else {
            Alert.alert("Alert", msg);
        }
    }
    const keyExtractor = useCallback((item, index) => `${index}`);
    const snapPoints = useMemo(() => ["32%", "80%"], []);
    const dismissModal = () => {
        // commentComponentClick("CLOSE", {});
    }
    const getItemLayout = (_, index) => (
        {
            length: ItemWidth,
            offset: (ItemWidth + 2) * index,
            index: index,
        }
    )
    const openLimitAccessSettings = async () => {
        
        // openLimitedPhotoLibraryPicker().then(() => {
        //     console.log("Click")
        // }).catch(() => {
        //     console.warn('Cannot open photo library picker');
        // });
        const canOpen = await Linking.canOpenURL('app-settings:LIMIT_ACCESS');
        if (canOpen) {
            Linking.openURL('app-settings:LIMIT_ACCESS');
        } else {
            console.log('Cannot open Limit Access settings');
        }
    };
    return (
        <>

            <BottomSheetModal
                ref={openGallerySheetRef}
                enableOverDrag={true}
                index={0}
                snapPoints={snapPoints}
                enableDismissOnClose={false}
                enablePanDownToClose={false}
                keyboardBlurBehavior={'restore'}
                keyboardBehavior="extend"
                android_keyboardInputMode="adjustResize"
                // handleIndicatorStyle={{ backgroundColor: '#000' }}
                // handleStyle={{ backgroundColor: "#CCC", borderTopEndRadius: 8, borderTopStartRadius: 8 }}
                onDismiss={() => dismissModal()}
                backgroundStyle={{
                    backgroundColor: theme.colors.backgroundColor
                }}
            >
                {/* <CustomProgressDialog
                    loadingMsg={""}
                    showLoading={pageLoading}
                /> */}
                <FlatList
                    keyboardShouldPersistTaps={'handled'}
                    ref={mediaScrollSheetRef}
                    numColumns={3}
                    data={mediaList}
                    renderItem={renderItem}
                    keyExtractor={keyExtractor}
                    refreshing={false}
                    maxToRenderPerBatch={21}
                    windowSize={40}
                    initialNumToRender={12}
                    updateCellsBatchingPeriod={50}
                    disableVirtualization
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    onEndReachedThreshold={0.5}
                    getItemLayout={getItemLayout}
                    onEndReached={onEndReached}
                // ListFooterComponent={loading ? <ActivityIndicator /> : null}
                />
                {/* <ScrollView>
                    {
                        mediaList.map((item,i)=>{
                            return <Image key={i} source={{uri:item.uri}} style={{height:100,width:100,borderWidth:1,borderColor:"#FFF"}}/>
                        })
                    }
                </ScrollView> */}
            </BottomSheetModal>

        </>
    )
}

export default GalleryImageDisplayComponent

const styles = theme => StyleSheet.create({

})