import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Keyboard,
  Platform,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import Video from 'react-native-video';
import {
  checkValueLength,
  creationOfCopyLink,
  decodeHtmlEntitessData,
  formatLikeNumber,
  getValueFromReactions,
  hasImageUrlExist,
  preventDoubleClick,
} from '../../utils/Utils';
import useSThemedStyles from '../../theme/useSThemedStyles';
import EntutoTextView from '../common/EntutoTextView';
import VerifiedIcon from '../../assets/Images/icon/verifiedicon.png';
import {TouchableOpacity} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import PlaylistPlaceholder from '../../assets/Images/full_user_image_place_holder.png';
import TempData from '../../data/TempData';
import PlayViewCountImage from '../../assets/Images/icon/play_view_icon.png';
import ServerConnector from '../../utils/ServerConnector';
import appData from '../../data/Data';
import ThreeDotVerticalIcon from '../../assets/Images/icon/profile_three_dot.png';
import ActionSheet from 'react-native-actions-sheet';
import ThreeDotMenuActionView from '../post/ThreeDotMenuActionView';
import Share from 'react-native-share';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import {TapGestureHandler} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
} from 'react-native-reanimated';
import LinearGradient from 'react-native-linear-gradient';
import HeartVideoActive from '../../assets/Images/icon/double_tap.png';
import PlayBtnIcon from '../../assets/Images/icon/play_btn.png';
import FullScreenPlayer from '../post/FullScreenPlayer';
import LockIcon from '../../assets/Images/icon/post_lock.png';
import {_RedirectionErrorList, CurrencySymbol} from '../../utils/Appconfig';
import UnlockPostActionView from '../post/UnlockPostActionView';
import LikeBtnComponent from '../common/LikeBtnComponent';
import {REACTION_LIKE} from '../../constants/Constants';
import UpdateUserLocationComponent from '../profile/UpdateUserLocationComponent';
import LIKE_ICON from '../../assets/Images/icon/like_icon.png';
import BOOKMARK_ICON from '../../assets/Images/icon/bookmark.png';
import COMMENT_ICON from '../../assets/Images/icon/comment.png';
import SHARE_ICON from '../../assets/Images/icon/share_icon.png';
import BookmarkBtnComponent from '../common/BookmarkBtnComponent';
import {RedirectionUrlFunction} from '../../utils/RedirectionUrl';
import ErrorMessages from '../../constants/ErrorMessages';

const {width} = Dimensions.get('window');
const AnimatedImage = Animated.createAnimatedComponent(Image);
const ReelsVideoComponent = ({
  post,
  isVisible,
  isNext,
  displayHeight,
  navigation,
  cameFrom,
  insets,
  fullUserDetails,
  postCardClick = null,
  __commentObj,
  homepagePostDataBackup,
  forceBookmark = false,
  appTheme,
}) => {
  const videoRef = useRef(null);
  const doubleTapVideoRef = useRef();
  const BOTTOM_ACTION_BOX_GAP = Platform.OS == 'ios' ? insets.bottom + 18 : 30;

  const style = useSThemedStyles(styles);
  const {
    media_file,
    media_cover,
    display_name,
    is_verified,
    profile_seq,
    post_seq,
    expire_on,
  } = post;
  const [videoLoaded, setVideoLoaded] = useState(false);

  const [videoPaused, setVideoPaused] = useState(false);

  let __has_state_city = fullUserDetails.hasOwnProperty('_has_state_city')
    ? fullUserDetails._has_state_city
    : 'NO';
  const __profile_seq = fullUserDetails.hasOwnProperty('_profile_seq')
    ? fullUserDetails._profile_seq
    : -1;
  useEffect(() => {
    __has_state_city = fullUserDetails.hasOwnProperty('_has_state_city')
      ? fullUserDetails._has_state_city
      : 'NO';
  }, [fullUserDetails]);

  const PostUnlockMsg = 'UNLOCK POST FOR ';
  const ProfileUnlockMsg = 'UNLOCK ALL POSTS FOR ';
  const [postUnlockFeesData, setPostUnlockFeesData] = useState({
    unlockFees: 0,
    unlockFeesValue: 0,
    unlockMsg: '',
    perMonthTxt: '',
  });
  const [playlistLogo, setPlaylistLogo] = useState(null);
  const [playlistSequence, setPlaylistSequence] = useState('');
  const [playViewCount, setPlayViewCount] = useState(0);
  const [submitPostViewsExecute, setSubmitPostViewsExecute] = useState(false);

  const [blockPost, setBlockPost] = useState(false);
  const [blockMsg, setBlockMsg] = useState('');
  const [blockMsgDB, setBlockMsgDB] = useState('');

  const [unlockPost, setUnlockPost] = useState(false);
  const [paidPost, setPaidPost] = useState(false);
  const [isPaidProfile, setIsPaidProfile] = useState(false);

  const [selectedReactions, setSelectedReactions] = useState([]);
  const [userReactions, setUserReactions] = useState({});
  const [disableActionBtn, setDisableActionBtn] = useState({
    like: true,
    comment: true,
    share: false,
    bookmark: true,
  });
  const [postIsLike, setPostIsLike] = useState(false);
  const [postIsBookmark, setpostIsBookmark] = useState(false);
  const [likeCount, setlikeCount] = useState(0);
  const [likeRemoveType, setLikeRemoveType] = useState({
    type: '',
    count: 0,
  });
  const [isLikeServiceExecute, setIsLikeServiceExecute] = useState(false);

  const [showCommentCount, setShowCommentCount] = useState(false);
  const [commentCount, setCommentCount] = useState(0);
  const [showLikeCountBox, setShowLikeCountBox] = useState(false);
  const [showCommentActive, setshowCommentActive] = useState(false);
  const [showBookmarkIcon, setshowBookmarkIcon] = useState(true);

  const threeDotMenuSheetRef = useRef(null);
  const unlockSheetRef = useRef(null);
  const updateUserLocationRef = useRef(null);
  const scale = useSharedValue(0);

  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  useEffect(() => {
    if (__commentObj != undefined) {
      if (__commentObj.postSeq == post_seq) {
        let sCmnt = false;
        if (__commentObj.commentRight == 'YES') {
          sCmnt = true;
        }
        setshowCommentActive(sCmnt);
      }
    }
  }, [__commentObj]);

  useEffect(() => {
    if (!post) return;
    const logicTimeOut = setTimeout(() => {
      const {
        is_tagged = '',
        post_caption_tags = [],
        views = 0,
        shares = 0,
        media_type = '',
        reactions = [],
        user_reactions = {},
        is_following = '',
        sequence = '',
        status = '',
        block_reason = '',
        post_comments,
        is_liked,
        likes,
        is_bookmarked,
        is_commented,
        is_subscribed,
        profile_seq,
        post_type,
        profile_type,
        is_profile_subscribed,
        viewer_fee_display,
        viewer_fee,
        profile_fee_display,
        profile_fee,
        enable_comment,
        is_restricted,
        comments,
      } = post;
      if (!isVisible) return;
      if (checkValueLength(media_file) && !videoLoaded) {
        return;
      }
      console.log('Logic Start', new Date());

      // Set selectedReactions
      setSelectedReactions(reactions);

      // Set userReactions
      setUserReactions(user_reactions);

      // Set playlistLogo and playlistSequence
      if (sequence && TempData.episodeData.url) {
        setPlaylistLogo(TempData.episodeData.url);
      }
      setPlaylistSequence(sequence);
      // Set playViewCount and shareCount
      setPlayViewCount(views);
      //Block Post
      if (status === 'BLOCKED') {
        setBlockMsgDB(block_reason);
        setBlockPost(true);
      }
      // const decodePostCmt = decodeHtmlEntitessData(post_comments);
      const isPostSubscribed = is_subscribed === 'YES';
      const isLike = is_liked === 'YES';
      const is_bookmarkedV = is_bookmarked === 'YES';
      const comntShow = is_commented === 'YES';
      setshowCommentActive(comntShow);
      let isProfilePaid = false;
      let postIsUnlock = false;
      let isLikeBtnDisable = true;
      let isBookmarkDisable = true;
      let isCommentDisable = true;
      if (__profile_seq == profile_seq) {
        postIsUnlock = true;
        isLikeBtnDisable = true;
        isBookmarkDisable = true;
        isCommentDisable = false;
      } else {
        if (post_type === 'PAID') {
          if (isPostSubscribed) {
            postIsUnlock = true;
            isLikeBtnDisable = false;
            isBookmarkDisable = false;
            isCommentDisable = false;
          } else {
            postIsUnlock = false;
            isLikeBtnDisable = true;
            isBookmarkDisable = true;
            isCommentDisable = true;
          }
        } else if (profile_type === 'PAID') {
          isProfilePaid = true;
          if (is_profile_subscribed === 'YES') {
            postIsUnlock = true;
            isLikeBtnDisable = false;
            isBookmarkDisable = false;
            isCommentDisable = false;
          } else {
            postIsUnlock = false;
            isLikeBtnDisable = true;
            isBookmarkDisable = true;
            isCommentDisable = true;
          }
        } else {
          postIsUnlock = true;
          isLikeBtnDisable = false;
          isBookmarkDisable = false;
          isCommentDisable = false;
        }
      }

      if (isProfilePaid) {
        setPostUnlockFeesData({
          unlockFees: profile_fee_display,
          unlockFeesValue: profile_fee,
          unlockMsg: ProfileUnlockMsg,
          perMonthTxt: ' PER MONTH',
        });
      } else {
        const valV = viewer_fee_display || viewer_fee;
        setPostUnlockFeesData({
          unlockFees: valV,
          unlockFeesValue: viewer_fee,
          unlockMsg: PostUnlockMsg,
          perMonthTxt: '',
        });
      }
      setIsPaidProfile(isProfilePaid);

      if (enable_comment === 'YES') {
        if (is_restricted === 'YES') {
          isCommentDisable = true;
        }
      } else {
        isCommentDisable = true;
      }

      setCommentCount(comments);

      setDisableActionBtn({
        like: isLikeBtnDisable,
        comment: isCommentDisable,
        share: false,
        bookmark: isBookmarkDisable,
      });

      //UnLock Post
      setUnlockPost(postIsUnlock);
      setPaidPost(!postIsUnlock);
      setPostIsLike(isLike);
      setpostIsBookmark(forceBookmark ? true : is_bookmarkedV);
      console.log('Logic End -', new Date());
    }, 200);
    return () => {
      clearTimeout(logicTimeOut);
    };
  }, [post_seq, isVisible, videoLoaded]);

  useEffect(() => {
    if (isVisible) {
      setVideoLoaded(false);
      setVideoPaused(false);
      if (videoRef && videoRef.current) {
        videoRef.current.seek(0);
      }
    }
  }, [isVisible, isNext]);

  const videoError = useCallback(error => {
    // Manage error here
    console.log('error', error);
  }, []);

  const videoOnLoad = useCallback(data => {
    setVideoLoaded(true);
    setDuration(data.duration);
  });
  const onSliderValueComplete = useCallback(() => {
    setVideoPaused(false);
  });

  // Video Play Pause
  useFocusEffect(
    React.useCallback(() => {
      if (isVisible) {
        setVideoPaused(false);
      }
      return () => {
        if (isVisible) {
          setVideoPaused(true);
          setTimeout(() => {
            setVideoPaused(true);
          }, 200);
        }
      };
    }, []),
  );
  const playVideo = () => {
    setVideoPaused(false);
  };
  const goToProfile = () => {
    setVideoPaused(true);
    if (__profile_seq == profile_seq) {
      navigation.navigate('HomeScreen', {screen: 'ProfileFeed'});
    } else {
      navigation.push('OthersProfileScreen', {
        profileSeq: profile_seq,
      });
    }
  };
  const reelsVideoStart = () => {
    let currentTime = new Date();
    let timeDiff = Math.abs(appData.videoViewExecuteTime - currentTime);
    let second = Math.floor(timeDiff / 1000);
    if (second >= 3) {
      appData.videoViewExecuteTime = new Date();
      if (__profile_seq != profile_seq) {
        if (!submitPostViewsExecute) {
          setSubmitPostViewsExecute(true);
          submitPostViewService();
        }
      }
    }
  };

  function submitPostViewService() {
    let hashMap = {
      _action_code: '11:SUBMIT_POST_VIEW',
      post_seq: post_seq,
      profile_seq: profile_seq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setPlayViewCount(data.data.view_count);
      },
      (errorCode, errorMessage, data) => {
        // failure method
      },
    );
  }
  const threeDotMenuClick = () => {
    setVideoPaused(true);
    Keyboard.dismiss();
    setTimeout(() => {
      threeDotMenuSheetRef.current?.show();
    }, 100);
  };
  const ThreeDotMenuPress = (clickId, obj) => {
    if (clickId == 'blockPost') {
      setBlockMsg(obj.msg);
      setBlockPost(true);
      threeDotMenuSheetRef.current?.hide();
    } else if (clickId == 'deletePost') {
      //TODO
      threeDotMenuSheetRef.current?.hide();
      if (postCardClick) {
        // postCardClick("DELETE_POST", {});
      }
    } else if (clickId == 'sharePost') {
      shareBtnPress();
    } else if (clickId == 'editPost') {
      threeDotMenuSheetRef.current?.hide();
    }
  };
  const shareBtnPress = () => {
    submitShareCountService();
    sharePostLink('SHARE');
  };
  const sharePostLink = async type => {
    let body1 = '';
    let body2 = '';
    if (type == 'SHARE') {
      let copyLinkText = creationOfCopyLink('POST', post_seq);
      const shareOptions = {
        message: 'Exclusive content on SoTrue\n',
        url: copyLinkText,
      };
      try {
        const shareResponse = await Share.open(shareOptions);
      } catch (error) {
        // console.log(error.message);
      }
      return;
    }
    //Here Share Model Exist but New Version We Disable this code exist in Post.js component
  };
  function submitShareCountService() {
    let hashMap = {
      _action_code: '11:UPDATE_SHARE_COUNT',
      post_seq: post_seq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
      },
      (errorCode, errorMessage, data) => {
        // failure method
      },
    );
  }
  const onSingleTapVideo = useCallback(() => {
    setVideoPaused(prevState => !prevState);
  }, []);
  const rStyle = useAnimatedStyle(() => ({
    transform: [{scale: Math.max(scale.value, 0)}],
  }));
  const onDoubleTap = useCallback(() => {
    if (!disableActionBtn.like) {
      scale.value = withSpring(1, undefined, isFinished => {
        if (isFinished) {
          scale.value = withDelay(500, withSpring(0));
        }
      });
      likeThePost(REACTION_LIKE);
    }
  }, []);
  const likeThePost = type => {
    if (!postIsLike) {
      likeIconBtnClick(type, likeCount, postIsLike);
    }
  };
  const VideoOverlayReels = () => {
    return (
      <LinearGradient
        colors={['#00000070', 'transparent', 'transparent', '#000000']}
        locations={[0, 0.2, 0.8, 0.97]}
        style={style.videoOverlayGrad}
      />
    );
  };
  const unlockBtnClick = () => {
    let __has_state_city_val = fullUserDetails.hasOwnProperty('_has_state_city')
      ? fullUserDetails._has_state_city
      : 'NO';
    if (__has_state_city_val == 'YES') {
      unlockSheetRef.current?.show();
      if (postCardClick) {
        postCardClick('OPEN_UNLOCK_POPUP', {});
      }
    } else {
      updateUserLocationRef.current?.show();
      if (postCardClick) {
        postCardClick('OPEN_UNLOCK_POPUP', {});
      }
    }
  };
  const unlockPostActionClick = (clickId, obj) => {
    if (clickId == 'negetive') {
      unlockSheetRef.current?.hide();
      if (postCardClick) {
        postCardClick('CLOSE_UNLOCK_POPUP', {});
      }
    }
    if (clickId == 'close') {
      unlockSheetRef.current?.hide();
      if (postCardClick) {
        postCardClick('CLOSE_UNLOCK_POPUP', {});
      }
      appData.__HomePageRefresh = Math.random();
      appData._profilePageRefresh = true;
      if (isPaidProfile) {
        setTimeout(() => {
          navigation.replace('OthersProfileScreen', {
            profileSeq: profile_seq,
          });
        }, 1000);
      } else {
        unlockSheetRef.current?.hide();
        if (postCardClick) {
          postCardClick('CLOSE_UNLOCK_POPUP', {});
        }
        appData.__HomePageRefresh = Math.random();
        appData._profilePostPageRefresh = true;
        setTimeout(() => {
          navigation.replace('UnlockSinglePostScreen', {
            postSeq: post_seq,
            postProfileSeq: profile_seq,
          });
        }, 1000);
      }
    }
  };
  const updateUserLocationPopupPress = (clickId, obj) => {
    if (clickId == 'negetive') {
      updateUserLocationRef.current?.hide();
    }
    if (clickId == 'close') {
      updateUserLocationRef.current?.hide();
      unlockSheetRef.current?.show();
      if (postCardClick) {
        postCardClick('OPEN_UNLOCK_POPUP', {});
      }
    }
  };
  const likeIconBtnClick = (type, count, isLike) => {
    if (__profile_seq == profile_seq) {
      Alert.alert('Error', 'Self Like is not Possible!');
      return;
    }
    let callRemoveService = false;
    if (type == REACTION_LIKE) {
      if (postIsLike) {
        callRemoveService = true;
      }
    } else {
      callRemoveService = isLike ? true : false;
    }
    if (callRemoveService) {
      removePostLike(type);
    } else {
      submitPostLike(type);
    }
  };
  function submitPostLike(type) {
    if (type == REACTION_LIKE) {
      setIsLikeServiceExecute(true);
      setPostIsLike(current => true);
    }
    let hashMap = {
      _action_code: '11:SUBMIT_POST_LIKE',
      post_seq: post_seq,
      type: type,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setIsLikeServiceExecute(false);
        if (type == 'LIKE') {
          setlikeCount(data.data.like_count);
        } else {
          const reaction = post.user_reactions[type];
          if (reaction) {
            reaction.count = data.data.like_count;
            reaction.selected = 'YES';
          } else {
            post.user_reactions[type] = {
              count: data.data.like_count,
              selected: 'YES',
            };
          }
          setLikeRemoveType({
            type: type,
            count: data.data.like_count,
          });
        }
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: post_seq,
          type: 'LIKE',
          action_code: 'SUBMIT_POST_LIKE',
          reactionType: type,
          count: data.data.like_count,
          profileSeq: profile_seq,
        });
        appData._homePagePostRefresh = 'YES';
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          setIsLikeServiceExecute(false);
        }
      },
    );
  }
  function removePostLike(type) {
    if (type == REACTION_LIKE) {
      setIsLikeServiceExecute(true);
      setPostIsLike(current => false);
    }
    let hashMap = {
      _action_code: '11:REMOVE_POST_LIKE',
      post_seq: post_seq,
      type: type,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setIsLikeServiceExecute(false);
        if (type == 'LIKE') {
          setlikeCount(data.data.like_count);
        } else {
          const reaction = post.user_reactions[type];
          if (reaction) {
            reaction.count = data.data.like_count;
            reaction.selected = 'NO';
          } else {
            post.user_reactions[type] = {
              count: data.data.like_count,
              selected: 'NO',
            };
          }
          setLikeRemoveType({
            type: type,
            count: data.data.like_count,
          });
        }
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: post_seq,
          type: 'LIKE',
          action_code: 'REMOVE_POST_LIKE',
          reactionType: type,
          count: data.data.like_count,
          profileSeq: profile_seq,
        });
        appData._homePagePostRefresh = 'YES';
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
          setIsLikeServiceExecute(false);
        }
      },
    );
  }
  useEffect(() => {
    if (isLikeServiceExecute) {
      setShowLikeCountBox(true);
    }
    const showCountTimeOut = setTimeout(() => {
      setShowLikeCountBox(false);
    }, 3000);
    return () => {
      clearTimeout(showCountTimeOut);
    };
  }, [likeCount]);
  useEffect(() => {
    const likeTimeoutV = setTimeout(() => {
      setLikeRemoveType({
        type: '',
        count: '',
      });
    }, 3000);
    return () => {
      clearTimeout(likeTimeoutV);
    };
  }, [likeRemoveType.type, likeRemoveType.count]);
  const commentBtnPress = () => {
    setVideoPaused(true);
    navigation.navigate('CommentScreen', {
      postSeq: post_seq,
      postProfileSeq: profile_seq,
    });
  };
  const bookmarkIconBtnClick = () => {
    if (preventDoubleClick(appData.buttonClickTime)) {
      appData.buttonClickTime = new Date();
      if (postIsBookmark) {
        removePostBookmark();
      } else {
        if (expire_on != '9999-12-31') {
          setSnackbarMsg(ErrorMessages.bookmarkPostExpiryHint);
          setsnackBarType('SUCCESS');
          setdisplaySnackbar(true);
          setrefreshSnackBar(Math.random());
        }
        submitPostBookmark();
      }
    }
  };
  function submitPostBookmark() {
    let hashMap = {
      _action_code: '11:ADD_BOOKMARK',
      post_seq: post_seq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setpostIsBookmark(true);
        post.is_bookmarked = 'YES';
        appData._homePagePostRefresh = 'YES';
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: post_seq,
          type: 'BOOKMARK',
          action_code: 'ADD_BOOKMARK',
          reactionType: '',
          count: 0,
          profileSeq: profile_seq,
        });
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
        }
      },
    );
  }
  function removePostBookmark() {
    let hashMap = {
      _action_code: '11:REMOVE_BOOKMARK',
      post_seq: post_seq,
    };
    let connector = new ServerConnector();
    connector.postData(
      hashMap,
      data => {
        // success method
        setpostIsBookmark(false);
        post.is_bookmarked = 'NO';
        homepagePostDataBackup.postStatusChangeData.push({
          post_seq: post_seq,
          type: 'BOOKMARK',
          action_code: 'REMOVE_BOOKMARK',
          reactionType: '',
          count: 0,
          profileSeq: profile_seq,
        });
        appData._homePagePostRefresh = 'YES';
      },
      (errorCode, errorMessage, data) => {
        // failure method
        if (_RedirectionErrorList.includes(errorCode)) {
          RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
        } else {
        }
      },
    );
  }
  return (
    <View style={[style.videoOuter, {height: displayHeight}]}>
      {/* Top Section */}
      <View style={{...style.topSection}}>
        <View style={{marginTop: 28}}>
          {/*Top Profile Section */}
          <TouchableOpacity onPress={() => goToProfile(profile_seq)}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <EntutoTextView style={style.topProfileName}>
                {display_name}
              </EntutoTextView>
              {is_verified == 'YES' ? (
                <Image
                  style={style.verifiedIcon}
                  source={VerifiedIcon}
                  resizeMode={'contain'}
                />
              ) : null}
            </View>
          </TouchableOpacity>
          {cameFrom == 'EPISODE' ? (
            <View style={style.playListBox}>
              <View style={style.playlistCircleBox} />
              <Image
                source={
                  playlistLogo != null
                    ? {uri: playlistLogo}
                    : PlaylistPlaceholder
                }
                style={style.playlistCover}
              />
              <EntutoTextView style={style.playListCountText}>
                {playlistSequence}
              </EntutoTextView>
            </View>
          ) : (
            <>
              <View
                style={{
                  ...style.NSActionIconBox,
                  paddingStart: 0,
                  marginTop: 8,
                }}>
                <Image
                  style={{
                    ...style.NSActionIcon,
                    tintColor: '#FFFFFF',
                    height: 24,
                    width: 24,
                  }}
                  source={PlayViewCountImage}
                  resizeMode="contain"
                />
                <EntutoTextView style={style.NSActionCountTxt}>
                  {playViewCount}
                </EntutoTextView>
              </View>
            </>
          )}
        </View>
      </View>
      {/* Three Dot icon Box */}
      {!blockPost ? (
        <View style={{...style.headerOptionIconBox}}>
          <TouchableOpacity
            style={{paddingHorizontal: 10, paddingBottom: 10}}
            onPress={() => threeDotMenuClick()}>
            <Image
              style={style.headerOptionIcon}
              source={ThreeDotVerticalIcon}
            />
          </TouchableOpacity>
        </View>
      ) : null}
      {blockPost ? (
        <>
          {blockMsg.length != 0 ? (
            <BottomSheetSuccessMsg successMsg={blockMsg} showCloseBtn={false} />
          ) : null}
          {blockMsgDB.length != 0 ? (
            <View style={style.blockErrMsgMainBox}>
              <View style={style.blockErrMsgBox}>
                <EntutoTextView style={style.blockErrMsgHeading}>
                  Post Blocked
                </EntutoTextView>
                <EntutoTextView style={style.blockErrMsg}>
                  {blockMsgDB}
                </EntutoTextView>
              </View>
            </View>
          ) : null}
        </>
      ) : null}

      {isVisible && checkValueLength(media_file) ? (
        <>
          {console.log('---------------Execute----------------', new Date())}

          <TapGestureHandler
            waitFor={doubleTapVideoRef}
            onActivated={onSingleTapVideo}>
            <TapGestureHandler
              maxDelayMs={250}
              ref={doubleTapVideoRef}
              numberOfTaps={2}
              onActivated={onDoubleTap}>
              <Animated.View style={{position: 'relative'}}>
                <FullScreenPlayer
                  playVideo={videoPaused}
                  insets={insets}
                  videoPlayerRef={videoRef}
                  media_cover={media_cover}
                  media_file={media_file}
                  onLoadStart={() => reelsVideoStart()}
                  onLoad={videoOnLoad}
                  height={displayHeight}
                  onSliderValueComplete={onSliderValueComplete}
                  style={{...style.videoView, height: displayHeight}}
                />
                {/* <Video
                                        ref={videoRef}
                                        poster={hasImageUrlExist(media_cover) ? media_cover : null}
                                        posterResizeMode={'cover'}
                                        fullscreenAutorotate={true}
                                        source={{ uri: media_file, cache: true }}
                                        autoPlay={true}
                                        onLoad={videoOnLoad}
                                        onLoadStart={() => reelsVideoStart()}
                                        // onProgress={(e) => onVideoProgress(e)}
                                        repeat={true}
                                        onError={videoError}
                                        resizeMode={'cover'}
                                        muted={(!isVisible && true) || false}
                                        style={[style.videoView, { height: displayHeight, }]}
                                        playInBackground={false}
                                        paused={videoPaused}
                                        ignoreSilentSwitch={'ignore'}
                                    /> */}
                <AnimatedImage
                  source={HeartVideoActive}
                  style={[
                    style.videoPostHeartImage,
                    {
                      shadowOffset: {
                        width: 0,
                        height: Platform.OS == 'ios' ? 2 : 20,
                      },
                      shadowOpacity: 0.35,
                      shadowRadius: Platform.OS == 'ios' ? 2 : 35,
                      position: 'absolute',
                      top: '40%',
                      left: '35%',
                      zIndex: 3,
                    },
                    rStyle,
                  ]}
                  resizeMode={'contain'}
                />
                <VideoOverlayReels />
                {!videoLoaded ? (
                  <View style={style.playBtnBox}>
                    <View style={style.playBtn}>
                      <ActivityIndicator
                        size={32}
                        color={appTheme.colors.primaryColor}
                      />
                    </View>
                  </View>
                ) : null}
                {videoPaused ? (
                  <View style={style.playBtnBox}>
                    <View style={style.playBtn}>
                      <Image
                        style={{
                          ...style.playBtnBoxIcon,
                          tintColor: appTheme.colors.primaryColor,
                        }}
                        source={PlayBtnIcon}
                        resizeMode="cover"
                      />
                    </View>
                  </View>
                ) : null}
              </Animated.View>
            </TapGestureHandler>
          </TapGestureHandler>
        </>
      ) : null}
      {paidPost ? (
        <View style={{...style.lockBox, height: displayHeight}}>
          <VideoOverlayReels />
          <View style={{...style.lockBtnBox, top: '35%'}}>
            <Image
              style={{
                ...style.lockBoxIcon,
                height: 64,
                width: 52,
                marginBottom: 20,
              }}
              source={LockIcon}
              resizeMode="contain"
            />

            {isPaidProfile ? (
              <TouchableOpacity
                style={style.lockButton}
                onPress={() => unlockBtnClick()}>
                <View style={{...style.lockBtnTextBox, alignItems: 'center'}}>
                  <View>
                    <EntutoTextView style={style.lockButtonTxt}>
                      {postUnlockFeesData.unlockMsg}
                    </EntutoTextView>
                  </View>
                  <View>
                    <EntutoTextView style={style.lockButtonPriceTxt}>
                      {CurrencySymbol}
                      {postUnlockFeesData.unlockFees}
                    </EntutoTextView>
                  </View>
                  <View>
                    <EntutoTextView style={style.lockButtonTxt}>
                      {postUnlockFeesData.perMonthTxt}
                    </EntutoTextView>
                  </View>
                </View>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={style.lockButton}
                onPress={() => unlockBtnClick()}>
                <View style={style.lockBtnTextBox}>
                  <View>
                    <EntutoTextView style={style.lockButtonTxt}>
                      {postUnlockFeesData.unlockMsg}
                    </EntutoTextView>
                  </View>
                  <View>
                    <EntutoTextView style={style.lockButtonPriceTxt}>
                      {CurrencySymbol}
                      {postUnlockFeesData.unlockFees}
                    </EntutoTextView>
                  </View>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
      ) : null}
      {!blockPost ? (
        <>
          <View
            style={{
              ...style.likeBtnBox,
              left: 0,
              zIndex: 99,
              bottom: BOTTOM_ACTION_BOX_GAP,
            }}>
            {selectedReactions.map((item, i) => {
              let countValue = 0;
              let isLike = false;
              const dataObj = getValueFromReactions(item);
              if (userReactions.hasOwnProperty(item)) {
                countValue = userReactions[item].count;
                if (userReactions[item].selected == 'YES') {
                  isLike = true;
                }
              }
              if (dataObj != null) {
                return (
                  <View
                    key={i}
                    style={{
                      ...style.NSActionIconBox,
                      ...style.iconGap,
                      paddingStart: 16,
                    }}>
                    <LikeBtnComponent
                      inActiveIcon={dataObj.icon}
                      activeIcon={dataObj.icon}
                      inActiveTintColor={
                        appTheme.colors.fullScreenIconTintColor
                      }
                      disable={disableActionBtn.like}
                      likeButtonPress={() =>
                        likeIconBtnClick(item, countValue, isLike)
                      }
                      isLike={isLike}
                      style={style.reelIcon}
                    />

                    {likeRemoveType.type == item ? (
                      <EntutoTextView style={style.NSActionCountTxt}>
                        {formatLikeNumber(countValue)}
                      </EntutoTextView>
                    ) : null}
                  </View>
                );
              }
            })}
            <View style={{...style.NSActionIconBox, paddingStart: 16}}>
              <LikeBtnComponent
                inActiveIcon={LIKE_ICON}
                activeIcon={LIKE_ICON}
                inActiveTintColor={appTheme.colors.fullScreenIconTintColor}
                disable={disableActionBtn.like}
                likeButtonPress={() =>
                  likeIconBtnClick(REACTION_LIKE, likeCount, postIsLike)
                }
                isLike={postIsLike}
                style={style.reelIcon}
              />
              {showLikeCountBox ? (
                <EntutoTextView style={style.NSActionCountTxt}>
                  {formatLikeNumber(likeCount)}
                </EntutoTextView>
              ) : null}
            </View>
          </View>
          <View
            style={{
              ...style.likeBtnBox,
              right: 0,
              zIndex: 99,
              alignItems: 'flex-end',
              bottom: BOTTOM_ACTION_BOX_GAP,
            }}>
            <View
              style={{
                ...style.NSActionIconBox,
                ...style.iconGap,
                justifyContent: 'flex-end',
              }}>
              <TouchableOpacity
                disabled={disableActionBtn.comment}
                onPress={() => commentBtnPress()}
                style={{flexDirection: 'row'}}>
                <Image
                  style={{
                    ...style.reelIcon,
                    tintColor: showCommentActive
                      ? appTheme.colors.postActiveIconColor
                      : appTheme.colors.fullScreenIconTintColor,
                  }}
                  source={COMMENT_ICON}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
            <View style={{...style.NSActionIconBox, ...style.iconGap}}>
              {showBookmarkIcon ? (
                <BookmarkBtnComponent
                  activeIcon={BOOKMARK_ICON}
                  inActiveIcon={BOOKMARK_ICON}
                  inActiveTintColor={appTheme.colors.fullScreenIconTintColor}
                  disable={disableActionBtn.bookmark}
                  bookmarkButtonPress={() => bookmarkIconBtnClick()}
                  isBookmark={postIsBookmark}
                  style={style.reelIcon}
                />
              ) : null}
            </View>
            <View style={{...style.NSActionIconBox, zIndex: 9}}>
              <TouchableOpacity onPress={() => shareBtnPress()}>
                <Image
                  style={{
                    ...style.NSActionIcon,
                    tintColor: appTheme.colors.fullScreenIconTintColor,
                  }}
                  source={SHARE_ICON}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </View>
          </View>
        </>
      ) : null}
      <ActionSheet
        ref={threeDotMenuSheetRef}
        statusBarTranslucent
        bounciness={4}
        gestureEnabled={false}
        defaultOverlayOpacity={0.3}
        openAnimationSpeed={8}
        onClose={() => {
          playVideo();
        }}>
        <ThreeDotMenuActionView
          navigation={navigation}
          isMyProfile={__profile_seq == profile_seq}
          unlockPost={unlockPost}
          postSeq={post_seq}
          ThreeDotMenuPress={(clickId, obj) => ThreeDotMenuPress(clickId, obj)}
          cameFrom={cameFrom}
        />
      </ActionSheet>
      <ActionSheet
        ref={unlockSheetRef}
        // statusBarTranslucent
        bounceOnOpen={false}
        onClose={() => null}
        closeOnPressBack={false}
        gestureEnabled={false}
        closeOnTouchBackdrop={false}
        defaultOverlayOpacity={0.3}
        openAnimationSpeed={8}>
        <ScrollView
          nestedScrollEnabled={true}
          onMomentumScrollEnd={() =>
            unlockSheetRef.current?.handleChildScrollEnd()
          }
          style={{backgroundColor: appTheme.colors.backgroundColor}}>
          <UnlockPostActionView
            postSeq={post_seq}
            profileSeq={profile_seq}
            amountValue={postUnlockFeesData.unlockFeesValue}
            refVal={unlockSheetRef}
            isPaidProfile={isPaidProfile}
            unlockPostActionClick={(clickId, obj) =>
              unlockPostActionClick(clickId, obj)
            }
          />
        </ScrollView>
      </ActionSheet>
      <ActionSheet
        ref={updateUserLocationRef}
        statusBarTranslucent
        bounciness={4}
        gestureEnabled={false}
        defaultOverlayOpacity={0.3}
        openAnimationSpeed={8}>
        <ScrollView
          nestedScrollEnabled={true}
          onMomentumScrollEnd={() =>
            updateUserLocationRef.current?.handleChildScrollEnd()
          }
          style={{backgroundColor: appTheme.colors.backgroundColor}}>
          <UpdateUserLocationComponent
            navigation={navigation}
            updateUserLocationPopupPress={(clickId, obj) =>
              updateUserLocationPopupPress(clickId, obj)
            }
          />
        </ScrollView>
      </ActionSheet>
    </View>
  );
};

export default React.memo(ReelsVideoComponent);

const styles = theme =>
  StyleSheet.create({
    topSection: {
      position: 'absolute',
      top: Platform.OS == 'ios' ? 48 : 32,
      left: 24,
      zIndex: 6,
    },
    headerOptionIconBox: {
      position: 'absolute',
      top: Platform.OS == 'ios' ? 40 : 32,
      right: 16,
      zIndex: 6,
    },
    topProfileName: {
      color: '#000000',
      fontSize: theme.calculateFontSize(
        theme.dimensions.postFullScreenProfileNameText,
      ),
    },
    verifiedIcon: {
      width: 13,
      height: 13,
      marginLeft: 8,
    },

    videoView: {
      width,
      opacity: 1,
      zIndex: 3,
    },
    videoOuter: {
      width,
      borderWidth: 1,
    },

    playBtnBox: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 4,
    },
    playBtn: {
      height: 63,
      width: 63,
      backgroundColor: '#00000080',
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.5,
      shadowRadius: 1.41,
      zIndex: 3,
    },
    playListBox: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 6,
      paddingLeft: 6,
      position: 'relative',
      // borderWidth: 1,
      height: 32,
    },
    playlistCircleBox: {
      width: 26,
      height: 26,
      borderRadius: 14,
      backgroundColor: '#000000',
      position: 'absolute',
      left: 0,
      top: 3,
      bottom: 0,
      right: 0,
      borderWidth: 1,
      borderColor: '#707070',
    },
    playlistCover: {
      width: 30,
      height: 32,
      resizeMode: 'contain',
      borderRadius: 2,
    },
    playListCountText: {
      marginLeft: 8,
      color: '#000000',
    },
    NSActionIconBox: {
      flexDirection: 'row',

      paddingEnd: 16,
      alignItems: 'center',
    },
    NSActionIcon: {
      width: 24,
      height: 24,
      marginRight: 6,
    },
    NSActionCountTxt: {
      fontSize: theme.calculateFontSize(theme.dimensions.postNSActionCountText),
      color: '#000000',
      marginStart: 6,
    },

    headerOptionIcon: {
      width: 18,
      height: 18,
      resizeMode: 'contain',
      tintColor: '#FFFFFF',
      zIndex: 1000,
    },
    blockErrMsgMainBox: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: Colors.backgroundColor,
      zIndex: 2,
    },
    blockErrMsgBox: {
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 15,
      marginBottom: 15,
    },
    blockErrMsgHeading: {
      fontSize: theme.calculateFontSize(
        theme.dimensions.postBlockErrorHeadingText,
      ),
      fontWeight: '600',
      paddingHorizontal: 15,
      color: Colors.primaryColor,
    },
    blockErrMsg: {
      fontSize: theme.calculateFontSize(theme.dimensions.postBlockErrorMsgText),
      paddingHorizontal: 15,
      color: Colors.errorColor,
    },
    bookmarkWarringBox: {
      borderRadius: 8,
      paddingHorizontal: 8,
      paddingVertical: 8,
      backgroundColor: '#ff323250',
      marginHorizontal: 14,
    },
    bookmarkWarringTxt: {
      color: '#FFF',
      fontSize: theme.calculateFontSize(
        theme.dimensions.postBookmarkWarringText,
      ),
    },
    videoOverlayGrad: {
      position: 'absolute',
      top: -4,
      bottom: -4,
      left: -4,
      right: -4,
      zIndex: 7,
    },
    videoPostHeartImage: {
      width: 120,
      height: 120,
      minHeight: 120,
      maxHeight: 120,
    },
    playBtnBoxIcon: {
      width: 34,
      height: 40,
      // tintColor: '#000',
      backgroundColor: 'transparent',
    },
    lockBox: {
      width: '100%',
      minHeight: 450,
      resizeMode: 'cover',
      borderRadius: 15,
      position: 'relative',
    },
    lockBtnBox: {
      position: 'absolute',
      top: '25%',
      left: 0,
      right: 0,
      alignItems: 'center',
    },
    lockBoxIcon: {
      width: 34,
      height: 40,
    },
    lockButton: {
      borderRadius: 1,
      // backgroundColor: '#FFFFFF',
      backgroundColor: 'rgba(0, 0, 0, 0.2)',
      borderColor: '#FFFFFF',
      borderWidth: 2,
      zIndex: 20,
      elevation: 2,
    },
    lockBtnTextBox: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 15,
    },
    lockButtonTxt: {
      color: '#FFFFFF',
      fontSize: theme.calculateFontSize(theme.dimensions.postLockBtnText),
      fontFamily: theme.getFontFamily('bold'),
    },
    lockButtonPriceTxt: {
      color: '#FFFFFF',
      fontSize: theme.calculateFontSize(theme.dimensions.postLockBtnText),
      fontFamily: theme.getFontFamily('bold'),
    },
    likeBtnBox: {
      position: 'absolute',
      bottom: 30,
    },
    iconGap: {
      marginBottom: 24,
    },
    NSActionIconBox: {
      flexDirection: 'row',
      paddingEnd: 16,
      alignItems: 'center',
    },
    reelIcon: {
      width: 32,
      height: 32,
      marginRight: 6,
    },
  });
