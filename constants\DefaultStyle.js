import { StyleSheet } from "react-native";
import Colors from "./Colors";
import Dimensions from "./Dimensions";

export default StyleSheet.create({
    container: {
        paddingHorizontal: 15,
        marginBottom: 15,

    },
    ListCardStyle: {
        marginVertical: 15,
        marginHorizontal: 15,
        flex: 1,
    },
    withoutPapercontainer: {
        marginHorizontal: 10,
    },
    overlay: {
        flex: 1,
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.5,
        backgroundColor: '#C4C4C4',
        width: '100%',
        height: '100%',
    },
    tabBar: {
        marginVertical: 15,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
    tabBarLabel: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    tabBarLabelTxt: {
        color: Colors.tabInActiveColor,
        fontSize: 14,
        fontWeight: 'bold',
        paddingVertical: 15,
        textTransform: 'uppercase',
        flexWrap: 'nowrap'
    },
    postBtn: {
        color: Colors.primaryColor,
        fontSize: 17,
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
        marginRight: 8,
    },
    popupHeadTxt: {
        color: '#43180B',
        fontSize: 24,
        fontFamily: theme.getFontFamily('bold'),
        marginTop: 16,
        zIndex: 4,
    },
    popupBodyTxt: {
        color: '#000000',
        fontSize: 14,
        marginTop: 10
    },
    popupWarringTxt: {
        color: '#FF4963',
        fontSize: 14,
        marginTop: 10
    },
    popupBtn: {
        flex: 1,
        backgroundColor: '#F2EBE9',
        borderRadius: 14,
    },
    popupBtnText: {
        textAlign: 'center',
        paddingVertical: 16,
        paddingHorizontal: 16,
        fontSize: 20,
        color: '#43180B',
    },
    popupBox: {
        paddingHorizontal: 16,
        paddingBottom: 16,
    },
    fullOneVectorImage: {
        height: Dimensions.screenHeight,
        width: Dimensions.screenWidth / 2,
        position: 'absolute',
        right: 0,
        top: 0,
    },
    dropdownContainer: {
        backgroundColor: 'transparent',
        marginVertical: 8,
        position: 'relative',
    },
    dropdownMain: {
        height: 50,
        borderBottomColor: 'gray',
        borderBottomWidth: 0.5,
        paddingStart: 10,
    },
    dropdownLabel: {
        position: 'absolute',
        backgroundColor: 'transparent',
        left: 0,
        top: -2,
        zIndex: 999,
        paddingHorizontal: 10,
        fontSize: 11,
        color: 'grey'
    },
    dropdownPlaceholderStyle: {
        fontSize: 16,
        color: 'grey'
    },
    dropdownSelectedTextStyle: {
        fontSize: 16,
        color: Colors.primaryTextColor
    },
    dropdownInputSearchStyle: {
        height: 40,
        fontSize: 16,
    },
    dropdownIcon: {
        marginRight: 5,
    },
    dropdownItem: {
        padding: 17,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    dropdownTextItem: {
        flex: 1,
        fontSize: 16,
    },
    dropdownSelectedStyle: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5,
        backgroundColor: '#FDAA6A1A',
        shadowColor: '#000',
        marginTop: 8,
        marginRight: 12,
        paddingHorizontal: 12,
        paddingVertical: 8,
    },
    dropdownTextSelectedStyle: {
        marginRight: 5,
        fontSize: 16,
        color: '#FDAA6A',
    },
    dropdownTextNotSelectedStyle: {
        marginRight: 5,
        fontSize: 16,
    },
    inputUnderLineTxt: {
        color: '#43180B',
        fontSize: 10,
    },
    inputUnderLineView: {
        marginTop: 6,
    },
    errorBoxOutside: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        marginBottom: 15,

    },
    verificationCameraBox: {
        width: 250,
        height: 167,
        backgroundColor: '#F2EBE9',
        borderRadius: 15,
        marginTop: 20,
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderStyle: 'dashed',
        borderColor: Colors.primaryColor,
        elevation: 2,

    },
    veriImage: {
        width: 250,
        height: 167,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 15,
    },
    veriCameraIconStyle: {
        width: 20,
        height: 20,
    },
    veriCameraIconTxt: {
        fontSize: 14,
        fontWeight: '400',
        textAlign: 'center',
        color: Colors.primaryColor,
        marginTop: 15,
    },
    veriCameraIconExtraTxt: {
        fontSize: 12,
        fontWeight: '400',
        textAlign: 'center',
        color: Colors.primaryTextColor,
        marginTop: 4,
    },
    veriCameraBox: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 15,
    },
    veriCameraBtnBox: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    veriCameraBoxBtn: {
        paddingVertical: 8,
        color: '#FFFFFF',
    },
    veriChangeBox: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        flexDirection: 'row',
        borderBottomEndRadius: 15,
        borderBottomStartRadius: 15,

    },
    veriLinearGradient: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        borderRadius: 15,
        borderTopRightRadius: 0,
        borderTopLeftRadius: 0,
    },
    underInputTxtBox: {
        marginTop: 6,
    },
    underInputTxt: {
        color: 'grey',
        fontSize: 12,
    },
    listUnderLineTxt: {
        color: 'grey',
        fontSize: 10,
    },
    listUnderLineView: {
        marginTop: 6,
    },
    customDropdown: {
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomColor: '#00000050',
        borderBottomWidth: 0.5,
        paddingBottom: 15,
        paddingTop: 15,
    },
    customMultiSelectBox: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        flex: 4,
    },
    customMultiSelectSingleTxt: {
        fontSize: 16,
        paddingLeft: 8,
        fontWeight: "600",
    },
    customMultiSelectSinglePH: {
        fontSize: 16,
        color: 'grey',
        paddingLeft: 8,
    },
    errorTxt: {
        fontSize: 12,
        marginTop: 6,
    },
    orTxtLine: {
        height: 0.5,
        flex: 1,
        backgroundColor: '#00000020',
    },
    orTxtBox: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    inputUnderCountBox: {
        position: 'absolute',
        right: 0,
        bottom: -15,
    },
    inputUnderCountBoxTxt: {
        color: Colors.primaryColor,
        fontSize: 12,
    },
    boxWithSwitchBoxDivider: {
        flex: 1,
        borderWidth: 0.5,
        borderColor: '#000000',
        opacity: 0.2
    },
    errorIndicatorBox: {
        flex: 1,
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        // backgroundColor: '#C4C4C450',
        width: '100%',
        height: '100%',
        zIndex: 999,
    },
    boldTagTxt: {
        color: Colors.primaryColor,
        fontWeight: 'bold',
    },
    signupTextBox: {
        paddingStart: 24,
        paddingEnd: 24,
        marginTop: 60
    },
    signupText: {
        color: '#FFFFFF',
        fontSize: 34,
        marginBottom: 4
    },
    signupSmallText: {
        color: '#FFFFFF',
        fontSize: 16,
        marginBottom: 4,
        marginTop: 8
    },
    signUpFormBox: {
        minHeight: 100,
        backgroundColor: '#FFFFFF',
        borderTopEndRadius: 24,
        borderBottomEndRadius: 24,
        flex: 1,
        marginEnd: 44,
        marginTop: 40,
        paddingHorizontal: 24,
        paddingVertical: 15,
    },
    signUpBtnBox: {
        marginTop: 100,
        flexDirection: 'row',
        marginBottom: 80
    },
    signUpBtn: {
        minHeight: 58,
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
        borderTopEndRadius: 58,
        borderBottomEndRadius: 58,
    },
    signUpBtnText: {
        color: '#FFFFFF',
        fontSize: 16,
    }

})
