import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import EntutoTextView from './EntutoTextView'
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSTheme from '../../theme/useSTheme'

const ButtonSheetButton = ({ btnLabel = "", style = {}, onPress = null, backgroundColor = "", btnTextColor = "" }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    return (
        <View style={{ flex: 1, }}>
            <TouchableOpacity
                onPress={onPress}
                style={{ ...defaultStyle.popupBtn, backgroundColor: backgroundColor.length != 0 ? backgroundColor : theme.colors.bottomSheetBtnBackground, ...style }}>
                <EntutoTextView style={{ ...defaultStyle.popupBtnText, color: btnTextColor.length != 0 ? btnTextColor : theme.colors.bottomSheetBtnText }}>
                    {btnLabel}
                </EntutoTextView>
            </TouchableOpacity>
        </View>
    )
}

export default ButtonSheetButton

const styles = StyleSheet.create({})