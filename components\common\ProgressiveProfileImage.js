import React from 'react'
import { StyleSheet, View, Animated } from 'react-native';

const ProgressiveImage = ({ defaultImageSource = require('../../assets/Images/user_image_place_holder.jpg'), source, style, ...props }) => {
    const defaultImageAnimated = new Animated.Value(0);
    const imageAnimated = new Animated.Value(0);

    const handleDefaultImageLoad = () => {
        Animated.timing(defaultImageAnimated, {
            toValue: 1,
            useNativeDriver: true,
        }).start();
    }
    const handleImageLoad = () => {
        Animated.timing(imageAnimated, {
            toValue: 1,
            useNativeDriver: true,
        }).start();
    }

    return (
        <View style={styles.defaultContainer}>
            <Animated.Image
                {...props}
                source={defaultImageSource}
                style={{ ...style, opacity: defaultImageAnimated, }}
                onLoad={handleDefaultImageLoad}
                blurRadius={1}
            />

            <Animated.Image
                {...props}
                source={source}
                style={{ ...style, opacity: imageAnimated, ...styles.imageOverlay }}
                onLoad={handleImageLoad}
            />

        </View>
    )
}

export default ProgressiveImage

const styles = StyleSheet.create({
    defaultContainer: {},
    imageOverlay: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
    }
})
