import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Vibration,
  Animated,
} from 'react-native';
import React, {useRef, useState, useEffect} from 'react';
import useSThemedStyles from '../../theme/useSThemedStyles';
import useSTheme from '../../theme/useSTheme';
import EntutoTextView from './EntutoTextView';
import LottieView from 'lottie-react-native';
import Sound from 'react-native-sound';

const OptionSelectionItem = ({
  isSearchIcon = false,
  onItemSelected = null,
  index = 0,
  label = '',
  value = '',
  isChecked = false,
  showImage = false,
  inactiveImageValue = null,
  activeImageValue = null,
  isAnimated = false,
  lottieFile = null,
  soundFileName = null,
  vibrationPattern = null, // Add new prop for vibration pattern
}) => {
  const style = useSThemedStyles(styles);
  const theme = useSTheme();
  const lottieRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [sound, setSound] = useState(null); // State to hold the sound object

  // Animation value for scaling
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Load the sound when the component mounts or soundFileName changes
  useEffect(() => {
    console.log('OptionSelectionItem: soundFileName prop:', soundFileName);
    if (soundFileName) {
      console.log('OptionSelectionItem: Loading sound:', soundFileName);
      const soundObject = new Sound(soundFileName, Sound.MAIN_BUNDLE, error => {
        if (error) {
          console.log(
            'OptionSelectionItem: Failed to load the sound',
            soundFileName,
            error,
          );
          return;
        }
        // loaded successfully
        console.log(
          'OptionSelectionItem: Sound loaded successfully:',
          soundFileName,
        );
        setSound(soundObject);
      });
    } else {
      console.log('OptionSelectionItem: soundFileName is null or undefined');
    }

    console.log('OptionSelectionItem: useEffect cleanup function');
    // Release the sound when the component unmounts
    return () => {
      if (sound) {
        sound.release();
      }
    };
  }, [soundFileName]); // Re-run effect if soundFileName changes

  // Function to perform scale animation
  const performScaleAnimation = () => {
    // Don't animate if the item is already checked/active
    if (isChecked) return;

    // Scale up to 2x
    Animated.sequence([
      // Scale up to 2x
      Animated.timing(scaleAnim, {
        toValue: 2,
        duration: 1000,
        useNativeDriver: true,
      }),
      // Add a 1 second (1000ms) pause
      Animated.delay(1000),
      // Scale back to original size
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]).start();
  };
  const searchBtnClick = () => {
    if (onItemSelected) {
      onItemSelected('SEARCH', {});
    }
  };

  const onOptionItemClick = () => {
    if (!isChecked) {
      // Play the lottie animation when clicked
      if (isAnimated && lottieRef.current) {
        lottieRef.current.reset();
        lottieRef.current.play();
        setIsPlaying(true);
      }

      // Perform scale animation for inactive items only
      if (isAnimated && showImage) {
        performScaleAnimation();
      }

      // Trigger vibration when selected
      isAnimated && vibrationPattern
        ? Vibration.vibrate(vibrationPattern)
        : Vibration.vibrate(100);

      // Play the sound if loaded and available and animation is enabled
      if (isAnimated && sound) {
        console.log(
          'OptionSelectionItem: Attempting to play sound:',
          soundFileName,
        );
        sound.play(success => {
          if (success) {
            console.log(
              'OptionSelectionItem: Successfully finished playing:',
              soundFileName,
            );
          } else {
            console.log(
              'OptionSelectionItem: Playback failed due to audio decoding errors for:',
              soundFileName,
            );
          }
        });
      }
    }

    if (onItemSelected) {
      onItemSelected('ITEM_CLICK', {index, value, isChecked});
    }
  };

  // Function to handle animation finish
  const onAnimationFinish = () => {
    setIsPlaying(false);
  };

  // Function to render the appropriate icon (Image or Lottie)
  const renderIcon = () => {
    if (isAnimated && lottieFile) {
      return (
        <Animated.View style={{transform: [{scale: scaleAnim}]}}>
          <LottieView
            ref={lottieRef}
            source={lottieFile}
            style={style.listItemIcon}
            loop={false}
            autoPlay={false}
            onAnimationFinish={onAnimationFinish}
          />
        </Animated.View>
      );
    } else {
      return (
        <Image
          source={isChecked ? activeImageValue : inactiveImageValue}
          style={style.listItemIcon}
        />
      );
    }
  };

  return (
    <>
      {isSearchIcon ? (
        <View>
          <TouchableOpacity onPress={() => searchBtnClick()}>
            <View
              style={[
                style.itemContainer,
                {
                  borderColor: theme.colors.optionSelectionItemColor,
                  minWidth: 90,
                },
              ]}>
              <Image
                source={isChecked ? activeImageValue : inactiveImageValue}
                style={style.searchIcon}
              />
            </View>
          </TouchableOpacity>
        </View>
      ) : (
        <View>
          <TouchableOpacity onPress={() => onOptionItemClick()}>
            <View
              style={[
                style.itemContainer,
                {
                  borderColor: isChecked
                    ? theme.colors.primaryColor
                    : theme.colors.optionSelectionItemColor,
                  flexDirection: 'row',
                },
              ]}>
              {showImage ? renderIcon() : null}
              {label ? (
                <EntutoTextView style={style.itemContainerText}>
                  {label}
                </EntutoTextView>
              ) : null}
            </View>
          </TouchableOpacity>
        </View>
      )}
    </>
  );
};

export default OptionSelectionItem;

const styles = theme =>
  StyleSheet.create({
    itemContainer: {
      borderWidth: 1,
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: 36,
      // borderRadius: 36 / 2,
      marginEnd: 8,
      marginBottom: 8,
      minWidth: 90,
      paddingHorizontal: 6,
      paddingVertical: 3,
    },
    itemContainerText: {},
    searchIcon: {
      width: 24,
      height: 24,
      resizeMode: 'contain',
      tintColor: theme.colors.optionSelectionItemColor,
      paddingHorizontal: 8,
    },
    listItemIcon: {
      width: 30,
      height: 30,
      resizeMode: 'contain',
      // tintColor: theme.colors.listItemIcon,
      marginEnd: 3,
    },
  });
