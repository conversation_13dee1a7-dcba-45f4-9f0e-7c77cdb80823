import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import SubheadingTxt from '../common/SubheadingTxt'
import SubheadingBodyTxt from '../common/SubheadingBodyTxt'
import { hasImageUrlExist } from '../../utils/Utils'
import ProgressiveImage from '../common/ProgressiveImage'
import { UserHandlePrefix } from '../../utils/Appconfig'
import useDefaultStyle from '../../theme/useDefaultStyle'
import useSThemedStyles from '../../theme/useSThemedStyles'

const SelectedTagProfile = ({ data, selectedTagPress }) => {
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    return (
        <>
            <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>

                <View style={style.profileImageBox}>
                    <ProgressiveImage
                        style={style.profileImage}
                        source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                        defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                        resizeMode={'cover'}
                    />
                </View>
                <View style={style.profileNameBox}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <SubheadingTxt>{data.display_name}</SubheadingTxt>
                        {
                            data.is_verified == "YES" ?
                                <Image
                                    style={style.verifiedIcon}
                                    source={require('../../assets/Images/icon/verifiedicon.png')}
                                    resizeMode={'contain'}
                                />
                                : null
                        }
                    </View>

                    <SubheadingBodyTxt>{UserHandlePrefix}{data.user_handle}</SubheadingBodyTxt>

                </View>
                <View style={{ marginLeft: 'auto' }}>
                    <TouchableOpacity
                        onPress={() => selectedTagPress("DELETE", { profileSeq: data.profile_seq })}>
                        <Image
                            source={require('../../assets/Images/icon/close_icon.png')}
                            style={style.searchCrossIcon}
                        />
                    </TouchableOpacity>

                </View>
            </View>
        </>
    )
}

export default SelectedTagProfile

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: theme.dimensions.sugProfileImgH,
        width: theme.dimensions.sugProfileImgW,
        borderRadius: theme.dimensions.sugProfileImgR,
    },
    profileNameBox: {
        flexDirection: 'column',
        marginLeft: theme.dimensions.sugProfileImgGapTxt,
    },
    verifiedIcon: {
        width: theme.dimensions.sugVerifiedIconW,
        height: theme.dimensions.sugVerifiedIconH,
        marginLeft: theme.dimensions.veritextLeftmargin,
    },
    searchCrossIcon: {
        height: 15,
        width: 15,
        marginRight: 8,
        padding: 8
    },
})