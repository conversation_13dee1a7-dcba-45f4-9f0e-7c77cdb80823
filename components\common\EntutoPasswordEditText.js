import React, { useContext, useState } from 'react'
import { Image, StyleSheet, Text, View } from 'react-native'
import { TextInput, DefaultTheme, HelperText } from 'react-native-paper'
import useSTheme from '../../theme/useSTheme'
import useSThemedStyles from '../../theme/useSThemedStyles'

const EntutoPasswordEditText = ({ labelTxt = "", placeholderTxt = "", showLeftIcon = false, leftImage = null, ...props }) => {

    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    const isPasswordField = props.hasOwnProperty('isPasswordField') ? props.isPasswordField : false;
    const [eyeIcon, seteyeIcon] = useState("eye-off");
    const [showPassword, setshowPassword] = useState(props.hasOwnProperty('isPasswordField') ? props.isPasswordField : false);
    const showErrorField = props.hasOwnProperty('showErrorField') ? props.showErrorField : false;
    const errorMsg = props.hasOwnProperty('errorMsg') ? props.errorMsg : "";
    const [activeUnderLineColor, setactiveUnderLineColor] = useState(theme.colors.inputOutlineColor)
    const eyeButtonPress = () => {
        setshowPassword(!showPassword);
        if (!showPassword) {
            seteyeIcon("eye-off")
        }
        else {
            seteyeIcon("eye")
        }
    }
    const onInputFocus = (e) => {
        setactiveUnderLineColor(theme.colors.primaryColor)
    }
    const onInputBlur = (e) => {
        setactiveUnderLineColor(theme.colors.inputOutlineColor)
    }
    return <View style={{ flexDirection: 'column', }}>
        <View style={{ flexDirection: 'row', flex: 1, }}>
            <View
                style={{ ...style.inputView, borderColor: activeUnderLineColor }}>
                {
                    showLeftIcon &&
                    <Image
                        style={style.leftIcon}
                        source={leftImage} />
                }

                <TextInput


                    secureTextEntry={showPassword}
                    right={
                        isPasswordField &&
                        <TextInput.Icon name={eyeIcon} onPress={() => eyeButtonPress()} />
                    }

                    label={labelTxt.length != 0 && labelTxt}
                    placeholder={placeholderTxt.length != 0 && placeholderTxt}
                    onFocus={onInputFocus}
                    onBlur={onInputBlur}
                    // selectionColor={theme.colors.primaryColor}
                    // outlineColor={theme.colors.transparentColor}
                    underlineColor={theme.colors.transparentColor}

                    // activeOutlineColor={theme.colors.transparentColor}
                    // activeUnderlineColor={theme.colors.transparentColor}

                    theme={{
                        roundness: theme.dimensions.textInputRadius,
                        colors: {
                            placeholder: theme.colors.inputPlaceholderColor,
                            text: theme.colors.inputTextColor,
                            primary: theme.colors.inputPrimaryColor,

                        },

                    }}
                    style={{
                        ...style.input,
                        backgroundColor: theme.colors.editTextBackgroundColor,
                        ...props.style
                    }}
                    {...props}
                />
                <HelperText type="error" visible={showErrorField}>
                    {errorMsg}
                </HelperText>

            </View>
        </View>
    </View>
}

export default EntutoPasswordEditText

const styles = theme => StyleSheet.create({
    inputView: {
        marginBottom: theme.dimensions.inputTextGap,
        flexDirection: 'row',
        borderBottomWidth: theme.dimensions.inputTextBorderBootom,
        alignItems: "center",

    },
    input: {
        flex: 1,
        paddingVertical: 0,
        fontSize: theme.calculateFontSize(theme.dimensions.inputTextFontSize),
    },
    leftIcon: {
        width: theme.dimensions.inputTextIconWidth,
        height: theme.dimensions.inputTextIconHeight,
    },
    labelTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.inputTextFontSize),
    }
})
