import React from 'react';
import {
  SESSION_ERR_KEY,
  UNAUTH_ERR_KEY,
  LOGIN_FAILED_ERR_KEY,
} from './Appconfig';
import {_clearAllData} from './AuthLogin';
import {CommonActions} from '@react-navigation/native';
import appData from '../data/Data';

export function RedirectionUrlFunction(
  errorCode,
  errorMessage,
  data,
  navigation,
) {
  if (errorCode == SESSION_ERR_KEY) {
    // Session Expired or Access Not Allowed
    _clearAllData();
    appData._userDetails = null;
    navigation.dispatch(
      CommonActions.reset({
        index: 1,
        routes: [
          {
            name: 'LoginScreen',
            params: {ErrorMsg: errorMessage},
          },
        ],
      }),
    );
    // navigation.replace("LoginScreen", {
    //     ErrorMsg: errorMessage,
    // });
  } else if (errorCode == UNAUTH_ERR_KEY) {
    // You are not authorized to perform this action
    _clearAllData();
    appData._userDetails = null;
    navigation.dispatch(
      CommonActions.reset({
        index: 1,
        routes: [
          {
            name: 'LoginScreen',
            params: {ErrorMsg: errorMessage},
          },
        ],
      }),
    );
    // navigation.replace("LoginScreen", {
    //     ErrorMsg: errorMessage,
    // });
  } else if (errorCode == LOGIN_FAILED_ERR_KEY) {
    // Login Failed
    _clearAllData();
    appData._userDetails = null;
    navigation.dispatch(
      CommonActions.reset({
        index: 1,
        routes: [
          {
            name: 'LoginScreen',
            params: {ErrorMsg: errorMessage},
          },
        ],
      }),
    );
    // navigation.replace("LoginScreen", {
    //     ErrorMsg: errorMessage,
    // });
  }
}
