import { StyleSheet, Text, View, ScrollView, Image, Pressable, Modal } from 'react-native'
import React, { useState, useEffect, useContext, useRef } from 'react'
import CustomStatusBar from '../components/common/CustomStatusBar';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import EntutoTextView from '../components/common/EntutoTextView';
import { TouchableOpacity } from 'react-native-gesture-handler';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import EntutoEditText from '../components/common/EntutoEditText';
import CustomSnackbar from '../components/common/CustomSnackbar';
import { AppStateContext } from '..';
import { FAB } from 'react-native-paper';
import { decodeHtmlEntitessData, encodeHtmlEntitessData, hasImageUrlExist, _inputFormatTextForTag } from '../utils/Utils';
import ServerConnector from '../utils/ServerConnector';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import { TAGGED_SYMBOL, _RedirectionErrorList } from '../utils/Appconfig';
import appData from '../data/Data';
import ErrorMessages from '../constants/ErrorMessages';
import InputTagPeopleSearchComponent from '../components/tag/InputTagPeopleSearchComponent';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const EditPostScreen = ({ navigation, route }) => {
    const { postSeq } = route.params;
    const [description, setdescription] = useState("");
    const [errorMsg, seterrorMsg] = useState("");
    const [refresfErrorMsg, setrefresfErrorMsg] = useState(Math.random());
    const { defaultStyle } = useDefaultStyle();
    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [showLoading, setShowLoading] = useState(false);
    const [snackBarType, setsnackBarType] = useState("FAILED");
    const { fullUserDetails } = useContext(AppStateContext);
    const __is_profile_paid = fullUserDetails.hasOwnProperty("_user_account_type") ? fullUserDetails._user_account_type == "PAID" ? true : false : false;

    const [mediaUri, setmediaUri] = useState(null);
    const [mediaThumbnailUri, setmediaThumbnailUri] = useState(null);
    const [mediaType, setmediaType] = useState("IMAGE");

    const [showTagPopup, setshowTagPopup] = useState(false);
    const [tagName, settagName] = useState("");
    const descInputRef = useRef(null);
    const theme = useSTheme();

    useEffect(() => {
        setShowLoading(true);
        getPostUserDataService();
    }, []);
    function getPostUserDataService() {
        let hashMap = {
            _action_code: "11:GET_USER_POST",
            post_seq: postSeq
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setmediaUri(data.data[0].media_file);
            setmediaType(data.data[0].media_type);
            setmediaThumbnailUri(data.data[0].media_cover);
            let post_comments = ""
            if (data.data[0].post_comments !== null) {
                let decodeBio = data.data[0].post_comments.replace(/<br\s*[\/]?>/gi, '\n');
                post_comments = decodeHtmlEntitessData(decodeBio);;
            }
            setdescription(post_comments);
            setShowLoading(false);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setShowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                seterrorMsg(errorMessage);
            }
        });
    }
    const descriptionChangeHandler = (text) => {
        let txtNewLineArr = text.split("\n");
        let lastNewLineWord = txtNewLineArr[txtNewLineArr.length - 1];
        let textStArr = lastNewLineWord.split(" ");
        let lastIndex = textStArr.length - 1;
        let lastWord = textStArr[lastIndex]
        if (lastWord == TAGGED_SYMBOL) {//.charAt(0)
            setshowTagPopup(true);
            settagName(lastWord);
        }
        else {
            settagName("");
            setshowTagPopup(false);
        }
        setdescription(text)
    }
    const playVideo = () => {
        navigation.navigate("VideoDisplayScreen", {
            mediaUri: mediaUri,
            thumbnailUri: mediaThumbnailUri
        })

    }
    const showImage = () => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: mediaUri
        })

    }
    const imageBtnPress = () => {
        if (mediaType == "IMAGE") {
            showImage();
        }
    }
    const postBtnPress = async () => {
        // console.log("ss")
        if (description.length == 0) {
            if (selection != "FREE" || __is_profile_paid) {
                seterrorMsg(ErrorMessages.addPostCaptionErr);
                setrefresfErrorMsg(Math.random());
                return;
            }
        }
        setShowLoading(true);
        updatePostService();
    }
    function updatePostService() {
        let encodeSt = encodeHtmlEntitessData(description);
        let hashMap = {
            _action_code: "11:UPDATE_POST_COMMENT",
            post_seq: postSeq,
            comments: encodeSt,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setSnackbarMsg(data.msg);
            setdisplaySnackbar(true);
            setsnackBarType("SUCCESS");
            setrefreshSnackBar(Math.random());
            setShowLoading(false);
            seterrorMsg("");
            appData._profilePagePostRefresh = true;
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setShowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                seterrorMsg(errorMessage);
            }
        });
    }
    const tagPeopleSearchPress = (clickID, obj) => {
        setshowTagPopup(false);
        if (clickID == "SELECTED") {
            let formatTxt = _inputFormatTextForTag(description, TAGGED_SYMBOL + obj.user_handle);
            setshowTagPopup(false);
            setdescription(formatTxt + " ");
            descInputRef.current.focus()
        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar title="Edit Post" showTopButton={true} navigation={navigation}
                buttonComponent={<Pressable disabled={showLoading} onPress={() => { postBtnPress() }}
                ><EntutoTextView style={defaultStyle.postBtn}>UPDATE</EntutoTextView></Pressable>} />
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside}>
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refresfErrorMsg} />
                    // </View>
                    : null
            }
            <Modal
                animationType="fade"
                visible={showTagPopup}
                onRequestClose={() => setshowTagPopup(false)}
                style={{ margin: 0, flex: 1 }}>
                <InputTagPeopleSearchComponent
                    tagPeopleSearchPress={tagPeopleSearchPress}
                    navigation={navigation}
                    preSearchStr={tagName}
                    captionTxt={description} />
            </Modal>
            <ScrollView
                style={{ backgroundColor: theme.colors.backgroundColor }}>
                <View style={defaultStyle.container}>
                    <View style={styles.addMediaBox}>
                        {
                            mediaType == "IMAGE" ?
                                <TouchableOpacity onPress={() => imageBtnPress()}>
                                    <Image
                                        style={styles.addMediaImage}
                                        source={hasImageUrlExist(mediaUri) ? { uri: mediaUri } : null}
                                        resizeMode='cover' />
                                </TouchableOpacity>
                                : null
                        }
                        {
                            mediaType == "VIDEO" ?
                                <>
                                    <Image
                                        style={styles.addMediaImage}
                                        source={hasImageUrlExist(mediaThumbnailUri) ? { uri: mediaThumbnailUri } : null}
                                        resizeMode='cover' />

                                    <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, justifyContent: 'center', alignItems: 'center' }}>
                                        <FAB
                                            style={{ backgroundColor: '#FFFFFF', }}
                                            small
                                            icon="play-circle-outline"
                                            onPress={() => playVideo()}
                                        />
                                    </View>
                                </>
                                : null
                        }
                    </View>


                    <View style={{ ...styles.addNewDesc, marginBottom: 20 }}>
                        <EntutoEditText
                            refValue={descInputRef}
                            multiline
                            placeholderTxt="Edit New Description"
                            labelTxt="Edit New Description"
                            value={description}
                            maxLength={1000}
                            maxHeight={150}
                            onChangeText={(text) => descriptionChangeHandler(text)} />
                        <View style={defaultStyle.inputUnderCountBox}>
                            <EntutoTextView style={defaultStyle.inputUnderCountBoxTxt}>{description.length}/1000</EntutoTextView>
                        </View>
                    </View>
                </View>
            </ScrollView>
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />
        </>
    )
}

export default EditPostScreen;

const styles = StyleSheet.create({
    addMediaBox: {
        flexDirection: 'column',
        backgroundColor: '#F2EBE9',
        borderRadius: 5,
        paddingHorizontal: 10,
        paddingVertical: 22,

    },
    addNewDesc: {
        marginVertical: 20,
    },
    addMediaImage: {
        width: '100%',
        height: 200,
        marginBottom: 15,
        // borderRadius: 15,
    },
})