import React, { useContext } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import ProgressiveImage from './common/ProgressiveImage'
import SubheadingBodyTxt from './common/SubheadingBodyTxt'
import SubheadingTxt from './common/SubheadingTxt'
import { hasImageUrlExist } from '../utils/Utils'
import { UserHandlePrefix } from '../utils/Appconfig'
import { AppStateContext } from '..'
import useDefaultStyle from '../theme/useDefaultStyle'
import useSTheme from '../theme/useSTheme'
import useSThemedStyles from '../theme/useSThemedStyles'
const SuggestionProfile = ({ navigation, data, ...props }) => {
    const { fullUserDetails } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const goToProfile = (profileSeq) => {
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
            <View style={style.profileImageBox}>
                <ProgressiveImage
                    style={style.profileImage}
                    source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                    defaultImageSource={require("../assets/Images/full_user_image_place_holder.png")}
                    resizeMode={'cover'}
                />

            </View>
            <View style={style.profileNameBox}>
                <TouchableOpacity onPress={() => goToProfile(data.profile_seq)}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <SubheadingTxt>{data.display_name}</SubheadingTxt>
                        {
                            data.is_verified == "YES" ?
                                <Image
                                    style={style.verifiedIcon}
                                    source={require('../assets/Images/icon/verifiedicon.png')}
                                    resizeMode={'contain'}
                                />
                                : null
                        }
                    </View>

                    <SubheadingBodyTxt>{UserHandlePrefix}{data.user_handle}</SubheadingBodyTxt>
                </TouchableOpacity>
            </View>

        </View>
    )
}

export default SuggestionProfile;

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    profileImageBox: {
        position: 'relative'
    },
    profileImage: {
        height: theme.dimensions.sugProfileImgH,
        width: theme.dimensions.sugProfileImgW,
        borderRadius: theme.dimensions.sugProfileImgR,
    },
    profileNameBox: {
        flexDirection: 'column',
        marginLeft: theme.dimensions.sugProfileImgGapTxt,
    },
    verifiedIcon: {
        width: theme.dimensions.sugVerifiedIconW,
        height: theme.dimensions.sugVerifiedIconH,
        marginLeft: theme.dimensions.veritextLeftmargin,
    },
})
