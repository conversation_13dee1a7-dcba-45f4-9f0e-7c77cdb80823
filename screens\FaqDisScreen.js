import React, { useState } from 'react'
import { ActivityIndicator, StyleSheet, View } from 'react-native'
import HomeTopNavigationBar from '../components/HomeTopNavigationBar'
import CustomStatusBar from '../components/common/CustomStatusBar';
import { WebView } from 'react-native-webview';
import Dimensions from '../constants/Dimensions';

const FaqDisScreen = ({ navigation }) => {
    const [showLoading, setshowLoading] = useState(true)
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar showBackBtn={true} showBorderBottom={false} title="FAQ's" navigation={navigation} />

            <View style={{ minHeight: Dimensions.screenHeight - 70 }}>
                {
                    showLoading ?
                        <ActivityIndicator size={'large'} />
                        : null
                }

                <WebView source={{ uri: 'https://www.sotrue.co.in/faq.html' }}
                    automaticallyAdjustContentInsets={false}
                    androidLayerType="software"
                    onLoadEnd={() => {
                        setshowLoading(false);
                    }} />

            </View>
        </>
    )
}

export default FaqDisScreen

const styles = StyleSheet.create({})
