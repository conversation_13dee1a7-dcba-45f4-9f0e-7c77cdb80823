import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import EntutoTextView from '../common/EntutoTextView';
import useSThemedStyles from '../../theme/useSThemedStyles';

const LoginModuleTitle = ({
  firstTitleText = '',
  secondTitleText = '',
  style = {},
}) => {
  const style2 = useSThemedStyles(styles);
  return (
    <View style={style}>
      <EntutoTextView style={style2.titleText}>{firstTitleText}</EntutoTextView>
      {secondTitleText && (
        <EntutoTextView style={style2.titleText}>
          {secondTitleText}
        </EntutoTextView>
      )}
    </View>
  );
};

export default LoginModuleTitle;

const styles = theme =>
  StyleSheet.create({
    titleText: {
      fontSize: theme.calculateFontSize(theme.dimensions.loginModuleTitleText),
    },
  });
