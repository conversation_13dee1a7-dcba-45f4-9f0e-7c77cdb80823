import React, { useRef, useState } from 'react'
import { Alert, Image, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { RNCamera } from 'react-native-camera';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import EntutoTextView from '../components/common/EntutoTextView';
import CustomStatusBar from '../components/common/CustomStatusBar';
import ErrorMessages from '../constants/ErrorMessages';


const CameraScreen = ({ route, navigation }) => {
    const { cameraType, cameFrom } = route.params;
    const isOnlyFrontCam = route.params.hasOwnProperty("isOnlyFrontCam") ? route.params.isOnlyFrontCam : false;
    const IS_IOS = Platform.OS == 'ios';
    const MAX_ZOOM = 8; // iOS only
    const ZOOM_F = IS_IOS ? 0.01 : 0.1;
    const BACK_TYPE = RNCamera.Constants.Type.back;
    const FRONT_TYPE = RNCamera.Constants.Type.front;

    //Auth Text
    const cameraNotAuthorized =
        <EntutoTextView transparent style={styles.cameraNotAuthorized}>
            Camera access was not granted. Please go to your phone's settings and allow camera access.
        </EntutoTextView>;


    let cameraRef;
    const [cameraReady, setcameraReady] = useState(false);
    const [flash, setflash] = useState(RNCamera.Constants.FlashMode.off);
    const [flashMode, setflashMode] = useState("off");
    const [takingPic, settakingPic] = useState(false);
    const [recording, setrecording] = useState(false);
    const [elapsed, setelapsed] = useState(0);
    const [videoSource, setvideoSource] = useState(null);

    const [frontCam, setfrontCam] = useState(isOnlyFrontCam);

    const flashIcons = {
        'on': <MaterialCommunityIcons name='flash' color={'#FFF'} size={30} />,
        'auto': <MaterialCommunityIcons name='flash-auto' color={'#FFF'} size={30} />,
        'off': <MaterialCommunityIcons name='flash-off' color={'#FFF'} size={30} />,
        'torch': <MaterialCommunityIcons name='flashlight' color={'#FFF'} size={30} />,
    }


    const toggleTorch = () => {
        if (flashMode === 'torch') {
            setflashMode('off')
        } else if (flashMode === 'off') {
            setflashMode('auto')
        } else if (flashMode === 'auto') {
            setflashMode('on')
        } else if (flashMode === 'on') {
            setflashMode('torch')
        }
    }
    const rotateCamera = () => {
        setfrontCam(!frontCam);
    }
    const closeCamera = () => {
        navigation.goBack();
    }
    const onPictureTaken = () => {
        settakingPic(false);
    }
    const takePicture = async () => {
        if (cameraRef) {
            if (takingPic || !cameraReady) {
                return;
            }
            let options = {
                quality: 0.85,
                fixOrientation: true,
                forceUpOrientation: true,
                writeExif: true,
            };
            settakingPic(true);
            let data = null;

            try {
                data = await cameraRef.takePictureAsync(options);
            }
            catch (err) {
                Alert.alert("Error", "Failed to take picture: " + (err.message || err));
                return;
            }
            navigation.replace("CaptureMediaDisplayScreen", {
                uriData: data.uri,
                captureType: "IMAGE", imageData: data,
                cameraType: cameraType, cameFrom: cameFrom
            });
            // Alert.alert("Picture Taken!", JSON.stringify(data, null, 2));
        }
    }
    const onCameraReady = () => {
        if (!cameraReady) {
            setcameraReady(true)
        }
    }

    const onCameraMountError = () => {
        setTimeout(() => {
            Alert.alert("Error", "Camera start failed.");
        }, 150);
    }
    const recordVideo = async () => {
        if (cameraRef) {
            try {
                const videoRecordPromise = cameraRef.recordAsync();

                if (videoRecordPromise) {
                    setrecording(true);
                    const data = await videoRecordPromise;
                    const source = data.uri;
                    if (source) {
                        navigation.replace("CaptureMediaDisplayScreen", {
                            uriData: source, captureType: "VIDEO",
                            imageData: data,
                            cameraType: cameraType, cameFrom: cameFrom
                        });
                        // setvideoSource(source);
                    }
                }
            } catch (error) {
                console.warn(error);
            }
        }
    };

    const stopVideoRecording = () => {
        if (cameraRef) {
            setrecording(false);
            cameraRef.stopRecording();
        }
    };

    return (
        <>
            <CustomStatusBar translucent={false} hidden={true} />
            <View style={styles.cameraContainer}>
                <RNCamera
                    ref={ref => (cameraRef = ref)}
                    style={styles.cameraView}
                    flashMode={flashMode}
                    type={
                        frontCam ?
                            RNCamera.Constants.Type.front
                            : RNCamera.Constants.Type.back
                    }
                    useNativeZoom={true}
                    focusable={true}
                    autoFocus={RNCamera.Constants.AutoFocus.on}
                    androidCameraPermissionOptions={{
                        title: ErrorMessages.permissionCameraHeaderMsg,
                        message: ErrorMessages.permissionCameraMsg,
                        buttonPositive: 'Ok',
                        buttonNegative: 'Cancel',
                    }}
                    androidRecordAudioPermissionOptions={{
                        title: ErrorMessages.permissionAudioHeaderMsg,
                        message: ErrorMessages.permissionAudioMsg,
                        buttonPositive: 'Ok',
                        buttonNegative: 'Cancel',
                    }}
                    onCameraReady={onCameraReady}
                    onMountError={onCameraMountError}
                    onPictureTaken={onPictureTaken}
                    defaultVideoQuality={RNCamera.Constants.VideoQuality["360p"]}
                    captureAudio={true}
                    notAuthorizedView={
                        <View>
                            {cameraNotAuthorized}
                        </View>
                    }
                />
                {
                    cameraReady &&
                    <>
                        <View style={styles.bottomCameraBox} >

                            {
                                recording ?
                                    <TouchableOpacity style={styles.videoIconBox} onPress={() => stopVideoRecording()}>
                                        <View style={styles.videoStopIcon} />
                                    </TouchableOpacity>
                                    :
                                    <>
                                        {
                                            cameraType == "ALL" || cameraType == "VIDEO" ?
                                                <TouchableOpacity style={styles.videoIconBox} onPress={() => recordVideo()}>
                                                    <View style={styles.videoCameraIcon} />
                                                </TouchableOpacity>
                                                : null

                                        }

                                    </>


                            }

                            {
                                !recording ?
                                    <>
                                        <View style={styles.captureBox}>
                                            <TouchableOpacity onPress={() => takePicture()} style={styles.capture}>
                                                <View style={styles.captureButton} />
                                            </TouchableOpacity>
                                        </View>
                                        {
                                            !isOnlyFrontCam ?
                                                <TouchableOpacity style={styles.rotateCamera} onPress={() => rotateCamera()}>
                                                    <MaterialIcons name="flip-camera-ios" color={'#FFF'} size={40} />
                                                </TouchableOpacity>
                                                : null
                                        }

                                    </>
                                    : null
                            }
                        </View>
                        <View style={styles.topCameraBox}>
                            <TouchableOpacity onPress={() => toggleTorch()} >
                                {flashIcons[flashMode]}

                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => closeCamera()} style={{ marginLeft: 'auto' }}>
                                <MaterialIcons name="close" color={'#FFF'} size={35} />
                            </TouchableOpacity>
                        </View>
                    </>
                }
                {(takingPic || recording) ?
                    <View
                        style={styles.capturingStyle}
                    >
                        {takingPic ?
                            <EntutoTextView style={styles.progressTxt}>Capturing Picture...</EntutoTextView> :
                            <EntutoTextView style={styles.progressTxt}>{`Capturing Video`}</EntutoTextView>}
                    </View>
                    : null}
            </View>


        </>
    )
}

export default CameraScreen;

const styles = StyleSheet.create({
    cameraContainer: {
        flex: 1,
        flexDirection: 'column',
        backgroundColor: 'black',
        position: 'relative',

    },
    cameraView: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'center',
        padding: 15,
        borderRadius: 20,
    },
    bottomCameraBox: {
        position: 'absolute',
        bottom: 10,
        left: 0,
        right: 0,
        flexDirection: 'row',
        margin: 15,
        // justifyContent: 'space-evenly',
        alignItems: 'baseline',
    },
    captureBox: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    capture: {
        borderColor: '#FFF',
        borderRadius: 60,
        borderWidth: 2,
        padding: 2,

    },
    captureButton: {
        width: 60,
        height: 60,
        backgroundColor: '#FFF',
        borderRadius: 60,
    },
    videoIconBox: {
        borderColor: '#FFF',
        borderRadius: 60,
        borderWidth: 2,
        padding: 2,
    },
    videoCameraIcon: {
        width: 40,
        height: 40,
        backgroundColor: 'red',
        borderRadius: 60,
    },
    videoStopIcon: {
        width: 20,
        height: 20,
        backgroundColor: '#FFF',
        margin: 10,
    },
    rotateCamera: {
        marginLeft: 'auto',
    },
    topCameraBox: {
        position: 'absolute',
        top: 10,
        left: 0,
        right: 0,
        marginHorizontal: 15,
        flexDirection: 'row',
        alignItems: 'center',
    },
    cameraNotAuthorized: {
        padding: 20,
        paddingTop: 35,
        color: '#FFF'
    },
    capturingStyle: {
        position: 'absolute',
        bottom: 0,
        width: '100%',
        backgroundColor: 'rgba(0,0,0,0.8)',
        padding: 15,

    },
    progressTxt: {
        color: '#FFF',
    }

})
