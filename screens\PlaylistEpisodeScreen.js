import { FlatList, Image, ImageBackground, Platform, Pressable, ScrollView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useCallback, useEffect, useState } from 'react'
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import LinearGradient from 'react-native-linear-gradient';
import { checkValueLength, decodeHtmlEntitessData, hasImageUrlExist, onMaxTextLengthReached, pluralize, secondsToHms } from '../utils/Utils';
import BackBtn from '../assets/Images/icon/back.png';
import EntutoTextView from '../components/common/EntutoTextView';
import { useSafeAreaFrame, useSafeAreaInsets } from 'react-native-safe-area-context';
import CustomStatusBar from '../components/common/CustomStatusBar';

import PlayIcon from '../assets/Images/icon/play_btn.png'
import LikeBtnComponent from '../components/common/LikeBtnComponent';
import FullSActiveBookmark from '../assets/Images/icon/bookmark.png';
import FullSInActiveLikeBookmark from '../assets/Images/icon/bookmark.png';
import TempData from '../data/TempData';
import ServerConnector from '../utils/ServerConnector';
import { _RedirectionErrorList, MaxPlaylistClipTxtLimit, MaxPlaylistDescTxtLimit, MaxPlaylistTitleTxtLimit } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import Dimensions from '../constants/Dimensions';

const FIXED_RATIO = 1.15;
const PlaylistEpisodeScreen = ({ navigation, route }) => {
    const episodeSeq = route.params != undefined ? route.params.episodeSeq : -1;
    const insets = useSafeAreaInsets();
    const COVER_HEIGHT = Platform.OS == 'ios' ? 219 - (insets.top / 2) : 219;
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [errorMsg, setErrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const [pageErrorMessage, setPageErrorMessage] = useState("");
    const [isEpisodeResume, setIsEpisodeResume] = useState(false);
    const [episodeDetails, setEpisodeDetails] = useState({
        episodeLogo: null,
        episodeTitle: "",
        episodeDescription: "",
        episodeThumbnail: null,
        episodePreview: null,
        clipCount: 0,
        yearOfReleased: "",
    });
    const [resumePostSeq, setResumePostSeq] = useState("");
    useEffect(() => {
        if (TempData.episodeData.hasOwnProperty("episode_seq")) {
            setEpisodeDetails(prevState => {
                return {
                    ...prevState,
                    episodeLogo: TempData.episodeData.title,
                    episodeTitle: TempData.episodeData.title,
                    episodeDescription: TempData.episodeData.description,
                    episodeThumbnail: checkValueLength(TempData.episodeData.banner_file) ? TempData.episodeData.banner_file : TempData.episodeData.url,
                    episodePreview: TempData.episodeData.preview_file,
                    clipCount: 0,
                    yearOfReleased: TempData.episodeData.release_year,
                }
            })
        }
    }, []);
    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            if (TempData.refreshPlayList == "YES") {
                TempData.refreshPlayList = "NO";
                let isResume = false;
                TempData.playlistResumeData.map(item => {
                    if (item.episodeSeq === episodeSeq) {
                        isResume = true;
                        setResumePostSeq(item.playPostSeq);
                    }
                });
                setIsEpisodeResume(isResume);
            }
        });
        return unsubscribe;
    }, [navigation]);

    useEffect(() => {
        getEpisodeDetailsService();
    }, []);
    const [clipList, setClipList] = useState([]);
    //Services
    const getEpisodeDetailsService = () => {
        setShowLoading(true);
        let hashMap = {
            _action_code: "11:GET_PLAYLIST_CLIPS",
            episode_seq: episodeSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setPageErrorMessage("");
            setClipList(data.data);
            setEpisodeDetails(prevState => {
                return {
                    ...prevState,
                    clipCount: data.data.length,
                }
            })
            setShowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setClipList([]);
                setPageErrorMessage(errorMessage)
            }
        });
        // let isResume = false;
        // TempData.playlistResumeData.map(item => {
        //     if (item.episodeSeq === episodeSeq) {
        //         isResume = true;
        //         setResumePostSeq(item.playPostSeq);
        //     }
        // });
        // setIsEpisodeResume(isResume);
    }

    const EpisodeClipItem = ({ data, clipItemPress, index }) => {
        const decodePostCmt = decodeHtmlEntitessData(data.post_comments);
        const [applyRatio, setApplyRatio] = useState(false);

        useEffect(() => {
            if (hasImageUrlExist(data.media_cover)) {
                Image.getSize(data.media_cover, (width, height) => {
                    let isApplied = false;
                    if (parseInt(width) > 0 && parseInt(height) > 0) {
                        let computeValue = parseInt(height) / parseInt(width);
                        if (computeValue >= FIXED_RATIO) {
                            isApplied = true;
                        }
                    }
                    setApplyRatio(isApplied);

                });
            }
        }, [])

        return (
            <View>
                <TouchableOpacity onPress={() => clipItemPress("ITEM", { index: index, })}>
                    <View style={style.episodeClipItemContainer}>

                        <View style={style.episodeClipItemLeftContainer}>

                            <Image
                                source={hasImageUrlExist(data.media_cover) ? { uri: data.media_cover } : null}
                                style={[
                                    style.episodeClipItemLeftImage,
                                    applyRatio ? style.episodeClipItemLeftImageRatio : null
                                ]}
                            />

                        </View>

                        <View style={style.episodeClipItemRightContainer}>
                            <Text allowFontScaling={false} style={{ ...style.episodeClipItemLeftText, marginBottom: 8 }} numberOfLines={3}>
                                {onMaxTextLengthReached(decodePostCmt, MaxPlaylistClipTxtLimit, "")}
                            </Text>
                            <View style={style.bottomTextBox}>
                                <EntutoTextView style={style.episodeClipItemClipText}>Clip {index + 1}</EntutoTextView>
                                <EntutoTextView style={style.episodeClipItemClipText}>{secondsToHms(data.video_duration)}</EntutoTextView>
                            </View>
                            {/* <Text style={style.episodeClipItemLeftText}>2m 34s</Text> */}
                        </View>
                        {/* <View style={{ marginStart: 'auto' }}>
                    <LikeBtnComponent
                        isLike={data.is_bookmarked == "YES"}
                        inActiveTintColor={"#CCC"}
                        activeTintColor={theme.colors.primaryColor}
                        inActiveIcon={FullSInActiveLikeBookmark}
                        activeIcon={FullSActiveBookmark}
                        likeButtonPress={() => clipItemPress("BOOKMARK", { index: index, })}
                        style={style.reactionIcon} />
                </View> */}
                    </View>
                </TouchableOpacity>
            </View>
        )
    }
    const backButtonPress = () => {
        navigation.goBack(null)
    }

    const HeaderComponent = () => {
        return <>
            <ImageBackground
                source={hasImageUrlExist(episodeDetails.episodeThumbnail) ? { uri: episodeDetails.episodeThumbnail } : null}
                style={{ ...style.playlistCoverImage, height: COVER_HEIGHT }}
                defaultImageSource={require("../assets/Images/default_image.jpg")} >
                {
                    theme.appThemeType == "DARK" ?
                        <LinearGradient
                            colors={[theme.colors.playlistLCOne, theme.colors.playlistLCTwo, theme.colors.playlistLCThree, theme.colors.playlistLCFour]}
                            // locations={[0, 0.45, 0.45, 0.9]}
                            style={style.linearGradient} />
                        :
                        <LinearGradient
                            colors={[theme.colors.playlistLCOne, theme.colors.playlistLCThree, theme.colors.playlistLCFour]}
                            // locations={[0, 0.45, 0.45, 0.9]}
                            style={style.linearGradient} />
                }
            </ImageBackground>
            <View style={{
                ...style.playlistCoverContainer, minHeight: COVER_HEIGHT,
                marginBottom: 10
            }}>
                <View style={{ ...style.topHeader, marginTop: Platform.OS == 'ios' ? 10 : 40 }}>
                    <View>
                        <Pressable onPress={() => backButtonPress()}
                            android_ripple={{
                                color: theme.colors.pressableRippleColor, borderless: true,
                                radius: 30,
                            }}>
                            <View style={{ padding: 10, }}>
                                <Image
                                    style={style.backBtnIcon}
                                    resizeMode='contain'
                                    source={BackBtn}
                                />
                            </View>
                        </Pressable>
                    </View>

                </View>
            </View>
            <View style={style.contentBody}>
                <View style={style.playlistTitleBox}>
                    <View style={style.titleBox}>
                        <EntutoTextView style={style.episodeTitle}>
                            {onMaxTextLengthReached(episodeDetails.episodeTitle, MaxPlaylistTitleTxtLimit, "")}
                        </EntutoTextView>
                    </View>
                    <View style={style.yearCountBox}>
                        <EntutoTextView style={style.yearCountBoxText}>{episodeDetails.yearOfReleased}</EntutoTextView>
                        <EntutoTextView style={{ ...style.yearCountBoxText, marginTop: 6 }}>
                            {pluralize("Clip", episodeDetails.clipCount) + " " + episodeDetails.clipCount}
                        </EntutoTextView>
                    </View>
                </View>
                <View style={style.descBox}>
                    <EntutoTextView style={style.descBoxText}>
                        {onMaxTextLengthReached(episodeDetails.episodeDescription, MaxPlaylistDescTxtLimit, "")}
                    </EntutoTextView>
                </View>


                {/* <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                        {
                            pageErrorMessage.length == 0 ?
                                <View>
                                    <TouchableOpacity onPress={() => playEpisodeBtnPress()}>
                                        <View style={style.playBtnBox}>
                                            <Image source={PlayIcon} style={style.btnIcon} />
                                            <EntutoTextView style={style.btnText}>{isEpisodeResume ? "Resume" : "Play"}</EntutoTextView>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                : null}
                    </View> */}
            </View>
            {
                pageErrorMessage.length != 0 ?
                    <SuccessFailureMsgBox visibleAllTime={true} alertMsg={pageErrorMessage} alertKey={pageErrorMessage.length} />
                    : null
            }

        </>
    }
    const clipItemPress = (clickID, obj) => {
        if (clickID == "ITEM") {
            const dataObj = clipList[obj.index];
            storePlaylistResumeData(dataObj.post_seq);
            navigation.navigate("VideoContentScreen", {
                postSeq: dataObj.post_seq, postProfileSeq: dataObj.profile_seq, episodeSeq: episodeSeq,
                cameFrom: "EPISODE"
            });
        }
        else if (clickID == "BOOKMARK") {
            const dataObj = clipList[obj.index];
            if (dataObj.is_bookmarked == "NO") {
                let dataArr = JSON.parse(JSON.stringify(clipList));
                dataArr[obj.index].is_bookmarked = "YES";
                setClipList(dataArr);
                submitPostBookmark(dataObj.post_seq)
            }
            else if (dataObj.is_bookmarked == "YES") {
                let dataArr = JSON.parse(JSON.stringify(clipList));
                dataArr[obj.index].is_bookmarked = "NO";
                setClipList(dataArr);
                removePostBookmark(dataObj.post_seq)
            }
        }

    }
    const renderItem = useCallback(

        ({ item, index }) => {
            return <EpisodeClipItem index={index} data={item} clipItemPress={clipItemPress} />
        },
        [clipList]
    );
    const keyExtractor = useCallback((item, index) => `${item.post_seq}_${index}`);
    const ItemSeparatorComponent = () => {
        return <View style={{ height: 16 }} />
    }
    const playEpisodeBtnPress = () => {
        storePlaylistResumeData(resumePostSeq);
        navigation.navigate("VideoContentScreen", {
            postSeq: resumePostSeq, postProfileSeq: -1, episodeSeq: episodeSeq,
            cameFrom: "EPISODE"
        });
    }
    const storePlaylistResumeData = (postSeq) => {
        const tempList = TempData.playlistResumeData;
        let episodeExist = false;
        tempList.map(item => {
            if (item.episodeSeq === episodeSeq) {
                episodeExist = true;
                item.playPostSeq = postSeq;
            }
        });
        if (!episodeExist) {
            tempList.push({ episodeSeq: episodeSeq, playPostSeq: postSeq })
        }
        TempData.refreshPlayList = "YES";
        TempData.playlistResumeData = tempList;
    }
    function submitPostBookmark(postSeq) {
        let hashMap = {
            _action_code: "11:ADD_BOOKMARK",
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method       

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }
    function removePostBookmark(postSeq) {
        let hashMap = {
            _action_code: "11:REMOVE_BOOKMARK",
            post_seq: postSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

            }
        });
    }

    return (
        <>
            <CustomStatusBar translucent={true} hidden={true} backgroundColor={'transparent'} />
            {/* <StatusBar hidden={true} /> */}
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <View style={{ ...style.container, backgroundColor: theme.colors.backgroundColor, }}>

                <FlatList
                    contentContainerStyle={{ paddingBottom: 80 }}
                    removeClippedSubviews
                    data={clipList}
                    ListHeaderComponent={HeaderComponent}
                    initialNumToRender={8}
                    renderItem={renderItem}
                    keyExtractor={keyExtractor}
                    ItemSeparatorComponent={ItemSeparatorComponent}
                />

            </View>

        </>
    )
}

export default PlaylistEpisodeScreen

const styles = theme => StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.backgroundColor,
        position: 'relative',
    },
    playlistCoverContainer: {
        width: '100%',
        position: 'relative',
        zIndex: 1,
    },
    linearGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    playlistCoverImage: {
        width: '100%',
        resizeMode: 'cover',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0

    },
    topHeader: {
        marginHorizontal: 16,
        flexDirection: 'row',
    },
    backBtnIcon: {
        height: 24,
        width: 24,
        tintColor: '#FFFFFF',
        resizeMode: 'contain',

    },
    reactionIcon: {
        width: 32,
        height: 32,
        resizeMode: 'contain',
    },
    playBtnBox: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        height: 35,
        borderColor: theme.colors.buttonBorderColor,
        borderWidth: 1,
        borderRadius: 1,
        paddingHorizontal: 50,
        marginTop: 20,
    },
    btnIcon: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
        tintColor: theme.colors.primaryColor,
        marginRight: 11,
    },
    btnText: {
        fontSize: theme.calculateFontSize(15),
        color: theme.colors.buttonTextColor,
        fontWeight: 'bold',
    },

    contentBody: {
        marginHorizontal: 28,
    },
    playlistTitleBox: {
        flexDirection: 'row',
        minHeight: 50,
    },
    titleBox: {
        maxHeight: theme.dimensions.playlistTitleBoxHeight,
        marginTop: 2,
        flex: 1
    },
    episodeTitle: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistTitleText),
        color: theme.colors.playlistBannerTitleColor,
    },
    yearCountBox: {
        justifyContent: 'center'
    },
    yearCountBoxText: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistSeasonCountText),//12
        color: theme.colors.playlistBannerYearCountColor
    },
    descBox: {
        marginTop: theme.dimensions.playlistDescTopMargin,
        marginBottom: 24
    },
    descBoxText: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistDescText),//11
        color: theme.colors.playlistBannerDescColor
    },
    episodeClipItemContainer: {
        flexDirection: 'row',
        marginBottom: 16,
        marginHorizontal: 28,
        // alignItems: 'center',

    },
    episodeClipItemLeftContainer: {
        height: 74,
        width: 131,
        // borderRadius: 14,
        overflow: 'hidden',
        backgroundColor: "#FFFFFF",
        // borderWidth: 1,
        // borderColor: "#FFFFFF",

    },
    episodeClipItemLeftImage: {
        height: 74,
        width: 131,
        resizeMode: 'cover'
    },
    episodeClipItemLeftImageRatio: {
        height: undefined,
        aspectRatio: 9 / 16 //
    },
    episodeClipItemRightContainer: {
        marginStart: 12,
        marginEnd: 12,
        flex: 1,
    },
    episodeClipItemLeftText: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistClipText),//14
        color: '#A1A1A1',
        flex: 1,
    },
    bottomTextBox: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    episodeClipItemClipText: {
        fontSize: theme.calculateFontSizeNew(theme.dimensions.playlistClipMinuteText),//11
        color: '#A1A1A1',
    },


})