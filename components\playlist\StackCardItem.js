import { Dimensions, Pressable, StyleSheet, Text, View } from 'react-native'
import React, { useContext, useEffect, useRef, useState } from 'react'
import Animated, {
    useAnimatedStyle,
    interpolate,
    withTiming,
    useSharedValue,
} from 'react-native-reanimated';
import {
    FlingGestureHandler,
    Directions,
    State,
    TouchableOpacity
} from 'react-native-gesture-handler';
import EntutoTextView from '../common/EntutoTextView';
import { ActivityIndicator } from 'react-native-paper';
const IMAGE_HEIGHT = Dimensions.get('screen').height / 3 > 400 ? 400 : Dimensions.get('screen').height / 3;
const IMAGE_WIDTH = Dimensions.get('screen').width / 3 > 200 ? 200 : Dimensions.get('screen').height / 3.5;
const StackCardItem = ({
    maxVisibleItems,
    item,
    index,
    dataLength,
    animatedValue,
    currentIndex,
    prevIndex,
    lockSlide,
    slideCallback,
    visibleIndex,
    indexValue,
    itemRowPress,
}) => {

    const animatedStyle = useAnimatedStyle(() => {
        const translateY = interpolate(
            animatedValue.value,
            [index - 1, index, index + 1],
            [-30, 1, 30],
        );
        const translateY2 = interpolate(
            animatedValue.value,
            [index - 1, index, index + 1],
            [-30, 1, 30],
        );
        const scale = interpolate(
            animatedValue.value,
            [index - 1, index, index + 1],
            [0.92, 1, 0],
        );
        const opacity = interpolate(
            animatedValue.value,
            [index - 1, index, index + 1],
            [1, 1, 0],
        );
        return {
            transform: [
                {
                    translateY: index === prevIndex.value ? translateY2 : translateY,
                },
                { scale },
            ],
            opacity: index < currentIndex.value + maxVisibleItems - 1
                ? opacity
                : index === currentIndex.value + maxVisibleItems - 1
                    ? withTiming(1)
                    : withTiming(0),
        };
    }, []);
    const [showSpinner, setShowSpinner] = useState(true)
    const onImageLoad = () => {
        setShowSpinner(false)
    }
    return (
        <FlingGestureHandler
            key="up"
            direction={Directions.UP}
            onHandlerStateChange={ev => {
                if (ev.nativeEvent.state === State.END) {
                    if (currentIndex.value !== 0) {
                        if (!lockSlide) {
                            animatedValue.value = withTiming((currentIndex.value -= 1));
                            prevIndex.value = currentIndex.value - 1;
                            slideCallback(prevIndex.value, currentIndex.value, "DOWN_TO_UP")
                        }
                    }
                }
            }}>
            <FlingGestureHandler
                key="down"
                direction={Directions.DOWN}
                onHandlerStateChange={ev => {
                    if (ev.nativeEvent.state === State.END) {
                        if (currentIndex.value !== dataLength - 1) {
                            if (!lockSlide) {
                                animatedValue.value = withTiming((currentIndex.value += 1));
                                prevIndex.value = currentIndex.value;
                                slideCallback(prevIndex.value, currentIndex.value, "UP_TO_DOWN")
                            }

                        }
                    }
                }}>

                <Animated.View
                    style={[
                        styles.container,
                        {
                            zIndex: dataLength - index + 5,
                            width: IMAGE_WIDTH,
                            height: IMAGE_HEIGHT,
                            bottom: 0,
                            backgroundColor: '#454545',

                        },
                        animatedStyle,
                    ]}>
                    <TouchableOpacity disabled={lockSlide} onPress={() => itemRowPress(indexValue)}
                        style={{ zIndex: dataLength - index + 5, }}>
                        <View style={{ position: 'relative' }}>
                            {
                                showSpinner ?
                                    <View style={{

                                        justifyContent: 'center', alignItems: 'center'
                                    }}>
                                        <ActivityIndicator size={30} />
                                    </View>
                                    : null
                            }


                            {
                                item.type == "IMAGE" ?
                                    <Animated.Image
                                        source={{ uri: item.url }}
                                        style={[
                                            styles.image,
                                            {
                                                width: IMAGE_WIDTH,
                                                height: IMAGE_HEIGHT,
                                            },
                                        ]}
                                        onLoad={() => onImageLoad()}
                                    />
                                    : null
                            }
                        </View>
                        {/* <EntutoTextView>{index === currentIndex.value ? "current" : "-----"}{indexValue}</EntutoTextView> */}
                    </TouchableOpacity>

                    {/*<EntutoTextView>{item.id}</EntutoTextView>
                    <EntutoTextView>{index}</EntutoTextView>
                    <EntutoTextView>{index === currentIndex.value ? "current" : ""}</EntutoTextView> */}
                </Animated.View>


            </FlingGestureHandler>
        </FlingGestureHandler>
    )
}

export default StackCardItem

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        shadowColor: '#00000078',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.5,
        shadowRadius: 2,
        elevation: 10,

        borderWidth: 0.5,
        borderColor: "#00000078",

        borderRadius: 7,
    },
    image: {
        borderRadius: 7,
        resizeMode: 'cover'
    },
})