import { ScrollView, StyleSheet, Text, Image, View } from 'react-native'
import React, { useState } from 'react'
import CustomStatusBar from '../components/common/CustomStatusBar'
import LinearGradient from 'react-native-linear-gradient';
import EntutoTextView from '../components/common/EntutoTextView';
import EntutoEditText from '../components/common/EntutoEditText';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';

const SignupNScreen = ({ route, navigation }) => {
    const [emailId, setemailId] = useState("");
    const [emailIdErr, setemailIdErr] = useState("");
    const [password, setpassword] = useState("");
    const [passwordErr, setpasswordErr] = useState("");
    const [fullName, setFullName] = useState("");
    const [fullNameErr, setFullNameErr] = useState("");
    const [referralCode, setreferralCode] = useState("");
    const [referralCodeErr, setreferralCodeErr] = useState("");

    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const goButtonPress = () => {
        // console.log("Go Btn Press")
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={{ flex: 1, position: 'relative' }}>
                <LinearGradient colors={[theme.colors.loginSignupGradient1, theme.colors.loginSignupGradient2, theme.colors.loginSignupGradient3]}
                    locations={[0.1, 0.5, 1]}
                    start={{ x: 0.5, y: 0.1 }}
                    end={{ x: 0.5, y: 1.0 }}
                    style={style.linearGrad} />
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View>
                        <View style={style.signupTextBox}>
                            <EntutoTextView style={style.signupText}>Signup to create</EntutoTextView>
                            <EntutoTextView style={style.signupText}>new account</EntutoTextView>
                        </View>
                        <View style={style.signUpFormBox}>
                            <EntutoEditText
                                showLeftIcon={false}
                                placeholderTxt="Full Name"
                                value={fullName}
                                onChangeText={text => { setFullName(text); setFullNameErr("") }}
                                showErrorField={fullNameErr.length}
                                errorMsg={fullNameErr}
                            />
                            <EntutoEditText
                                showLeftIcon={false}
                                placeholderTxt="Email"
                                value={emailId}
                                onChangeText={text => { setEmailId(text); setEmailIdErr("") }}
                                showErrorField={emailIdErr.length}
                                errorMsg={emailIdErr}
                            />
                            <EntutoEditText
                                placeholderTxt="Password"
                                value={password}
                                onChangeText={text => { setpassword(text); setpasswordErr("") }}
                                showErrorField={passwordErr.length}
                                errorMsg={passwordErr}
                                secureEntryTxt={true}
                                showRightIcon={true}
                                returnKeyType="go"
                                onSubmitEditing={() => goButtonPress()}
                            />
                            <EntutoEditText
                                showLeftIcon={false}
                                placeholderTxt="Referral Code(optional)"
                                value={referralCode}
                                onChangeText={text => { setreferralCode(text); setreferralCodeErr("") }}
                                showErrorField={referralCodeErr.length}
                                errorMsg={referralCodeErr}
                            />
                        </View>
                        <View style={style.signUpBtnBox}>
                            <View style={{ ...style.signUpBtn, backgroundColor: theme.colors.signUpBtnBackground }}>
                                <EntutoTextView style={style.signUpBtnText}>Signup</EntutoTextView>
                            </View>
                            <View style={style.signUpBtn}>
                                <EntutoTextView style={style.signUpBtnText}>Login</EntutoTextView>
                            </View>
                        </View>

                    </View>
                </ScrollView>


            </View>
        </>
    )
}

export default SignupNScreen;
const styles = theme => StyleSheet.create({
    linearGrad: {
        position: 'absolute',
        width: '100%',
        height: '100%',
    },
    signupTextBox: {
        paddingStart: 24,
        paddingEnd: 24,
        marginTop: 60
    },
    signupText: {
        color: '#FFFFFF',
        fontSize: 34,
        marginBottom: 4
    },
    signUpFormBox: {
        minHeight: 200,
        backgroundColor: '#FFFFFF',
        borderTopEndRadius: 24,
        borderBottomEndRadius: 24,
        flex: 1,
        marginEnd: 44,
        marginTop: 40,
        paddingHorizontal: 24,
        paddingVertical: 15,
    },
    signUpBtnBox: {
        marginTop: 58,
        flexDirection: 'row',

    },
    signUpBtn: {
        minHeight: 58,
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,
        borderTopEndRadius: 58,
        borderBottomEndRadius: 58,
    },
    signUpBtnText: {
        color: '#FFFFFF',
        fontSize: 16,
    }

})