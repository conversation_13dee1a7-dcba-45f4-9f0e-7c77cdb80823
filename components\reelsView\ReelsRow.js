import { ImageBackground, StyleSheet, View } from 'react-native'
import React from 'react'
import ReelsVideoComponent from './ReelsVideoComponent'
import { hasImageUrlExist } from '../../utils/Utils';
import useSThemedStyles from '../../theme/useSThemedStyles';


const ReelsRow = ({ item, isNext, isVisible, index, transitionAnimation, displayHeight,
    navigation, cameFrom, insets, fullUserDetails, homepagePostDataBackup, __commentObj, appTheme
}) => {
    const { post } = item;
    const style = useSThemedStyles(styles);
    return (
        <View style={{ height: displayHeight }}>
            <ImageBackground
                source={hasImageUrlExist(post.media_cover) ? { uri: post.media_cover } : hasImageUrlExist(post.fuzzy_image) ? { uri: post.fuzzy_image } : null}
                resizeMode='cover'
                style={style.videPostImage} />
            <ReelsVideoComponent post={post} isNext={isNext} isVisible={isVisible}
                displayHeight={displayHeight}
                navigation={navigation}
                cameFrom={cameFrom}
                insets={insets}
                fullUserDetails={fullUserDetails}
                __commentObj={__commentObj}
                appTheme={appTheme}
                homepagePostDataBackup={homepagePostDataBackup}
            />
        </View>
    )
}

export default React.memo(ReelsRow)

const styles = theme => StyleSheet.create({
    videPostImage: {
        width: '100%',
        height: "100%", //400
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
})