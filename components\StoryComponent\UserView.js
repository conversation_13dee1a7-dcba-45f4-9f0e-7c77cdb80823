import { useNavigation } from '@react-navigation/native'
import React, { useContext, useRef } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import ActionSheet from 'react-native-actions-sheet'
import { AppStateContext } from '../..'
import appData from '../../data/Data'
import { UserHandlePrefix } from '../../utils/Appconfig'
import { hasImageUrlExist } from '../../utils/Utils'
import ProgressiveImage from '../common/ProgressiveImage'
import ThreeDotMenuStoryActionView from './ThreeDotMenuStoryActionView'

const UserView = ({ ...props }) => {
    const navigation = useNavigation();
    const threeDotMenuSheetRef = useRef(null);
    const { fullUserDetails } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : "";
    const threeDotMenuBtnPress = () => {
        props.onPauseChange(true);
        threeDotMenuSheetRef.current?.show();
    }
    const ThreeDotMenuPress = (clickId, obj) => {
        if (clickId == "delete_story") {
            appData.__StoryPageRefreshCheck = "YES";
            threeDotMenuSheetRef.current?.hide();
            props.onPauseChange(false);
            props.onClosePress()
        }
        if (clickId == "blockPost") {
            appData.__StoryPageRefreshCheck = "YES";
            threeDotMenuSheetRef.current?.hide();
            props.onPauseChange(false);
            props.onClosePress()
        }
        else {
            threeDotMenuSheetRef.current?.hide();
            props.onPauseChange(false);
        }
    }
    const profileIconPress = () => {
        if (__ProfileSeq == props.profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: props.profileSeq,
            });
        }

    }
    return (
        <View style={styles.userView}>
            <TouchableOpacity onPress={props.onStoryClosePress}>
                <Image style={styles.arrowIcon}
                    resizeMode="contain"
                    source={require('../../assets/Images/icon/Arrow.png')} />
            </TouchableOpacity>
            <ProgressiveImage
                style={styles.image}
                source={hasImageUrlExist(props.profilePic) ? { uri: props.profilePic } : null}
                defaultImageSource={require("../../assets/Images/full_user_image_place_holder.png")}
                resizeMode={'cover'}
            />
            <View style={{ flex: 1, marginLeft: 10, }}>
                <View style={styles.barUsername}>
                    <Text style={styles.name}>{props.displayName}</Text>
                    {
                        props.isVerified == "YES" ?
                            <Image
                                source={require("../../assets/Images/icon/verifiedicon.png")}
                                style={styles.verifyIcon}
                                resizeMode={'contain'}
                            />
                            : null
                    }


                </View>
                <View>
                    <TouchableOpacity onPress={() => profileIconPress()}>
                        <Text style={styles.time}>
                            {UserHandlePrefix}{props.userHandle}
                        </Text>
                    </TouchableOpacity>
                </View>


            </View>
            {/* {
                __ProfileSeq === props.profileSeq ? */}
            <View style={{ marginStart: 'auto', }}>
                <TouchableOpacity style={{ padding: 15 }}
                    onPress={() => threeDotMenuBtnPress()}>
                    <Image
                        source={require("../../assets/Images/icon/profile_three_dot.png")}
                        style={styles.threeDotIcon} />
                </TouchableOpacity>
            </View>
            {/* : null
            } */}

            <ActionSheet ref={threeDotMenuSheetRef}
                statusBarTranslucent

                bounciness={4}
                gestureEnabled={false}
                defaultOverlayOpacity={0.3}
                openAnimationSpeed={8}>
                <ThreeDotMenuStoryActionView
                    currentProfileSeq={__ProfileSeq}
                    profileSeq={props.profileSeq} storySeq={props.storySeq}
                    ThreeDotMenuPress={(clickId, obj) => ThreeDotMenuPress(clickId, obj)} />
            </ActionSheet >
        </View>
    )
}

export default UserView

const styles = StyleSheet.create({
    arrowIcon: {
        height: 24,
        width: 24,
        marginEnd: 5,
        opacity: 0.8
    },
    barUsername: {
        flexDirection: "row",
        alignItems: "center",
    },
    image: {
        width: 40,
        height: 40,
        borderRadius: 20,
        marginLeft: 8,
    },
    verifyIcon: {
        width: 15,
        height: 15,
        marginLeft: 10,
    },
    userView: {
        flexDirection: "row",
        position: "absolute",
        top: 0,
        left: 0,
        paddingLeft: 10,
        width: "100%",
        alignItems: "center",
        backgroundColor: '#00000050',
        paddingTop: 35,
        paddingBottom: 8
    },
    name: {
        fontSize: 13,
        fontWeight: "500",
        marginLeft: 8,
        color: "#FFFFFF",
    },
    time: {
        fontSize: 12,
        fontWeight: "400",
        marginTop: 3,
        marginLeft: 8,
        color: "#FFFFFF",
        opacity: 0.9
    },
    threeDotIcon: {
        width: 5,
        height: 20,
    },
})
