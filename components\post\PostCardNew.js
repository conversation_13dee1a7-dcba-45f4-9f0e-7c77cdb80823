import { ImageBackground, Platform, StyleSheet, Text, View } from 'react-native'
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { AppStateContext, SinglePostContext } from '../..';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import ProgressiveImage from '../common/ProgressiveImage';
import ProfileImagePlaceholder from '../../assets/Images/full_user_image_place_holder.png';
import { hasImageUrlExist } from '../../utils/Utils';
import { Dimensions } from 'react-native';
import { TouchableOpacity } from 'react-native';
import EntutoTextView from '../common/EntutoTextView';
import { UserHandlePrefix } from '../../utils/Appconfig';
import { Image } from 'react-native';
import VerifiedIcon from '../../assets/Images/icon/verifiedicon.png';
import LinearGradient from 'react-native-linear-gradient';
import BookmarkBtnComponent from '../common/BookmarkBtnComponent';
import ErrorMessages from '../../constants/ErrorMessages';
import FullSActiveBookmark from '../../assets/Images/icon/bookmark.png';
import FullSInActiveBookmarkIcon from '../../assets/Images/icon/new_bookmark_icon.png';
import ThreeDotVerticalIcon from '../../assets/Images/icon/profile_three_dot.png';
import { TapGestureHandler } from 'react-native-gesture-handler';
import Animated, { useAnimatedStyle, useSharedValue, withDelay, withSpring } from 'react-native-reanimated';
import HeartActive from '../../assets/Images/icon/double_tap.png';
import HeartVideoActive from '../../assets/Images/icon/double_tap.png';
import PlayBtnIcon from '../../assets/Images/icon/play_btn.png';
import SHARE_ICON from '../../assets/Images/icon/share_icon.png';

const AnimatedImage = Animated.createAnimatedComponent(Image);

const PostCardNew = ({ itemData, navigation, isMyProfile, isForceUnlock = false, postCardType = "",
    forceBookmark = false, showBookmarkWarrings = false, postCardClick, ...props }) => {
    const { fullUserDetails, __commentObj } = useContext(AppStateContext);
    const { changeSingleProfileObj } = useContext(SinglePostContext);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    let __has_state_city = fullUserDetails.hasOwnProperty("_has_state_city") ? fullUserDetails._has_state_city : "NO";
    const __profile_seq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    useEffect(() => {
        __has_state_city = fullUserDetails.hasOwnProperty("_has_state_city") ? fullUserDetails._has_state_city : "NO";
    }, [fullUserDetails]);

    const [isBlockPost, setIsBlockPost] = useState(false);
    const [showBookmarkIcon, setShowBookmarkIcon] = useState(true);
    const [bookmarkBtnDisable, setBookmarkBtnDisable] = useState(true);
    const [postIsBookmark, setPostIsBookmark] = useState(false);
    const [isPostUnlock, setIsPostUnlock] = useState(true);
    const [likeBtnDisable, setLikeBtnDisable] = useState(false);
    const [postIsLike, setPostIsLike] = useState(false);

    const scale = useSharedValue(0);
    const doubleTapRef = useRef();
    const doubleTapVideoRef = useRef();

    const goToProfile = (profileSeq) => {
        if (__profile_seq == profileSeq) {
            if (!isMyProfile) {
                navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
            }
        }
        else {
            if (cardType == "TAGGED") {
                navigation.push('OthersProfileScreen', {
                    profileSeq: profileSeq,
                });
            }
            else {
                navigation.navigate('OthersProfileScreen', {
                    profileSeq: profileSeq,
                });
            }

        }
    }
    const bookmarkIconBtnClick = () => {
        if (postIsBookmark) {
            setIsServiceExecute(true);
            removePostBookmark();
        }
        else {
            if (expiryPostDate != "9999-12-31") {
                setSnackbarMsg(ErrorMessages.bookmarkPostExpiryHint);
                setsnackBarType("SUCCESS");
                setdisplaySnackbar(true);
                setrefreshSnackBar(Math.random())
            }
            submitPostBookmark();
        }
    }
    const ImageOverlayNew = () => {
        return <LinearGradient
            colors={[
                '#000000',
                'transparent',
                'transparent',
                '#000000'
            ]}
            locations={[0, 0.18, 0.8, 0.97]}
            style={style.linearGradientPost}
        />
    }
    const rStyle = useAnimatedStyle(() => ({
        transform: [{ scale: Math.max(scale.value, 0) }],
    }));
    const onDoubleTap = useCallback(() => {
        if (!likeBtnDisable) {
            scale.value = withSpring(1, undefined, (isFinished) => {
                if (isFinished) {
                    scale.value = withDelay(500, withSpring(0));
                }
            });
            // likeThePost();
        }

    }, [likeBtnDisable, postIsLike]);

    return (
        <View style={{ marginBottom: 8 }}>
            <View style={style.postCardContainer}>
                <View style={style.postCardHeader}>
                    <View style={style.headerProfileContainer}>
                        <View style={style.headerImageBox}>
                            <ProgressiveImage
                                style={style.headerImage}
                                source={hasImageUrlExist(itemData.profile_picture) ? { uri: itemData.profile_picture } : null}
                                defaultImageSource={ProfileImagePlaceholder}
                            />
                        </View>
                        <View style={style.headerTextBox}>
                            <TouchableOpacity onPress={() => goToProfile(itemData.profile_seq)}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <EntutoTextView style={style.headerProfileName}>{itemData.display_name}</EntutoTextView>
                                    {
                                        itemData.is_verified == "YES" ?
                                            <Image
                                                style={style.verifiedIcon}
                                                source={VerifiedIcon}
                                                resizeMode={'contain'}
                                            />
                                            :
                                            null
                                    }
                                </View>

                            </TouchableOpacity>
                            <TouchableOpacity onPress={() => goToProfile(itemData.profile_seq)}>
                                <EntutoTextView style={style.headerProfileHandle}>
                                    {/* {itemData.posted_on} */}
                                    {UserHandlePrefix}{itemData.user_handle}
                                </EntutoTextView>
                            </TouchableOpacity>
                        </View>
                    </View>
                    <View style={style.headerOptionIconBox}>
                        {
                            showBookmarkIcon ?
                                <BookmarkBtnComponent
                                    activeIcon={FullSActiveBookmark}
                                    inActiveIcon={FullSInActiveBookmarkIcon}
                                    disable={bookmarkBtnDisable}
                                    // bookmarkButtonPress={() => bookmarkIconBtnClick()}
                                    isBookmark={postIsBookmark}
                                    style={style.postActionIcon} />
                                : null
                        }

                        <TouchableOpacity style={{ padding: 10, }} onPress={() => threeDotMenuClick()}>
                            <Image
                                style={style.headerOptionIcon}
                                source={ThreeDotVerticalIcon}

                            />
                        </TouchableOpacity>

                    </View>
                </View>
                <View style={style.postCardMediaBoxContainer}>
                    {
                        isPostUnlock ?
                            <View style={{ flex: 1, flexDirection: 'column', width: '100%', }}>
                                {
                                    itemData.media_type == "IMAGE" ?
                                        <TapGestureHandler waitFor={doubleTapRef}>
                                            <View style={{ ...style.postImageContainer }}>
                                                <TapGestureHandler
                                                    maxDelayMs={250}
                                                    ref={doubleTapRef}
                                                    numberOfTaps={2}
                                                    onActivated={onDoubleTap}
                                                >
                                                    <Animated.View>
                                                        <ImageBackground
                                                            imageStyle={{ borderRadius: 27, }}
                                                            source={hasImageUrlExist(itemData.media_file) ? { uri: itemData.media_file } : null}
                                                            style={style.postImageContainerImg} >
                                                            <ImageOverlayNew />
                                                            <AnimatedImage
                                                                source={HeartActive}
                                                                style={[
                                                                    style.postImageContainerHeartIcon,
                                                                    {
                                                                        shadowOffset: { width: 0, height: Platform.OS == 'ios' ? 2 : 20 },
                                                                        shadowOpacity: 0.35,
                                                                        shadowRadius: Platform.OS == 'ios' ? 2 : 35,
                                                                    },
                                                                    rStyle,
                                                                ]}
                                                                resizeMode={'contain'}
                                                            />
                                                        </ImageBackground>
                                                    </Animated.View>
                                                </TapGestureHandler>
                                            </View>
                                        </TapGestureHandler>
                                        : null}
                                {
                                    itemData.media_type == "VIDEO" ?
                                        <TapGestureHandler waitFor={doubleTapVideoRef}>
                                            <View style={{ ...style.videoBox, }}>
                                                <TapGestureHandler
                                                    maxDelayMs={250}
                                                    ref={doubleTapVideoRef}
                                                    numberOfTaps={2}
                                                    onActivated={onDoubleTap}
                                                >
                                                    <Animated.View>
                                                        <ImageBackground
                                                            source={hasImageUrlExist(itemData.media_cover) ? { uri: itemData.media_cover } : null}
                                                            style={style.postImageContainerImg} >
                                                            <ImageOverlayNew />
                                                            <View style={style.playBtnBox}>
                                                                <Image
                                                                    style={style.playBtnBoxIcon}
                                                                    source={PlayBtnIcon}
                                                                    resizeMode="cover"
                                                                />
                                                            </View>
                                                            <AnimatedImage
                                                                source={HeartVideoActive}
                                                                style={[
                                                                    style.videPostHeartImage,
                                                                    {
                                                                        shadowOffset: { width: 0, height: Platform.OS == 'ios' ? 2 : 20 },
                                                                        shadowOpacity: 0.35,
                                                                        shadowRadius: Platform.OS == 'ios' ? 2 : 35,
                                                                    },
                                                                    rStyle,
                                                                ]}
                                                                resizeMode={'contain'}
                                                            />
                                                        </ImageBackground>
                                                    </Animated.View>

                                                </TapGestureHandler>


                                            </View>
                                        </TapGestureHandler>

                                        : null
                                }
                            </View>
                            : null//TODO DISPLAY LOCK ICON
                    }

                </View>
                <View style={style.postCardActionBtnContainer}>

                </View>

            </View>
            <View style={style.postCardBottomContainer}>

            </View>
        </View>
    )
}

export default PostCardNew

const styles = theme => StyleSheet.create({
    postCardContainer: {
        position: 'relative',
        height: 450,
    },
    postCardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 999,
        backgroundColor: "rgba(0,0,0,0.5)",
        minHeight: 64,
    },

    headerProfileContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    headerImageBox: {
        height: 31.5,
        width: 31.5,
        marginRight: 10,
        borderRadius: 31.5,
        marginLeft: 18,

    },
    headerImage: {
        height: 31.5,
        width: 31.5,
        borderRadius: 31.5
    },
    headerTextBox: {
        flexDirection: 'column',
        alignContent: 'center'
    },
    headerProfileName: {
        fontSize: 12,
        color: "#FFFFFF",
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
        flexDirection: 'row',
    },
    headerProfileHandle: {
        fontSize: 9,
        color: "#FFFFFF",
        fontWeight: '400',
    },
    verifiedIcon: {
        width: 13,
        height: 13,
        marginLeft: 8,
    },
    headerOptionIconBox: {
        marginLeft: 'auto',
        flexDirection: 'row',
        alignItems: 'center',
    },
    postActionIconBox: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        position: 'absolute',
        backgroundColor: '#FFF',
        justifyContent: 'space-around',
        bottom: -10,
        left: 16,
        right: 16,
        borderRadius: 7,
    },
    headerOptionIcon: {
        width: 18,
        height: 18,
        resizeMode: 'contain',
        tintColor: '#FFFFFF',
        zIndex: 1000,
    },
    linearGradientPost: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
    },
})