import { StyleSheet, Text, View } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import { AppStateContext } from '..';
import CustomStatusBar from '../components/common/CustomStatusBar';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import { FlatList } from 'react-native-gesture-handler';
import { ActivityIndicator } from 'react-native-paper';
import { getSecondsBetweenDates } from '../utils/Utils';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import { _RedirectionErrorList } from '../utils/Appconfig';
import ServerConnector from '../utils/ServerConnector';
import SearchProfileRow from '../components/search/SearchProfileRow';
import Colors from '../constants/Colors';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';

const TagPeopleListScreen = ({ route, navigation }) => {
    const { postSeq } = route.params;
    const { fullUserDetails, userProfileImage } = useContext(AppStateContext);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;

    const [errorMsg, seterrorMsg] = useState("");
    const [errorMsgKey, seterrorMsgKey] = useState(Math.random());
    const [showLoading, setshowLoading] = useState(true);

    const RowsPerPage = 10;
    const [startRecord, setstartRecord] = useState(0);
    const [bottomLoading, setbottomLoading] = useState(false);
    const [isNoDataFound, setisNoDataFound] = useState(false);
    const [bottomReachTime, setbottomReachTime] = useState(new Date());
    const [tagPeopleList, settagPeopleList] = useState([]);
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const renderProfileRow = ({ item }) => {
        return (
            <SearchProfileRow data={item} navigation={navigation} />
        );
    };
    useEffect(() => {
        getTagPeopleResult(0, RowsPerPage);

    }, []);

    function getTagPeopleResult(startRec, rowsPerPage) {
        setstartRecord(startRec)
        let hashMap = {
            _action_code: "11:GET_TAGGED_USERS",
            post_seq: postSeq,
            // _start_row: startRec,
            // _rows_page: rowsPerPage,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false);
            setbottomLoading(false);
            if (parseInt(startRec) == 0) {
                settagPeopleList([...[], ...data.data]);
            }
            else {
                settagPeopleList([...tagPeopleList, ...data.data]);
            }
            seterrorMsg("");
            seterrorMsgKey(Math.random())
            setisNoDataFound(false);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                setbottomLoading(false);
                setisNoDataFound(true);
                if (parseInt(startRec) == 0) {
                    settagPeopleList([]);
                    seterrorMsg(errorMessage);
                    seterrorMsgKey(Math.random());
                }
            }
        });
    }
    // const handleEndRefresh = () => {
    //     if (!isNoDataFound) {
    //         let currentTime = new Date();
    //         let diffTime = getSecondsBetweenDates(bottomReachTime, currentTime);
    //         if (diffTime > 4) {
    //             let startRec = startRecord + RowsPerPage;
    //             setbottomLoading(true);
    //             getTagPeopleResult(startRec, RowsPerPage);
    //             setbottomReachTime(new Date());
    //         }

    //     }
    // }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar showBorderBottom={false} showBackBtn={true} title="Tag People" navigation={navigation} />
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside}>
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        :
                        <>
                            {
                                showLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <>
                                        <FlatList
                                            contentContainerStyle={{ paddingBottom: 20 }}
                                            data={tagPeopleList}
                                            renderItem={renderProfileRow}
                                            keyExtractor={(item, index) => `${index}`}
                                            // onEndReached={handleEndRefresh}
                                            initialNumToRender={10}

                                        />
                                        {/* {
                                        bottomLoading ?
                                            <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 15 }}>
                                                <ActivityIndicator animating={true} color={Colors.primaryColor} size={'large'} />
                                            </View>
                                            : null
                                    } */}
                                    </>

                            }
                        </>
                }
            </View>
        </>
    )
}

export default TagPeopleListScreen

const styles = StyleSheet.create({})