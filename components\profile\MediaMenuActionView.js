import React from 'react'
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import Colors from '../../constants/Colors';
import EntutoTextView from '../common/EntutoTextView';
import useSThemedStyles from '../../theme/useSThemedStyles';

const MediaMenuActionView = ({ mediaMenuActionViewPress, showChooseMedia = false, showCaptureImage = true, showRemoveMedia = true, }) => {

    const style = useSThemedStyles(styles);
    return (
        <View style={style.containerBackground}>
            <View style={style.popupBox}>
                {
                    showCaptureImage ?
                        <View style={style.popupListRowBox}>
                            <TouchableOpacity style={style.linkBox} onPress={() => mediaMenuActionViewPress("CAPTURE", {})}>
                                <EntutoTextView style={style.linkTxt}>Capture Image</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                        : null
                }

                {
                    showChooseMedia ?
                        <>
                            <View style={style.popupListRowBox}>
                                <TouchableOpacity style={style.linkBox} onPress={() => mediaMenuActionViewPress("GALLERY", {})}>
                                    <EntutoTextView style={style.linkTxt}>Choose Image From Gallery </EntutoTextView>
                                </TouchableOpacity>
                            </View>
                        </>
                        : null
                }
                {
                    showRemoveMedia ?
                        <View style={{ ...style.popupListRowBox, ...style.noBorder }}>
                            <TouchableOpacity style={style.linkBox} onPress={() => mediaMenuActionViewPress("REMOVE", {})}>
                                <EntutoTextView style={{ ...style.linkTxt, color: Colors.errorColor }}>Remove Media</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                        : null
                }

            </View>
        </View>
    )
}

export default MediaMenuActionView

const styles = theme => StyleSheet.create({
    containerBackground: {
        backgroundColor: theme.colors.backgroundColor
    },
    popupBox: {
        paddingHorizontal: 16,
        paddingBottom: 16,
        flexDirection: 'column'
    },
    popupDivider: {
        height: 0.5,
        backgroundColor: theme.colors.threeDotMenuDivider,
        opacity: 0.5,
    },
    popupListRowBox: {
        borderBottomColor: theme.colors.threeDotMenuDivider,
        borderBottomWidth: 0.5
    },
    noBorder: {
        borderBottomColor: theme.colors.threeDotMenuDivider,
        borderBottomWidth: 0
    },
    linkBox: {
        paddingVertical: 15,
    },
    linkTxt: {
        color: theme.colors.threeDotMenuText,
        fontSize: theme.calculateFontSize(theme.dimensions.threeDotMenuText),

    },
})
