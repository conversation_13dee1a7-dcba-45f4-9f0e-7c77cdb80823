import React, { useEffect, useState } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import EntutoTextView from '../common/EntutoTextView';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import BottomSheetLoader from '../common/BottomSheetLoader';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import { _clearAllData } from '../../utils/AuthLogin';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import { PopupNegativeButton } from '../common/PopupButton';

const UnBlockUnRestrictActionView = ({ refVal, profileSeq, restrictSeq, type = "BLOCK", subscriptionSeq = -1,
    navigation, ...props }) => {

    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [showLoading, setshowLoading] = useState(false);
    const [showSuccessMsg, setshowSuccessMsg] = useState(false);
    const [successMsg, setsuccessMsg] = useState("");

    const [errorMsg, seterrorMsg] = useState("");

    const [popupHeading, setpopupHeading] = useState("Un-Block Account");
    const [submitBtnTxt, setsubmitBtnTxt] = useState("Un-Block");
    const [confirmTxt, setconfirmTxt] = useState("Are you sure you want to unblock the account?");
    useEffect(() => {
        if (type == "RESTRICT") {
            setpopupHeading("Un-Restrict Account");
            setsubmitBtnTxt("Un-Restrict");
            setconfirmTxt("Are you sure you want to unrestrict the account?");
        }
        if (type == "UN_SUBS") {
            setpopupHeading("Un-Subscribe");
            setsubmitBtnTxt("Un-Subscribe");
            setconfirmTxt("Are you sure you want to unsubscribe the profile?");
        }
    }, []);


    const cancelBtnPress = () => {
        props.unblockUnrestrictPress("negetive", {})
    }
    const closeBtnClick = () => {
        props.unblockUnrestrictPress("close", {})
    }

    const confirmDeleteBtnPress = () => {
        setshowLoading(true);
        if (type == "BLOCK") {
            unblockAccountService();
        }
        if (type == "RESTRICT") {
            unrestrictAccountService();
        }
        if (type == "UN_SUBS") {
            unSubscribeProfileService();
        }

    }
    function unrestrictAccountService() {
        let hashMap = {
            _action_code: "11:UNRESTRICT_ACCOUNT",
            restrict_profile_seq: profileSeq,
            restrict_seq: restrictSeq
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setshowLoading(false);
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.restrict_profile_seq) {
                            seterrorMsg(data.data.restrict_profile_seq)
                            return;
                        }
                        if (data.data.restrict_seq) {
                            seterrorMsg(data.data.restrict_seq)
                            return;
                        }
                    }

                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                }
            }
        });
    }
    function unSubscribeProfileService() {
        let hashMap = {
            _action_code: "11:UNSUBSCRIBE_PROFILE",
            subscribe_profile_seq: profileSeq,
            subscription_seq: subscriptionSeq
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setshowLoading(false);
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.restrict_profile_seq) {
                            seterrorMsg(data.data.restrict_profile_seq)
                            return;
                        }
                        if (data.data.restrict_seq) {
                            seterrorMsg(data.data.restrict_seq)
                            return;
                        }
                    }

                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                }
            }
        });
    }
    function unblockAccountService() {
        let hashMap = {
            _action_code: "11:UNBLOCK_ACCOUNT",
            block_profile_seq: profileSeq,
            restrict_seq: restrictSeq
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setshowLoading(false);
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.block_profile_seq) {
                            seterrorMsg(data.data.block_profile_seq)
                            return;
                        }
                        if (data.data.restrict_seq) {
                            seterrorMsg(data.data.restrict_seq)
                            return;
                        }
                    }

                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                }
            }
        });
    }
    return (
        <View>
            <View style={defaultStyle.popupBox}>
                <EntutoTextView style={defaultStyle.popupHeadTxt}>{popupHeading}</EntutoTextView>
                <EntutoTextView style={{ ...defaultStyle.popupBodyTxt, marginBottom: 40, marginTop: 20, }}>
                    {confirmTxt}
                </EntutoTextView>
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                {
                    showSuccessMsg ?
                        <BottomSheetSuccessMsg successMsg={successMsg} cancelBtnClick={() => closeBtnClick()} />
                        : null
                }

                {
                    errorMsg.length != 0 ?
                        // <View style={defaultStyle.errorBoxOutside}>
                        <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                        // </View>
                        : null
                }
                <View style={{ flexDirection: 'row', flex: 1, marginTop: 16, marginBottom: 16, }}>
                    <View style={{ flex: 1 }}>
                        <PopupNegativeButton
                            onPress={() => cancelBtnPress()}
                            btnText='No'
                            style={{ marginEnd: theme.dimensions.popupBtnGap }} />

                    </View>
                    <View style={{ flex: 1 }}>
                        <PopupPositiveButton
                            onPress={() => confirmDeleteBtnPress()}
                            btnText={submitBtnTxt} />

                    </View>
                </View>
            </View>
        </View>
    )
}

export default UnBlockUnRestrictActionView;

const styles = StyleSheet.create({})
