import React, { useContext, useEffect, useRef, useState } from 'react'
import { Image, Modal, PermissionsAndroid, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { launchImageLibrary } from 'react-native-image-picker';
import { FAB } from 'react-native-paper';
import { AppStateContext } from '..';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import CustomSnackbar from '../components/common/CustomSnackbar';
import CustomStatusBar from '../components/common/CustomStatusBar';
import EntutoEditText from '../components/common/EntutoEditText';
import EntutoTextView from '../components/common/EntutoTextView';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import appData from '../data/Data';
import { DEFAULT_MAX_FILE_SIZE, TAGGED_SYMBOL, _RedirectionErrorList } from '../utils/Appconfig';
import MimeTypeList from '../utils/MimeTypeList';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import { dateDbFormat, decodeHtmlEntitessData, encodeHtmlEntitessData, isVideo, _inputFormatTextForTag } from '../utils/Utils';
import { getRealPath } from 'react-native-compressor';
import { moveFile, stat, TemporaryDirectoryPath, unlink as deleteFile } from 'react-native-fs';
import ImagePicker from 'react-native-image-crop-picker';
import ErrorMessages from '../constants/ErrorMessages';
import InputTagPeopleSearchComponent from '../components/tag/InputTagPeopleSearchComponent';
import Video from 'react-native-video';
import { requestStoragePermission } from '../utils/PermissionManager';
import useDefaultStyle from '../theme/useDefaultStyle';

const AddStoryScreen = ({ route, navigation }) => {
    const { defaultStyle } = useDefaultStyle();
    const [description, setdescription] = useState("");
    const { captureStoryMedia, changeStoryMedia, fullUserDetails } = useContext(AppStateContext);
    const [mediaData, setmediaData] = useState(null);
    const [errorMsg, seterrorMsg] = useState("");
    const [refresfErrorMsg, setrefresfErrorMsg] = useState(Math.random());
    const __is_profile_paid = fullUserDetails.hasOwnProperty("_user_account_type") ? fullUserDetails._user_account_type == "PAID" ? true : false : false;
    const __MAX_FILE_SIZE = fullUserDetails.hasOwnProperty("_max_file_size") ? fullUserDetails._max_file_size : DEFAULT_MAX_FILE_SIZE;
    const mediaSizeErrorMsg = `Oh no! Your content exceeds the temporary ${__MAX_FILE_SIZE}MB limit. Please try posting something else?`

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [showLoading, setShowLoading] = useState(false);
    const [isGalleryBtnPress, setisGalleryBtnPress] = useState(false);
    const [snackBarType, setsnackBarType] = useState("FAILED");

    const [disabledBtn, setdisabledBtn] = useState(true);

    const [showTagPopup, setshowTagPopup] = useState(false);
    const [tagName, settagName] = useState("");
    const descInputRef = useRef(null);
    let videoDestUri = null;
    useEffect(() => {
        setShowLoading(false)
        changeStoryMedia(null);
    }, [])

    useEffect(() => {
        if (captureStoryMedia != null) {
            setdisabledBtn(false)
        }
        setmediaData(captureStoryMedia);
    }, [captureStoryMedia]);
    const descriptionChangeHandler = (text) => {

        let txtNewLineArr = text.split("\n");
        let lastNewLineWord = txtNewLineArr[txtNewLineArr.length - 1];
        let textStArr = lastNewLineWord.split(" ");
        let lastIndex = textStArr.length - 1;
        let lastWord = textStArr[lastIndex]
        if (lastWord == TAGGED_SYMBOL) {//.charAt(0)
            setshowTagPopup(true);
            settagName(lastWord);
        }
        else {
            settagName("");
            setshowTagPopup(false);
        }

        setdescription(text)
        setdisabledBtn(false)
    }
    const removeMediaBox = () => {
        changeStoryMedia(null);
        setdisabledBtn(false)
    }
    const postBtnPress = async () => {
        seterrorMsg("");
        setdisabledBtn(false)
        setrefresfErrorMsg(Math.random());
        if (description.length == 0) {
            if (__is_profile_paid) {
                seterrorMsg(ErrorMessages.addStoryCaptionErr);
                setrefresfErrorMsg(Math.random());
                return;
            }
        }
        if (mediaData == null) {
            seterrorMsg(ErrorMessages.addStoryMediaErr);
            setrefresfErrorMsg(Math.random());
            return;
        }

        setShowLoading(true);
        if (mediaData.captureType == "VIDEO") {
            if (isGalleryBtnPress) {
                validateVideoFile(mediaData.uri);
            }
            else {
                const statResult = await stat(mediaData.uri);
                let fileMaxSize = __MAX_FILE_SIZE * 1024 * 1000;
                if (statResult.size <= fileMaxSize) {
                    validateVideoFile(mediaData.uri);
                }
                else {
                    seterrorMsg(mediaSizeErrorMsg);
                    setrefresfErrorMsg(Math.random());
                    setShowLoading(false);
                }
            }

        }
        else {
            submitPost(mediaData.uri, mediaData.captureType);
        }

    }
    function submitPost(mediaUriValue, mediaUriType) {
        setdisabledBtn(true)
        let fileFormat = "";
        let dbfileFormat = "";
        if (isGalleryBtnPress) {
            if (mediaUriType == "IMAGE") {
                let parts = mediaUriValue.split('.');
                fileFormat = parts[parts.length - 1];
                dbfileFormat = fileFormat;
                // let mType = mediaData.imageData.type.split('/');
                // let mTypeVal = mType[mType.length - 1];
                // fileFormat = mTypeVal;
            }
            else {
                let parts = mediaUriValue.split('.');
                fileFormat = parts[parts.length - 1];
                dbfileFormat = fileFormat;
                if (fileFormat.toUpperCase() == "MOV") {
                    dbfileFormat = "QUICKTIME";
                }
            }
        }
        else {
            let parts = mediaData.uri.split('.');
            fileFormat = parts[parts.length - 1];
            dbfileFormat = fileFormat
        }
        let encodeSt = encodeHtmlEntitessData(description);
        let hashMap = {
            _action_code: "11:SUBMIT_STORY",
            file_format: dbfileFormat.toUpperCase(),
            media_type: mediaUriType,
            caption: encodeURIComponent(encodeSt),

        }
        let captureFile = {
            uri: Platform.OS === 'android' ? mediaUriValue : mediaUriValue.replace('file://', ''),
            name: "media_" + dateDbFormat(new Date()) + "." + fileFormat,
            type: MimeTypeList[fileFormat]
        };
        let imageHashMap = [];
        if (mediaData != null) {
            imageHashMap.push({ inputName: "File1", imageData: captureFile });
        }
        let connector = new ServerConnector();
        connector.postDataMultiPart(hashMap, imageHashMap, (data) => { // success method

            setSnackbarMsg(data.msg);
            setdisplaySnackbar(true);
            setsnackBarType("SUCCESS");
            setrefreshSnackBar(Math.random());
            changeStoryMedia(null);
            setdescription("");
            setShowLoading(false);
            setdisabledBtn(true)
            appData.__StoryPageRefreshCheck = "YES";
            // appData.__HomePageRefresh = Math.random();
            if (videoDestUri != null) {
                deleteTempFile(videoDestUri);
            }
            setTimeout(() => {
                navigation.replace("HomeScreen");
            }, 500);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setdisabledBtn(false)
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.caption) {
                            seterrorMsg(data.data.caption);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                        if (data.data.file_format) {
                            seterrorMsg(data.data.file_format);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                        if (data.data.media_type) {
                            seterrorMsg(data.data.media_type);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                        if (data.data.post_type) {
                            seterrorMsg(data.data.post_type);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                        if (data.data.viewer_fee) {
                            seterrorMsg(data.data.viewer_fee);
                            setrefresfErrorMsg(Math.random());
                            return;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage);
                    setrefresfErrorMsg(Math.random());
                }

            }
        });
    }
    const mediaBtnPress = () => {
        setisGalleryBtnPress(true);
        launchImageLibraryData();
    }
    const launchImageLibraryData = async () => {
        const checkPermission = requestStoragePermission();
        checkPermission.then(res => {
            if (res) {
                openImageFolder();
            }
        })


    }
    const openImageFolder = () => {
        setShowLoading(true);
        var options = {
            mediaType: 'mixed', //to allow only photo to select ...no video
            // saveToPhotos: true,  //to store captured photo via camera to photos or else it will be stored in temp folders and will get deleted on temp clear
            includeBase64: false,
            videoQuality: 'low',
            durationLimit: appData._videoDurationLimit,
            selectionLimit: 1,
        };
        launchImageLibrary(options, (response) => {

            if (response.didCancel) {
                setShowLoading(false);
                // console.log('User cancelled image picker');
            } else if (response.error) {
                setShowLoading(false);
                // console.log('ImagePicker Error: ', response.error);
            } else if (response.customButton) {
                setShowLoading(false);
                // console.log('User tapped custom button: ', response.customButton);
            } else {
                setShowLoading(false);
                if (response.hasOwnProperty("assets")) {
                    if (response.assets[0].fileSize <= 153600000) {//150 MB //15MB 15360000
                        displayTheFile(response);
                    }
                    else {
                        if (response.assets[0].hasOwnProperty("fileSize")) {
                            if (response.assets[0].fileSize == null || response.assets[0].fileSize == 0) {
                                displayTheFile(response, true);
                            }
                            else {
                                seterrorMsg(ErrorMessages.addStoryMediaSizeErr);
                                setrefresfErrorMsg(Math.random());
                            }
                        }
                        else {
                            displayTheFile(response, true);
                        }
                    }
                }
                else {
                    setSnackbarMsg("Somethings went wrong!");
                    setdisplaySnackbar(true);
                    setrefreshSnackBar(Math.random());
                    setsnackBarType("FAILED");
                }
            }
        });
    }
    const displayTheFile = (response, errorHappen = false) => {
        let captureType = "IMAGE";
        // let responseUri = response.assets[0].uri;
        // let mType = response.assets[0].type.split('/');
        // let mTypeVal = mType[mType.length - 1]
        // if (isVideo(mTypeVal)) {
        //     captureType = "VIDEO";
        // }
        let responseUri = response.assets[0].uri;
        let mType = response.assets[0].type.split('/');
        let mTypeVal = mType[0];
        if (mTypeVal === 'video') {
            captureType = "VIDEO";
        }
        if (captureType == "IMAGE") {
            if (errorHappen) {
                changeStoryMedia({ uri: responseUri, captureType: "IMAGE", imageData: response.assets[0] });
            }
            else {
                corpGalleryImage(responseUri);
            }
        }
        else {
            changeStoryMedia({ uri: responseUri, captureType: captureType, imageData: response.assets[0] });
        }
    }
    const cameraBtnPress = () => {
        setisGalleryBtnPress(false);
        navigation.navigate("CameraScreen", {
            cameraType: "ALL", cameFrom: "STORY"
        })
    }
    const playVideo = () => {
        navigation.navigate("VideoDisplayScreen", {
            mediaUri: mediaData.uri,
            thumbnailUri: null
        })

    }
    const showImage = () => {
        navigation.navigate("ImageDisplayScreen", {
            mediaUri: mediaData.uri
        })

    }
    const imageBtnPress = () => {
        if (mediaData.captureType == "IMAGE") {
            showImage();
        }
        if (mediaData.captureType == "VIDEO") {
            playVideo();
        }
    }
    const validateVideoFile = async (sourceVideo) => {
        let videouri = sourceVideo;
        if (sourceVideo.startsWith('content://')) {
            let mType = mediaData.imageData.type.split('/');
            let fileFormat = mType[mType.length - 1]
            if (fileFormat.toUpperCase() == "QUICKTIME") {
                fileFormat = "mov";
            }
            let fileName = dateDbFormat(new Date()) + "." + fileFormat;
            const destPath = `${TemporaryDirectoryPath}/${fileName}`;
            moveFile(sourceVideo, destPath)
                .then(() => {
                    videouri = destPath;//"file://" +
                    uploadToServer(videouri);
                })
                .catch((err) => {
                    // console.log(err.message);
                    seterrorMsg(ErrorMessages.addStoryMediaInvalidErr);
                    setrefresfErrorMsg(Math.random());
                    setShowLoading(false);
                });

        }
        else {
            uploadToServer(videouri);
        }
        // const dstUrl = await Video.compress(
        //     videouri,
        //     {
        //         compressionMethod: 'manual',
        //         minimumFileSizeForCompress: 0,
        //         bitrate: 480,
        //     },
        //     (progress) => {
        //         // console.log('Compression Progress: ', progress);
        //     }
        // );
        // const realPath = await getRealPath(dstUrl, 'video');
        // submitPost(realPath, "VIDEO");
    }
    const uploadToServer = async (videoUri) => {
        videoDestUri = videoUri;
        const realPath = await getRealPath(videoUri, 'video');
        submitPost(realPath, "VIDEO");
    }
    const corpGalleryImage = (imgUrl) => {
        // if (Platform.OS == "ios") {
        //     imgUrl = imgUrl.replace('file://', '')
        //     changeStoryMedia({ uri: imgUrl, captureType: "IMAGE", imageData: {} });
        // }
        // else {
        setTimeout(() => {
            ImagePicker.openCropper({
                path: imgUrl,
                freeStyleCropEnabled: true,
                cropping: true,
                // width: 1080,
                // height: 1920,
                cropperCircleOverlay: false,
            }).then(image => {
                changeStoryMedia({ uri: image.path, captureType: "IMAGE", imageData: {} });
            }).catch(err => {

            });
        }, 500);

        // }
    }
    const tagPeopleSearchPress = (clickID, obj) => {
        setshowTagPopup(false);
        if (clickID == "SELECTED") {
            let formatTxt = _inputFormatTextForTag(description, TAGGED_SYMBOL + obj.user_handle);
            setshowTagPopup(false);
            setdescription(formatTxt + " ");
            descInputRef.current.focus()
        }
    }
    const deleteTempFile = (tempuri) => {
        try {
            // console.log("Deletion tempuri", tempuri);
            deleteFile(tempuri)
                .then(() => {
                    // console.log('FILE DELETED', tempuri);
                    if (tempuri == videoDestUri) {
                        videoDestUri = null;
                    }
                })
        } catch (error) {
            // console.log("DeletionErr", error);
        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar title="New Story" showTopButton={true} navigation={navigation}
                buttonComponent={<TouchableOpacity disabled={disabledBtn} onPress={() => { postBtnPress() }}
                ><EntutoTextView style={defaultStyle.postBtn}>POST</EntutoTextView></TouchableOpacity>} />
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside}>
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refresfErrorMsg} />
                    // </View>
                    : null
            }
            <Modal
                animationType="fade"
                visible={showTagPopup}
                onRequestClose={() => setshowTagPopup(false)}
                style={{ margin: 0, flex: 1 }}>
                <InputTagPeopleSearchComponent
                    tagPeopleSearchPress={tagPeopleSearchPress}
                    navigation={navigation}
                    preSearchStr={tagName}
                    captionTxt={description} />
            </Modal>
            <ScrollView>
                <View style={defaultStyle.container}>

                    <View style={styles.addMediaBox}>
                        {
                            mediaData != null ?
                                <View>
                                    {
                                        mediaData.captureType == "VIDEO" ?
                                            <View>
                                                <TouchableOpacity onPress={() => imageBtnPress()}>
                                                    <View style={{ position: 'relative', height: 200 }}>
                                                        <Video source={{ uri: mediaData.uri }}
                                                            pointerEvents={'none'}
                                                            style={styles.addMediaImage}
                                                            paused={true}
                                                            controls={false}
                                                        />
                                                        <View style={{
                                                            position: 'absolute', top: 0, left: 0, right: 0, bottom: 0,
                                                            justifyContent: 'center', alignItems: 'center', zIndex: 999
                                                        }}>
                                                            <FAB
                                                                style={{ backgroundColor: '#FFFFFF', }}
                                                                small
                                                                icon="play-circle-outline"
                                                            />
                                                        </View>
                                                    </View>

                                                </TouchableOpacity>
                                            </View>
                                            :
                                            <TouchableOpacity onPress={() => imageBtnPress()}>
                                                <View>

                                                    <Image
                                                        style={styles.addMediaImage}
                                                        source={{ uri: mediaData.uri }}
                                                        resizeMode='cover' />
                                                </View>
                                            </TouchableOpacity>
                                    }

                                    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                                        <TouchableOpacity onPress={() => removeMediaBox()}>
                                            <EntutoTextView style={styles.removeMediaTxt}>Remove Media</EntutoTextView>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                                :
                                <View style={{ flexDirection: 'row', }}>
                                    <EntutoTextView style={styles.addMediaTxt}>
                                        Add Media
                                    </EntutoTextView>
                                    <View style={{ marginLeft: 'auto', flexDirection: 'row', }}>
                                        <TouchableOpacity onPress={() => mediaBtnPress()}>
                                            <Image
                                                source={require('../assets/Images/icon/addmedia.png')}
                                                resizeMode={'cover'}
                                                style={styles.addMediaIcon} />
                                        </TouchableOpacity>
                                        <TouchableOpacity onPress={() => cameraBtnPress()}>
                                            <Image
                                                source={require('../assets/Images/icon/addcamera.png')}
                                                resizeMode={'cover'}
                                                style={styles.addMediaIcon} />
                                        </TouchableOpacity>
                                    </View>
                                </View>
                        }
                    </View>
                    <View style={styles.addNewDesc}>
                        <EntutoEditText
                            refValue={descInputRef}
                            multiline
                            placeholderTxt="Caption"
                            labelTxt="Caption"
                            value={description}
                            maxLength={250}
                            maxHeight={150}
                            onChangeText={(text) => descriptionChangeHandler(text)} />
                        <View style={defaultStyle.inputUnderCountBox}>
                            <EntutoTextView style={defaultStyle.inputUnderCountBoxTxt}>{description.length}/250</EntutoTextView>
                        </View>
                    </View>
                </View>
            </ScrollView>
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />

        </>
    )
}

export default AddStoryScreen;

const styles = StyleSheet.create({
    addMediaBox: {
        flexDirection: 'column',
        backgroundColor: '#F2EBE9',
        borderRadius: 5,
        paddingHorizontal: 10,
        paddingVertical: 22,

    },
    addMediaTxt: {
        fontSize: 17,
        color: '#000000',
        fontWeight: '700'
    },

    addMediaIcon: {
        width: 24,
        height: 24,
        marginHorizontal: 10,
    },
    addNewDesc: {
        marginVertical: 20,
    },
    addMediaImage: {
        width: '100%',
        height: 200,
        marginBottom: 15,
        // borderRadius: 15,
    },
    removeMediaTxt: {
        color: '#FF4963',
        paddingHorizontal: 10,

    },
})
