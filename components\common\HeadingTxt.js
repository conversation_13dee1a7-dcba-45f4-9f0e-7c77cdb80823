import React from 'react'
import { StyleSheet, } from 'react-native'
import EntutoTextView from './EntutoTextView';
import useSTheme from '../../theme/useSTheme';

const HeadingTxt = props => {
    const theme = useSTheme();
    return <EntutoTextView style={{
        ...styles.default,
        color: theme.colors.mainHeadingColor,
        fontSize: theme.calculateFontSize(theme.dimensions.HeadingTxt),
        fontFamily: theme.getFontFamily('bold'),
        ...props.style
    }}>{props.children}</EntutoTextView>
}

export default HeadingTxt;

const styles = StyleSheet.create({
    default: {
        fontWeight: "bold"
    }
})
