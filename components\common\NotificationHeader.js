import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import EntutoTextView from './EntutoTextView'
import useSThemedStyles from '../../theme/useSThemedStyles';

const NotificationHeader = ({ headerTitle = "", viewAllBtnLabel = "View All", headerValue, showViewAllBtn = true, viewAllBtnPress }) => {
    const style = useSThemedStyles(styles);
    return (
        <View style={style.notiHeaderBox}>
            <EntutoTextView style={style.notiHeaderText}>{headerTitle}</EntutoTextView>
            {
                showViewAllBtn ?
                    <View>
                        <TouchableOpacity onPress={() => viewAllBtnPress(headerValue)}>
                            <EntutoTextView style={style.notiHeaderViewAllTxt}>{viewAllBtnLabel}</EntutoTextView>
                        </TouchableOpacity>
                    </View>
                    : null
            }

        </View>
    )
}

export default NotificationHeader

const styles = theme => StyleSheet.create({
    notiHeaderBox: {
        flexDirection: 'row',
        marginHorizontal: 23,
        alignItems: 'center',
        marginBottom: 15,
    },
    notiHeaderText: {
        color: theme.colors.notiHeaderText,
        fontSize: theme.calculateFontSize(theme.dimensions.notificationHeaderText),
        fontWeight: '600',
        flex: 1,
    },
    notiHeaderViewAllTxt: {
        color: theme.colors.notiHeaderViewAllText,
        fontSize: theme.calculateFontSize(theme.dimensions.notificationViewAllText),
        fontWeight: '600',
    },
})