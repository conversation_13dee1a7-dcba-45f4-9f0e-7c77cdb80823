import { Animated, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import EntutoTextView from '../common/EntutoTextView'
import { FlatList } from 'react-native-gesture-handler'
import IntroSliderBoxItem from './IntroSliderBoxItem'
import IntroSliderPaginator from './IntroSliderPaginator';
import TickIcon from '../../assets/Images/icon/list_tick_icon.png';
import useSThemedStyles from '../../theme/useSThemedStyles'

const IntroSliderBoxComponent = ({ navigation, sliderBoxComponentPress }) => {

    // const [sliderData, setsliderData] = useState([
    //     { id: 1, source: "http://192.168.213.119/Slate1.mp4" },
    //     { id: 2, source: "http://192.168.213.119/Slate2.mp4" },
    //     { id: 3, source: "http://192.168.213.119/Slate3.mp4" },
    // ]);
    const [sliderData, setsliderData] = useState([
        { id: 1, source: "https://www.sotrue.co.in/intro_video/Slate1.mp4" },
        // { id: 2, source: "https://www.sotrue.co.in/intro_video/Slate2.mp4" },
        // { id: 3, source: "https://www.sotrue.co.in/intro_video/Slate3.mp4" },
    ]);
    const style = useSThemedStyles(styles);
    const mediaRefs = useRef([])
    const scrollX = useRef(new Animated.Value(0)).current;
    const [currentIndex, setCurrentIndex] = useState(0);
    const slidesRef = useRef(null);
    const [activeVideo, setActiveVideo] = useState(null);
    const [currentVisibleIndex, setcurrentVisibleIndex] = useState(0);

    const viewableItemsChanged = useRef(({ viewableItems, changed }) => {
        if (viewableItems && viewableItems.length > 0) {
            setcurrentVisibleIndex(viewableItems[0].index)
        }
        setCurrentIndex(viewableItems[0].index);
    }).current;

    const renderItem = ({ item, index }) => {
        return (
            <IntroSliderBoxItem currentIndex={index}
                currentVisibleIndex={currentVisibleIndex}
                videoEnd={() => videoEnded()} itemData={item} />

        )
    }
    const [showNextBtn, setshowNextBtn] = useState(true);
    const [showPreviousBtn, setshowPreviousBtn] = useState(false);

    useEffect(() => {
        let nextBtn = true;
        let prevBtn = true;
        if (currentIndex == 0) {
            prevBtn = false;
        }
        if (currentIndex == sliderData.length - 1) {
            nextBtn = false;
        }
        setshowNextBtn(nextBtn);
        setshowPreviousBtn(prevBtn);
    }, [currentIndex])

    const nextButtonPress = () => {
        if (currentIndex < sliderData.length - 1) {
            slidesRef.current.scrollToIndex({ index: currentIndex + 1 })
        }
    }
    const previousButtonPress = () => {
        if (currentIndex > 0) {
            slidesRef.current.scrollToIndex({ index: currentIndex - 1 })
        }
    }
    const finishButtonPress = () => {
        finishTheSlide();
    }
    const closebtnPress = () => {
        finishTheSlide();
    }
    const finishTheSlide = () => {
        sliderBoxComponentPress("CLOSE", {})
    }
    const helpBtnPress = () => {
        navigation.navigate('FaqDisScreen');
    }
    const videoEnded = () => {
        nextButtonPress();
    }
    return (
        <>
            <View style={style.sliderHeader}>
                <TouchableOpacity onPress={() => helpBtnPress()}>
                    <EntutoTextView style={style.helpTxt}>HELP</EntutoTextView>
                </TouchableOpacity>
                <View style={{ marginLeft: 'auto', }}>
                    <TouchableOpacity onPress={() => closebtnPress()}>
                        <Image style={style.crossIcon}
                            resizeMode="contain"
                            source={require('../../assets/Images/icon/close_icon.png')} />
                    </TouchableOpacity>
                </View>
            </View>
            <View style={style.sliderContentBox}>
                <FlatList
                    data={sliderData}
                    renderItem={renderItem}
                    keyExtractor={(item, index) => `${index}`}
                    horizontal
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    pagingEnabled
                    bounces={false}
                    onScroll={Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], {
                        useNativeDriver: false
                    })}
                    decelerationRate={'normal'}
                    initialNumToRender={0}
                    removeClippedSubviews
                    onViewableItemsChanged={viewableItemsChanged}
                    viewabilityConfig={{
                        itemVisiblePercentThreshold: 0
                    }}
                    ref={slidesRef}
                />
            </View>
            {/* <View style={style.sliderBottomBox}>
                <View>
                    {
                        showPreviousBtn ?

                            <TouchableOpacity onPress={() => previousButtonPress()}>
                                <Image style={style.previousIcon}
                                    source={NavigationIcon}
                                    resizeMode='contain' />
                            </TouchableOpacity>
                            : null}
                </View>
                <IntroSliderPaginator data={sliderData} scrollX={scrollX} />
                <View>
                    {
                        showNextBtn ?
                            <TouchableOpacity onPress={() => nextButtonPress()}>
                                <Image style={style.nextIcon}
                                    source={NavigationIcon}
                                    resizeMode='contain' />
                            </TouchableOpacity>
                            :
                            <TouchableOpacity onPress={() => finishButtonPress()}>
                                <Image style={style.previousIcon}
                                    source={TickIcon}
                                    resizeMode='contain' />
                            </TouchableOpacity>}

                </View>
            </View> */}

        </>
    )
}

export default IntroSliderBoxComponent

const styles = theme => StyleSheet.create({
    sliderHeader: {
        paddingHorizontal: 10,
        paddingVertical: 10,
        flexDirection: 'row',
        justifyContent: 'center',
        // borderWidth: 1,
    },
    helpTxt: {
        color: theme.colors.primaryColor,
        fontWeight: 'bold',
        fontFamily: theme.getFontFamily('bold'),
        fontSize: theme.calculateFontSize(16)
    },
    crossIcon: {
        width: 16,
        height: 16,
        tintColor: theme.colors.primaryColor,
    },
    sliderContentBox: {
        flex: 1,
    },
    sliderBottomBox: {
        height: theme.dimensions.bottonTabNavHeight,
        // borderWidth: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 15,
    },
    previousIcon: {
        width: 24,
        height: 24,
    },
    nextIcon: {
        width: 24,
        height: 24,
        transform: [{ rotate: '180deg' }]
    },
})