import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Image, Keyboard, StyleSheet, TextInput, TouchableOpacity, View, FlatList, ScrollView } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import CustomActivityIndicator from '../components/common/CustomActivityIndicator';
import CustomStatusBar from '../components/common/CustomStatusBar';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import SearchProfileRow from '../components/search/SearchProfileRow';
import { _RedirectionErrorList, KEYWARDS_ARRAY_SIZE } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import { checkValueLength, getSecondsBetweenDates } from '../utils/Utils';
import { AppStateContext } from '..';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';
import HeadingTxt from '../components/common/HeadingTxt';
import EntutoTextView from '../components/common/EntutoTextView';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import TempData from '../data/TempData';


const SearchResultMainScreen = ({ navigation, route }) => {
    const { queryName } = route.params;
    const [searchQuery, setSearchQuery] = React.useState('');
    const [isTyped, setisTyped] = useState(false);
    const [searchList, setsearchList] = useState([]);
    const [failedCount, setfailedCount] = useState(0);
    const [errorMsg, seterrorMsg] = useState("");
    const [errorMsgKey, seterrorMsgKey] = useState(Math.random());
    const [showLoading, setshowLoading] = useState(false);

    const RowsPerPage = 10;
    const [startRecord, setstartRecord] = useState(0);
    const [bottomLoading, setbottomLoading] = useState(false);
    const [isNoDataFound, setisNoDataFound] = useState(false);
    const [bottomReachTime, setbottomReachTime] = useState(new Date());
    const [keywordsData, setKeywordsData] = useState([]);

    const { searchKeywardHistory, changeKeywardHistory } = useContext(AppStateContext)
    const [searchedKeys, setSearchedKeys] = useState([]);
    const [showKeywordLoading, setShowKeywordLoading] = useState(false);
    const [showMoreBtn, setShowMoreBtn] = useState(false);

    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    useEffect(() => {
        if (checkValueLength(queryName)) {
            setSearchQuery(queryName);
            setisTyped(true);
            setshowLoading(true);
            getSearchResult(queryName, 0, RowsPerPage);
        }
    }, [queryName]);

    useEffect(() => {
        const timeOut = setTimeout(() => {
            if (checkValueLength(searchQuery) && searchQuery.length > 2) {
                setshowLoading(true);
                getSearchResult(searchQuery, 0, RowsPerPage);
            }
        }, 500);
        return () => {
            clearTimeout(timeOut)
        }
    }, [searchQuery]);


    const renderSearchRow = ({ item }) => {
        return (
            <SearchProfileRow data={item} navigation={navigation}
                searchQueryInsert={true} searchQuery={searchQuery} />
        );
    };

    const onChangeSearch = query => {
        if (query.length != 0) {
            setisTyped(true);
            seterrorMsg("");
            setsearchList([]);
            // if (query.length > 2) {
            //     setshowLoading(true);
            //     getSearchResult(query, 0, RowsPerPage);
            // }
        }
        else {
            setisTyped(false);
            seterrorMsg("");
            seterrorMsgKey(Math.random());
            setsearchList([]);
        }
        setSearchQuery(query);
    }
    const clearSearchTxt = () => {
        setSearchQuery("");
        seterrorMsg("");
        seterrorMsgKey(Math.random())
        setsearchList([]);
        setisTyped(false);
    }
    const cancelBtnClick = () => {
        setSearchQuery("");
        Keyboard.dismiss();
        navigation.goBack();
    }
    const storeKeyWards = (queryValue) => {
        let historyKeys = searchKeywardHistory;
        if (!historyKeys.includes(queryValue)) {
            if (historyKeys.length == KEYWARDS_ARRAY_SIZE) {
                historyKeys.pop();
            }
            historyKeys.unshift(queryValue);
            changeKeywardHistory(historyKeys);
        }
    }

    function getSearchResult(query, startRecordV, rowsPerPage) {
        setstartRecord(startRecordV)
        let hashMap = {
            _action_code: "11:DO_SEARCH",
            search_str: query,
            _start_row: startRecordV,
            _rows_page: rowsPerPage,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setshowLoading(false);
            setbottomLoading(false);
            if (parseInt(startRecordV) == 0) {
                setsearchList([...[], ...data.data]);
            }
            else {
                setsearchList([...searchList, ...data.data]);
            }
            if (startRecordV == 0) {
                storeKeyWards(query)
            }
            seterrorMsg("");
            seterrorMsgKey(Math.random())
            if (data.data.length < RowsPerPage) {
                setisNoDataFound(true);
            }
            else {
                setisNoDataFound(false);
            }


        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setshowLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                setbottomLoading(false);
                setisNoDataFound(true);
                if (parseInt(startRecordV) == 0) {
                    setsearchList([]);
                    let errrMsg2 = "";
                    let failedCountV = failedCount + 1;
                    if (failedCount === 0) {
                        errrMsg2 = "Are you sure?";
                    }
                    else if (failedCount === 1) {
                        errrMsg2 = "We don’t have this.";
                    }
                    else {
                        failedCountV = 0;
                        errrMsg2 = "Ask again ";
                    }
                    setfailedCount(failedCountV);
                    seterrorMsg(errrMsg2);
                    seterrorMsgKey(Math.random());
                }
            }
        });
    }
    const handleEndRefresh = () => {
        if (!isNoDataFound) {
            let currentTime = new Date();
            let diffTime = getSecondsBetweenDates(bottomReachTime, currentTime);
            if (diffTime > 4) {
                let startRec = startRecord + RowsPerPage;
                setbottomLoading(true);
                getSearchResult(searchQuery, startRec, RowsPerPage);
                setbottomReachTime(new Date());
            }

        }
    }
    //History
    useEffect(() => {
        assignSearchKeys();

    }, [searchKeywardHistory]);
    const assignSearchKeys = (moreBtn = false) => {
        const dataList = JSON.parse(JSON.stringify(searchKeywardHistory));
        if (dataList.length > 4) {
            setShowMoreBtn(true)
            let tempList = [];

            dataList.map((item, i) => {
                // tempList.push(item);
                if (moreBtn) {
                    tempList.push(item);
                } else {
                    if (i < 4) {
                        tempList.push(item);
                    }
                }

            });
            setSearchedKeys([...[], ...tempList]);
        }
        else {
            setShowMoreBtn(false)
            setSearchedKeys([...[], ...dataList]);
        }
    }
    const [showMoreHistory, setShowMoreHistory] = useState(false);
    const moreButtonPress = () => {
        assignSearchKeys(!showMoreHistory);
        setShowMoreHistory(!showMoreHistory)
    }
    const categoryNamePress = (categoryName) => {
        Keyboard.dismiss();
        setisTyped(true);
        setSearchQuery(categoryName)

        setshowLoading(true);
        getSearchResult(categoryName, 0, RowsPerPage);

        let historyKeys = searchKeywardHistory;
        if (!historyKeys.includes(categoryName)) {
            if (historyKeys.length == KEYWARDS_ARRAY_SIZE) {
                historyKeys.pop();
            }
            historyKeys.unshift(categoryName);
            changeKeywardHistory(historyKeys);
        }
    }
    const clearRowHistory = (index, item) => {
        let tempArray = JSON.parse(JSON.stringify(searchKeywardHistory));
        const tempIndex = tempArray.indexOf(item);
        if (tempIndex > -1) {
            tempArray.splice(tempIndex, 1);
        }
        changeKeywardHistory(tempArray);
    }
    //Popular Categories
    useEffect(() => {
        if (TempData.categoriesData.length == 0) {
            setShowKeywordLoading(true);
            popularCategoriesService();
        }
        else {
            setKeywordsData(TempData.categoriesData);
            setShowKeywordLoading(false);
        }

    }, [])

    const popularCategoriesService = () => {
        let hashMap = {
            _action_code: "11:GET_POPULAR_CATEGORIES",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let catKeyword = [];
            data.data.map(obj => {
                catKeyword.push(obj.display_value)
            });
            TempData.categoriesData = catKeyword;
            setKeywordsData(catKeyword);
            setShowKeywordLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            setShowKeywordLoading(false);
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setKeywordsData([]);
            }
        });

    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={{ ...defaultStyle.container, backgroundColor: theme.colors.backgroundColor }}>
                <View style={style.searchBarBox}>
                    <View >
                        <TouchableOpacity onPress={() => cancelBtnClick()} style={{ marginRight: 20, }}>
                            <View>
                                <Image
                                    source={require('../assets/Images/icon/search_arrow.png')}
                                    style={style.searchIcon}
                                    resizeMode='contain'
                                />
                            </View>
                        </TouchableOpacity>
                    </View>

                    <View
                        style={style.searchBar}>
                        <Image
                            source={require('../assets/Images/icon/search_icon.png')}
                            style={style.searchIcon}
                        />
                        <TextInput
                            numberOfLines={1}
                            style={style.input}
                            placeholder="Search"
                            placeholderTextColor={theme.colors.inputPlaceholderColor}
                            value={searchQuery}
                            autoCorrect={false}
                            autoFocus={true}
                            onChangeText={onChangeSearch}
                            selectionColor={theme.colors.primaryColor}
                        />
                        {isTyped && (
                            <TouchableOpacity onPress={() => clearSearchTxt()}>
                                <View>
                                    <Image
                                        source={require('../assets/Images/icon/c_remove.png')}
                                        style={style.searchCrossIcon}
                                    />
                                </View>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>

            </View>

            <View style={{ ...defaultStyle.container, flex: 1, backgroundColor: theme.colors.backgroundColor }}>

                {
                    checkValueLength(searchQuery) ?
                        <View>
                            {
                                showLoading ?
                                    <CustomActivityIndicator progress={showLoading} />
                                    : null
                            }
                            {
                                checkValueLength(errorMsg) ?
                                    <View style={{ ...defaultStyle.errorBoxOutside, marginTop: 50, minHeight: 100, backgroundColor: theme.colors.backgroundColor }} >
                                        <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsgKey} />
                                    </View>
                                    : null
                            }
                            <FlatList
                                keyboardShouldPersistTaps={'handled'}
                                contentContainerStyle={{ paddingBottom: 20, backgroundColor: theme.colors.backgroundColor }}
                                data={searchList}
                                renderItem={renderSearchRow}
                                keyExtractor={(item, index) => `${index}`}
                                onEndReached={handleEndRefresh}
                                initialNumToRender={10}

                            />
                            {
                                bottomLoading ?
                                    <View style={{ alignItems: 'center', justifyContent: 'center', paddingVertical: 15 }}>
                                        <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={'large'} />
                                    </View>
                                    : null
                            }
                        </View>
                        :
                        <View style={{ flex: 1 }}>
                            <ScrollView showsHorizontalScrollIndicator={false}
                                keyboardShouldPersistTaps={'handled'}>
                                <View>
                                    {
                                        searchedKeys.length != 0 ?
                                            <>
                                                <View style={{ marginTop: 1, marginBottom: 12 }}>
                                                    <HeadingTxt>History</HeadingTxt>
                                                </View>
                                                <View style={style.listBox}>
                                                    {
                                                        searchedKeys.map((item, i) => {
                                                            return <KeyWordItemRow key={i} index={i} item={item}
                                                                showLinearGrad={false}
                                                                boxStyle={style.historySearchBox}
                                                                textStyle={style.historySearchBoxTxt}
                                                                showCrossIcon={true}
                                                                categoryNamePress={categoryNamePress}
                                                                clearRowHistory={clearRowHistory} />
                                                        })
                                                    }
                                                </View>
                                                {
                                                    showMoreBtn ?
                                                        <View>
                                                            <TouchableOpacity onPress={() => moreButtonPress()}>
                                                                <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
                                                                    <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', }}>
                                                                        <EntutoTextView style={style.moreText}>
                                                                            {showMoreHistory ? "Less" : "More"}
                                                                        </EntutoTextView>
                                                                        <MaterialCommunityIcons name={showMoreHistory ? "chevron-up" : "chevron-down"} color={'#4953ED'} size={18} />
                                                                    </View>
                                                                </View>
                                                            </TouchableOpacity>
                                                        </View>
                                                        : null
                                                }
                                            </>
                                            : null
                                    }

                                    <View style={{ marginTop: 16, marginBottom: 12 }}>
                                        <HeadingTxt>Top Search</HeadingTxt>
                                    </View>
                                    {
                                        showKeywordLoading ?
                                            <CustomActivityIndicator progress={showKeywordLoading} />
                                            : null
                                    }
                                    <View style={style.listBox}>
                                        {
                                            keywordsData.map((item, i) => {
                                                return <KeyWordItemRow
                                                    key={i} index={i}
                                                    item={item}
                                                    boxStyle={{ marginBottom: 8 }}
                                                    categoryNamePress={categoryNamePress}
                                                    clearRowHistory={clearRowHistory}
                                                />
                                            })
                                        }
                                    </View>

                                </View>
                            </ScrollView>
                        </View>
                }
            </View>
        </>
    )
}

export default SearchResultMainScreen;
const KeyWordItemRow = ({ item, index, showLinearGrad = true, boxStyle = {},
    textStyle = {}, showCrossIcon = false, categoryNamePress, clearRowHistory }) => {
    const style = useSThemedStyles(styles);
    return <View style={{ ...style.popularSearchBox, ...boxStyle, }}>
        {
            showLinearGrad ?
                <LinearGradient
                    colors={["#FC6767", "#FC6767",]}
                    locations={[0, 1]}
                    start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
                    style={style.linearGradPopular} />
                : null
        }

        <TouchableOpacity onPress={() => categoryNamePress(item)}>
            <View>
                <EntutoTextView style={{ ...style.popularSearchBoxTxt, ...textStyle }} >{item}</EntutoTextView>
            </View>
        </TouchableOpacity>
        {
            showCrossIcon ?
                <TouchableOpacity onPress={() => clearRowHistory(index, item)}>
                    <View>
                        <Image
                            source={require('../assets/Images/icon/close_icon.png')}
                            style={style.historyCrossIcon}
                        />
                    </View>
                </TouchableOpacity>
                : null
        }

    </View>
}

const styles = theme => StyleSheet.create({
    searchBarBox: {
        alignItems: "center",
        flexDirection: "row",
        marginTop: 8,
    },
    searchBar: {
        flex: 1,
        flexDirection: "row",
        borderWidth: 1,
        borderColor: '#C1C0C8',
        alignItems: "center",
        minHeight: 40,
        paddingStart: 16
    },
    searchIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        tintColor: '#CCC'
    },
    input: {
        fontSize: theme.calculateFontSize(theme.dimensions.searchResultInputText),
        marginHorizontal: 8,
        padding: 0,
        flex: 1,
        color: theme.colors.inputTextColor,
        minHeight: 40
    },
    searchCrossIcon: {
        height: theme.dimensions.searchInputIconH,
        width: theme.dimensions.searchInputIconH,
        marginRight: 16,
    },
    historyCrossIcon: {
        height: 10,
        width: 10,
        marginRight: 10,
        tintColor: "#FFF"
    },
    popularSearchBox: {
        position: 'relative',
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 10,
        marginRight: 8,
    },
    popularSearchBoxTxt: {
        color: '#FFFFFF',
        fontSize: theme.calculateFontSize(theme.dimensions.searchResultPopularText),
        paddingHorizontal: 8,
        paddingVertical: 8
    },
    historySearchBox: {
        borderWidth: 1,
        borderColor: '#CCC',
        flexDirection: 'row',
    },
    historySearchBoxTxt: {
        color: '#FFFFFF',
    },

    linearGradPopular: {
        position: 'absolute',
        width: '100%',
        height: '100%',
    },
    moreText: {
        color: '#4953ED',
        fontSize: theme.calculateFontSize(theme.dimensions.searchResultMoreText),
        marginEnd: 8,
    },
    cancelBtnText: {
        color: theme.colors.primaryColor,
        fontSize: theme.calculateFontSize(theme.dimensions.searchResultCancelBtnText),
        fontWeight: '700',
        marginLeft: 16,
    },
    topTabView: {
        marginVertical: 8,
        flexDirection: 'row',
        alignItems: 'center',
    },
    topTabBarLabel: {
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
    },
    listBox: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    }
})
