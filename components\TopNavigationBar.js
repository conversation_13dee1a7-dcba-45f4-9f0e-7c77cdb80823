import React from 'react'
import { StyleSheet, Text, View, Image, TouchableOpacity, Pressable } from 'react-native';
import Arrowicon from '../assets/Images/icon/Arrow.png';
import useSThemedStyles from '../theme/useSThemedStyles'
import useSTheme from '../theme/useSTheme';
const TopNavigationBar = ({ navigation, inside }) => {
    const goBackPrevious = () => {
        navigation.goBack(null);
    }
    const style = useSThemedStyles(styles);
    const theme = useSTheme();
    return (
        <View style={style.appBar} >
            <View>
                <Pressable onPress={() => goBackPrevious()}
                    android_ripple={{
                        color: theme.colors.pressableRippleColor, borderless: true,
                        radius: 30,
                    }}
                    style={{ paddingVertical: 8, }}>
                    <View style={{ paddingHorizontal: 8, }}>
                        <Image style={style.arrowIcon}
                            source={Arrowicon} />
                    </View>
                </Pressable>
            </View>


        </View>
    )
}

export default TopNavigationBar

const styles = theme => StyleSheet.create({
    appBar: {
        height: 56,
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: theme.colors.appBarBackgroundColor
    },
    arrowIcon: {
        height: 24,
        width: 24,
        resizeMode: "contain",
        tintColor: theme.colors.topHeaderColor
    }
})
