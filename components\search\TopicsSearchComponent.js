import { FlatList, Keyboard, Platform, RefreshControl, StyleSheet, Text, View } from 'react-native'
import React, { useCallback, useEffect, useState } from 'react'
import { checkValueLength } from '../../utils/Utils';
import ServerConnector from '../../utils/ServerConnector';
import { RenderItemTypeOne, RenderItemTypeThree, RenderItemTypeTwo } from '../profile/PostImageItem';
import { ActivityIndicator } from 'react-native-paper';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import TempData from '../../data/TempData';

const TopicsSearchComponent = ({
    searchText = "",
    navigation, itemCallback = null

}) => {
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [errorMsg, setErrorMsg] = useState("");
    const [errorMsgKey, setErrorMsgKey] = useState(Math.random());
    const [showLoading, setShowLoading] = useState(false);
    const [selectedQuery, setSelectedQuery] = useState("");
    const [progressLoading, setProgressLoading] = useState(false)
    useEffect(() => {
        const timeOutVal = setTimeout(() => {
            //Comment out for Service
            if (!showLoading && !bottomLoadingExplore) {
                if (searchText.length > 1) {
                    setShowLoading(true);
                    setErrorMsg("");
                    getExploreDataService(0, searchText)
                }
                else {
                    if (searchText.length == 0) {
                        setShowLoading(true);
                        setErrorMsg("");
                        if (TempData.topicsBackupData.length != 0) {
                            modifyArrayData(TempData.topicsBackupData);
                            setIsNoDataFoundExplore(false);
                            setFirstTimeLoad(true);
                            setExploreStartRecord(0)
                        }
                        else {
                            modifyArrayData(DEFAULT_LIST);
                            getExploreDataService(0, "");
                        }

                    }
                }
            }
        }, 500);
        return () => {
            clearTimeout(timeOutVal)
        }
    }, [searchText]);
    const DEFAULT_LIST = [
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
        { "media_file": null, "media_type": "IMAGE", "post_seq": "-1", disableClick: true },
    ];

    const [explorePostList, setExplorePostList] = useState([]);
    const [explorePostListBackup, setExplorePostListBackup] = useState([]);
    const ExploreRowsPerPage = 21;
    const [exploreStartRecord, setExploreStartRecord] = useState(0);
    const [isNoDataFoundExplore, setIsNoDataFoundExplore] = useState(false)
    const [bottomLoadingExplore, setbottomLoadingExplore] = useState(false);
    const [bottomReachTimeExplore, setbottomReachTimeExplore] = useState(new Date());
    const [firstTimeLoad, setFirstTimeLoad] = useState(false);
    function getExploreDataService(startRec, query) {
        setExploreStartRecord(startRec)
        setSelectedQuery(query);
        let hashMap = {
            _action_code: "11:DO_TOPIC_SEARCH",
            _start_row: startRec,
            _rows_page: ExploreRowsPerPage,
        }
        if (checkValueLength(query)) {
            hashMap.search_str = query;
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setFirstTimeLoad(true);
            setShowLoading(false);
            setProgressLoading(false);
            setbottomLoadingExplore(false);
            data.data.map(item => item.disableClick = false);
            if (parseInt(startRec) == 0) {
                modifyArrayData(data.data);
            }
            else {
                modifyArrayData(data.data, true);
            }
            if (parseInt(startRec) == 0 && checkValueLength(query)) {
                if (itemCallback) {
                    itemCallback("STORE_QUERY", { query: query })
                }
            }
            if (parseInt(startRec) == 0 && !checkValueLength(query)) {
                TempData.topicsBackupData = data.data;
            }
            // if (itemCallback) {
            //     itemCallback("STORE_BACKUP_PROFILE", { query: query, data: list });
            // }
            if (data.data.length < ExploreRowsPerPage) {
                setIsNoDataFoundExplore(true);
            }
            else {
                setIsNoDataFoundExplore(false);
            }
        }, (errorCode, errorMessage, data) => { // failure method            
            setFirstTimeLoad(true);
            setShowLoading(false);
            setProgressLoading(false);
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setbottomLoadingExplore(false)
                if (parseInt(startRec) == 0) {
                    modifyArrayData([]);
                    setErrorMsg(errorMessage);
                    setErrorMsgKey(Math.random());
                }
                setIsNoDataFoundExplore(true)
            }
        });
    }
    const modifyArrayData = (arrayData, joinData = false) => {
        let finalData = [];
        let tempDataArray = [];
        if (joinData) {
            let tempData = JSON.parse(JSON.stringify(explorePostListBackup));
            tempDataArray = tempData.concat(arrayData);
        }
        else {
            tempDataArray = arrayData;
        }

        for (let i = 0; i < tempDataArray.length; i += 3) {
            let j = 0;
            let data = [];
            while (j < 3) {
                tempDataArray[i + j] && data.push(tempDataArray[i + j])
                j += 1;
            }
            finalData.push({ id: tempDataArray[i].post_seq, disableClick: tempDataArray[i].disableClick, ui_type: (finalData.length % 3) + 1, data })
        }
        setExplorePostList([...[], ...finalData]);
        setExplorePostListBackup(tempDataArray);
        setShowLoading(false);
        // console.log("Execute Function Block", new Date());
    }
    const postCardClick = () => {

    }
    const renderItem = useCallback(
        ({ item }) => {
            switch (item.ui_type) {
                case 1:
                    return <RenderItemTypeOne postCameFrom="SEARCH" postCardType={"ALL"} itemData={item.data}
                        navigation={navigation} isMyProfile={false} postCardClick={postCardClick} disableClick={item.disableClick} />
                case 2:
                    return <RenderItemTypeTwo postCameFrom="SEARCH" postCardType={"ALL"} itemData={item.data}
                        navigation={navigation} isMyProfile={false} postCardClick={postCardClick} disableClick={item.disableClick} />
                case 3:
                    return <RenderItemTypeThree postCameFrom="SEARCH" postCardType={"ALL"} itemData={item.data}
                        navigation={navigation} isMyProfile={false} postCardClick={postCardClick} disableClick={item.disableClick} />
                default:
                    return null;
            }
        },
        [explorePostList]
    );
    const keyExtractor = useCallback((item, index) => `${item.id}_${index}`);
    const ItemSeparatorComponent = () => {
        return <View style={{ height: 1 }} />
    }
    const handleEndExploreRefresh = () => {
        if (firstTimeLoad) {
            if (!bottomLoadingExplore) {
                if (!isNoDataFoundExplore) {
                    let startRec = exploreStartRecord + ExploreRowsPerPage;
                    setbottomLoadingExplore(true);
                    getExploreDataService(startRec, selectedQuery);
                }
            }
        }

    }
    const listFooterComponent = useCallback(
        () => {
            return (
                <View style={{ height: 42, marginTop: 8, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <ActivityIndicator size={32} />
                </View>

            );
        },
        [bottomLoadingExplore],
    )
    const handleRefresh = () => {
        setProgressLoading(true);
        getExploreDataService(0, selectedQuery);
    }
    const [keyboardHeight, setKeyboardHeight] = useState(80)
    const handleShowKeyboard = (e) => {
        const height = e.endCoordinates.height;
        setKeyboardHeight(height);
    };

    const handleHideKeyboard = () => {
        setKeyboardHeight(80);
    };

    React.useEffect(() => {
        if (Platform.OS == 'android') {
            return;
        }
        const keyboardDidShowSubscribtion = Keyboard.addListener(
            'keyboardDidShow',
            handleShowKeyboard,
        );
        const keyboardWillHideSubscribtion = Keyboard.addListener(
            'keyboardWillHide',
            handleHideKeyboard,
        );
        return () => {
            Keyboard.removeSubscription?.(keyboardDidShowSubscribtion);
            Keyboard.removeSubscription?.(keyboardWillHideSubscribtion);
        };
    }, []);

    return (
        <View>
            {
                showLoading ?
                    <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                        <ActivityIndicator animating={true} />
                    </View>
                    : null
            }
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsgKey} />
                    : null
            }
            <FlatList
                onScrollBeginDrag={() => Keyboard.dismiss()}
                keyboardShouldPersistTaps={'always'}
                contentContainerStyle={{ paddingBottom: keyboardHeight, backgroundColor: theme.colors.backgroundColor }}
                removeClippedSubviews={true}
                data={explorePostList}
                renderItem={renderItem}
                keyExtractor={(item, index) => `${item.post_seq}_${index}`}
                // keyExtractor={keyExtractor}
                ItemSeparatorComponent={ItemSeparatorComponent}
                onEndReached={handleEndExploreRefresh}
                maxToRenderPerBatch={21}
                windowSize={60}
                updateCellsBatchingPeriod={21}
                initialNumToRender={21}
                disableVirtualization
                getItemLayout={(data, index) => {
                    if (data.ui_type == 1 && data.ui_type == 3) {
                        return { index, length: 200, offset: 200 * index }
                    }
                    else {
                        return { index, length: 100, offset: 100 * index }
                    }
                }}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
                ListFooterComponent={bottomLoadingExplore && listFooterComponent}
                refreshControl={
                    <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                }
            />
        </View>
    )
}

export default TopicsSearchComponent

const styles = theme => StyleSheet.create({})