import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { ThemeColorList } from '../constants/Constants';
import LinearGradient from 'react-native-linear-gradient';
import useSTheme from '../theme/useSTheme';

const ThemeColorComponent = ({
    themeColorChange = null,
    selectedUIColor = "COLOR_1"
}) => {
    const [gradientColorArray, setGradientColorArray] = useState([]);
    const [colorList, setColorList] = useState([]);
    const theme = useSTheme();
    useEffect(() => {
        let themeColorList = JSON.parse(JSON.stringify(ThemeColorList));
        let gradientColorArrayTemp = [];
        let colorListTemp = [];
        themeColorList.forEach((item, index) => {
            gradientColorArrayTemp.push(item.color)
            if (item.value == selectedUIColor) {
                item.isChecked = true;
            }
            else {
                item.isChecked = false;
            }

            colorListTemp.push(item)
        });
        setColorList(colorListTemp);
        setGradientColorArray(gradientColorArrayTemp);
    }, [selectedUIColor]);



    const onColorChange = (selectColor) => {
        let tempColorList = JSON.parse(JSON.stringify(colorList));
        tempColorList.forEach((item, index) => {
            if (item.value === selectColor) {
                item.isChecked = true;
            } else {
                item.isChecked = false;
            }
        });
        if (themeColorChange) {
            themeColorChange(selectColor)
        }
        setColorList(tempColorList);


    }

    return (
        <View style={styles.themeContainer}>
            {
                gradientColorArray.length > 0 ?
                    <LinearGradient colors={gradientColorArray}
                        start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }}
                        style={styles.themGradientBox} />
                    : null
            }
            {
                colorList.map((obj, i) => {
                    return <ColorItemBox key={i} color={obj.color} value={obj.value}
                        isChecked={obj.isChecked} index={i} onColorChange={onColorChange} />
                })
            }


        </View>
    )
}
const ColorItemBox = ({ color = "", value = "", index, isChecked = false, onColorChange }) => {
    return (
        <View style={styles.colorItemBox}>
            {
                isChecked ?
                    <View style={styles.colorDivider} />
                    : null
            }
            <TouchableOpacity onPress={() => onColorChange(value)}
                style={{ paddingLeft: index == 0 ? 8 : 8, paddingRight: index == 9 ? 8 : 8, paddingVertical: 4 }}
            >
                <View style={[styles.colorItem, { backgroundColor: color, borderColor: isChecked ? "#FFF" : color, borderWidth: 1 }]} />
            </TouchableOpacity>
        </View>
    )
}

export default ThemeColorComponent

const styles = StyleSheet.create({
    themeContainer: {
        position: 'relative',
        justifyContent: 'space-between',
        height: 64,
        flexDirection: 'row',
        alignItems: 'flex-end',
    },
    themGradientBox: {
        position: 'absolute',
        top: 10,
        left: 0,
        right: 0,
        height: 12,
        flex: 1,
        borderRadius: 6,
    },
    colorItemBox: {
        justifyContent: 'center',
        alignItems: 'center'
    },
    colorItem: {
        height: 17,
        width: 17,
        borderRadius: 17 / 2,
        marginBottom: 2,

    },
    colorDivider: {
        height: 28,
        width: 5,
        borderRadius: 5,
        backgroundColor: '#FFFFFF',
        marginBottom: 10,

    }
})