import React from 'react'
import { StyleSheet, Text, View } from 'react-native'
import Animated from 'react-native-reanimated'
import Colors from '../constants/Colors'
import Dimensions from '../constants/Dimensions'

const AnimatedBottomSheet = ({ translateY, ...props }) => {

    return (
        <Animated.View style={{
            ...styles.bottomSheet,
            transform: [{ translateY: translateY }]
        }}>
            <Text>adasd</Text>
        </Animated.View>
    )
}

export default AnimatedBottomSheet

const styles = StyleSheet.create({
    bottomSheet: {
        position: 'absolute',
        bottom: 0,
        width: Dimensions.screenWidth,
        height: 300,
        backgroundColor: Colors.bottomSheetColor,
        borderTopEndRadius: 25,
        borderTopStartRadius: 25,
        marginHorizontal: 0,


    }
})
