import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';

const StyleSelector = ({selectedStyle, onStyleChange}) => {
  const theme = useSTheme();
  // console.log('Theme colors:', theme.colors);
  const styles = useSThemedStyles(styled);
  // console.log('Generated styles:', styles);

  return (
    <View style={styles.styleContainer}>
      <Text style={styles.styleLabel}>Select your style</Text>
      <View style={styles.styleToggleContainer}>
        <TouchableOpacity
          style={[
            styles.styleToggleButton,

            selectedStyle === 'Light'
              ? styles.activeButton
              : styles.inactiveButton,
          ]}
          onPress={() => onStyleChange('Light')}>
          <Text
            style={[
              styles.styleToggleText,
              selectedStyle === 'Light'
                ? styles.activeText
                : styles.inactiveText,
            ]}>
            Light
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.styleToggleButton,

            selectedStyle === 'Dark'
              ? styles.activeButton
              : styles.inactiveButton,
          ]}
          onPress={() => onStyleChange('Dark')}>
          <Text
            style={[
              styles.styleToggleText,
              selectedStyle === 'Dark'
                ? styles.activeText
                : styles.inactiveText,
            ]}>
            Dark
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styled = theme =>
  StyleSheet.create({
    styleContainer: {
      marginBottom: 20,
    },
    styleLabel: {
      fontSize: theme.calculateFontSize(16),
      color: theme.colors.primaryTextColor,
      marginBottom: 12,
    },
    styleToggleContainer: {
      flexDirection: 'row',
      backgroundColor: theme.colors.buttonGroupBackColor,
      height: theme.dimensions.buttonHeight,
      overflow: 'hidden',
      padding: 4,
      borderWidth: 1,
      borderColor: theme.colors.buttonGroupBorderColor,
    },
    styleToggleButton: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 12,
    },

    activeButton: {
      backgroundColor:
        theme.colors.primaryColor || theme.colors.submitBtnBackground,
    },
    inactiveButton: {
      backgroundColor: 'transparent',
    },
    styleToggleText: {
      fontSize: theme.calculateFontSize(theme.dimensions.primaryBtnText),
      fontWeight: '500',
    },
    activeText: {
      color: theme.colors.submitBtnText,
    },
    inactiveText: {
      color: theme.colors.inputPlaceholderColor,
    },
  });
export default StyleSelector;
