import React, { useContext, useEffect, useRef, useState } from 'react'
import { FlatList, Keyboard, KeyboardAvoidingView, Platform, RefreshControl, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native'
import { AppStateContext } from '..';
import CommentRow from '../components/comments/CommentRow';
import CustomStatusBar from '../components/common/CustomStatusBar';
import EntutoTextView from '../components/common/EntutoTextView';
import ProfileImageComponent from '../components/common/ProfileImageComponent';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import NotiRowPlaceholder from '../components/placeholder/NotiRowPlaceholder';
import { MaxReplyUserTxtLimit, _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import appData from '../data/Data';
import CustomSnackbar from '../components/common/CustomSnackbar';
import { encodeHtmlEntitessData } from '../utils/Utils';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';

const CommentScreen = ({ route, navigation }) => {
    const { postSeq, postProfileSeq } = route.params;
    const { fullUserDetails, changeCommentObj, userProfileImage, changeShowCommentCount, homepagePostDataBackup } = useContext(AppStateContext);
    const __ProfileImage = fullUserDetails.hasOwnProperty("_profile_picture") ? fullUserDetails._profile_picture : "";
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    const [commentsList, setcommentsList] = useState([]);
    const [progressLoading, setprogressLoading] = useState(true);
    const [commentTxt, setcommentTxt] = useState("");
    const [showPostButton, setshowPostButton] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const textInputRef = useRef(null);
    const [replyToUser, setreplyToUser] = useState("")
    const [isReply, setisReply] = useState(false);
    const [replyCommentSeq, setreplyCommentSeq] = useState(-1)

    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [snackBarType, setsnackBarType] = useState("FAILED");

    const [isCommentModified, setisCommentModified] = useState(false);
    const [profileImage, setprofileImage] = useState(null);
    useEffect(() => {
        setprofileImage(userProfileImage);
    }, [userProfileImage])

    useEffect(() => {
        setprogressLoading(true);
        getPostCommentService();
    }, []);

    const commentRowClick = (clickID, obj) => {
        if (clickID == "DELETE") {
            deletePostCommentService(obj.commentSeq);
        }
        if (clickID == "REPLY") {
            setreplyToUser(obj.commentUserName);
            setreplyCommentSeq(obj.commentSeq);
            setisReply(true);
            textInputRef.current.focus();
        }
        if (clickID == "DELETE_OTHERS") {
            deletePostOtherCommentService(obj.commentSeq);
        }
    }

    const renderItem = ({ item }) => {
        let self_comment = false;
        if (item.self_comment == "YES") {
            self_comment = true;
        }
        let self_like = false;
        if (item.self_like == "YES") {
            self_like = true;
        }
        return <CommentRow navigation={navigation} currentProfileSeq={__ProfileSeq}
            selfComment={self_comment} selfLike={self_like} data={item} postSeq={postSeq}
            postProfileSeq={postProfileSeq}
            commentRowClick={commentRowClick} />;
    };
    const handleRefresh = () => {
        setprogressLoading(true);
        seterrorMsg("");
        getPostCommentService();
    }
    const onCommentChangeText = (text) => {
        if (text.length != 0) {
            setshowPostButton(true);
        }
        else {
            setshowPostButton(false);
        }
        setcommentTxt(text);
    }
    const commentBoxBlur = (e) => {
        setshowPostButton(false);
    }
    function getPostCommentService(commentDeleted = false) {
        let hashMap = {
            _action_code: "11:GET_POST_COMMENTS",
            post_seq: postSeq,

        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(false);
            let currentUserCommented = "NO";
            data.data.map(obj => {
                if (String(obj.comment_profile_seq) == String(__ProfileSeq)) {
                    currentUserCommented = "YES";
                }
            })
            if (commentDeleted) {
                changeCommentObj({
                    postSeq: postSeq, commentRight: currentUserCommented,
                    postCommentRefresh: Math.random(),
                })
            }
            setcommentsList([...[], ...data.data]);
            seterrorMsg("");
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                setprogressLoading(false);
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {

                setprogressLoading(false);
                setcommentsList([]);
                seterrorMsg(errorMessage);
                changeCommentObj({
                    postSeq: postSeq, commentRight: "NO",
                    postCommentRefresh: Math.random(),
                })
            }
        });
    }
    const submitPost = () => {
        Keyboard.dismiss();
        if (commentTxt.length != 0) {
            if (isReply) {
                submitPostCommentCommentService(commentTxt);
            }
            else {
                submitPostCommentService(commentTxt);
            }

        }
    }
    function submitPostCommentService(cmtTxt) {
        setprogressLoading(true);
        seterrorMsg("");
        setcommentTxt("");
        let encodeSt = encodeHtmlEntitessData(cmtTxt);
        let hashMap = {
            _action_code: "11:SUBMIT_POST_COMMENT",
            post_seq: postSeq,
            comment: encodeSt,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(true);
            seterrorMsg("");
            setcommentTxt("");
            setshowPostButton(false);
            Keyboard.dismiss();
            getPostCommentService();
            // setSnackbarMsg(data.msg);
            // setdisplaySnackbar(true);
            // setsnackBarType("SUCCESS");
            // setrefreshSnackBar(Math.random());
            appData.commentPostSeq = postSeq;
            appData.commentPostCount = data.data.comment_count;
            homepagePostDataBackup.postStatusChangeData.push(
                {
                    post_seq: postSeq,
                    type: "COMMENT",
                    action_code: "SUBMIT_POST_COMMENT",
                    reactionType: "",
                    count: data.data.comment_count,
                    profileSeq: -1
                }
            )
            changeCommentObj({
                postSeq: postSeq, commentRight: "YES",
                postCommentRefresh: Math.random(),
            })

            // appData._homePagePostRefresh = "YES";
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.comment) {
                            setSnackbarMsg(data.data.comment);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            return
                        }
                    }
                }
                setSnackbarMsg(errorMessage);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
            }
        });
    }
    function submitPostCommentCommentService(cmtTxt) {
        setprogressLoading(true);
        let encodeSt = encodeHtmlEntitessData(cmtTxt);
        seterrorMsg("");
        setcommentTxt("");
        let hashMap = {
            _action_code: "11:SUBMIT_COMMENT_COMMENT",
            post_seq: postSeq,
            comment_seq: replyCommentSeq,
            comment: encodeSt,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(true);
            seterrorMsg("");
            setcommentTxt("");
            setshowPostButton(false);
            Keyboard.dismiss();
            setreplyToUser("");
            setisReply(false);
            setreplyCommentSeq(-1);
            appData.commentPostSeq = postSeq;
            appData.commentPostCount = data.data.comment_count;
            // appData._homePagePostRefresh = "YES";
            homepagePostDataBackup.postStatusChangeData.push(
                {
                    post_seq: postSeq,
                    type: "COMMENT",
                    action_code: "SUBMIT_POST_COMMENT",
                    reactionType: "",
                    count: data.data.comment_count,
                    profileSeq: -1
                }
            )
            changeCommentObj({
                postSeq: postSeq, commentRight: "YES",
                postCommentRefresh: Math.random(),
            })
            getPostCommentService();
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {
                        if (data.data.comment) {
                            setSnackbarMsg(data.data.comment);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            return
                        }
                    }
                }
                setSnackbarMsg(errorMessage);
                setdisplaySnackbar(true);
                setsnackBarType("FAILED");
                setrefreshSnackBar(Math.random());
            }
        });
    }
    function deletePostCommentService(commentSeq) {
        setprogressLoading(true);
        let hashMap = {
            _action_code: "11:REMOVE_POST_COMMENT",
            post_seq: postSeq,
            comment_seq: commentSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(true);
            seterrorMsg("");
            setcommentTxt("");
            setshowPostButton(false);
            Keyboard.dismiss();
            setisCommentModified(true)
            getPostCommentService(true);
            homepagePostDataBackup.postStatusChangeData.push(
                {
                    post_seq: postSeq,
                    type: "COMMENT",
                    action_code: "REMOVE_POST_COMMENT",
                    reactionType: "",
                    count: data.data.comment_count,
                    profileSeq: -1
                }
            )

            // appData._homePagePostRefresh = "YES";
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                seterrorMsg(errorMessage);
            }
        });
    }
    const clearReplyBtnPress = () => {
        setreplyToUser("");
        setisReply(false);
        setreplyCommentSeq(-1);
    }
    function deletePostOtherCommentService(commentSeq) {
        setprogressLoading(true);
        let hashMap = {
            _action_code: "11:DELETE_USER_POST_COMMENT",
            post_seq: postSeq,
            comment_seq: commentSeq,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setprogressLoading(true);
            seterrorMsg("");
            setcommentTxt("");
            setshowPostButton(false);
            Keyboard.dismiss();
            setisCommentModified(true)
            getPostCommentService(true);
            homepagePostDataBackup.postStatusChangeData.push(
                {
                    post_seq: postSeq,
                    type: "COMMENT",
                    action_code: "REMOVE_POST_COMMENT",
                    reactionType: "",
                    count: data.data.comment_count,
                    profileSeq: -1
                }
            )
            // appData._homePagePostRefresh = "YES";
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                seterrorMsg(errorMessage);
            }
        });
    }
    function extrabackBtnPress() {
        changeShowCommentCount(true);
        if (navigation.canGoBack()) {
            navigation.goBack(null);
        }
        else {
            navigation.replace("HomeScreen");
        }
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar showBorderBottom={false} showBackBtn={true} title="Comments" navigation={navigation}
                extrabackBtn={true} extrabackBtnPress={extrabackBtnPress} />
            <View style={{ flex: 1, backgroundColor: theme.colors.backgroundColor }}>
                {/* <NotiRowPlaceholder /> */}
                {
                    errorMsg.length != 0 ?
                        <View style={defaultStyle.errorBoxOutside}>
                            <SuccessFailureMsgBox visibleAllTime={true} alertMsg={errorMsg} alertKey={errorMsg} />
                        </View>
                        :
                        <>
                            {
                                progressLoading ?
                                    <NotiRowPlaceholder />
                                    :
                                    <FlatList
                                        contentContainerStyle={{ paddingBottom: 80 }}
                                        data={commentsList}
                                        renderItem={renderItem}
                                        keyExtractor={(item, index) => `${index}`}
                                        refreshControl={
                                            <RefreshControl refreshing={progressLoading} onRefresh={() => handleRefresh()} />
                                        }
                                    />
                            }
                        </>

                }

            </View>
            <KeyboardAvoidingView
                enabled={Platform.OS == 'ios' ? true : true}

                behavior={Platform.OS === 'ios' ? 'position' : 'height'}
                style={{ backgroundColor: theme.colors.backgroundColor }}>
                <View style={style.addCommentBox}>
                    {/* {
                        !isReply ?
                            <ProfileImageComponent
                                source={profileImage}
                                resizeMode="cover"
                                style={style.profileImage}

                            />
                            : null
                    } */}
                    <View style={style.inputBox}>
                        {
                            isReply ?
                                <View style={style.replyBox}>
                                    <EntutoTextView style={style.replyTxt}>
                                        {((replyToUser).length > MaxReplyUserTxtLimit) ?
                                            (((replyToUser).substring(0, MaxReplyUserTxtLimit - 3)) + '...') :
                                            replyToUser}

                                    </EntutoTextView>
                                    <TouchableOpacity style={style.crossBox} onPress={() => clearReplyBtnPress()}>
                                        <MaterialIcons name="close" color={'#FFF'} />
                                    </TouchableOpacity>
                                </View>
                                : null
                        }

                        <TextInput

                            ref={textInputRef}
                            style={[style.input, isReply && { paddingLeft: 8, }]}
                            onChangeText={text => onCommentChangeText(text)}
                            multiline={true}
                            value={commentTxt}
                            placeholder="Add a Comment"
                            placeholderTextColor={theme.colors.inputPlaceholderColor}
                            onBlur={(e) => commentBoxBlur(e)}
                        />
                        <TouchableOpacity onPress={() => submitPost()} style={style.postBtn}>
                            <EntutoTextView style={style.postBtnText}>Send</EntutoTextView>
                        </TouchableOpacity>
                    </View>
                </View>
            </KeyboardAvoidingView>


            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />
        </>
    )
}

export default CommentScreen;

const styles = theme => StyleSheet.create({
    addCommentBox: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        backgroundColor: theme.colors.backgroundColor,
        minHeight: 76,
        paddingVertical: 5,
        flexDirection: 'row',
        width: theme.dimensions.screenWidth,
        alignItems: 'center'
    },
    profileImage: {
        width: 36,
        height: 36,
        borderRadius: 36,
        marginLeft: 16,
    },
    inputBox: {
        alignItems: 'center',
        flexDirection: 'row',
        flex: 1,
        borderColor: theme.colors.primaryColor,
        borderWidth: 1,
        borderRadius: 0,
        marginLeft: 10,
        marginRight: 16,
        marginVertical: 8,
        minHeight: 48,
    },
    input: {
        flex: 1,
        paddingLeft: 20,
        paddingRight: 16,
        color: theme.colors.inputTextColor,


    },
    replyBox: {
        alignItems: 'center',
        backgroundColor: theme.colors.backgroundColor,
        marginStart: 8,
        borderRadius: 10,
        padding: 4,
        flexDirection: 'row',

    },
    replyTxt: {
        fontSize: theme.calculateFontSize(theme.dimensions.commentReplyTextBtn)
    },
    crossBox: {
        height: 16,
        width: 16,
        borderRadius: 16,
        padding: 2,
        backgroundColor: theme.colors.darkGray,
        alignItems: 'center',
        justifyContent: 'center',
        marginStart: 4,
    },
    postBtn: {
        backgroundColor: theme.colors.primaryColor,
        margin: 4,
        height: 42,
        justifyContent: 'center',
        alignItems: 'center',

    },
    postBtnText: {
        color: "#FFFFFF",
        fontSize: theme.calculateFontSize(theme.dimensions.commentPostBtnText),
        paddingHorizontal: 20,
        fontWeight: 'bold',
    },
})
