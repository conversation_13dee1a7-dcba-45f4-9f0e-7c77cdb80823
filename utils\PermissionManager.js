import { Alert, PermissionsAndroid, Platform } from "react-native";
import ErrorMessages from "../constants/ErrorMessages";
import { Linking } from "react-native";
// import Geolocation from 'react-native-geolocation-service';

export const requestStoragePermission = async () => {
    if (Platform.OS == "android") {
        const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
            {
                title: ErrorMessages.permissionStorageHeaderMsg,
                message: ErrorMessages.permissionStorageMsg,
                buttonNegative: 'Cancel',
                buttonPositive: 'OK',
            },
        );
        if (granted == PermissionsAndroid.RESULTS.GRANTED) {
            return true;
        }
        else if (granted == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            if (Platform.Version < 29) {
                openSettingForStorage();
                return false;
            }
            else {
                return true;
            }
        }
        else {
            return false;
        }
    }
    else if (Platform.OS == "ios") {
        return true;
    }
}
export function openSettingForStorage() {
    Alert.alert(ErrorMessages.permissionStorageHeaderMsg, ErrorMessages.permissionStorageMsg, [
        {
            text: 'Permission',
            onPress: () => Linking.openSettings(),
        },
        {
            text: 'Cancel',
            style: 'cancel',
        },
    ]);
    return false;
}
export const requestLocationPermission = async () => {
    if (Platform.OS == "android") {
        const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            {
                title: ErrorMessages.permissionLocationHeaderMsg,
                message: ErrorMessages.permissionLocationMsg,
                buttonNegative: 'Cancel',
                buttonPositive: 'OK',
            },
        );
        if (granted == PermissionsAndroid.RESULTS.GRANTED) {
            return true;
        }
        else {
            Alert.alert(ErrorMessages.permissionLocationHeaderMsg, ErrorMessages.permissionLocationMsg, [
                {
                    text: "Permission",
                    onPress: () => openSettings(),
                },
                {
                    text: "Cancel",
                    // onPress: () => console.log('Cancel Pressed'),
                    style: 'cancel',
                },
            ]);
            return false;
        }
    }
    // if(Platform.OS=='ios'){
    //     const status = await Geolocation.requestAuthorization('whenInUse');
    //     if (status === 'granted') {
    //         console.log('Location permission granted');
    //         return true;
    //       } else if (status === 'denied') {
    //         console.log('Location permission denied');
    //         openSettings();
    //         return false;
    //       } else if (status === 'restricted') {
    //         console.log('Location permission restricted');
    //         openSettings();
    //         return false;
    //       }

    // }
}
const openSettings = () => {
    Linking.openSettings();
};