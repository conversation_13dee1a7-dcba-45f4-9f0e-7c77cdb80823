import React, { useContext } from 'react'
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { hasImageUrlExist } from '../../utils/Utils';
import EntutoTextView from '../common/EntutoTextView';
import ProgressiveImage from '../common/ProgressiveImage';
import ProfileImagePlaceholder from '../../assets/Images/full_user_image_place_holder.png';
import { CurrencySymbol, UserHandlePrefix } from '../../utils/Appconfig';
import { AppStateContext } from '../..';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSThemedStyles from '../../theme/useSThemedStyles';

const MySubsPostRow = ({ navigation, data }) => {
    const { fullUserDetails } = useContext(AppStateContext);
    const { defaultStyle } = useDefaultStyle();
    const style = useSThemedStyles(styles);
    const __ProfileSeq = fullUserDetails.hasOwnProperty("_profile_seq") ? fullUserDetails._profile_seq : -1;
    const goToProfile = (profileSeq) => {
        if (__ProfileSeq == profileSeq) {
            navigation.navigate("HomeScreen", { screen: 'ProfileFeed' });
        }
        else {
            navigation.navigate('OthersProfileScreen', {
                profileSeq: profileSeq,
            });
        }
    }

    return (
        <View style={{ ...defaultStyle.ListCardStyle, ...style.cardView }}>
            <View>
                <TouchableOpacity onPress={() => goToProfile(data.subscribed_by_seq)}>
                    <ProgressiveImage
                        style={style.subsPostBox}
                        source={hasImageUrlExist(data.profile_picture) ? { uri: data.profile_picture } : null}
                        defaultImageSource={ProfileImagePlaceholder}
                        resizeMode={'cover'}
                    />
                </TouchableOpacity>
            </View>
            <View style={{ marginStart: 15 }}>{/*style={{marginStart: 15}}  */}
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <TouchableOpacity onPress={() => goToProfile(data.subscribed_by_seq)}>
                        <EntutoTextView style={style.profileName}>{data.display_name}</EntutoTextView>
                    </TouchableOpacity>
                    {
                        data.is_verified == "YES" ?
                            <Image
                                style={style.verifiedIcon}
                                source={require('../../assets/Images/icon/verifiedicon.png')}
                                resizeMode={'contain'}
                            />
                            : null
                    }
                </View>
                <View>
                    <EntutoTextView style={style.profileHandle}>{UserHandlePrefix}{data.user_handle}</EntutoTextView>
                </View>
            </View>
            <View style={style.postRightBox}>
                <EntutoTextView style={style.priceVal}>{CurrencySymbol}{data.paid_amount}</EntutoTextView>
                <EntutoTextView style={style.dateVal}>{data.subscribed_on}</EntutoTextView>
            </View>
        </View>
    )
}

export default MySubsPostRow;

const styles = theme => StyleSheet.create({
    cardView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    subsPostBox: {
        // width: 68,
        // height: 54,
        // borderRadius: 5,

        width: 50,
        height: 50,
        borderRadius: 50,
    },
    subsPostProfile: {
        width: 20,
        height: 20,
        borderRadius: 20,
    },
    profileName: {
        color: theme.colors.primaryTextColor,
        fontWeight: '700',
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileNameText),
        // marginStart: 5,
    },
    profileHandle: {
        color: theme.colors.primaryTextColor,
        fontWeight: '400',
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileIDText),
        marginTop: 9,
    },
    postRightBox: {
        marginLeft: 'auto',
        alignItems: 'flex-end',
    },
    priceVal: {
        color: theme.colors.primaryColor,
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfilePriceText),
        fontWeight: '700',
    },
    dateVal: {
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.rowProfileDateText),
        fontWeight: '400',
    },
    verifiedIcon: {
        width: 16,
        height: 15,
        marginLeft: theme.dimensions.veritextLeftmargin,
        // position: 'absolute',
        // right: -6,
        // top: 20,
    },

})
