PODS:
  - AppAuth (1.7.5):
    - AppAuth/Core (= 1.7.5)
    - AppAuth/ExternalUserAgent (= 1.7.5)
  - AppAuth/Core (1.7.5)
  - AppAuth/ExternalUserAgent (1.7.5):
    - AppAuth/Core
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.66.5)
  - FBReactNativeSpec (0.66.5):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.66.5)
    - RCTTypeSafety (= 0.66.5)
    - React-Core (= 0.66.5)
    - React-jsi (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - Firebase (9.6.0):
    - Firebase/Core (= 9.6.0)
  - Firebase/Core (9.6.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 9.6.0)
  - Firebase/CoreOnly (9.6.0):
    - FirebaseCore (= 9.6.0)
  - Firebase/DynamicLinks (9.6.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 9.6.0)
  - Firebase/Messaging (9.6.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 9.6.0)
  - FirebaseAnalytics (9.6.0):
    - FirebaseAnalytics/AdIdSupport (= 9.6.0)
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (9.6.0):
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleAppMeasurement (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (9.6.0):
    - FirebaseCoreDiagnostics (~> 9.0)
    - FirebaseCoreInternal (~> 9.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (9.6.0):
    - GoogleDataTransport (< 10.0.0, >= 9.1.4)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCoreExtension (9.6.0):
    - FirebaseCore (~> 9.0)
  - FirebaseCoreInternal (9.6.0):
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
  - FirebaseDynamicLinks (9.6.0):
    - FirebaseCore (~> 9.0)
  - FirebaseInstallations (9.6.0):
    - FirebaseCore (~> 9.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (9.6.0):
    - FirebaseCore (~> 9.0)
    - FirebaseInstallations (~> 9.0)
    - GoogleDataTransport (< 10.0.0, >= 9.1.4)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Reachability (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (9.6.0):
    - GoogleAppMeasurement/AdIdSupport (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (9.6.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (9.6.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleSignIn (6.1.0):
    - AppAuth (~> 1.4)
    - GTMAppAuth (~> 1.0)
    - GTMSessionFetcher/Core (~> 1.1)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (1.3.1):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 3.0, >= 1.5)
  - GTMSessionFetcher/Core (1.7.2)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - lottie-ios (3.4.4)
  - lottie-react-native (5.1.6):
    - lottie-ios (~> 3.4.0)
    - React-Core
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - NextLevelSessionExporter (0.4.6)
  - Permission-Camera (3.6.1):
    - RNPermissions
  - Permission-PhotoLibrary (3.6.1):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - razorpay-pod (1.3.13)
  - RCT-Folly (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.06.28.00-v2)
  - RCT-Folly/Default (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.66.5)
  - RCTTypeSafety (0.66.5):
    - FBLazyVector (= 0.66.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.66.5)
    - React-Core (= 0.66.5)
  - React (0.66.5):
    - React-Core (= 0.66.5)
    - React-Core/DevSupport (= 0.66.5)
    - React-Core/RCTWebSocket (= 0.66.5)
    - React-RCTActionSheet (= 0.66.5)
    - React-RCTAnimation (= 0.66.5)
    - React-RCTBlob (= 0.66.5)
    - React-RCTImage (= 0.66.5)
    - React-RCTLinking (= 0.66.5)
    - React-RCTNetwork (= 0.66.5)
    - React-RCTSettings (= 0.66.5)
    - React-RCTText (= 0.66.5)
    - React-RCTVibration (= 0.66.5)
  - React-callinvoker (0.66.5)
  - React-Core (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.66.5)
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/CoreModulesHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/Default (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/DevSupport (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.66.5)
    - React-Core/RCTWebSocket (= 0.66.5)
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-jsinspector (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTBlobHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTImageHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTTextHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-Core/RCTWebSocket (0.66.5):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.66.5)
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsiexecutor (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - Yoga
  - React-CoreModules (0.66.5):
    - FBReactNativeSpec (= 0.66.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.5)
    - React-Core/CoreModulesHeaders (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-RCTImage (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - React-cxxreact (0.66.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-jsinspector (= 0.66.5)
    - React-logger (= 0.66.5)
    - React-perflogger (= 0.66.5)
    - React-runtimeexecutor (= 0.66.5)
  - React-jsi (0.66.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-jsi/Default (= 0.66.5)
  - React-jsi/Default (0.66.5):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
  - React-jsiexecutor (0.66.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-perflogger (= 0.66.5)
  - React-jsinspector (0.66.5)
  - React-logger (0.66.5):
    - glog
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-cameraroll (7.8.3):
    - React-Core
  - react-native-compressor (1.6.1):
    - NextLevelSessionExporter
    - React-Core
  - react-native-image-picker (4.10.3):
    - React-Core
  - react-native-netinfo (7.1.12):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-razorpay (2.3.0):
    - razorpay-pod
    - React-Core
  - react-native-safe-area-context (3.4.1):
    - React-Core
  - react-native-screenguard (0.2.2):
    - React-Core
  - react-native-slider (4.4.0):
    - React-Core
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-webview (11.26.0):
    - React-Core
  - React-perflogger (0.66.5)
  - React-RCTActionSheet (0.66.5):
    - React-Core/RCTActionSheetHeaders (= 0.66.5)
  - React-RCTAnimation (0.66.5):
    - FBReactNativeSpec (= 0.66.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.5)
    - React-Core/RCTAnimationHeaders (= 0.66.5)
    - React-jsi (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - React-RCTBlob (0.66.5):
    - FBReactNativeSpec (= 0.66.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/RCTBlobHeaders (= 0.66.5)
    - React-Core/RCTWebSocket (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-RCTNetwork (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - React-RCTImage (0.66.5):
    - FBReactNativeSpec (= 0.66.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.5)
    - React-Core/RCTImageHeaders (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-RCTNetwork (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - React-RCTLinking (0.66.5):
    - FBReactNativeSpec (= 0.66.5)
    - React-Core/RCTLinkingHeaders (= 0.66.5)
    - React-jsi (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - React-RCTNetwork (0.66.5):
    - FBReactNativeSpec (= 0.66.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.5)
    - React-Core/RCTNetworkHeaders (= 0.66.5)
    - React-jsi (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - React-RCTSettings (0.66.5):
    - FBReactNativeSpec (= 0.66.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.5)
    - React-Core/RCTSettingsHeaders (= 0.66.5)
    - React-jsi (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - React-RCTText (0.66.5):
    - React-Core/RCTTextHeaders (= 0.66.5)
  - React-RCTVibration (0.66.5):
    - FBReactNativeSpec (= 0.66.5)
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/RCTVibrationHeaders (= 0.66.5)
    - React-jsi (= 0.66.5)
    - ReactCommon/turbomodule/core (= 0.66.5)
  - React-runtimeexecutor (0.66.5):
    - React-jsi (= 0.66.5)
  - ReactCommon/turbomodule/core (0.66.5):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.66.5)
    - React-Core (= 0.66.5)
    - React-cxxreact (= 0.66.5)
    - React-jsi (= 0.66.5)
    - React-logger (= 0.66.5)
    - React-perflogger (= 0.66.5)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNAppleAuthentication (2.2.2):
    - React-Core
  - RNCAsyncStorage (1.17.11):
    - React-Core
  - RNCClipboard (1.11.1):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React-Core
  - RNDateTimePicker (4.0.1):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (15.7.0):
    - Firebase/CoreOnly (= 9.6.0)
    - React-Core
  - RNFBDynamicLinks (15.7.0):
    - Firebase/DynamicLinks (= 9.6.0)
    - GoogleUtilities/AppDelegateSwizzler
    - React-Core
    - RNFBApp
  - RNFBMessaging (15.7.0):
    - Firebase/Messaging (= 9.6.0)
    - FirebaseCoreExtension (= 9.6.0)
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.9.0):
    - React-Core
  - RNGoogleSignin (7.2.2):
    - GoogleSignIn (~> 6.1.0)
    - React-Core
  - RNImageCropPicker (0.41.0):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.41.0)
    - TOCropViewController (~> 2.7.3)
  - RNImageCropPicker/QBImagePickerController (0.41.0):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.3)
  - RNPermissions (3.6.1):
    - React-Core
  - RNReanimated (2.14.1):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.18.2):
    - React-Core
    - React-RCTImage
  - RNShare (7.9.1):
    - React-Core
  - RNSVG (12.5.0):
    - React-Core
  - RNVectorIcons (9.2.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - FirebaseCore
  - FirebaseCoreInternal
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - Permission-PhotoLibrary (from `../node_modules/react-native-permissions/ios/PhotoLibrary`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-compressor (from `../node_modules/react-native-compressor`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-razorpay (from `../node_modules/react-native-razorpay`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-screenguard (from `../node_modules/react-native-screenguard`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBDynamicLinks (from `../node_modules/@react-native-firebase/dynamic-links`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - libwebp
    - lottie-ios
    - nanopb
    - NextLevelSessionExporter
    - PromisesObjC
    - razorpay-pod
    - SDWebImage
    - SDWebImageWebPCoder
    - TOCropViewController

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  Permission-PhotoLibrary:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibrary"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-compressor:
    :path: "../node_modules/react-native-compressor"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-razorpay:
    :path: "../node_modules/react-native-razorpay"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-screenguard:
    :path: "../node_modules/react-native-screenguard"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBDynamicLinks:
    :path: "../node_modules/@react-native-firebase/dynamic-links"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppAuth: 501c04eda8a8d11f179dbe8637b7a91bb7e5d2fa
  boost: a7c83b31436843459a1961bfd74b96033dc77234
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  DoubleConversion: 831926d9b8bf8166fd87886c4abab286c2422662
  FBLazyVector: a926a9aaa3596b181972abf0f47eff3dee796222
  FBReactNativeSpec: f1141d5407f4a27c397bca5db03cc03919357b0d
  Firebase: 5ae8b7cf8efce559a653aef0ad95bab3f427c351
  FirebaseAnalytics: 89ad762c6c3852a685794174757e2c60a36b6a82
  FirebaseCore: 2082fffcd855f95f883c0a1641133eb9bbe76d40
  FirebaseCoreDiagnostics: 99a495094b10a57eeb3ae8efa1665700ad0bdaa6
  FirebaseCoreExtension: e83465d1236b166d1d445bbf0e82b65acb30b73b
  FirebaseCoreInternal: bca76517fe1ed381e989f5e7d8abb0da8d85bed3
  FirebaseDynamicLinks: 894ee3b4e56a77abee067d371c9a23e7b5a3c686
  FirebaseInstallations: 0a115432c4e223c5ab20b0dbbe4cbefa793a0e8e
  FirebaseMessaging: a4d7910e4af663c9cbfc1071c5bef34651690949
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 5337263514dd6f09803962437687240c5dc39aa4
  GoogleAppMeasurement: 6de2b1a69e4326eb82ee05d138f6a5cb7311bcb1
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleSignIn: c90b5bec45e780f54c6a8e1e3c182a86e3dda69d
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: 0ff230db599948a9ad7470ca667337803b3fc4dd
  GTMSessionFetcher: 5595ec75acf5be50814f81e9189490412bad82ba
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  lottie-ios: 8f97d3271e155c2d688875c29cd3c74908aef5f8
  lottie-react-native: 8f9d4be452e23f6e5ca0fdc11669dc99ab52be81
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  NextLevelSessionExporter: 4d8aa5e617f1c709724f2453efe5d4628480f65a
  Permission-Camera: bf6791b17c7f614b6826019fcfdcc286d3a107f6
  Permission-PhotoLibrary: 5b34ca67279f7201ae109cef36f9806a6596002d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  razorpay-pod: def525ccff21db4382808c99503d863ad0640243
  RCT-Folly: a21c126816d8025b547704b777a2ba552f3d9fa9
  RCTRequired: 405e24508a0feed1771d48caebb85c581db53122
  RCTTypeSafety: 0654998ea6afd3dccecbf6bb379d7c10d1361a72
  React: cce3ac45191e66a78c79234582cbfe322e4dfd00
  React-callinvoker: 613b19264ce63cab0a2bbb6546caa24f2ad0a679
  React-Core: fe529d7c1d74b3eb9505fdfc2f4b10f2f2984a85
  React-CoreModules: 71f5d032922a043ab6f9023acda41371a774bf5c
  React-cxxreact: 808c0d39b270860af712848082009d623180999c
  React-jsi: 01b246df3667ad921a33adeb0ce199772afe9e2b
  React-jsiexecutor: 50a73168582868421112609d2fb155e607e34ec8
  React-jsinspector: 953260b8580780a6e81f2a6d319a8d42fd5028d8
  React-logger: fa4ff1e9c7e115648f7c5dafb7c20822ab4f7a7e
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-cameraroll: afb503d3a24148da1ee06ff71451dcf2f2f02081
  react-native-compressor: ebcf8bc36f8e44581a8f831583caee8a27e55a01
  react-native-image-picker: 60f4246eb5bb7187fc15638a8c1f13abd3820695
  react-native-netinfo: c023b7c36ff34e3b607d1446002b5b39089a860b
  react-native-pager-view: da490aa1f902c9a5aeecf0909cc975ad0e92e53e
  react-native-razorpay: 5b7c354868a1016e83b5a843ea0b743acd643c96
  react-native-safe-area-context: 9e40fb181dac02619414ba1294d6c2a807056ab9
  react-native-screenguard: edef681c812cc67a2d3a32242816490640682713
  react-native-slider: d2938a12c4e439a227c70eec65d119136eb4aeb5
  react-native-video: c26780b224543c62d5e1b2a7244a5cd1b50e8253
  react-native-webview: 994b9f8fbb504d6314dc40d83f94f27c6831b3bf
  React-perflogger: 169fb34f60c5fd793b370002ee9c916eba9bc4ae
  React-RCTActionSheet: 2355539e02ad5cd4b1328682ab046487e1e1e920
  React-RCTAnimation: 150644a38c24d80f1f761859c10c727412303f57
  React-RCTBlob: 66042a0ab4206f112ed453130f2cb8802dd7cd82
  React-RCTImage: 3b954d8398ec4bed26cec10e10d311cb3533818b
  React-RCTLinking: 331d9b8a0702c751c7843ddc65b64297c264adc2
  React-RCTNetwork: 96e10dad824ce112087445199ea734b79791ad14
  React-RCTSettings: 41feb3f5fb3319846ad0ba9e8d339e54b5774b67
  React-RCTText: 254741e11c41516759e93ab0b8c38b90f8998f61
  React-RCTVibration: 96dbefca7504f3e52ff47cd0ad0826d20e3a789f
  React-runtimeexecutor: 09041c28ce04143a113eac2d357a6b06bd64b607
  ReactCommon: 8a7a138ae43c04bb8dd760935589f326ca810484
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNAppleAuthentication: 0571c08da8c327ae2afc0261b48b4a515b0286a6
  RNCAsyncStorage: 8616bd5a58af409453ea4e1b246521bb76578d60
  RNCClipboard: 2834e1c4af68697089cdd455ee4a4cdd198fa7dd
  RNCMaskedView: 38c3d9675c1d352e4becc13cbf321c7b1176267c
  RNDateTimePicker: a654a0ebfa3dd78bbe5389421aa2d3d5b2744e47
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFBApp: ad6ad2dd37ccf10e737ec8688208761026331d64
  RNFBDynamicLinks: ceddbfe29b52c4c2a1705571c5bf11e6d42130d6
  RNFBMessaging: f75d2d54bbf6ddc29304e2f45152bc397a8b5b3f
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 071d7a9ad81e8b83fe7663b303d132406a7d8f39
  RNGoogleSignin: d7f8368b82d94f1dec964785d496387ca7f19092
  RNImageCropPicker: 13eab07a785c7a8f8047a1146f7e59d1911c7bb8
  RNPermissions: dcdb7b99796bbeda6975a6e79ad519c41b251b1c
  RNReanimated: 9e1b5e122fa1fc1c8a19e8f2982119859959f3a3
  RNScreens: 34cc502acf1b916c582c60003dc3089fa01dc66d
  RNShare: a5dc3b9c53ddc73e155b8cd9a94c70c91913c43c
  RNSVG: 6adc5c52d2488a476248413064b7f2832e639057
  RNVectorIcons: fcc2f6cb32f5735b586e66d14103a74ce6ad61f8
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: b316a990bb6d115afa1b436b5626ac7c61717d17

PODFILE CHECKSUM: ca1ec8fb7f6855371c92b79695cae09d270fc20d

COCOAPODS: 1.14.3
