#import "AppDelegate.h"

#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTLinkingManager.h>
#import <Firebase.h>
#import <RNFBDynamicLinksAppDelegateInterceptor.h>
#import <AVFoundation/AVFoundation.h>

#import <FBSDKCoreKit.h>

#ifdef FB_SONARKIT_ENABLED
#import <FlipperKit/FlipperClient.h>
#import <FlipperKitLayoutPlugin/FlipperKitLayoutPlugin.h>
#import <FlipperKitUserDefaultsPlugin/FKUserDefaultsPlugin.h>
#import <FlipperKitNetworkPlugin/FlipperKitNetworkPlugin.h>
#import <SKIOSNetworkPlugin/SKIOSNetworkAdapter.h>
#import <FlipperKitReactPlugin/FlipperKitReactPlugin.h>

static void InitializeFlipper(UIApplication *application) {
  FlipperClient *client = [FlipperClient sharedClient];
  SKDescriptorMapper *layoutDescriptorMapper = [[SKDescriptorMapper alloc] initWithDefaults];
  [client addPlugin:[[FlipperKitLayoutPlugin alloc] initWithRootNode:application withDescriptorMapper:layoutDescriptorMapper]];
  [client addPlugin:[[FKUserDefaultsPlugin alloc] initWithSuiteName:nil]];
  [client addPlugin:[FlipperKitReactPlugin new]];
  [client addPlugin:[[FlipperKitNetworkPlugin alloc] initWithNetworkAdapter:[SKIOSNetworkAdapter new]]];
  [client start];
}
#endif

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  [RNFBDynamicLinksAppDelegateInterceptor sharedInstance];
  [FIRApp configure];
#ifdef FB_SONARKIT_ENABLED
  InitializeFlipper(application);
#endif
  RCTBridge *bridge = [[RCTBridge alloc] initWithDelegate:self launchOptions:launchOptions];
  RCTRootView *rootView = [[RCTRootView alloc] initWithBridge:bridge
                                                   moduleName:@"SotrueMobileProject"
                                            initialProperties:nil];
  if (@available(iOS 11.0, *)) {
      [[NSNotificationCenter defaultCenter] addObserver:self
                             selector:@selector(handleScreenCaptureChange)
                name:UIScreenCapturedDidChangeNotification object:nil];
   }
  if (@available(iOS 11.0, *)) {
      [[NSNotificationCenter defaultCenter] addObserver:self
                             selector:@selector(handleScreenCaptureChange)
                name:UIApplicationDidBecomeActiveNotification object:nil];
    }
  if (@available(iOS 13.0, *)) {
      rootView.backgroundColor = [UIColor systemBackgroundColor];
  } else {
      rootView.backgroundColor = [UIColor whiteColor];
  }

  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  UIViewController *rootViewController = [UIViewController new];
  rootViewController.view = rootView;
  self.window.rootViewController = rootViewController;
  [self.window makeKeyAndVisible];
  [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:nil];
  [[FBSDKApplicationDelegate sharedInstance] application:application didFinishLaunchingWithOptions:launchOptions];
  return YES;
}
-(void)handleScreenCaptureChange
{
  if (@available(iOS 11.0, *)) {
    BOOL isCaptured = [[UIScreen mainScreen] isCaptured];
        if (isCaptured) {
//          if([self.window viewWithTag:1234] ==nil){
//            UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
//            UIVisualEffectView *blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
//            [blurEffectView setFrame:self.window.bounds];
//            blurEffectView.tag = 1234;
//            blurEffectView.alpha = 0;
//            [self.window addSubview:blurEffectView];
//            [self.window bringSubviewToFront:blurEffectView];
//            [UIView animateWithDuration:0.5 animations:^{
//              blurEffectView.alpha = 1;
//            }];
//          }
//         
        }
        else {
//          if([self.window viewWithTag:1234] !=nil){
//            UIVisualEffectView *blurEffectView = [self.window viewWithTag:1234];
//            [UIView animateWithDuration:0.5 animations:^{
//              blurEffectView.alpha = 0;
//            } completion:^(BOOL finished) {
//              [blurEffectView removeFromSuperview];
//            }];
//          }
        }
      }
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index" fallbackResource:nil];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}
// Add this above `@end`:
- (BOOL)application:(UIApplication *)application
   openURL:(NSURL *)url
   options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options
{
  return [RCTLinkingManager application:application openURL:url options:options];
}
- (void)applicationWillResignActive:(UIApplication *)application {
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
    UIVisualEffectView *blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    [blurEffectView setFrame:self.window.bounds];
    blurEffectView.tag = 12345;
    blurEffectView.alpha = 0;
//    [self.window addSubview:blurEffectView];
//    [self.window bringSubviewToFront:blurEffectView];
//    [UIView animateWithDuration:0.5 animations:^{
//        blurEffectView.alpha = 1;
//    }];
    // fill screen with our own colour
    UIView *colourView = [[UIView alloc]initWithFrame:self.window.frame];
    colourView.backgroundColor = [UIColor whiteColor];
    colourView.tag = 56789;
    colourView.alpha = 0;
//    [self.window addSubview:colourView];
//    [self.window bringSubviewToFront:colourView];

  // fade in the view
//  [UIView animateWithDuration:0.5 animations:^{
//    colourView.alpha = 1;
//  }];
  
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    UIVisualEffectView *blurEffectView = [self.window viewWithTag:12345];
    [UIView animateWithDuration:0.5 animations:^{
      blurEffectView.alpha = 0;
    } completion:^(BOOL finished) {
        [blurEffectView removeFromSuperview];
    }];
  
  // grab a reference to our coloured view
  UIView *colourView = [self.window viewWithTag:56789];
  // fade away colour view from main view
  [UIView animateWithDuration:0.5 animations:^{
  colourView.alpha = 0;
  } completion:^(BOOL finished) {
  // remove when finished fading
  [colourView removeFromSuperview];
  }];
  
}

// Add this above `@end`:
- (BOOL)application:(UIApplication *)application continueUserActivity:(nonnull NSUserActivity *)userActivity
 restorationHandler:(nonnull void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler
{
 return [RCTLinkingManager application:application
                  continueUserActivity:userActivity
                    restorationHandler:restorationHandler];
}

@end
