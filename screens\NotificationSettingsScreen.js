import React, { useState } from 'react'
import { <PERSON><PERSON>, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import CustomSnackbar from '../components/common/CustomSnackbar';
import CustomStatusBar from '../components/common/CustomStatusBar';
import EntutoSwitch from '../components/common/EntutoSwitch';
import EntutoTextView from '../components/common/EntutoTextView';
import HeadingTxt from '../components/common/HeadingTxt';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import Colors from '../constants/Colors';
import ErrorMessages from '../constants/ErrorMessages';
import { _RedirectionErrorList } from '../utils/Appconfig';
import { RedirectionUrlFunction } from '../utils/RedirectionUrl';
import ServerConnector from '../utils/ServerConnector';
import useDefaultStyle from '../theme/useDefaultStyle';
import useSTheme from '../theme/useSTheme';
import useSThemedStyles from '../theme/useSThemedStyles';

const NotificationSettingsScreen = ({ route, navigation }) => {
    const [emailNoti, setemailNoti] = useState(false);
    const { defaultStyle } = useDefaultStyle();
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [snackBarType, setsnackBarType] = useState("FAILED");
    const theme = useSTheme();
    const style = useSThemedStyles(styles);

    const [disableUpdateBtn, setdisableUpdateBtn] = useState(true);

    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(true);

    React.useEffect(() => {
        getNotificationSettingService();
    }, []);

    function getNotificationSettingService() {
        let hashMap = {
            _action_code: "11:GET_ALERT_SETTINGS",
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            let email_alert = false;
            if (data.data[0].email_alert === "YES") {
                email_alert = true;
            }
            setemailNoti(email_alert);

            setShowLoading(false)
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                seterrorMsg(errorMessage);
            }
        });
    }

    const emailNotiChange = () => {
        setemailNoti(!emailNoti);
        setdisableUpdateBtn(false);
    }
    React.useEffect(
        () =>
            navigation.addListener('beforeRemove', (e) => {
                if (disableUpdateBtn) {
                    // If we don't have unsaved changes, then we don't need to do anything
                    return;
                }

                // Prevent default behavior of leaving the screen
                e.preventDefault();

                // Prompt the user before leaving the screen
                Alert.alert(
                    ErrorMessages.discardChangesTitle,
                    ErrorMessages.discardChangesMsg,
                    [
                        { text: "Don't leave", style: 'cancel', onPress: () => { } },
                        {
                            text: 'Discard',
                            style: 'destructive',
                            // If the user confirmed, then we dispatch the action we blocked earlier
                            // This will continue the action that had triggered the removal of the screen
                            onPress: () => navigation.dispatch(e.data.action),
                        },
                    ]
                );
            }),
        [navigation, disableUpdateBtn]
    );
    const updateBtnPress = () => {
        setShowLoading(true);
        updateUserProfilePrivacy();
    }
    function updateUserProfilePrivacy() {

        let email_alert = "NO";
        if (emailNoti) {
            email_alert = "YES"
        }

        let hashMap = {
            _action_code: "11:UPDATE_ALERT_SETTINGS",
            email_alert: email_alert,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            // getUserProfileService();
            setSnackbarMsg(data.msg);
            setdisplaySnackbar(true);
            setsnackBarType("SUCCESS");
            setrefreshSnackBar(Math.random());
            setdisableUpdateBtn(true)
            setShowLoading(false);

        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setShowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.email_alert) {
                            setSnackbarMsg(data.data.email_alert);
                            setdisplaySnackbar(true);
                            setsnackBarType("FAILED");
                            setrefreshSnackBar(Math.random());
                            fieldErrorShown = true;
                            return;
                        }
                    }
                }
                if (!fieldErrorShown) {
                    setSnackbarMsg(errorMessage);
                    setdisplaySnackbar(true);
                    setsnackBarType("FAILED");
                    setrefreshSnackBar(Math.random());
                }
            }
        });
    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar title="Notification" showBackBtn={true} showBorderBottom={false} navigation={navigation}
                showTopButton={true}
                buttonComponent={<TouchableOpacity
                    onPress={() => updateBtnPress()}
                    disabled={disableUpdateBtn}
                ><EntutoTextView style={{ ...defaultStyle.postBtn, opacity: disableUpdateBtn ? 0.4 : 1 }}>UPDATE</EntutoTextView></TouchableOpacity>}
            />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside}>
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                    // </View>
                    : null
            }
            <ScrollView
                style={{ backgroundColor: theme.colors.backgroundColor }}>
                <View style={defaultStyle.container}>

                    {/* <View style={{ marginTop: 15, marginBottom: 5, }}>
                        <HeadingTxt>Profile Related</HeadingTxt>
                    </View> */}
                    <View style={style.boxWithSwitchBox}>
                        <View style={{ flexDirection: 'row' }}>
                            <EntutoTextView style={style.boxWithSwitchTxt}>Email Notification</EntutoTextView>
                            <EntutoSwitch value={emailNoti} onValueChange={() => emailNotiChange()} />
                        </View>
                        <View style={defaultStyle.listUnderLineView}>
                            <EntutoTextView style={defaultStyle.listUnderLineTxt}>
                                Get emails for important notifications like earning settlement, new subscribers, report details and more
                            </EntutoTextView>
                        </View>
                    </View>
                    {/* <View style={style.boxWithSwitchBoxDivider} /> */}
                </View>

            </ScrollView>
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />
        </>
    )
}

export default NotificationSettingsScreen;

const styles = theme => StyleSheet.create({
    boxWithSwitchBox: {
        flexDirection: 'column',
        marginVertical: 15,

    },
    boxWithSwitchTxt: {
        flex: 1,
        color: theme.colors.primaryTextColor,
        fontSize: theme.calculateFontSize(theme.dimensions.boxWithSwitchTxt),
        fontWeight: '600'
    },
    boxWithSwitchBoxDivider: {
        flex: 1,
        borderWidth: 0.5,
        borderColor: theme.colors.primaryTextColor,
        opacity: 0.2
    }
})
