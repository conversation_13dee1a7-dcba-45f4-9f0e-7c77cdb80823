import React, { useState } from 'react'
import { ActivityIndicator, StyleSheet, View } from 'react-native'
import HomeTopNavigationBar from '../components/HomeTopNavigationBar'
import CustomStatusBar from '../components/common/CustomStatusBar';
import { WebView } from 'react-native-webview';
import Dimensions from '../constants/Dimensions';

const TermsAndUseScreen = ({ navigation }) => {
    const [showLoading, setshowLoading] = useState(true)
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <HomeTopNavigationBar showBackBtn={true} showBorderBottom={false} title="Terms and use" navigation={navigation} />

            <View style={{ minHeight: Dimensions.screenHeight - 70 }}>
                {
                    showLoading ?
                        <ActivityIndicator size={'large'} />
                        : null
                }
                <WebView source={{ uri: 'https://www.sotrue.co.in/terms_and_use_app.html' }}
                    automaticallyAdjustContentInsets={false}
                    androidLayerType="software"
                    onLoadEnd={() => {
                        setshowLoading(false);
                    }} />

            </View>
        </>
    )
}

export default TermsAndUseScreen

const styles = StyleSheet.create({})
