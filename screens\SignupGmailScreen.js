import React, { useState, useCallback, useEffect, useContext } from 'react'
import { StyleSheet, View, ScrollView, TouchableOpacity, Linking, Image, Modal, Alert } from 'react-native'
import Colors from '../constants/Colors';
import { Checkbox } from 'react-native-paper';
import EntutoTextView from '../components/common/EntutoTextView';
import EntutoEditText from '../components/common/EntutoEditText';
import ServerConnector from '../utils/ServerConnector';
import CustomProgressDialog from '../components/common/CustomProgressDialog';
import SuccessFailureMsgBox from '../components/common/SuccessFailureMsgBox';
import ErrorMessages from '../constants/ErrorMessages';
import { _getFirstTimeUser, _setFirstTimeUser } from '../utils/AuthLogin';
import { TERMS_AND_COND_URL } from '../utils/Appconfig';
import { AppStateContext } from '..';
import { CommonActions } from '@react-navigation/native';
import CustomStatusBar from '../components/common/CustomStatusBar';
import LoginSignUpLinearGrad from '../components/common/LoginSignUpLinearGrad';
import useDefaultStyle from '../theme/useDefaultStyle';

const SignupGmailScreen = ({ route, navigation }) => {
    const [emailId, setemailId] = useState("");
    const [emailIdErr, setemailIdErr] = useState("");
    const [password, setpassword] = useState("");
    const [passwordErr, setpasswordErr] = useState("");
    const [fullName, setfullName] = useState("");
    const [fullNameErr, setfullNameErr] = useState("");
    const [referralCode, setreferralCode] = useState("");
    const [referralCodeErr, setreferralCodeErr] = useState("");
    const [checked, setChecked] = useState(false);
    const [errorMsg, seterrorMsg] = useState("");
    const [showLoading, setShowLoading] = useState(false);
    const [refreshKey, setrefreshKey] = useState(Math.random());
    const { changeUserDetails, acceptTerms, changeAcceptTerms } = useContext(AppStateContext)
    const userInfoDD = route.params != undefined ? route.params.userInfo : {};
    const { defaultStyle } = useDefaultStyle();
    useEffect(() => {
        if (userInfoDD.hasOwnProperty("idToken")) {
            let emailT = userInfoDD.user.email;
            let passT = userInfoDD.idToken;
            let fullNameT = userInfoDD.user.name;
            setemailId(emailT);
            setpassword(passT);
            setfullName(fullNameT);

        }
    }, [])


    const termsUrl = TERMS_AND_COND_URL;
    const termsBtnClick = useCallback(async () => {
        // Checking if the link is supported for links with custom URL scheme.
        const supported = await Linking.canOpenURL(termsUrl);

        if (supported) {
            // Opening the link with some app, if the URL scheme is "http" the web link should be opened
            // by some browser in the mobile
            await Linking.openURL(termsUrl);
        } else {
            Alert.alert(`Don't know how to open this URL: ${termsUrl}`);
        }
    }, [termsUrl]);

    function registerBtnClick() {

        // Alert.alert("Button Click","Button Click")
        if (!acceptTerms) {
            navigation.navigate('TermsAndConditionScreen')
        }
        else {
            var isValid = true;
            setemailIdErr("");
            setpasswordErr("");
            setfullNameErr("");
            if (emailId.length === 0) {
                setemailIdErr(ErrorMessages.signupEmailIdErr);
                isValid = false;
            }
            if (fullName.length === 0) {
                setfullNameErr(ErrorMessages.signupFullNameErr);
                isValid = false;
            }
            if (isValid) {
                setShowLoading(true);
                appServiceCall();
            }
        }
    }
    function appServiceCall() {

        // Alert.alert("Register Service","Execute")
        let hashMap = {
            _action_code: "11:APP_REGISTER",
            full_name: fullName,
            email: emailId,
            password: password,
            mode: "GMAIL"
        }
        if (referralCode.trim().length !== 0) {
            hashMap.referral_code = referralCode;
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(true);
            seterrorMsg("");
            changeAcceptTerms(false);
            appGmailLoginServiceCall();


        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.full_name) {
                        seterrorMsg(data.data.full_name);
                        setrefreshKey(Math.random());
                        fieldErrorShown = true;
                    }
                    if (data.data.email) {
                        seterrorMsg(data.data.email);
                        setrefreshKey(Math.random());
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        seterrorMsg(data.data.password);
                        setrefreshKey(Math.random());
                        fieldErrorShown = true;
                    }
                    if (data.data.referral_code) {
                        setreferralCodeErr(data.data.referral_code);
                        fieldErrorShown = true;
                    }

                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }
    function appGmailLoginServiceCall() {

        let hashMap = {
            _action_code: "11:GMAIL_LOGIN",
            user_id: emailId,
            password: password,
        }

        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method
            setShowLoading(false);
            let userDeatails = {
                _username: data.data.uid,
                _password: data.data.pwd,
                _profile_seq: data.data.profile_seq,
                _user_seq: data.data.user_seq,
                _user_handle: data.data.user_handle,
                _user_account_type: data.data.account_type,
                _user_display_name: "",
                _has_bank_details: "NO",
                _is_profile_verified: "NO",
                _is_gmail_login: "YES",
                _max_file_size: data.data.max_file_size,
            }
            changeUserDetails(userDeatails);
            navigation.dispatch(
                CommonActions.reset({
                    index: 1,
                    routes: [
                        {
                            name: 'HomeScreen',
                        },
                    ],
                })
            );
            // navigation.replace('HomeScreen')
        }, (errorCode, errorMessage, data) => { // failure method
            setShowLoading(false);
            var fieldErrorShown = false;
            if (errorCode === "E006") {

                if (data && data != null && data.data) {

                    if (data.data.user_id) {
                        seterrorMsg(data.data.user_id);
                        setrefreshKey(Math.random())
                        fieldErrorShown = true;
                    }
                    if (data.data.password) {
                        seterrorMsg(data.data.password);
                        setrefreshKey(Math.random())

                        fieldErrorShown = true;
                    }
                }
            }
            if (!fieldErrorShown) {
                seterrorMsg(errorMessage);
                setrefreshKey(Math.random())
            }
        });
    }
    const loginBtnPress = () => {
        navigation.goBack(null);
    }

    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <View style={{ flex: 1, position: 'relative' }}>
                <LoginSignUpLinearGrad />
                <CustomProgressDialog
                    showLoading={showLoading}
                />
                <ScrollView
                    keyboardShouldPersistTaps="handled">
                    <View>
                        <View style={defaultStyle.signupTextBox}>
                            <EntutoTextView style={defaultStyle.signupText}>Signup to create</EntutoTextView>
                            <EntutoTextView style={defaultStyle.signupText}>new account</EntutoTextView>
                        </View>
                        <View style={defaultStyle.signUpFormBox}>
                            <EntutoEditText
                                showLeftIcon={false}
                                // leftImage={UserIcon}
                                placeholderTxt="Full Name"
                                value={fullName}
                                onChangeText={text => { }}
                                showErrorField={fullNameErr.length}
                                errorMsg={fullNameErr}
                                editable={false}
                            />
                            <EntutoEditText
                                showLeftIcon={false}
                                // leftImage={MailIcon}
                                placeholderTxt="Email"
                                value={emailId}
                                onChangeText={text => { }}
                                showErrorField={emailIdErr.length}
                                errorMsg={emailIdErr}
                                editable={false}
                            />
                            <EntutoEditText
                                showLeftIcon={false}
                                // leftImage={UserRefIcon}
                                placeholderTxt="Referral Code(optional)"
                                value={referralCode}
                                onChangeText={text => { setreferralCode(text); setreferralCodeErr("") }}
                                showErrorField={referralCodeErr.length}
                                errorMsg={referralCodeErr}
                            />
                        </View>
                        <View style={defaultStyle.signUpBtnBox}>
                            <TouchableOpacity style={{ ...defaultStyle.signUpBtn, backgroundColor: '#E59D80' }}
                                onPress={() => registerBtnClick()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Register With Google</EntutoTextView>
                            </TouchableOpacity>
                            <TouchableOpacity style={defaultStyle.signUpBtn}
                                onPress={() => loginBtnPress()}>
                                <EntutoTextView style={defaultStyle.signUpBtnText}>Back</EntutoTextView>
                            </TouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
            </View>
            {
                errorMsg.length != 0 ?
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={refreshKey} />
                    : null
            }
        </>
    )
}

export default SignupGmailScreen;

const styles = StyleSheet.create({
    container: {
        padding: 8,
    },
    signupBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 18,
    },
    signupTxt: {
        color: Colors.bodyTextColor
    },
    signupTxtVal: {
        color: Colors.primaryColor,
    },
    termsconBox: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 5

    },
    termsTxt: {
        borderBottomWidth: 1,
        color: Colors.bodyTextColor
    },
    loginHeadTxt: {
        marginTop: 20,
        marginBottom: 1,
    },
    headBodyTxt: {
        marginTop: 6,
        marginBottom: 18,
    },

})
