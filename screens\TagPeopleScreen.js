import { BackHandler, Image, Modal, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import CustomStatusBar from '../components/common/CustomStatusBar'
import CustomProgressDialog from '../components/common/CustomProgressDialog'
import HomeTopNavigationBar from '../components/HomeTopNavigationBar'
import EntutoTextView from '../components/common/EntutoTextView'
import { AppStateContext } from '..'
import { ScrollView } from 'react-native-gesture-handler'
import TagPeopleSearchComponent from '../components/tag/TagPeopleSearchComponent'
import SelectedTagProfile from '../components/tag/SelectedTagProfile'
import Dimensions from '../constants/Dimensions'
import useDefaultStyle from '../theme/useDefaultStyle'
import useSTheme from '../theme/useSTheme'
import useSThemedStyles from '../theme/useSThemedStyles'

const TagPeopleScreen = ({ navigation }) => {
    const [disabledBtn, setdisabledBtn] = useState(true);
    const [showLoading, setShowLoading] = useState(false);
    const { captureMedia, selectedTagProfileList, changeTagProfileList, } = useContext(AppStateContext);
    const [mediaData, setmediaData] = useState(null);
    const [selectedTagList, setselectedTagList] = useState([]);

    const [modalVisible, setModalVisible] = useState(false);

    const [selectedProfileList, setselectedProfileList] = useState([])
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const style = useSThemedStyles(styles);
    useEffect(() => {
        BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
        return () => {
            BackHandler.removeEventListener("hardwareBackPress", handleBackButtonClick);
        };
    }, []);
    useEffect(() => {
        setselectedTagList([...[], ...selectedTagProfileList]);
    }, [selectedTagProfileList])

    function handleBackButtonClick() {
        // console.log("Hi")
        if (modalVisible) {
            setModalVisible(false);
        }
        else {
            navigation.goBack();
        }
        return true;
    }
    useEffect(() => {
        if (captureMedia != null) {
            setdisabledBtn(false);
        }
        setmediaData(captureMedia);
    }, [captureMedia])

    const tagPeopleDoneBtnPress = () => {
        changeTagProfileList(selectedTagList);
        navigation.goBack();
    }
    const tapPhotoPress = () => {
        let list = selectedTagList;
        let selP = [];
        list.map(obj => {
            selP.push(obj.profile_seq);
        });
        setselectedProfileList([...[], ...selP]);
        setModalVisible(true);
    }
    const tagPeopleSearchPress = (clickID, obj) => {
        setModalVisible(false);
        if (clickID == "SELECTED") {
            let tempVal = selectedTagList;
            setselectedTagList([...tempVal, ...obj.listData]);
        }
    }
    const selectedTagPress = (clickID, obj) => {
        if (clickID == "DELETE") {
            const newAr = [...selectedTagList]
            const index = selectedTagList.findIndex((obj2) => obj2.profile_seq === obj.profileSeq);
            newAr.splice(index, 1);
            setselectedTagList(newAr);
        }

    }
    return (
        <>
            <CustomStatusBar translucent={false} hidden={false} />
            <CustomProgressDialog
                showLoading={showLoading}
            />
            <HomeTopNavigationBar title="Tag People"
                showBorderBottom={false}
                showTopButton={true} navigation={navigation}
                buttonComponent={<TouchableOpacity disabled={disabledBtn} onPress={() => { tagPeopleDoneBtnPress() }}
                ><EntutoTextView style={{ opacity: disabledBtn ? 0.4 : 1, ...defaultStyle.postBtn }}>Done</EntutoTextView></TouchableOpacity>} />

            <Modal
                animationType="fade"
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
                style={{ margin: 0, flex: 1 }}>
                <TagPeopleSearchComponent
                    selectedPList={selectedProfileList}
                    tagPeopleSearchPress={tagPeopleSearchPress}
                    navigation={navigation} />
            </Modal>
            <ScrollView
                style={{ backgroundColor: theme.colors.backgroundColor }}>
                <View>
                    {
                        mediaData != null ?
                            <Pressable
                                onPress={() => tapPhotoPress()}
                                android_ripple={{ color: theme.colors.pressableRippleColor, borderless: false }}
                            >
                                <Image
                                    style={style.tagMedia}
                                    source={{ uri: mediaData.uri }}
                                    resizeMode='contain'
                                />
                            </Pressable>
                            : null}
                    {/* <View style={{ borderBottomColor: theme.colors.borderBottomColor, borderBottomWidth: 1 }} /> */}

                    {
                        selectedTagList.length != 0 ?
                            <>
                                {
                                    selectedTagList.map((obj, i) => {
                                        return <SelectedTagProfile
                                            selectedTagPress={selectedTagPress}
                                            data={obj}
                                            key={i} />
                                    })
                                }
                            </>
                            :
                            <View style={style.tapPhotoTxtBox}>
                                <EntutoTextView style={style.tapPhotoTxt}>
                                    Tap the photo to tag people
                                </EntutoTextView>
                            </View>
                    }


                </View>
            </ScrollView>
        </>
    )
}

export default TagPeopleScreen;

const styles = theme => StyleSheet.create({
    tagMedia: {
        height: Dimensions.screenHeight / 2,
        width: Dimensions.screenWidth,
        marginVertical: 8,
    },
    tapPhotoTxtBox: {
        marginTop: 16,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    tapPhotoTxt: {
        color: theme.colors.primaryColor
    }
})