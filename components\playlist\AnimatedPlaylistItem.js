import React from 'react';
import {
  View,
  Platform,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import LinearGradient from 'react-native-linear-gradient';
import EntutoTextView from '../common/EntutoTextView';
import {checkValueLength, onMaxTextLengthReached} from '../../utils/Utils';
import {MaxPlaylistGridTxtLimit} from '../../utils/Appconfig';

const HOME_IMAGE_HEIGHT = 326;
const HOME_IMAGE_WIDTH = 184;

const AnimatedPlaylistItem = ({
  item,
  index,
  scrollX,
  ITEM_SIZE,
  EMPTY_ITEM_SIZE,
  onPress,
}) => {
  if (!item.thumb_file) {
    if (Platform.OS === 'android') {
      return <View style={{width: EMPTY_ITEM_SIZE}} />;
    } else if (Platform.OS === 'ios') {
      if (item.key === 'spacer-left') {
        return <View style={{width: ITEM_SIZE + 20}} />;
      }
      if (item.key === 'spacer-right') {
        return <View style={{width: EMPTY_ITEM_SIZE}} />;
      }
    }
  }

  let outputRange = [0.7, 1, 0.7];
  if (Platform.OS === 'ios') {
    outputRange = [0.5, 1, 0.8];
  }

  const animatedStyle = useAnimatedStyle(() => {
    'worklet'; // Explicitly mark this as a worklet

    const inputRange = [
      (index - 2) * ITEM_SIZE,
      (index - 1) * ITEM_SIZE,
      index * ITEM_SIZE,
    ];

    const scale = interpolate(
      scrollX.value,
      inputRange,
      outputRange,
      Extrapolation.CLAMP,
    );

    return {
      transform: [{scale}],
    };
  });

  const titleVal = checkValueLength(item.grid_title)
    ? item.grid_title
    : item.title;

  return (
    <View style={{width: ITEM_SIZE}}>
      <TouchableOpacity
        onPress={() => {
          onPress('CLICK', {
            userSeq: item.user_seq,
            profile_seq: item.profile_seq,
            show_seq: item.show_seq,
          });
        }}>
        <Animated.View style={[styles.animatedContainer, animatedStyle]}>
          <Image
            source={{
              uri: checkValueLength(item.home_file)
                ? item.home_file
                : item.thumb_file,
            }}
            style={styles.posterImage}
          />
          <LinearGradient
            colors={['#11111100', '#11111100', '#11111100', '#111111']}
            style={styles.linearGradient}
          />
          <View style={styles.textBox}>
            <EntutoTextView style={styles.textBoxValue}>
              {onMaxTextLengthReached(titleVal, MaxPlaylistGridTxtLimit, '')}asd
            </EntutoTextView>
          </View>
        </Animated.View>
      </TouchableOpacity>
    </View>
  );
};

export default AnimatedPlaylistItem;

const styles = StyleSheet.create({
  animatedContainer: {
    alignItems: 'center',
    height: HOME_IMAGE_HEIGHT,
  },
  posterImage: {
    width: HOME_IMAGE_WIDTH,
    height: HOME_IMAGE_HEIGHT,
    resizeMode: 'cover',
    borderRadius: 7,
    marginBottom: 10,
  },
  linearGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  textBox: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    alignItems: 'center',
  },
  textBoxValue: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
    paddingHorizontal: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: {width: -1, height: 1},
    textShadowRadius: 2,
  },
});
