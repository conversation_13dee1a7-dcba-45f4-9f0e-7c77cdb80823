import React, { useEffect } from 'react'
import { Image, Pressable, StyleSheet, View } from 'react-native'
import Animated, { Extrapolate, interpolate, useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import BookmarkIcon from '../../assets/Images/icon/bookmark.png';
import BookmarkActiveIcon from '../../assets/Images/icon/bookmark.png';
import useSTheme from '../../theme/useSTheme';
const BookmarkBtnComponent = ({ bookmarkButtonPress, inActiveIcon = BookmarkIcon,
    activeIcon = BookmarkActiveIcon, inActiveTintColor = null, activeTintColor = null,
    isBookmark, disable = false, style }) => {
    const bookmarked = useSharedValue(0);
    const theme = useSTheme();
    useEffect(() => {
        if (isBookmark) {
            bookmarked.value = withSpring(1);
        }
        else {
            bookmarked.value = withSpring(0);
        }

    }, [isBookmark])
    const outlineStyle = useAnimatedStyle(() => {
        let testRefresh = Math.random();
        return {
            transform: [
                {
                    scale: interpolate(bookmarked.value, [0, 1], [1, 0], Extrapolate.CLAMP),
                },
            ],
        };
    });

    const fillStyle = useAnimatedStyle(() => {
        let testRefresh = Math.random();
        return {
            transform: [
                {
                    scale: bookmarked.value,
                },
            ],
            opacity: bookmarked.value,
        };
    }, [bookmarked.value]);
    const BookmarkButtonPress = () => {
        bookmarked.value = withSpring(bookmarked.value ? 0 : 1);
        bookmarkButtonPress(bookmarked.value)
    }

    return (
        <Pressable onPress={() => BookmarkButtonPress()} disabled={disable}>
            <Animated.View style={[StyleSheet.absoluteFillObject, outlineStyle]}>
                <Image
                    style={{ ...styles.likeIcon, ...style, tintColor: inActiveTintColor ? inActiveTintColor : theme.colors.postInActiveIconColor }}
                    source={inActiveIcon}
                    resizeMode={'contain'}
                />
            </Animated.View>

            <Animated.View style={fillStyle}>
                <Image
                    style={{ ...styles.likeIcon, ...style, tintColor: activeTintColor ? activeTintColor : theme.colors.postActiveIconColor }}
                    source={activeIcon}
                    resizeMode={'contain'}
                />
            </Animated.View>
        </Pressable>
    );
}

export default BookmarkBtnComponent;

const styles = StyleSheet.create({
    likeIcon: {
        width: 20,
        height: 17,
    },
})
