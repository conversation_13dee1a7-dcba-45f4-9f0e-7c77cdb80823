import React, { useEffect, useState } from 'react'
import { Image, PermissionsAndroid, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'

import EntutoEditText from '../common/EntutoEditText';
import EntutoTextView from '../common/EntutoTextView';
import { Dropdown } from 'react-native-element-dropdown';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import ServerConnector from '../../utils/ServerConnector';
import { _RedirectionErrorList } from '../../utils/Appconfig';
import { RedirectionUrlFunction } from '../../utils/RedirectionUrl';
import LinearGradient from 'react-native-linear-gradient';
import MediaIcon from "../../assets/Images/icon/addmedia.png";
import BottomSheetLoader from '../common/BottomSheetLoader';
import BottomSheetSuccessMsg from '../common/BottomSheetSuccessMsg';
import SuccessFailureMsgBox from '../common/SuccessFailureMsgBox';
import { launchImageLibrary } from 'react-native-image-picker';
import MimeTypeList from '../../utils/MimeTypeList';
import ErrorMessages from '../../constants/ErrorMessages';
import { requestStoragePermission } from '../../utils/PermissionManager';
import CustomSnackbar from '../common/CustomSnackbar';
import useDefaultStyle from '../../theme/useDefaultStyle';
import useSTheme from '../../theme/useSTheme';
import EntutoDropdown from '../common/EntutoDropdown';
import { PopupNegativeButton, PopupPositiveButton } from '../common/PopupButton';

const HelpSetting = ({ navigation, refVal, postSeq, settingsType, ...props }) => {
    const { defaultStyle } = useDefaultStyle();
    const theme = useSTheme();
    const [reasonVal, setreasonVal] = useState("");
    const [reasonValFocus, setreasonValFocus] = useState(false);
    const [reasonData, setreasonData] = useState([]);
    const [comment, setcomment] = useState("");
    const [commentErr, setcommentErr] = useState("");
    const [isSubmitDisable, setisSubmitDisable] = useState(true);

    const [popupHeading, setpopupHeading] = useState("Report Post");
    const [submitBtnTxt, setsubmitBtnTxt] = useState("Report Post");
    const [screenshotMedia, setscreenshotMedia] = useState(null);
    const [showLinkBox, setshowLinkBox] = useState(false);

    const [existLink, setexistLink] = useState("");
    const [existLinkErr, setexistLinkErr] = useState("");

    const [stolenLink, setstolenLink] = useState("");
    const [stolenLinkErr, setstolenLinkErr] = useState("");

    const [showLoading, setshowLoading] = useState(true);
    const [showSuccessMsg, setshowSuccessMsg] = useState(false);
    const [successMsg, setsuccessMsg] = useState("");

    const [errorMsg, seterrorMsg] = useState("");

    const [imageUrl, setimageUrl] = useState(null);

    const [commentLabel, setcommentLabel] = useState("Comment");
    const [maxDropdownHeight, setmaxDropdownHeight] = useState(0)


    const [SnackbarMsg, setSnackbarMsg] = useState("");
    const [displaySnackbar, setdisplaySnackbar] = useState(false);
    const [refreshSnackBar, setrefreshSnackBar] = useState(Math.random());
    const [snackBarType, setsnackBarType] = useState("FAILED");


    useEffect(() => {
        let serviceCodeV = "POST_CATEGORY";
        if (settingsType === "HELP") {
            setpopupHeading("Help");
            setsubmitBtnTxt("Submit");
            setcommentLabel("How can we help you?");
            serviceCodeV = "HELP_OPTION";
        }
        else if (settingsType === "STORY") {
            setpopupHeading("Report Stroy");
            setsubmitBtnTxt("Submit");
            setcommentLabel("Comment");
            serviceCodeV = "POST_CATEGORY";
        }
        getPostCategoryService(serviceCodeV);

    }, [])
    function getPostCategoryService(serviceCode) {
        let hashMap = {
            _action_code: "11:GET_CODE_VALUES",
            code_type: serviceCode,
        }
        let connector = new ServerConnector();
        connector.postData(hashMap, (data) => { // success method            
            setreasonData(data.data);
            let height = 300;
            if (data.data.length <= 2) {
                height = 120;
            }
            setmaxDropdownHeight(height)
            setshowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setreasonData([]);
                setmaxDropdownHeight(0)
                setshowLoading(false);
            }
        });
    }
    const commentChangeHandler = (text) => {
        setcomment(text);
        if (text.length != 0) {
            setisSubmitDisable(false);
        }
        else {
            setisSubmitDisable(true);
        }

    }
    const existLinkChangeHandler = (text) => {
        setexistLink(text);
        setexistLinkErr("");
    }
    const stolenLinkChangeHandler = (text) => {
        setstolenLink(text);
        setstolenLinkErr("");
    }

    const cancelBtnPress = () => {
        props.helpBtnActionClick("negetive", {})
    }
    const submitBtnPress = () => {
        let isFormValid = true;
        if (reasonVal.length === 0) {
            seterrorMsg(ErrorMessages.helpReasonErr);
            isFormValid = false;
        }
        if (comment.length === 0) {
            setcommentErr(ErrorMessages.helpCommentErr)
            isFormValid = false;
        }
        if (reasonVal === "STOLEN" || reasonVal === "HELP_STOLEN") {
            // if (existLink.length === 0) {
            //     setexistLinkErr(ErrorMessages.helpExistLinkErr);
            //     isFormValid = false;
            // }
            if (stolenLink.length === 0) {
                setstolenLinkErr(ErrorMessages.helpStolenLinkErr);
                isFormValid = false;
            }
            //TODO Media
            if (imageUrl == null) {
                seterrorMsg(ErrorMessages.helpStolenImageErr);
                isFormValid = false;
            }
        }
        if (isFormValid) {
            setshowLoading(true);
            reportPostService();
        }
    }
    function reportPostService() {
        let hashMap = {
            _action_code: "11:REPORT_ISSUE",
            post_seq: postSeq,
            reason_code: reasonVal,
            source: settingsType,
            comment: encodeURIComponent(comment),
        }
        let imageHashMap = [];
        if (reasonVal === "STOLEN" || reasonVal === "HELP_STOLEN") {
            // hashMap.link_existing = encodeURIComponent(existLink);
            hashMap.link_stolen = encodeURIComponent(stolenLink);
            var parts = imageUrl.split('.');
            let fileFormat = parts[parts.length - 1];
            let captureFile = {
                uri: Platform.OS === 'android' ? imageUrl : imageUrl.replace('file://', ''),
                name: "media_" + new Date() + "." + fileFormat,
                type: MimeTypeList[fileFormat]
            };
            if (imageUrl != null) {
                imageHashMap.push({ inputName: "File1", imageData: captureFile });
            }
        }
        let connector = new ServerConnector();
        connector.postDataMultiPart(hashMap, imageHashMap, (data) => { // success method            
            setsuccessMsg(data.msg);
            setshowSuccessMsg(true)
            setshowLoading(false);
        }, (errorCode, errorMessage, data) => { // failure method
            if (_RedirectionErrorList.includes(errorCode)) {
                RedirectionUrlFunction(errorCode, errorMessage, data, navigation);
            }
            else {
                setshowLoading(false);
                var fieldErrorShown = false;
                if (errorCode == "E006") {
                    if (data && data != null && data.data) {

                        if (data.data.reason_code) {
                            seterrorMsg(data.data.reason_code);
                            fieldErrorShown = true;
                            return;
                        }
                        if (data.data.comment) {
                            setcommentErr(data.data.commen);
                            fieldErrorShown = true;
                        }

                        if (data.data.source) {
                            seterrorMsg(data.data.source);
                            return;
                        }
                        if (data.data.link_existing) {
                            setexistLinkErr(data.data.link_existing);
                            fieldErrorShown = true;
                        }
                        if (data.data.link_stolen) {
                            setstolenLinkErr(data.data.link_stolen);
                            fieldErrorShown = true;
                        }

                    }
                }
                if (!fieldErrorShown) {
                    seterrorMsg(errorMessage)
                }

            }
        });
    }
    const dropdownLabelBox = (placeholderTxt, dropValue, valueFocus) => {
        if (dropValue || valueFocus) {
            return (
                <Text
                    style={[defaultStyle.dropdownLabel,
                    valueFocus && { color: theme.colors.primaryColor }]}>
                    {placeholderTxt}
                </Text>
            );
        }
        return null;
    };

    const screenshotMediaPress = () => {
        launchImageLibraryData();
    }
    const screenshotRemoveMediaPress = () => {
        setimageUrl(null);
    }
    const resonValueChangeHandler = (item) => {
        let value = item.config_key
        setreasonVal(value);
        if (value === "STOLEN" || value === "HELP_STOLEN") {
            setshowLinkBox(true);
        }
        else {
            setshowLinkBox(false);
        }
        if (settingsType == "HELP") {
            if (value === "HELP_FEEDBACK") {
                setcommentLabel("How can we be better for you?");
            }
            else {
                setcommentLabel("How can we help you?");

            }

        }
    }
    const closeBtnClick = () => {
        props.helpBtnActionClick("close", { erMsg: successMsg })
    }
    const launchImageLibraryData = async () => {
        const checkPermission = requestStoragePermission();
        checkPermission.then(res => {
            if (res) {
                var options = {
                    mediaType: 'photo', //to allow only photo to select ...no video
                    // saveToPhotos: true,  //to store captured photo via camera to photos or else it will be stored in temp folders and will get deleted on temp clear
                    includeBase64: false,
                    // videoQuality: 'low',
                    // durationLimit: appData._videoDurationLimit,
                };
                launchImageLibrary(options, (response) => {

                    if (response.didCancel) {
                        // console.log('User cancelled image picker');
                    } else if (response.error) {
                        // console.log('ImagePicker Error: ', response.error);
                    } else if (response.customButton) {
                        // console.log('User tapped custom button: ', response.customButton);
                    } else {
                        if (response.hasOwnProperty("assets")) {
                            let captureType = "IMAGE";
                            let responseUri = response.assets[0].uri;
                            setimageUrl(responseUri);
                        }
                        else {
                        }
                    }
                });
            }
        });
    }
    return (
        <>
            {
                errorMsg.length != 0 ?
                    // <View style={defaultStyle.errorBoxOutside}>
                    <SuccessFailureMsgBox alertMsg={errorMsg} alertKey={errorMsg} />
                    // </View>
                    : null
            }
            <View style={{ ...defaultStyle.popupBox, }}>

                <EntutoTextView style={defaultStyle.popupHeadTxt}>{popupHeading}</EntutoTextView>
                {
                    showLoading ?
                        <BottomSheetLoader />
                        : null
                }
                {
                    showSuccessMsg ?
                        <BottomSheetSuccessMsg successMsg={successMsg} cancelBtnClick={() => closeBtnClick()} />
                        : null
                }

                <View style={{ ...defaultStyle.dropdownContainer, flexGrow: 1 }}>
                    <EntutoDropdown
                        label='Reason'
                        placeholder='Select Reason'
                        labelField="display_value"
                        valueField="config_key"
                        value={reasonVal}
                        options={reasonData}
                        onOptionChange={resonValueChangeHandler} />
                    {/* {dropdownLabelBox("Reason", reasonVal, reasonValFocus)}
                    <Dropdown
                        style={[defaultStyle.dropdownMain, reasonValFocus && {
                            borderBottomColor: theme.colors.dropdownActiveColor,
                            borderBottomWidth: theme.dimensions.dropdownActiveBorder
                        }]}
                        placeholderStyle={defaultStyle.dropdownPlaceholderStyle}
                        selectedTextStyle={defaultStyle.dropdownSelectedTextStyle}
                        inputSearchStyle={defaultStyle.dropdownInputSearchStyle}
                        data={reasonData}
                        maxHeight={maxDropdownHeight}
                        labelField="display_value"
                        valueField="config_key"
                        placeholder={"Reason"}
                        value={reasonVal}
                        onFocus={() => setreasonValFocus(true)}
                        onBlur={() => setreasonValFocus(false)}
                        onChange={item => {
                            resonValueChangeHandler(item.config_key);
                            setreasonValFocus(false);
                        }}
                        renderRightIcon={() => (
                            <MaterialIcons style={defaultStyle.dropdownIcon} color={reasonValFocus
                                ? theme.colors.dropdownActiveColor : theme.colors.dropdownInActiveColor}
                                name="keyboard-arrow-down" size={theme.dimensions.dropdownRightIcon} />
                        )}
                    /> */}
                </View>
                {
                    showLinkBox ?
                        <>
                            {/* <EntutoEditText
                                labelTxt="Existing content link"
                                placeholderTxt="Existing content link"
                                value={existLink}
                                onChangeText={(text) => existLinkChangeHandler(text)}
                                showErrorField={existLinkErr.length}
                                errorMsg={existLinkErr}
                            /> */}
                            <EntutoEditText
                                labelTxt="Stolen content link"
                                placeholderTxt="Stolen content link"
                                value={stolenLink}
                                onChangeText={(text) => stolenLinkChangeHandler(text)}
                                showErrorField={stolenLinkErr.length}
                                errorMsg={stolenLinkErr}
                            />
                            {
                                !showSuccessMsg ?
                                    <View style={{
                                        ...defaultStyle.verificationCameraBox,
                                        elevation: showLoading ? 0 : 2,
                                        zIndex: 1,
                                    }}>
                                        {
                                            imageUrl != null ?
                                                <>
                                                    <Image
                                                        style={defaultStyle.veriImage}
                                                        source={{ uri: imageUrl }}
                                                        resizeMode='cover' />
                                                    <View style={defaultStyle.veriChangeBox}>
                                                        <LinearGradient colors={[theme.colors.veriGradient1stColor, theme.colors.veriGradient2ndColor]}
                                                            start={{ x: 0, y: 0 }} end={{ x: 0, y: 1 }}
                                                            style={defaultStyle.veriLinearGradient}>
                                                        </LinearGradient>
                                                        <TouchableOpacity style={defaultStyle.veriCameraBtnBox}
                                                            onPress={() => screenshotMediaPress()}>
                                                            <View style={defaultStyle.veriCameraBtnBox}>
                                                                <EntutoTextView style={defaultStyle.veriCameraBoxBtn}>Change</EntutoTextView>
                                                            </View>
                                                        </TouchableOpacity>
                                                        <TouchableOpacity style={defaultStyle.veriCameraBtnBox}
                                                            onPress={() => screenshotRemoveMediaPress()}>
                                                            <View style={defaultStyle.veriCameraBtnBox}>
                                                                <EntutoTextView style={defaultStyle.veriCameraBoxBtn}>Remove</EntutoTextView>
                                                            </View>
                                                        </TouchableOpacity>

                                                    </View>
                                                </>
                                                :
                                                <TouchableOpacity onPress={() => screenshotMediaPress()}>
                                                    <View style={defaultStyle.veriCameraBox}>
                                                        <Image source={MediaIcon}
                                                            style={defaultStyle.veriCameraIconStyle} />
                                                        <EntutoTextView style={defaultStyle.veriCameraIconTxt}>
                                                            Add screenshot of stolen content
                                                        </EntutoTextView>
                                                    </View>
                                                </TouchableOpacity>
                                        }
                                    </View>
                                    : null
                            }
                        </>
                        : null
                }
                <EntutoEditText
                    labelTxt={commentLabel}
                    placeholderTxt={commentLabel}
                    value={comment}
                    onChangeText={(text) => commentChangeHandler(text)}
                    showErrorField={commentErr.length}
                    errorMsg={commentErr}
                />

                <View style={{ flexDirection: 'row', flex: 1, marginTop: 16, marginBottom: 15, }}>
                    <View style={{ flex: 1 }}>
                        <PopupNegativeButton
                            onPress={() => cancelBtnPress()}
                            btnText='No'
                            style={{ marginEnd: theme.dimensions.popupBtnGap }} />
                    </View>
                    <View style={{ flex: 1 }}>
                        <PopupPositiveButton
                            disabled={isSubmitDisable}
                            onPress={() => submitBtnPress()}
                            btnText={submitBtnTxt} />
                    </View>
                </View>
            </View>
            <CustomSnackbar snackType={snackBarType} snackMsg={SnackbarMsg} displaySnackbar={displaySnackbar} refreshSnack={refreshSnackBar} />
        </>
    )
}

export default HelpSetting;

const styles = StyleSheet.create({})
