import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import IntroSliderBoxComponent from '../components/introslider/IntroSliderBoxComponent'
import CustomStatusBar from '../components/common/CustomStatusBar'
import { _setFirstTimeUser } from '../utils/AuthLogin'

const AppIntroSliderScreen = ({ navigation }) => {
  const sliderBoxComponentPress = () => {
    _setFirstTimeUser("YES");
    navigation.goBack();
  }
  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <IntroSliderBoxComponent navigation={navigation} sliderBoxComponentPress={() => sliderBoxComponentPress()} />
    </>
  )
}

export default AppIntroSliderScreen

const styles = StyleSheet.create({})