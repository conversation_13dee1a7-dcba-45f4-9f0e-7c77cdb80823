import { Animated, StyleSheet, Text, useWindowDimensions, View } from 'react-native'
import React from 'react'
import Colors from '../../constants/Colors'

const IntroSliderPaginator = ({ data, scrollX }) => {
    const { width } = useWindowDimensions();
    return (
        <View style={{ flexDirection: 'row', }}>
            {
                data.map((_, i) => {
                    const inputRange = [(i - 1) * width, i * width, (i + 1) * width];
                    const dotWidth = scrollX.interpolate({
                        inputRange,
                        outputRange: [10, 30, 10],
                        extrapolate: 'clamp'
                    })
                    const opacity = scrollX.interpolate({
                        inputRange,
                        outputRange: [0.3, 1, 0.3],
                        extrapolate: 'clamp'
                    })
                    const backgroundColor = scrollX.interpolate({
                        inputRange,
                        outputRange: ["#e0ac9f", Colors.primaryColor, "#e0ac9f"],
                        extrapolate: 'clamp'
                    })
                    return <Animated.View style={[styles.dot, { width: dotWidth, backgroundColor }]} key={i.toString()} />
                })
            }
        </View>
    )
}

export default IntroSliderPaginator

const styles = StyleSheet.create({
    dot: {
        height: 10,
        borderRadius: 5,
        marginHorizontal: 8,
    }
})