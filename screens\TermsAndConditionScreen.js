import {StyleSheet, Text, View} from 'react-native';
import React, {useContext, useState} from 'react';
import CustomStatusBar from '../components/common/CustomStatusBar';
import HomeTopNavigationBar from '../components/HomeTopNavigationBar';
import Dimensions from '../constants/Dimensions';
import {ActivityIndicator} from 'react-native-paper';
import {AppStateContext} from '..';
import ButtonSheetButton from '../components/common/ButtonSheetButton';
import LinearGradient from 'react-native-linear-gradient';

const TermsAndConditionScreen = ({route, navigation}) => {
  const {changeAcceptTerms} = useContext(AppStateContext);
  const acceptBtnPress = () => {
    changeAcceptTerms(true);
    navigation.goBack(null);
  };
  const rejectBtnPress = () => {
    changeAcceptTerms(false);
    navigation.goBack(null);
  };
  return (
    <>
      <CustomStatusBar translucent={false} hidden={false} />
      <HomeTopNavigationBar
        showBackBtn={true}
        showBorderBottom={false}
        title="Terms and Use"
        navigation={navigation}
      />
      <View
        style={{
          minHeight: Dimensions.screenHeight - 70,
          paddingBottom: 160,
          backgroundColor: '#111111',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Text
          style={{color: 'white', textAlign: 'center', marginHorizontal: 20}}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua. Lorem
          ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Lorem ipsum dolor
          sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
          incididunt ut labore et dolore magna aliqua. Lorem ipsum dolor sit
          amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut
          labore et dolore magna aliqua. Lorem ipsum dolor sit amet, consectetur
          adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore
          magna aliqua. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua. Lorem
          ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod
          tempor incididunt ut labore et dolore magna aliqua. Lorem ipsum dolor
          sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
          incididunt ut labore et dolore magna aliqua.
        </Text>
      </View>
      <View style={styles.acceptBtnBox}>
        <LinearGradient
          colors={['#FFFFFF00', '#FFFFFF']}
          locations={[0.05, 0.6]}
          start={{x: 0.5, y: 0.1}}
          end={{x: 0.5, y: 1.0}}
          style={styles.linearGrad}
        />
        <View style={styles.boxView}>
          <ButtonSheetButton
            onPress={acceptBtnPress}
            btnLabel="Accept"
            style={{marginLeft: 16, marginRight: 8, borderRadius: 1}}
          />
          <ButtonSheetButton
            onPress={rejectBtnPress}
            btnLabel="Decline"
            style={{
              marginLeft: 8,
              marginRight: 16,
              borderWidth: 1,
              borderColor: '#D7DDE1',
              borderRadius: 1,
            }}
            backgroundColor="#FFFFFF"
            btnTextColor="#778087"
          />
        </View>
      </View>
    </>
  );
};

export default TermsAndConditionScreen;

const styles = StyleSheet.create({
  acceptBtnBox: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    minHeight: 170,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  boxView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  linearGrad: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
});
