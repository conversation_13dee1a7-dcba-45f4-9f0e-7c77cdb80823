import { decode as decodeEntites, encode as encodeEntites } from 'html-entities';
import { Alert, Linking } from 'react-native';
import ErrorMessages from '../constants/ErrorMessages';
import { REACTION_LIST } from '../data/MetaData';
export function HasValueInObject(object, key) {
    let ret_data = "--";
    if (object.hasOwnProperty(key)) {
        if (object[key] != null && object[key].length != 0) {
            ret_data = object[key]
        }
    }
    return ret_data;
}
export function dateDbFormat(_date) {

    let converted = new Date(_date);
    var month = converted.getMonth() + 1;
    var day = converted.getDate();
    var year = converted.getFullYear();
    return year + "-" + (month.toString().length === 1 ? "0" + month.toString() : month) + "-" + (day.toString().length === 1 ? "0" + day.toString() : day);
}
export function dispalyTimeFormat(_time) {

    var timeData = (_time || '').split(':')
    var hours = timeData[0];
    var minutes = timeData[1];
    var minutesData = timeData[1];

    var ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12;
    if (parseInt(minutes) === 0) {
        minutesData = minutes;
    }
    else if (parseInt(minutes) < 10) {
        minutesData = '0' + minutes;
    }
    return hours + ':' + minutesData + ' ' + ampm;
}
export function dbTimeFormat(_time) {

    let converted = new Date(_time);
    var hours = converted.getHours();
    var minutes = converted.getMinutes();
    var seconds = converted.getSeconds();
    if (isNaN(hours)) {
        return "";
    }
    let hoursD = hours.toString().length === 1 ? "0" + hours.toString() : hours;
    return hoursD + ":" + (minutes.toString().length === 1 ? "0" + minutes.toString() : minutes) + ":" + (seconds.toString().length === 1 ? "0" + seconds.toString() : seconds);
}
export const month_names = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
export const month_names_short = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
export function DateDisplayFormat(_date) {

    let converted = new Date(_date);
    var month = converted.getMonth();
    var day = converted.getDate();
    var year = converted.getFullYear();
    return (day.toString().length === 1 ? "0" + day.toString() : day) + "-" + month_names_short[month] + "-" + year;
}
export function hasImageUrlExist(url) {
    if (url == undefined) {
        return false;
    }
    if (url == null) {
        return false;
    }
    if (url.length == 0) {
        return false;
    }
    return true;
}
export function indianMoneyFormatDisplay(value) {
    let formatter = new Intl.NumberFormat('en-IN', { maximumSignificantDigits: 3 });

    let fValue = formatter.format(value);
    let formatValue = fValue;
    if (Number.isInteger(value)) {
        formatValue = fValue + ".00";
    }
    else if (typeof value == "string") {
        formatValue = "0.00";
    }

    return formatValue;
}
export function getExtension(filename) {
    var parts = filename.split('.');
    return parts[parts.length - 1];
}
const videoExtList = ['m4v', 'avi', 'mpg', 'mp4', 'webm', '3gp', 'mpeg', 'wmv', 'mov', 'ogg'];
export function isImage(filename) {
    var ext = getExtension(filename);
    let imgExtList = ['jpg', 'gif', 'bmp', 'png', 'jpeg', 'apng', 'avif', 'jfif', 'pjpeg', 'pjp', 'svg', 'webp', 'tif', 'tiff', 'ico'];
    let isTypeExist = imgExtList.includes(ext.toLowerCase());
    return isTypeExist;
}
export function isVideoFilename(filename) {
    var ext = getExtension(filename);
    let isTypeExist = videoExtList.includes(ext.toLowerCase());
    return isTypeExist;
}
export function isVideo(ext) {
    let isTypeExist = videoExtList.includes(ext.toLowerCase());
    return isTypeExist;
}
export const decodeHtmlEntitessData = (strValue) => {
    if (strValue == null || strValue.length == 0) return "";
    let str = strValue;
    let result1 = decodeEntites(str, { level: 'html5' });
    let result2 = decodeEntites(result1, { level: 'html5' });
    return result2;
}
export function getSecondsBetweenDates(fromDate, toDate) {
    let t1 = new Date(fromDate);
    let t2 = new Date(toDate);
    let dif = t1.getTime() - t2.getTime();
    let Seconds_from_T1_to_T2 = dif / 1000;
    let Seconds_Between_Dates = Math.abs(Seconds_from_T1_to_T2);
    return Seconds_Between_Dates;
}
export const _getPanNoFromGst = (gstVal) => {
    let gst = gstVal;//"29AADCE9398M1ZN";//29AADCE9398M1ZN
    let res = gst.substring(2, 12);
    return res;
}
export const encodeHtmlEntitessData = (strValue) => {
    if (strValue.length == 0) return "";
    let str = strValue;
    let result1 = encodeEntites(str, { mode: 'extensive' });
    return result1;
}
export const _inputFormatTextForTag = (mainTxt, updateValue) => {
    let txtNewLineArr = mainTxt.split("\n");
    let lastNewLineWord = txtNewLineArr[txtNewLineArr.length - 1]
    let textStArr = lastNewLineWord.split(" ");
    let lastIndex = textStArr.length - 1;
    textStArr[lastIndex] = updateValue;

    let formatTxt = textStArr.join(" ");
    txtNewLineArr[txtNewLineArr.length - 1] = formatTxt;
    let formatLinetxt = txtNewLineArr.join("\n");
    return formatLinetxt;
}
export function hasDataExist(url) {
    if (url == undefined) {
        return false;
    }
    if (url == null) {
        return false;
    }
    if (url.length == 0) {
        return false;
    }
    return true;
}
export function AlertForCameraPermission() {
    Alert.alert(ErrorMessages.permissionCameraHeaderMsg, ErrorMessages.permissionCameraMsg, [
        {
            text: 'Permission',
            onPress: () => Linking.openSettings(),
        },
        {
            text: 'Cancel',
            onPress: () => console.log('Cancel Pressed'),
            style: 'cancel',
        },
    ]);
    return false;
}
export function AlertForStoragePermission() {
    Alert.alert(ErrorMessages.permissionStorageHeaderMsg, ErrorMessages.permissionStorageMsg, [
        {
            text: 'Permission',
            onPress: () => Linking.openSettings(),
        },
        {
            text: 'Cancel',
            onPress: () => console.log('Cancel Pressed'),
            style: 'cancel',
        },
    ]);
    return false;
}
export function AlertForReadStoragePermission() {
    Alert.alert(ErrorMessages.permissionStorageHeaderMsg, ErrorMessages.permissionStorageMsg, [
        {
            text: 'Permission',
            onPress: () => Linking.openSettings(),
        },
        {
            text: 'Cancel',
            onPress: () => console.log('Cancel Pressed'),
            style: 'cancel',
        },
    ]);
    return false;
}
//Encryption Logic
const ALOGO_KEY_ARRAY = [
    { decrypt_key: "0", encrypt_key: "Ac" },
    { decrypt_key: "1", encrypt_key: "RT" },
    { decrypt_key: "2", encrypt_key: "WD" },
    { decrypt_key: "3", encrypt_key: "cA" },
    { decrypt_key: "4", encrypt_key: "AB" },
    { decrypt_key: "5", encrypt_key: "ui" },
    { decrypt_key: "6", encrypt_key: "Nb" },
    { decrypt_key: "7", encrypt_key: "Po" },
    { decrypt_key: "8", encrypt_key: "qw" },
    { decrypt_key: "9", encrypt_key: "Er" },
]
export function encryptOnlyNumber(number) {
    let numberString = String(number);
    let encryptString = "";
    for (let i = 0; i < numberString.length; i++) {
        let encrypt_val = getValueEncryptDecrypt(numberString[i], "ENC")
        encryptString += encrypt_val;
    }
    return encryptString;
}
export function decryptOnlyEncryptNumber(encryptVal) {
    let encryptValString = String(encryptVal);
    let convertedString = "";
    let keyFound = true;
    for (let j = 0; j < encryptValString.length; j += 2) {
        if (j + 1 <= encryptValString.length - 1) {
            let valueString = encryptValString[j] + encryptValString[j + 1];
            let decrypt_val = getValueEncryptDecrypt(valueString, "DEC")
            if (decrypt_val.length != 0) {
                convertedString += decrypt_val;
            }
            else {
                keyFound = false;
            }
        }
        else {
            keyFound = false;
        }
    }
    if (!keyFound) {
        return -1
    } else {
        return convertedString
    }
}
function getValueEncryptDecrypt(value, type) {
    let res_val = ""
    ALOGO_KEY_ARRAY.map((obj) => {
        if (type == "ENC") {
            if (String(obj.decrypt_key) == String(value)) {
                res_val = obj.encrypt_key;
            }
        }
        if (type == "DEC") {
            if (String(obj.encrypt_key) == String(value)) {
                res_val = obj.decrypt_key;
            }
        }
    });
    return res_val;
}
export function creationOfCopyLink(type, value) {
    let link = "";
    if (type === "POST") {
        let encryptPostSeq = encryptOnlyNumber(value);
        link = "https://sotrue.co.in/post/?id=" + encryptPostSeq;
    }
    else if (type === "PROFILE") {
        let encryptProfileSeq = encryptOnlyNumber(value);
        link = "https://sotrue.co.in/profile/?id=" + encryptProfileSeq;
    }
    else {
        link = "No Link Found!"
    }
    return link;
}
export function preventDoubleClick(lastClick = new Date(), clickHappen = 3) {
    let now = new Date();
    let timeDiff = Math.abs(lastClick - now);
    let second = Math.floor((timeDiff / 1000))
    if (second >= clickHappen) {
        return true;
    }
    return false;
}
export function checkValueLength(value) {
    if (value != null && value.length != 0) {
        return true;
    }
    return false;
}
export function DisplayTimeFormat(dateVal) {
    let date = new Date(dateVal);
    let formatter = new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
    let formattedTime = formatter.format(date);
    return formattedTime;
}
export function DBDateTimeFormat(_timestamp) {
    let timeStampVal = dateDbFormat(_timestamp) + " " + dbTimeFormat(_timestamp)
    return timeStampVal;
}
export function getValueFromReactions(checkedValue = "", checkedKey = "value", returnType = "object",) {
    const tempList = JSON.parse(JSON.stringify(REACTION_LIST));
    const filteredList = tempList.filter(item => item[checkedKey] === checkedValue);
    if (filteredList.length > 0) {
        if (returnType == "object") {
            return filteredList[0];
        }
        else if (returnType == "icon") {
            return filteredList[0].icon;
        }
        else if (returnType == "label") {
            return filteredList[0].label;
        }
        else if (returnType == "value") {
            return filteredList[0].value;
        }
        else {
            return filteredList;
        }
    }
    else {
        return null;
    }


}
export function currentTimeDiffCheck(lastCheck = new Date(), diffSec = 300) {
    let now = new Date();
    let timeDiff = Math.abs(lastCheck - now);
    let second = Math.floor((timeDiff / 1000))
    if (second >= diffSec) {
        return true;
    }
    return false;
}
export function getDisplayNameFromArray(arrayList, value, keyName, displayKey) {
    let displayNameObj = arrayList.filter(item => item[keyName] === value);
    if (displayNameObj.length != 0) {
        return displayNameObj[0][displayKey];
    }
    return value;
}
export function formatLikeNumber(value) {
    if (value == undefined || value == -1) {
        return 0;
    }
    return value;
}
export function onMaxTextLengthReached(text = "", maxLength = 24, lastText = "...") {
    if (!text || maxLength <= 0) return text;
    if (text.length <= maxLength) return text;
    return `${text.substring(0, maxLength)}${lastText}`;
}
export function pluralize(text, count) {
    return `${count === 1 ? text : text + 's'}`;
}
export function secondsToHms(secondValue) {
    secondValue = parseFloat(secondValue);
    const h = Math.floor(secondValue / 3600);
    const m = Math.floor((secondValue % 3600) / 60);
    const s = Math.floor(secondValue % 60);

    const format = (val, unit) => val > 0 ? `${val}${unit}${val === 1 ? '' : ''}` : '';
    const hDisplay = format(h, 'h');
    const mDisplay = format(m, 'm');
    const sDisplay = format(s, 's');

    return [hDisplay, mDisplay, sDisplay].filter(Boolean).join(' ');
}
