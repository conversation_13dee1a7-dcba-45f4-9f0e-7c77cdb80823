import React, {useState, useEffect} from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {TextInput} from 'react-native-paper';
import EntutoTextView from './EntutoTextView';
import useSTheme from '../../theme/useSTheme';
import useSThemedStyles from '../../theme/useSThemedStyles';

const EntutoEditText = ({
  labelTxt = '',
  refValue = null,
  placeholderTxt = '',
  showErrorField = false,
  errorMsg = '',
  showLeftIcon = false,
  leftImage = null,
  leftComponent = null,
  maxLength = 200,
  endComponent = null,
  paddingEndValue = 0,
  secureEntryTxt = false,
  showRightIcon = false,
  rightComponent = null,
  newDesign = false,
  boxStyle = {},
  disabledField = false,
  marginBottom = null,
  noLabel = false,
  showPasswordIcon = false,
  mode = 'outlined',
  textColor = null,
  activeUnderlineColor = false,
  backgroundColor = null,
  showChips = false,
  chipData = [],
  onChipPress = null,
  ...props
}) => {
  const [showPassword, setshowPassword] = useState(false);
  const [secureEntry, setsecureEntry] = useState(secureEntryTxt);
  const [isFocused, setIsFocused] = useState(false);
  const theme = useSTheme();
  const style = useSThemedStyles(styles);
  const [activeUnderLineColor, setactiveUnderLineColor] = useState(
    theme.colors.inputOutlineColor,
  );
  const [borderWidth, setborderWidth] = useState(1);

  const onInputFocus = e => {
    setIsFocused(true);
    setborderWidth(2);
    let isErrorMsgDisplay = false;
    if (showErrorField || showErrorField != 0) {
      if (errorMsg.length != 0) {
        isErrorMsgDisplay = true;
      }
    }
    if (isErrorMsgDisplay) {
      setactiveUnderLineColor(theme.colors.errorColor);
    } else {
      setactiveUnderLineColor(theme.colors.primaryColor);
    }
    // Call original onFocus if provided
    if (props.onFocus) {
      props.onFocus(e);
    }
  };

  const onInputBlur = e => {
    setIsFocused(false);
    setborderWidth(1);
    let isErrorMsgDisplay = false;
    if (showErrorField || showErrorField != 0) {
      if (errorMsg.length != 0) {
        isErrorMsgDisplay = true;
      }
    }

    if (isErrorMsgDisplay) {
      setactiveUnderLineColor(theme.colors.errorColor);
    } else {
      setactiveUnderLineColor(theme.colors.inputOutlineColor);
    }
    // Call original onBlur if provided
    if (props.onBlur) {
      props.onBlur(e);
    }
  };
  const passwordBtnPress = () => {
    setsecureEntry(prevState => !prevState);
    setshowPassword(prevState => !prevState);
  };
  useEffect(() => {
    if (showErrorField || showErrorField.length != 0) {
      if (errorMsg.length != 0) {
        setborderWidth(1);
        setactiveUnderLineColor(theme.colors.errorColor);
      }
    }
  }, [showErrorField, errorMsg]);
  useEffect(() => {
    onInputBlur();
  }, [disabledField]);

  const handleInputChange = text => {
    if (!disabledField) {
      props.onChangeText && props.onChangeText(text);
    }
  };

  const handleChipPress = chipText => {
    if (onChipPress) {
      onChipPress(chipText);
    }
  };

  const renderChips = () => {
    if (!showChips || !isFocused || chipData.length === 0) {
      return null;
    }

    return (
      <View style={style.chipsContainer}>
        {chipData.map((chip, index) => (
          <TouchableOpacity
            key={index}
            style={style.chip}
            onPress={() => handleChipPress(chip)}>
            <EntutoTextView style={style.chipText}>{chip}</EntutoTextView>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View
      style={{
        flexDirection: 'column',
        marginBottom:
          marginBottom != null ? marginBottom : theme.dimensions.inputTextGap,
        ...boxStyle,
      }}>
      {newDesign ? (
        <View style={style.newDesignContainer}>
          <TextInput
            ref={refValue}
            label={labelTxt.length != 0 ? labelTxt : ''}
            placeholder={placeholderTxt.length != 0 ? placeholderTxt : ''}
            onFocus={onInputFocus}
            onBlur={onInputBlur}
            mode={mode}
            underlineColor="transparent"
            activeUnderlineColor="transparent"
            secureTextEntry={secureEntry}
            selectionColor={theme.colors.primaryColor}
            theme={{
              roundness: theme.dimensions.textInputRadius,
              colors: {
                placeholder: disabledField
                  ? theme.colors.inputDisabledTextColor
                  : theme.colors.inputPlaceholderColor,
                text: disabledField
                  ? theme.colors.inputDisabledTextColor
                  : theme.colors.inputTextColor,
                primary: disabledField
                  ? theme.colors.inputDisabledTextColor
                  : theme.colors.inputPrimaryColor,
              },
              fonts: {
                regular: {
                  fontFamily: theme.getFontFamily(),
                },
              },
            }}
            style={{
              ...style.input,
              backgroundColor: theme.colors.editTextBackgroundColor,
              ...props.style,
            }}
            maxLength={maxLength}
            contentStyle={{fontFamily: 'monospace'}}
            {...props}
          />
        </View>
      ) : (
        <View style={{flexDirection: 'row', flex: 1}}>
          {showLeftIcon && (
            <>
              <View
                style={{
                  borderBottomColor: activeUnderLineColor,
                  borderBottomWidth: borderWidth,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                {leftComponent != null ? (
                  leftComponent
                ) : (
                  <Image style={style.leftIcon} source={leftImage} />
                )}
              </View>
            </>
          )}
          <View
            style={{
              ...style.inputView,
              borderColor: activeUnderLineColor,
              flex: 1,
              position: 'relative',
            }}>
            <TextInput
              ref={refValue}
              label={!noLabel ? (labelTxt.length != 0 ? labelTxt : '') : ''}
              placeholder={placeholderTxt.length != 0 ? placeholderTxt : ''}
              onFocus={onInputFocus}
              onBlur={onInputBlur}
              mode={mode}
              // selectionColor={theme.colors.primaryColor}
              // outlineColor={theme.colors.transparentColor}
              underlineColor={activeUnderLineColor}
              activeOutlineColor={theme.colors.primaryColor}
              activeUnderlineColor={
                activeUnderlineColor ? theme.colors.primaryColor : 'transparent'
              }
              secureTextEntry={secureEntry}
              theme={{
                roundness: theme.dimensions.textInputRadius,
                colors: {
                  placeholder: disabledField
                    ? theme.colors.inputDisabledTextColor
                    : theme.colors.inputPlaceholderColor,
                  text: disabledField
                    ? theme.colors.inputDisabledTextColor
                    : theme.colors.inputTextColor,
                  primary: disabledField
                    ? theme.colors.inputDisabledTextColor
                    : theme.colors.inputPrimaryColor,
                  ...(textColor ? {text: textColor, primary: textColor} : {}),
                },
                fonts: {
                  regular: {
                    fontFamily: theme.getFontFamily(),
                  },
                },
              }}
              style={{
                ...style.input,
                ...props.style,
                ...(backgroundColor != null && backgroundColor),
                ...(endComponent != null && {paddingEnd: paddingEndValue}),
                ...(showPasswordIcon && {paddingEnd: 80}),
              }}
              editable={!disabledField}
              maxLength={maxLength}
              {...props}
              onChangeText={text => handleInputChange(text)}
            />
            {endComponent != null ? (
              <View
                style={{
                  position: 'absolute',
                  top: 0,
                  right: 1,
                  bottom: 0,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                {endComponent}
              </View>
            ) : null}
            {showPasswordIcon ? (
              <View
                style={{
                  position: 'absolute',
                  top: 0,
                  right: 10,
                  bottom: 0,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <TouchableOpacity
                  style={{paddingVertical: 8, paddingLeft: 8, paddingRight: 8}}
                  onPress={() => passwordBtnPress()}>
                  <View style={{alignSelf: 'center', alignItems: 'center'}}>
                    {showPassword ? (
                      <Image
                        style={style.eyeOpenStyle}
                        source={require('../../assets/Images/icon/eye_open.png')}
                        resizeMode="cover"
                      />
                    ) : (
                      <Image
                        style={style.eyeCloseStyle}
                        source={require('../../assets/Images/icon/eye_close.png')}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              </View>
            ) : null}
          </View>
          {showRightIcon && (
            <>
              <View
                style={{
                  borderBottomColor: activeUnderLineColor,
                  borderBottomWidth: borderWidth,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                {rightComponent != null ? (
                  rightComponent
                ) : (
                  <TouchableOpacity
                    style={{
                      paddingVertical: 8,
                      paddingLeft: 8,
                      paddingRight: 8,
                    }}
                    onPress={() => passwordBtnPress()}>
                    <View style={{alignSelf: 'center', alignItems: 'center'}}>
                      {showPassword ? (
                        <Image
                          style={style.eyeOpenStyle}
                          source={require('../../assets/Images/icon/eye_open.png')}
                          resizeMode="cover"
                        />
                      ) : (
                        <Image
                          style={style.eyeCloseStyle}
                          source={require('../../assets/Images/icon/eye_close.png')}
                        />
                      )}
                    </View>
                  </TouchableOpacity>
                )}
              </View>
            </>
          )}
        </View>
      )}

      {/* Render chips */}
      {renderChips()}

      {showErrorField ? (
        <EntutoTextView
          style={{...style.errorTxt, color: theme.colors.errorColor}}>
          {errorMsg}
        </EntutoTextView>
      ) : null}
    </View>
  );
};

export default EntutoEditText;

const styles = theme =>
  StyleSheet.create({
    inputView: {
      flexDirection: 'row',
      // borderBottomWidth: theme.dimensions.inputTextBorderBootom,
      alignItems: 'center',
      justifyContent: 'center',
    },
    input: {
      flex: 1,
      paddingVertical: 0,
      fontSize: theme.calculateFontSize(theme.dimensions.inputTextFontSize),
      // alignSelf: 'baseline',
      marginBottom: 0,
      borderRadius: 0,
    },
    leftIcon: {
      width: theme.dimensions.inputTextIconWidth,
      height: theme.dimensions.inputTextIconHeight,
      alignSelf: 'center',
    },
    labelTxt: {
      fontSize: theme.calculateFontSize(theme.dimensions.inputTextFontSize),
    },
    errorTxt: {
      fontSize: theme.calculateFontSize(theme.dimensions.inputTextErrorText),
      marginTop: 6,
    },
    eyeCloseStyle: {
      width: 24,
      height: 8,
      alignSelf: 'center',
    },
    eyeOpenStyle: {
      width: 24,
      height: 18,
      alignSelf: 'center',
    },
    newDesignContainer: {
      height: 50,
      borderRadius: 50 / 2,
      paddingHorizontal: 20,
      borderWidth: 1,
      borderColor: theme.colors.primaryColor,
      backgroundColor: theme.colors.textInputBackgroundColor,
      flexDirection: 'row',
      alignItems: 'center',
    },
    // Chip styles
    chipsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginTop: 8,
    },

    chip: {
      width: '48%', // Two per row
      paddingVertical: 8,
      borderWidth: 1,
      borderColor: theme.colors.primaryTextColor,

      marginBottom: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },

    chipText: {
      fontSize: 12,
      color: theme.colors.primaryTextColor,
      fontWeight: '500',
    },
  });
