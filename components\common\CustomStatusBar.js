import React from 'react'
import { StatusBar, SafeAreaView, View, Platform } from 'react-native'
import Colors from '../../constants/Colors'
import useSTheme from '../../theme/useSTheme';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const CustomStatusBar = ({ animated = true, translucent = false, hidden = false, ...props }) => {
    const theme = useSTheme();
    const insets = useSafeAreaInsets();
    return (
        <>
            {
                Platform.OS == "android" ?
                    <>
                        <StatusBar
                            hidden={hidden}
                            showHideTransition={'none'}
                            translucent={translucent}
                            animated={animated}
                            barStyle={theme.appThemeType == 'DARK' ? 'light-content' : 'dark-content'}
                            backgroundColor={theme.colors.backgroundColor}
                            {...props}
                        />
                        {
                            !translucent ?
                                <View style={{ paddingTop: insets.top, backgroundColor: theme.colors.backgroundColor }} /> : null
                        }
                    </>
                    : null
            }
            {
                Platform.OS == 'ios' ?
                    <>
                        {/* <SafeAreaView> */}

                        <View style={{ height: insets.top, backgroundColor: theme.colors.backgroundColor }} />

                        <StatusBar
                            // hidden={hidden}
                            translucent={translucent}
                            animated={animated}
                            barStyle={theme.appThemeType == 'DARK' ? 'light-content' : 'dark-content'}
                            backgroundColor={theme.colors.backgroundColor}
                            {...props}
                        />
                        {/* </SafeAreaView> */}
                    </> : null
            }
        </>
    )
}

export default CustomStatusBar;
