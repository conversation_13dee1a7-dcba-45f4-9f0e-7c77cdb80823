import { Dimensions, Easing, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import { Image } from 'react-native'
import ShareIcon from '../../assets/Images/share_animation.gif'
import Animated, { FadeInDown, FadeOutDown, FadeOutUp } from 'react-native-reanimated'

const SharePostIcon = ({ style }) => {

    return (

        <Image
            style={{ ...styles.postActionIcon, }}
            source={ShareIcon}
            resizeMode="contain"

        />
    )
}

export default SharePostIcon

const styles = StyleSheet.create({
    postActionIcon: {
        width: 32,
        height: 32,
        position: 'absolute',
        left: -21,
        right: 0,
        top: -20, bottom: 0,
        zIndex: 100
    }
})