import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { ActivityIndicator } from 'react-native-paper';
import useSTheme from '../../theme/useSTheme';

const BottomSheetLoader = () => {
    const theme = useSTheme();
    return (
        <View style={{ ...styles.loaderBox, backgroundColor: theme.colors.backgroundColor }}>
            <ActivityIndicator animating={true} color={theme.colors.primaryColor} size={40} />
        </View>
    );
};

export default BottomSheetLoader;

const styles = StyleSheet.create({
    loaderBox: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 3,

    }
});
